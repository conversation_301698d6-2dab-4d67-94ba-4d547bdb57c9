<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="推广名称">
        <el-select
          v-model="queryParams.productId"
          filterable
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border>
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="分配账号" prop="name" align="center" />
      <el-table-column label="线索量" prop="clueSum" align="center" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getClewStatistics,
  getPartyaAdminProductlist,
} from "@/api/partyManage";
export default {
  data() {
    return {
      total: 0,
      dataList: [],
      options: [],
      value1: [this.getTime(), this.getTime()],
      queryParams: {
        productId: "",
        pageNum: 1,
        pageSize: 10,
        startTime: this.getTime(),
        stopTime: this.getTime(),
      },
    };
  },
  methods: {
    handleQuery() {
      if (this.value1 !== null) {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.value1[0];
        this.queryParams.stopTime = this.value1[1];
      } else {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = "";
        this.queryParams.stopTime = "";
      }
      this.getList();
    },
    getList() {
      getClewStatistics(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total;
      });
    },
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
  },
  mounted() {
    this.getList();
    getPartyaAdminProductlist().then((res) => {
      this.options = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
</style>
