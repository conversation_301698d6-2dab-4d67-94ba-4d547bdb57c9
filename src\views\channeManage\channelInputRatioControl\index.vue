<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" size="small">
      <el-form-item label="渠道名称" prop="channelName">
        <el-input
          clearable
          placeholder="请输入渠道名称"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道ID" prop="channelId">
        <el-input
          clearable
          placeholder="请输入渠道ID"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
          v-model="queryParams.channelId"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="channelList">
      <el-table-column label="渠道ID" prop="id" align="center" />
      <el-table-column label="渠道名称" prop="channelName" align="center" />
      <el-table-column label="渠道类型" prop="type" align="center">
        <template slot-scope="{ row }">
          <span>{{ getChannelType(row.type) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="进量比例%" prop="inputRatio" align="center" width="150">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.inputRatio"
            type="number"
            :min="0"
            :max="100"
            style="width: 130px"
            @change="handleChangeInputRatio(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getChannelInputRatioList,
  updateChannelInputRatio
} from "@/api/channeManage/channelInputRatioControl";

export default {
  name: "ChannelInputRatioControl",
  data() {
    return {
      total: 0,
      channelList: [],
      queryParams: {
        channelName: "",
        channelId: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    getList() {
      getChannelInputRatioList(this.queryParams).then((res) => {
        this.channelList = res.rows;
        this.total = res.total;
      });
    },
    //查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 获取渠道类型名称
    getChannelType(type) {
      const typeMap = {
        1: '联登',
        2: '半流程',
        3: 'api',
        4: '全流程UV',
        5: '信息流'
      }
      return typeMap[type] || '-'
    },
    handleChangeInputRatio(row) {
      if (row.inputRatio) {
        row.inputRatio = parseInt(row.inputRatio)
      }
      
      this.$confirm(
        `${row.channelName}(ID:${row.id}) 进量比例改为 ${row.inputRatio}%？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          updateChannelInputRatio({
            id: row.id,
            num: row.inputRatio
          })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch(() => {
              this.getList();
            });
        })
        .catch(() => {
          this.getList();
        });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input {
  input[type="number"] {
    padding-right: 0px;
    appearance: textfield;
    // -moz-appearance: textfield;
    // -webkit-appearance: textfield;
    // 解决el-input设置类型为number时，中文输入法光标上移问题
    line-height: 1px !important;
    text-align: center;
  }
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    appearance: none;
    // -webkit-appearance: none;
    margin: 0;
  }
}
</style> 