<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="68px"
    >
      <el-form-item>
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['saas:repaymentmethod:add']"
          >新增</el-button
        >
      </el-form-item>
    </el-form>
    <el-table :data="tableLsit">
      <el-table-column label="id" width="80" align="center" prop="id" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="名称" align="center" prop="methodName" />
      <el-table-column label="排序" align="center" prop="serialNumber" />
      <el-table-column
        label="状态"
        align="center"
        prop="methodStatus"
        v-hasPermi="['saas:repaymentmethod:update']"
      >
        <template  slot-scope="{row}">
          <div>
            <el-switch
              v-model="row.methodStatus"


              :active-value="1"
              :inactive-value="2"
              active-text="启用"
              inactive-text="禁用"
              @change="changeStatus($event, row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="新增还款方式"
      :visible.sync="avisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="120px"
        label-position="left"
      >
        <el-form-item label="还款方式名称" prop="methodName">
          <el-input
            size="small"
            placeholder="请输入还款方式名称"
            v-model="formData.methodName"
          ></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="serialNumber">
          <el-input
            size="small"
            v-model="formData.serialNumber"
            type="number"
            placeholder="请输入序号"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getRepaymentmethodList,
  updatestatus,
  addRepaymentOne,
} from "@/api/saas/repayment";
export default {
  name: "SaasRepayment",
  data() {
    return {
      avisible: false,
      tableLsit: [],
      formData: {
        methodName: "",
        serialNumber: "",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      rules: {
        serialNumber: [
          { required: true, message: "序号不能为空", trigger: "blur" },
        ],
        methodName: [
          { required: true, message: "还款方式名称不能为空", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    getList() {
      getRepaymentmethodList(this.queryParams).then((res) => {
        this.tableLsit = res.data;
      });
    },
    changeStatus(e, row) {

      this.$confirm(`确定${row.methodStatus == 1 ? "启用" : "禁用"}吗？`, {
        type: "warning",
      })
        .then((res) => {
          updatestatus({ id: row.id, status: row.methodStatus }).then((res) => {
            this.$message.success("修改成功");
            this.getList();
          });
        })
        .catch((err) => {
          row.methodStatus = row.methodStatus == 1 ? 2 : 1;
        });
    },
    handleAdd() {
      this.avisible = true;
    },

    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          addRepaymentOne(this.formData).then((res) => {
            this.getList();
            this.cancel();
            this.$message.success("新增成功");
          });
        }
      });
    },
    cancel() {
      this.formData = {
        methodName: "",
        serialNumber: "",
      };
      this.avisible = false;
      if (this.$refs.formData) {
        this.$refs.formData.resetFields();
      }
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
