import request from '@/utils/request'

/**
 * 获取企微产品趋势统计
 * @param {Object} params 查询参数
 * @param {String} params.day 日期 yyyy-MM-dd格式
 * @param {Number} params.platformType 平台类型
 * @param {Number} params.productId 产品ID
 * @param {String} params.productName 产品名称
 * @param {Number} params.pageNum 页码
 * @param {Number} params.pageSize 页面大小
 * @returns {Promise}
 */
export function getQwProductTrendStatistics(params) {
  return request({
    url: '/stats/product/conversion/qwProductTrendStatistics',
    method: 'post',
    data: params
  })
} 