import { Message } from 'element-ui';

/**
 * 节流函数
 * @param {Function} func 要执行的回调函数
 * @param {Number} wait 延时的时间（毫秒）
 * @param {Boolean} immediate 是否立即执行
 * @param {Object} options 额外选项
 * @param {String} options.message 冷却期间的提示消息
 * @param {String} options.type 提示消息的类型（success/warning/info/error）
 * @return {Function} 返回新的节流函数
 */
function throttle(func, wait = 1000, immediate = true, options = {}) {
  let timer = null;
  let flag = false;
  const defaultMessage = '操作太频繁，请稍后再试';
  const defaultType = 'warning';

  return function(...args) {
    if (immediate) {
      if (flag) {
        // 在冷却期间尝试触发时显示提示
        Message({
          message: options.message || defaultMessage,
          type: options.type || defaultType,
          duration: 2000
        })
      } else {
        flag = true
        typeof func === 'function' && func.apply(this, args)
        timer = setTimeout(() => {
          flag = false
        }, wait)
      }
    } else {
      if (flag) {
        // 在冷却期间尝试触发时显示提示
        Message({
          message: options.message || defaultMessage,
          type: options.type || defaultType,
          duration: 2000
        })
      } else {
        flag = true
        timer = setTimeout(() => {
          flag = false
          typeof func === 'function' && func.apply(this, args)
        }, wait)
      }
    }
  };
}

export default throttle;
