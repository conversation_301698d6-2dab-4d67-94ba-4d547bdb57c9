import request from '@/utils/request'

// 获取列表
export function getList(query) {
  return request({
    url: 'loan/xm/qwAccessTestPlan/list',
    method: 'get',
    params: query
  })
}

// 新增测试计划
export function add(data) {
  return request({
    url: 'loan/xm/qwAccessTestPlan/add',
    method: 'post',
    data
  })
}

// 修改测试计划
export function update(data) {
  return request({
    url: 'loan/xm/qwAccessTestPlan/update',
    method: 'post',
    data
  })
}

// 修改计划状态
export function updateStatus(data) {
  return request({
    url: 'loan/xm/qwAccessTestPlan/updateStatus',
    method: 'post',
    data
  })
}

// 修改控量
export function updateApplyControl(data) {
  return request({
    url: 'loan/xm/qwAccessTestPlan/updateApplyControl',
    method: 'post',
    data
  })
}

// 录入接入数
export function enterAccessNum(data) {
  return request({
    url: 'loan/xm/qwAccessTestPlan/enterAccessNum',
    method: 'post',
    data
  })
}