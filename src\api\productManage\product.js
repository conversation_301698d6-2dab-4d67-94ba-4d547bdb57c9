import request from '@/utils/request'
//上传图片
export const uploadingImage = (data) => {
  return request({
    url: "/loan/product/uploadingImage",
    method: 'post',
    data
  })
}

//获取产品列表
export const getloanModeList = (data) => {
  return request({
    url: "/loan/product/productList",
    method: "get",
    params: data
  })
}

//查询助贷类型

export const getloanModeListsAll = () => {
  return request({
    url: "/loan/product/loanModeLists",
    method: "get"
  })
}
//查询所属商户
export const getAcquirePartyAall = () => {
  return request({
    // url: '/loan/product/acquirePartyA',
    url: "loan/product/auditedPartyA",
    method: 'get'
  })
}

//添加商品
export const addProductOne = (data) => {
  return request({
    url: "/loan/product/productAdd",
    method: 'post',
    data
  })
}
//获取商品详情
export const getProductOne = (data) => {
  return request({
    url: "/loan/product/productDetailedness",
    method: 'get',
    params: data
  })
}
//修改产信息
export const editProductCompile = (data) => {
  return request({
    url: "/loan/product/productCompile",
    method: "post",
    data
  })
}

//修改产品排序
export const productCompileSort = (data) => {
  return request({
    url: "/loan/product/productCompileSort",
    method: "post",
    data
  })
}

//修改产品状态
export const productCondition = (data) => {
  return request({
    url: "/loan/product/productCondition",
    method: "post",
    data
  })
}
//修改产品助贷状态
export const productCooperations = (data) => {
  return request({
    url: "/loan/product/productCooperations",
    method: "post",
    data
  })
}

//查询所有城市
export const getCityAll = () => {
  return request({
    url: "/loan/product/getCity",
    method: 'get'
  })
}

//获取产品规则详情信息
export const getProductRule = (id) => {
  return request({
    url: `/loan/productRule/query/${id}`,
    method: 'get'
  })
}
//新增商品规则
export const addProductRule = (data) => {
  return request({
    url: '/loan/productRule/add',
    method: 'post',
    data
  })
}
//产品返点
export const productRebate = (data) => {
  return request({
    url: '/loan/product/rebate',
    method: "post",
    data
  })
}

//获取产品收益列表
export const productEarningsList = (data) => {
  return request({
    url: "/loan/productProfit/list",
    method: "get",
    params: data
  })
}
//根据日期查询收益信息
export const getProductProfit = (data) => {
  return request({
    url: "/loan/productProfit/queryDay",
    method: 'get',
    params: data
  })
}
//获取产品收益是否归属cps
export const checkProductIsCps = (data) => {
  return request({
    url: `/loan/productProfit/withCps/${data}`,
    method: 'get'
  })
}
//新增产品收益是
export const addproductProfitOne = (data) => {
  return request({
    url: `/loan/productProfit/add`,
    method: 'post',
    data
  })
}
//修改产品收益
export const editproductProfitOne = (data) => {
  return request({
    url: `/loan/productProfit/edit`,
    method: 'post',
    data
  })
}
//产品控量设置
export const addControlOne = (data) => {
  return request({
    url: "/loan/product/controlAdd",
    method: "post",
    data
  })
}
//产品控量设置
export const controlFilterControlOne = (data) => {
  return request({
    url: "/loan/product/controlFilter",
    method: "post",
    data
  })
}
//获取产品列表
export const getControlList = (data) => {
  return request({
    url: `/loan/product/controlList/${data}`,
    method: "get",

  })
}

//获取产品api
export const getProductApiList = () => {
  return request({
    url: '/loan/product/getProductApiList',
    method: "get"
  })
}

//获取产品模拟匹配的列表
export const getMatchingInfo = () => {
  return request({
    url: "/simulation/matching/query",
    method: "get"
  })
}
// 获取产品模糊匹配列表
export const getMatchingList = (data) => {
  return request({
    url: "/simulation/matching/list",
    method: "post",
    data
  })
}

//修改产品是否是vip
export const changeVipProduct = (data) => {
  return request({
    url: "/loan/product/checkoutVip",
    method: "post",
    data

  })
}
//查询所有渠道号
export const getChannelList = () => {
  return request({
    url: "/loan/productRule/getChannelList",
    method: "get"
  })
}
//修改产品在渠道的显示
export const updateBasicsChannel = (data) => {
  return request({
    url: "/loan/productRule/updateBasicsChannel",
    method: 'post',
    data
  })
}
//获取产品城市
export const getProductCity = () => {
  return request({
    url: "/loan/productRule/getCity",
    method: "get"
  })
}
//提交产品基本规则
export const baseProductRule = (data) => {
  return request({
    url: '/loan/productRule/updateBasics',
    method: "post",
    data
  })
}

//获取审核产品列表
export const getAuditProductList = (data) => {
  return request({
    url: "/loan/product/auditProductList",
    method: "get",
    params: data
  })
}

//获取审核产品详情
export const getAuditProductInfo = (data) => {
  return request({
    url: "/loan/product/auditProductInfo",
    method: "get",
    params: data
  })
}

//获取所有的城市

export const getparyACityAll = () => {
  return request({
    url: '/loan/partyaAdmin/product/getCity',
    method: "get"
  })
}

//审核商户产品
export const editProductAudit = (data) => {
  return request({
    url: '/loan/product/productAudit',
    method: 'post',
    data
  })
}

//导出产品分布
export const exportProductDistribution = () => {
  return request({
    url: "/loan/product/exportProductDistribution",
    method: "get",
    responseType: "arraybuffer"
  })
}

//获取产品流量包列表
export const getPackageList = (data) => {
  return request({
    url: '/loan/product/flow/package/list',
    method: 'get',
    params: data
  })
}
//新增流量包
export const addPackagetOne = (data) => {
  return request({
    url: "/loan/product/flow/package/add",
    method: "post",
    data
  })
}
//编辑流量包
export const editPackagetOne = (data) => {
  return request({
    url: "/loan/product/flow/package/update",
    method: "post",
    data
  })
}
//修改流量包状态
export const changePackagetOne = (data) => {
  return request({
    url: "/loan/product/flow/package/updateStatus",
    method: "post",
    data
  })
}

//查询流量包城市
export const getFlowCityAll = () => {
  return request({
    url: '/loan/product/flow/package/getCity',
    method: "get"
  })
}

//查询流量包详情
export const getPackgetOne = (data) => {
  return request({
    url: `/loan/product/flow/package/query/${data}`,
    method: 'post'
  })
}

//查询流量包下属城市
export const getPackCity = (data) => {
  return request({
    url: `/loan/product/flow/package/getCityInfo/${data}`,
    method: "get"
  })
}



//查询流量包列表
export const getFlowPackageList = () => {
  return request({
    url: '/loan/product/flowPackageList',
    method: 'get'
  })
}



//查询开屏页状态
export const getConductSwitchStatus = () => {
  return request({
    url: '/loan/product/conductSwitchShow',
    method: 'get'
  })
}
//修改开屏页状态
export const editConductSwitchStatus = () => {
  return request({
    url: '/loan/product/conductSwitch',
    method: 'get'
  })
}
//修改开屏页产品
export const editProductConductSwitch = (data) => {

  return request({
    url: '/loan/product/productConductSwitch',
    method: 'get',
    params: data
  })
}
//修改开屏页产品
export const editProductQiWeiSwitch = (data) => {
  return request({
    url: '/loan/product/productQiWeiSwitch',
    method: 'get',
    params: data
  })
}
//修改开屏页产品
export const getProductLimitList = (data) => {
  return request({
    url: '/loan/productRule/showChannelRestriction',
    method: 'get',
    params: data
  })
}
// 产品申请接口
export const exportaa = (data) => {
  return request({
    url: '/loan/productProfit/export',
    method: 'post',
    responseType: "arraybuffer",
    data
  })
}
// 产品申请接口
export const getOnlineSetType = () => {
  return request({
    url: '/loan/product/getOnlineSetType',
    method: 'get',
  })
}
// 产品申请接口
export const setOnlineSetType = (data) => {
  return request({
    url: '/loan/product/setOnlineSetType',
    method: 'get',
    params: data
  })
}

// 产品匹配价格列表
export const getMatchingPriceList = (data) => {
  return request({
    url: '/loan/product/price/list',
    method: 'get',
    params: data
  })
}

// 新增产品匹配价格数据
export const addMatchingPrice = (data) => {
  return request({
    url: '/loan/product/price/add',
    method: 'post',
    data
  })
}

// 修改投放产品时，校验企微链接是否重复，以及当前用户能否修改投放产品配置
export const checkCooperationLink = (data) => {
  return request({
    url: '/loan/product/checkQiWeiLink',
    method: 'get',
    params: data
  })
}
//获取组明
export const getGroupProductList = (data) => {
  return request({
    url: '/loan/group/product/list',
    method: 'get',
    params: data
  })
}
//获取组明
export const updateGroupProductList = (data) => {
  return request({
    url: '/loan/group/product/update ',
    method: 'post',
    data
  })
}

// 手机号批量撞库测试
export const batchCheckPhone = (data) => {
  return request({
    url: '/test/batch/check',
    method: 'post',
    data
  })
}

// 产品批量推送测试
export const batchPushProduct = (data) => {
  return request({
    url: '/test/batch/push',
    method: 'post',
    data
  })
}

// 获取产品规则详情
export function getProductRuleDetail(params) {
  return request({
    url: '/loan/product/productRuleDetail',
    method: 'get',
    params
  })
}

// 查询子平台渠道
export function getSubPlatformChannels(platformId) {
  return request({
    url: `/loan/product/subPlatformChannels/${platformId}`,
    method: 'get'
  })
}

// 子平台渠道规则设置
export function updateSubPlatformChannel(data) {
  return request({
    url: '/loan/product/subPlatformUpdateBasicsChannel',
    method: 'post',
    data
  })
}

// 获取子平台产品信息
export function getSubPlatformProductInfo(params) {
  return request({
    url: '/loan/product/subPlatformProductInfo',
    method: 'get',
    params
  })
}

// 接入录入
export function saveAccessProductRecord(data) {
  return request({
    url: '/loan/xm/access/product/saveAccessProductRecord',
    method: 'post',
    data
  })
}