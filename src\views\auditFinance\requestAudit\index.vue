<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" class="demo-form-inline">
      <el-form-item label="申请人">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入申请人"
        ></el-input>
      </el-form-item>
      <el-form-item label="时间查询">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="申请类型">
        <el-select
          clearable
          size="small"
          v-model="queryParams.type"
          @change="handleQuery"
        >
          <el-option :value="1" label="报销"> </el-option>
          <el-option :value="2" label="外部返点"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          clearable
          size="small"
          v-model="queryParams.status"
          @change="handleQuery"
        >
          <el-option :value="-1" label="全部"> </el-option>
          <el-option :value="0" label="待审核"> </el-option>
          <el-option :value="1" label="已通过"> </el-option>
          <el-option :value="2" label="已驳回"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="date" align="center" label="申请时间">
      </el-table-column>
      <el-table-column prop="billno" align="center" label="请款单号">
      </el-table-column>
      <el-table-column prop="name" align="center" label="申请人" width="180">
      </el-table-column>
      <el-table-column
        prop="typeStr"
        label="申请类型"
        align="center"
        width="180"
      >
      </el-table-column>
      <el-table-column prop="price" label="金额" align="center" width="180">
      </el-table-column>
      <el-table-column
        prop="statusStr"
        label="审核状态"
        align="center"
        width="180"
      >
      </el-table-column>

      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button
              @click="hanldeShow(row)"
              type="text"
              v-if="row.date != '合计'"
              v-hasPermi="['sys:personal:list']"
            >
              {{ row.isAction ? "审核" : "查看" }}</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="请款详情"
      :visible.sync="showApply"
      width="900px"
      append-to-body
      center
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form label-position="left" label-width="100px">
            <el-form-item label="报销金额">
              <el-input v-model="applyDetail.price" disabled></el-input>
            </el-form-item>
            <el-form-item label="报销类型">
              <el-input v-model="applyDetail.typeStr" disabled></el-input>
            </el-form-item>
            <el-form-item label="报销人">
              <el-input v-model="applyDetail.username" disabled></el-input>
            </el-form-item>
            <el-form-item label="报销凭证">
              <!-- <el-input v-model="applyDetail.proceedsAccount" disabled></el-input> -->
              <div v-for="(item, index) in files" :key="index" class="files">
                <span> {{ item }}</span>
                <el-button
                  size="mini"
                  type="primary"
                  @click="handleDownload(item)"
                  >下载</el-button
                >
              </div>
            </el-form-item>
            <el-form-item label="收款人姓名">
              <el-input v-model="applyDetail.createUser" disabled></el-input>
            </el-form-item>
            <el-form-item label="收款银行卡号">
              <el-input v-model="applyDetail.bankCardNo" disabled></el-input>
            </el-form-item>
            <el-form-item label="收款卡开户行">
              <el-input v-model="applyDetail.bankCardName" disabled></el-input>
            </el-form-item>
            <el-form-item label="凭证上传" v-if="applyDetail.mustCertificate">
              <el-upload
                class="avatar-uploader"
                action=""
                :show-file-list="false"
                :auto-upload="false"
                :on-change="changeUpImg"
              >
                <img
                  v-if="imageUrl"
                  style="vertical-align: middle; max-height: 100px"
                  :src="imageUrl"
                />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div class="el-upload__tip" slot="tip">通过须上传凭证</div>
              </el-upload>
            </el-form-item>
            <el-form-item label="凭证" v-if="applyDetail.certificate">
              <el-image
                style="width: 100px; height: 100px"
                :src="applyDetail.certificate"
                :preview-src-list="[applyDetail.certificate]"
              >
              </el-image>
            </el-form-item>
            <el-form-item label="审批意见" v-if="isShowHandle">
              <el-input
                v-model="remark"
                type="textarea"
                maxlength="20"
                show-word-limit
                placeholder="请输入审批意见,驳回必填"
              >
              </el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <div>
            <div class="check-title">审核流程</div>
            <div class="check-info1" v-if="processList.length">
              <el-timeline>
                <el-timeline-item
                  v-for="(item, index) in processList"
                  :key="index"
                  :color="processColor(item)"
                >
                  <div :class="[item.status == 2 ? 'c_red' : '']">
                    <div>{{ item.userRemark }}</div>
                    <div>时间:{{ item.checkTime || "-" }}</div>
                    <div v-if="item.status != 3">
                      状态：{{ item.checkResult || "-" }}
                    </div>
                    <div v-if="item.status != 3">
                      备注：{{ item.checkRemark || "-" }}
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
            <div class="check-center" v-else>暂无数据</div>
          </div>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer" v-if="isShowHandle">
        <el-button
          type="primary"
          @click="submitPass(1, applyDetail.cashOutId)"
          v-hasPermi="['sys:personal:execute']"
          >通 过</el-button
        >
        <el-button
          @click="submitReject(1, applyDetail.cashOutId)"
          v-hasPermi="['sys:personal:execute']"
          >驳回</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="外部返点详情"
      :visible.sync="showBack"
      width="900px"
      append-to-body
      center
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-row :gutter="20">
        <el-col :span="15">
          <el-form label-position="left" label-width="120px">
            <el-form-item label="外部返点金额">
              <el-input v-model="backDetail.price" disabled></el-input>
            </el-form-item>
            <el-form-item label="返点产品明细">
              <el-table
                :data="productList"
                border
                max-height="250px"
                style="width: 100%"
              >
                <el-table-column
                  prop="productName"
                  align="center"
                  label="推广名称"
                >
                </el-table-column>
                <el-table-column prop="date" align="center" label="返点日期">
                </el-table-column>
                <el-table-column
                  prop="matchingNum"
                  align="center"
                  label="有效曝光量"
                >
                </el-table-column>
                <el-table-column prop="price" align="center" label="返点金额">
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-form-item label="收款人姓名">
              <el-input v-model="backDetail.username" disabled></el-input>
            </el-form-item>
            <el-form-item label="收款人银行卡号">
              <el-input v-model="backDetail.bankCardNo" disabled></el-input>
            </el-form-item>
            <el-form-item label="收款卡开户行">
              <el-input v-model="backDetail.bankCardName" disabled></el-input>
            </el-form-item>

            <el-form-item label="凭证上传" v-if="backDetail.mustCertificate">
              <el-upload
                class="avatar-uploader"
                action=""
                :show-file-list="false"
                :auto-upload="false"
                :on-change="changeUpImg"
              >
                <img
                  v-if="imageUrl"
                  style="vertical-align: middle; max-height: 100px"
                  :src="imageUrl"
                />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div class="el-upload__tip" slot="tip">通过须上传凭证</div>
              </el-upload>
            </el-form-item>
            <el-form-item label="凭证" v-if="backDetail.certificate">
              <el-image
                style="width: 100px; height: 100px"
                :src="backDetail.certificate"
                :preview-src-list="[backDetail.certificate]"
              >
              </el-image>
            </el-form-item>
            <el-form-item label="审批意见" v-if="isShowHandle">
              <el-input
                v-model="remark"
                type="textarea"
                maxlength="20"
                show-word-limit
                placeholder="请输入审批意见,驳回必填"
              >
              </el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="9">
          <div>
            <div class="check-title">审核流程</div>
            <div class="check-info1" v-if="processList.length">
              <el-timeline>
                <el-timeline-item
                  v-for="(item, index) in processList"
                  :key="index"
                  :color="processColor(item)"
                >
                  <div :class="[item.status == 2 ? 'c_red' : '']">
                    <div>{{ item.userRemark }}</div>
                    <div>时间:{{ item.checkTime || "-" }}</div>
                    <div v-if="item.status != 3">
                      状态：{{ item.checkResult || "-" }}
                    </div>
                    <div v-if="item.status != 3">
                      备注：{{ item.checkRemark || "-" }}
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
            <div class="check-center" v-else>暂无数据</div>
          </div>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer" v-if="isShowHandle">
        <el-button
          type="primary"
          @click="submitPass(2, backDetail.cashOutId)"
          v-hasPermi="['sys:personal:execute']"
          >通 过
        </el-button>
        <el-button
          @click="submitReject(2, backDetail.cashOutId)"
          v-hasPermi="['sys:personal:execute']"
          >驳回</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPersonalCashOutList,
  getReimbursementDetail,
  getRebatesDeatil,
  checkDoPass,
  checkDoReject,
} from "@/api/auditFinance/request";
export default {
  data() {
    return {
      total: 0,
      showApply: false,
      showBack: false,
      isShowHandle: true,
      remark: "",
      imageUrl: "",
      file: "",
      typeStatus: {
        0: "待审核",
        1: "已通过",
        2: "已拒绝",
      },
      colorType: {
        0: "color:#1E90FF",
        1: "color:#008000",
        2: "color:red",
      },
      files: [],
      processList: [],
      productList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: -1,
        startTime: "",
        stopTime: "",
      },

      applyDetail: {},
      backDetail: {},
      tableData: [],
      dateRange: [],
    };
  },
  methods: {
    //请款乙方列表
    getList() {
      getPersonalCashOutList(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.tableData.push({
          date: "合计",
          price: res.extra.sum,
        });
        this.total = res.total;
      });
    },
    //上传凭证
    changeUpImg(e) {
      const isJPG =
        e.raw.type === "image/jpeg" ||
        e.raw.type === "image/jpg" ||
        e.raw.type === "image/png";
      const isLt2M = e.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传图片只能是 JPG/JPEG/PNG 格式!");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
        return;
      }
      this.imageUrl = URL.createObjectURL(e.raw);
      this.file = e.raw;
    },
    cancel() {
      this.file = "";
      this.imageUrl = "";
      this.remark = "";
      this.applyDetail = {};
      this.backDetail = {};
    },
    hanldeShow(row) {
      if (row.status == 0 && row.isAction) {
        this.isShowHandle = true;
      } else {
        this.isShowHandle = false;
      }
      if (row.type == 1) {
        getReimbursementDetail({ cashOutId: row.cashOutId }).then((res) => {
          this.showApply = true;
          this.applyDetail = {
            ...res.data,
            mustCertificate: row.mustCertificate,
          };
          this.files = res.data.files || [];
          this.processList = res.data.processes || [];
        });
      } else {
        getRebatesDeatil({ cashOutId: row.cashOutId }).then((res) => {
          this.showBack = true;
          this.backDetail = {
            ...res.data,
            mustCertificate: row.mustCertificate,
          };
          this.processList = res.data.processes || [];
          this.productList = res.data.productRebates || [];
        });
      }
    },
    submitPass(type, id) {
      if (
        (this.backDetail.mustCertificate || this.applyDetail.mustCertificate) &&
        !this.file
      )
        return this.$message.error("凭证不能为空");
      let obj = {
        id: id,
        type: type,
        remark: this.remark,
        file: this.file,
      };
      let data = new FormData();
      for (let i in obj) {
        data.append(i, obj[i]);
      }
      checkDoPass(data).then((res) => {
        this.getList();
        this.showApply = false;
        this.showBack = false;
        this.cancel();
        this.$message.success("操作成功");
      });
    },
    submitReject(type, id) {
      if (!this.remark) return this.$message.error("审批意见不能为空");
      let obj = {
        id: id,
        type: type,
        remark: this.remark,
        file: this.file,
      };
      let data = new FormData();
      for (let i in obj) {
        data.append(i, obj[i]);
      }
      checkDoReject(data).then((res) => {
        this.getList();
        this.showApply = false;
        this.showBack = false;
        this.cancel();
        this.$message.success("操作成功");
      });
    },
    //时间查询
    handleQuery() {
      if (this.dateRange != null) {
        var end = this.dateRange[1];
        var start = this.dateRange[0];
        this.queryParams.startTime = start;
        this.queryParams.stopTime = end;
      } else {
        this.queryParams.startTime = "";
        this.queryParams.stopTime = "";
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleDownload(e) {
      const ele = document.createElement("a");
      ele.setAttribute("href", e); //设置下载文件的url地址
      ele.setAttribute("download", "download"); //用于设置下载文件的文件名
      ele.click();
    },
  },
  computed: {
    processColor: () => {
      return (item) => {
        if (item.status == -1) {
          return "";
        } else if (item.status == 2) {
          return "#ff0000";
        } else {
          return "#00a607";
        }
      };
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.files {
  display: flex;
  margin-top: 5px;

  span {
    display: block;
    width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.check-info1 {
  margin-top: 10px;
  max-height: 500px;
  overflow: auto;
}
</style>
