import { getYxqbToken, setYxqbToken, removeYxqbToken } from '@/utils/auth'
import { login } from '@/api/yxqb/login.js'

const yxqb = {
  namespaced: true,
  state: {
    token: getYxqbToken()
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        login(userInfo).then(res => {
          const token = res.data
          setYxqbToken(token)
          commit('SET_TOKEN', token)
          resolve(res)
        })
      })
    },

    // 退出
    Logout({ commit }) {
      return new Promise((resolve, reject) => {
        commit('SET_TOKEN', '')
        removeYxqbToken()
        resolve()
      })
    }
  }
}

export default yxqb
