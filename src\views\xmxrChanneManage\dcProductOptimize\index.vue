<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="handleOpenDialog"
          >配置贷超优化产品</el-button
        >
      </el-col>
    </el-row>

    <div
      v-if="Object.keys(groupedList).length === 0 && !firstLoadDone"
      class="loading-text"
    >
      加载中...
    </div>
    <div
      v-else-if="Object.keys(groupedList).length === 0 && firstLoadDone"
      class="empty-text"
    >
      暂无已配置的贷超优化产品
    </div>
    <product-display-card
      v-for="(platformData, platformId) in groupedList"
      :key="platformId"
      :platform-id="platformId"
      :platform-data="platformData"
      @delete-product="handleDeleteProduct"
    />

    <!-- 使用新的配置对话框组件 -->
    <configure-ignore-dialog
      :visible.sync="dialogVisible"
      :grouped-list="groupedList"
      @confirm-selection="handleDialogConfirmation"
    />
  </div>
</template>

<script>
import {
  getDcQwProductOptimizedList,
  setDcQwProductOptimized,
} from "@/api/xmxrChanneManage/dcProductOptimize";
import ProductDisplayCard from "./components/ProductDisplayCard.vue";
import ConfigureIgnoreDialog from "./components/ConfigureIgnoreDialog.vue";

export default {
  name: "QwIgnoreCityProduct",
  components: { ProductDisplayCard, ConfigureIgnoreDialog },
  data() {
    return {
      groupedList: {},
      firstLoadDone: false,
      dialogVisible: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      getDcQwProductOptimizedList()
        .then((response) => {
          const data = response.data || [];
          const grouped = {};
          data.forEach((item) => {
            if (!grouped[item.platformId]) {
              grouped[item.platformId] = {
                platformName: item.platformName,
                products: [],
              };
            }
            grouped[item.platformId].products.push({
              productId: item.productId,
              productName: item.productName,
            });
          });
          this.groupedList = grouped;
        })
        .catch(() => {
          this.groupedList = {};
          this.msgError("加载忽略产品列表失败");
        })
        .finally(() => {
          this.firstLoadDone = true;
        });
    },
    handleOpenDialog() {
      this.dialogVisible = true;
    },
    handleDialogConfirmation(selectedProductsPayload) {
      setDcQwProductOptimized(selectedProductsPayload)
        .then(() => {
          this.dialogVisible = false;
          this.$message.success("配置成功");
          this.getList();
        })
        .catch(() => {
          // 错误已由全局拦截器处理，或在 ConfigureIgnoreDialog 中提示
        });
    },
    handleDeleteProduct(platformId, productToRemove) {
      this.$confirm(
        `移除产品 "${productToRemove.productName}" (ID: ${productToRemove.productId}) ？此操作立即生效。`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          const payload = [];
          for (const platformKey in this.groupedList) {
            const platformData = this.groupedList[platformKey];
            platformData.products.forEach((p) => {
              if (
                !(platformKey == platformId && p.productId == productToRemove.productId)
              ) {
                payload.push({
                  productId: p.productId,
                  productName: p.productName,
                  platformName: platformData.platformName,
                  platformId: parseInt(platformKey),
                });
              }
            });
          }
          setDcQwProductOptimized(payload)
            .then(() => {
              this.$message.success("移除成功");
              this.getList();
            })
            .catch(() => {
              // 错误已由全局拦截器处理
            });
        })
        .catch(() => {
          this.$message.info("已取消移除");
        });
    },
  },
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 20px;
}

.loading-text,
.empty-text {
  text-align: center;
  padding: 20px;
  color: #909399;
}
</style>
