<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" class="demo-form-inline">
      <el-form-item label="时间查询">
        <el-col :span="11">
          <el-date-picker v-model="value2" type="datetimerange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss"
            @change="Scounfung">
          </el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item label="状态">
        <el-select clearable size="small" v-model="queryParams.status" @change="OutList">
          <el-option :value="-1" label="全部"> </el-option>
          <el-option :value="0" label="待审核"> </el-option>
          <el-option :value="1" label="已通过"> </el-option>
          <el-option :value="2" label="已驳回"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="Scounfung">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="cashOutNumber" align="center" label="请款单号">
      </el-table-column>
      <el-table-column prop="money" label="请款金额" align="center" width="180">
      </el-table-column>
      <el-table-column prop="cashOutMs" align="center" label="请款提交时间">
      </el-table-column>
      <el-table-column prop="partyUsername" align="center" label="请款人" width="180">
      </el-table-column>
      <el-table-column label="当前审核人" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.postName }} <br> {{ row.userNames }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="审核状态" width="180">
        <template slot-scope="{ row }">
          <div :style="colorType[row.status]">
            {{ typeStatus[row.status] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="查看凭证" prop="price" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.fileName">
            <el-image style="width: 100px; height: 100px" :src="row.fileName" :preview-src-list="[row.fileName]">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="reason" align="center" label="备注" width="180">
        <template  slot-scope="{row}">
          <div>
            {{ row.reason || "-" }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="OutList" />
  </div>
</template>

<script>
import { getCashOutList } from "@/api/partyB";
export default {
  data() {
    return {
      total: 0,
      typeStatus: {
        0: "待审核",
        1: "已通过",
        2: "已拒绝",
      },
      colorType: {
        0: "color:#1E90FF",
        1: "color:#008000",
        2: "color:red",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: "",
        status: -1,
        startTime: "",
        stopTime: "",
      },
      formInline: {},

      tableData: [],
      value2: "",
    };
  },
  methods: {
    //请款乙方列表
    OutList() {
      getCashOutList(this.queryParams).then((res) => {
        this.tableData = res.rows;

        this.total = res.total;
      });
    },
    //时间查询

    Scounfung() {
      if (this.value2 != null) {
        var end = this.value2[1];
        var start = this.value2[0];
        this.queryParams.startTime = start;
        this.queryParams.stopTime = end;
      } else {
        this.queryParams.startTime = "";
        this.queryParams.stopTime = "";
      }
      this.OutList();
    },
  },
  mounted() {
    this.queryParams.id = this.$route.query.id;
    this.OutList();
  },
};
</script>

<style lang="scss" scoped>
.replayImage {
  border: 0;
  width: 80vh;
  max-height: 100vh;
}
</style>
