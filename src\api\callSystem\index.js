import request from '@/utils/request'

/**
 * 获取所有话务平台列表
 * @description 获取所有可用的话务平台选项列表
 * @returns {Promise<object>} 返回Promise对象
 * @returns {Promise<object>} response - 响应对象
 * @returns {number} response.code - 响应状态码，200表示成功
 * @returns {string} response.state - 响应状态，"OK"表示成功
 * @returns {string} response.msg - 响应消息，"success"表示成功
 * @returns {object[]} response.data - 平台列表数组
 * @returns {string} response.data[].platformId - 平台ID
 * @returns {string} response.data[].platformName - 平台名称
 * @returns {object} response.extend - 扩展信息对象
 * @returns {string|null} response.exMsg - 异常消息，无异常时为null
 * @returns {boolean} response.fail - 是否失败，false表示成功
 * @returns {boolean} response.success - 是否成功，true表示成功
 * @example
 * // 调用示例
 * const res = await getPlatforms()
 * console.log(res.data)
 * // [
 * //   { platformId: "1", platformName: "话务平台-肖宇" }
 * // ]
 */
export function getPlatforms() {
  return request({
    url: '/outbound/platform/platforms',
    method: 'get'
  })
}

/**
 * 获取团队列表
 * @description 获取指定平台的团队列表
 * @param {object} params - 请求参数
 * @param {string} params.platformId - 平台ID
 * @returns {Promise<object>} 返回Promise对象
 * @returns {Promise<object>} response - 响应对象
 * @returns {number} response.code - 响应状态码，200表示成功
 * @returns {string} response.state - 响应状态，"OK"表示成功
 * @returns {string} response.msg - 响应消息，"success"表示成功
 * @returns {object[]} response.data - 团队列表数组
 * @returns {number} response.data[].id - 团队ID
 * @returns {string} response.data[].name - 团队名称
 * @returns {number} response.data[].creator - 创建者ID
 * @returns {string} response.data[].created - 创建时间
 * @returns {number} response.data[].updater - 更新者ID
 * @returns {string} response.data[].updated - 更新时间
 * @returns {object} response.extend - 扩展信息对象
 * @returns {string|null} response.exMsg - 异常消息，无异常时为null
 * @returns {boolean} response.fail - 是否失败，false表示成功
 * @returns {boolean} response.success - 是否成功，true表示成功
 * @example
 * // 调用示例
 * const params = { platformId: "1" }
 * const res = await getTeamList(params)
 * console.log(res.data)
 * // [
 * //   {
 * //     id: 17,
 * //     name: "开发团队",
 * //     creator: 1,
 * //     created: "2025-07-21T14:45:43",
 * //     updater: 1,
 * //     updated: "2025-07-21T14:45:45"
 * //   }
 * // ]
 */
export function getTeamList(params) {
  return request({
    url: '/outbound/platform/teamList',
    method: 'get',
    params
  })
}

/**
 * 获取跟进客户统计数据
 * @description 根据查询条件获取客户跟进的统计数据
 * @param {object} params - 查询参数对象
 * @param {string} [params.startTime] - 开始时间，格式：yyyy-MM-dd HH:mm:ss
 * @param {string} [params.endTime] - 结束时间，格式：yyyy-MM-dd HH:mm:ss
 * @returns {Promise<object>} 返回Promise对象
 * @returns {Promise<object>} response - 响应对象
 * @returns {number} response.code - 响应状态码，200表示成功
 * @returns {string} response.state - 响应状态，"OK"表示成功
 * @returns {string} response.msg - 响应消息，"success"表示成功
 * @returns {object[]} response.data - 统计数据数组
 * @returns {string} response.data[].date - 统计日期，如"汇总"
 * @returns {number} response.data[].total - 总数
 * @returns {number} response.data[].poor - 差评数
 * @returns {string} response.data[].poorRate - 差评率，如"0"
 * @returns {number} response.data[].quality - 好评数
 * @returns {string} response.data[].qualityRate - 好评率，如"0"
 * @returns {object} response.extend - 扩展信息对象
 * @returns {string|null} response.exMsg - 异常消息，无异常时为null
 * @returns {boolean} response.fail - 是否失败，false表示成功
 * @returns {boolean} response.success - 是否成功，true表示成功
 * @example
 * // 调用示例
 * const params = {
 *   startTime: '2025-07-25 00:00:00',
 *   endTime: '2025-07-25 23:59:59'
 * }
 * const res = await getFollowStatistics(params)
 * console.log(res.data)
 * // [
 * //   {
 * //     date: "汇总",
 * //     total: 0,
 * //     poor: 0,
 * //     poorRate: "0",
 * //     quality: 0,
 * //     qualityRate: "0"
 * //   }
 * // ]
 */
export function getFollowStatistics(params) {
  return request({
    url: '/outbound/platform/followStatistics',
    method: 'post',
    data: params
  })
}

/**
 * 获取员工跟进详情统计数据
 * @description 根据查询条件获取员工维度的客户跟进详情统计数据
 * @param {object} params - 查询参数对象
 * @param {string} params.startTime - 开始时间，格式：yyyy-MM-dd HH:mm:ss
 * @param {string} params.endTime - 结束时间，格式：yyyy-MM-dd HH:mm:ss
 * @param {number[]} [params.teamIds] - 团队ID数组，可选，用于筛选特定团队
 * @returns {Promise<object>} 返回Promise对象
 * @returns {Promise<object>} response - 响应对象
 * @returns {number} response.code - 响应状态码，200表示成功
 * @returns {string} response.state - 响应状态，"OK"表示成功
 * @returns {string} response.msg - 响应消息，"success"表示成功
 * @returns {object} response.data - 员工统计数据对象
 * @returns {object} response.extend - 扩展信息对象
 * @returns {string|null} response.exMsg - 异常消息，无异常时为null
 * @returns {boolean} response.fail - 是否失败，false表示成功
 * @returns {boolean} response.success - 是否成功，true表示成功
 * @example
 * // 调用示例
 * const params = {
 *   startTime: '2025-07-25 00:00:00',
 *   endTime: '2025-07-25 23:59:59',
 *   teamIds: [17]
 * }
 * const res = await getEmployeeDetail(params)
 * console.log(res.data) // 员工统计数据对象
 */
export function getEmployeeDetail(params) {
  return request({
    url: '/outbound/platform/employeeDetail',
    method: 'post',
    data: params
  })
}

/**
 * 获取话务来源列表
 * @description 获取所有可用的话务来源选项列表
 * @returns {Promise<object>} 返回Promise对象
 * @returns {Promise<object>} response - 响应对象
 * @returns {number} response.code - 响应状态码，200表示成功
 * @returns {string} response.msg - 响应消息
 * @returns {string[]} response.data - 话务来源列表，字符串数组
 * @example
 * // 调用示例
 * const res = await getOutboundSources()
 * console.log(res.data) // ['来源1', '来源2', '来源3']
 */
export function getOutboundSources() {
  return request({
    url: '/api/outbound/sources',
    method: 'get'
  })
}

/**
 * 获取话务标记详情列表
 * @description 根据查询条件获取话务标记的详细记录列表，支持分页查询
 * @param {object} params - 查询参数对象
 * @param {number[]} [params.channelIds] - 渠道ID数组，可选，用于筛选特定渠道
 * @param {string} [params.consumerPhone] - 客户手机号，可选，用于筛选特定客户
 * @param {string} [params.endTime] - 结束时间，格式：yyyy-MM-dd HH:mm:ss
 * @param {string} [params.outboundName] - 标记人名称，可选，用于筛选特定标记人
 * @param {string} [params.outboundPhone] - 标记人手机号，可选，用于筛选特定标记人
 * @param {string} [params.outboundSource] - 话务来源，可选，用于筛选特定来源
 * @param {number} [params.page] - 页码，从1开始
 * @param {number} [params.size] - 每页记录数
 * @param {string} [params.startTime] - 开始时间，格式：yyyy-MM-dd HH:mm:ss
 * @param {number} [params.type] - 标记类型，可选，0: 异常用户，1: 优质用户
 * @returns {Promise<object>} 返回Promise对象
 * @returns {Promise<object>} response - 响应对象
 * @returns {number} response.code - 响应状态码，200表示成功
 * @returns {string} response.msg - 响应消息
 * @returns {object} response.data - 分页数据对象
 * @returns {object[]} response.data.records - 详情记录列表
 * @returns {number} response.data.records[].channelId - 渠道ID
 * @returns {string} response.data.records[].source - 渠道来源
 * @returns {string} response.data.records[].phone - 客户手机号
 * @returns {string} response.data.records[].outboundSource - 话务系统来源
 * @returns {string} response.data.records[].outboundName - 话务系统标记人姓名
 * @returns {string} response.data.records[].outboundPhone - 话务系统标记人手机号
 * @returns {number} response.data.records[].type - 标记类型，0: 异常用户，1: 优质用户
 * @returns {string} response.data.records[].reason - 标记原因
 * @returns {string} response.data.records[].created - 标记时间，格式：yyyy-MM-dd HH:mm:ss
 * @returns {string} response.data.records[].updated - 标记更新时间，格式：yyyy-MM-dd HH:mm:ss
 * @returns {number} response.data.total - 总记录数
 * @example
 * // 调用示例
 * const params = {
 *   channelIds: [1, 2, 3],
 *   page: 1,
 *   size: 10,
 *   startTime: '2024-01-01 00:00:00',
 *   endTime: '2024-01-31 23:59:59'
 * }
 * const res = await getFlagDetails(params)
 * console.log(res.data.records) // 详情记录数组
 * // [
 * //   {
 * //     channelId: 55010,
 * //     source: "渠道来源名称",
 * //     phone: "13800138000",
 * //     outboundSource: "话务来源",
 * //     outboundName: "张三",
 * //     outboundPhone: "13900139000",
 * //     type: 0,
 * //     reason: "标记原因描述",
 * //     created: "2024-01-15 10:30:00",
 * //     updated: "2024-01-15 10:30:00"
 * //   }
 * // ]
 * console.log(res.data.total) // 总记录数
 */
export function getFlagDetails(params) {
  return request({
    url: '/api/outbound/flagDetails',
    method: 'post',
    data: params
  })
}

/**
 * 获取话务标记统计数据
 * @description 根据查询条件获取话务标记的统计数据，按日期和渠道维度进行统计
 * @param {object} params - 查询参数对象
 * @param {number[]} [params.channelIds] - 渠道ID数组，可选，用于筛选特定渠道
 * @param {string} [params.consumerPhone] - 客户手机号，可选，用于筛选特定客户
 * @param {string} [params.endTime] - 结束时间，格式：yyyy-MM-dd HH:mm:ss
 * @param {string} [params.outboundName] - 标记人名称，可选，用于筛选特定标记人
 * @param {string} [params.outboundPhone] - 标记人手机号，可选，用于筛选特定标记人
 * @param {string} [params.outboundSource] - 话务来源，可选，用于筛选特定来源
 * @param {string} [params.startTime] - 开始时间，格式：yyyy-MM-dd HH:mm:ss
 * @param {number} [params.type] - 标记类型，可选，0: 异常用户，1: 优质用户
 * @returns {Promise<object>} 返回Promise对象
 * @returns {Promise<object>} response - 响应对象
 * @returns {number} response.code - 响应状态码，200表示成功
 * @returns {string} response.state - 响应状态，"OK"表示成功
 * @returns {string} response.msg - 响应消息，"success"表示成功
 * @returns {object[]} response.data - 统计数据列表
 * @returns {string} response.data[].date - 统计日期，格式：yyyy-MM-dd
 * @returns {number} response.data[].channel_id - 渠道ID
 * @returns {number} response.data[].riskUsers - 风险用户数量
 * @returns {number} response.data[].qualityUsers - 优质用户数量
 * @returns {object} response.extend - 扩展信息对象
 * @returns {string|null} response.exMsg - 异常消息，无异常时为null
 * @returns {boolean} response.fail - 是否失败，false表示成功
 * @returns {boolean} response.success - 是否成功，true表示成功
 * @example
 * // 调用示例
 * const params = {
 *   channelIds: [55010],
 *   startTime: '2025-07-22 00:00:00',
 *   endTime: '2025-07-23 23:59:59',
 *   type: 0
 * }
 * const res = await getFlagStatistics(params)
 * console.log(res.data)
 * // [
 * //   { date: "2025-07-23", riskUsers: 1, qualityUsers: 0, channel_id: 55010 },
 * //   { date: "2025-07-22", riskUsers: 0, qualityUsers: 1, channel_id: 55010 }
 * // ]
 */
export function getFlagStatistics(params) {
  return request({
    url: '/api/outbound/statistics',
    method: 'post',
    data: params
  })
}
