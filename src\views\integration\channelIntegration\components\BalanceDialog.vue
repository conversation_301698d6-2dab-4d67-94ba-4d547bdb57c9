<template>
  <el-dialog
    title="编辑余额"
    :visible.sync="dialogVisible"
    width="500px"
    append-to-body
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="balanceForm"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="整合渠道名称">
        <el-input v-model="form.integrationName" disabled />
      </el-form-item>
      <el-form-item label="余额金额" prop="balanceAmount">
        <el-input-number
          v-model="form.balanceAmount"
          :min="0"
          :precision="2"
        />
      </el-form-item>
      <el-form-item label="成本金额" prop="costAmount">
        <el-input-number
          v-model="form.costAmount"
          :min="0"
          :precision="2"
        />
      </el-form-item>
      <el-form-item label="预付金额" prop="prepayAmount">
        <el-input-number
          v-model="form.prepayAmount"
          :min="0"
          :precision="2"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateIntegrationAccount } from "@/api/integration";

export default {
  name: "BalanceDialog",
  data() {
    return {
      // 内部状态管理
      dialogVisible: false,
      // 表单数据
      form: {
        id: null,
        integrationId: null,
        integrationName: "",
        balanceAmount: 0,
        costAmount: 0,
        prepayAmount: 0,
      },
      // 表单验证规则
      rules: {
        balanceAmount: [
          { required: true, message: "请输入余额金额", trigger: "blur" },
        ],
        costAmount: [
          { required: true, message: "请输入成本金额", trigger: "blur" },
        ],
        prepayAmount: [
          { required: true, message: "请输入预付金额", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    /** 打开余额编辑弹窗 */
    open(rowData) {
      // 回填表单数据
      this.form = {
        id: rowData.id,
        integrationId: rowData.integrationId,
        integrationName: rowData.integrationName,
        balanceAmount: rowData.balanceAmount || 0,
        costAmount: rowData.costAmount || 0,
        prepayAmount: rowData.prepayAmount || 0,
      };
      
      this.dialogVisible = true;
    },

    /** 提交表单 */
    handleSubmit() {
      this.$refs["balanceForm"].validate((valid) => {
        if (valid) {
          const formData = {
            id: this.form.id,
            integrationId: this.form.integrationId,
            balanceAmount: this.form.balanceAmount,
            costAmount: this.form.costAmount,
            prepayAmount: this.form.prepayAmount,
          };

          updateIntegrationAccount(formData).then((response) => {
            this.$modal.msgSuccess("更新成功");
            this.handleClose();
            this.$emit("success");
          });
        }
      });
    },
    
    /** 取消 */
    handleCancel() {
      this.handleClose();
    },
    
    /** 关闭弹窗 */
    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
    },
    
    /** 重置表单 */
    resetForm() {
      this.form = {
        id: null,
        integrationId: null,
        integrationName: "",
        balanceAmount: 0,
        costAmount: 0,
        prepayAmount: 0,
      };
      this.$nextTick(() => {
        if (this.$refs.balanceForm) {
          this.$refs.balanceForm.resetFields();
        }
      });
    },
  },
};
</script> 