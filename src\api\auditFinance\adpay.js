import request from '@/utils/request'

export function getRefundOrderList(data) {
  return request({
    url: "/loan/adPay/refund/getRefundOrderList",
    method: "get",
    params: data
  })
}


export function applyRefundOrderOne(data) {
  return request({
    url: "/loan/adPay/refund/apply",
    method: "post",
     data
  })
}

export function queryRefund(data) {
  return request({
    url: "/loan/adPay/refund/queryRefund",
    method: "get",
    params: data
  })
}

