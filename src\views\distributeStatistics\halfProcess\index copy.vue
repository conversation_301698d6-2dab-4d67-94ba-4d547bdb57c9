<template>
  <div class="app-container half-process">
    <!-- 右侧固定按钮 -->
    <div class="fixed-actions">
      <div
        class="action-btn add-btn"
        @click="clickAdd"
      >
        <i class="el-icon-plus"></i>
      </div>
      <div
        class="action-btn nav-btn"
        @click="showNav = true"
      >
        <i class="el-icon-menu"></i>
      </div>
      <div
        class="action-btn top-btn"
        @click="scrollToTop"
      >
        <i class="el-icon-top"></i>
      </div>
    </div>

    <!-- 分组导航抽屉 -->
    <el-drawer
      title="分组导航"
      :visible.sync="showNav"
      direction="rtl"
      size="400px"
      :modal-append-to-body="false"
    >
      <div class="drawer-content">
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="搜索分组"
            prefix-icon="el-icon-search"
            clearable
            size="small"
          />
        </div>
        <div class="nav-list">
          <div
            v-for="(item, index) in filteredGroups"
            :key="item.id"
            class="nav-item"
            :class="{ active: currentGroupIndex == index }"
            @click="handleNavItemClick(index)"
          >
            {{ item.groupName }}
          </div>
        </div>
      </div>
    </el-drawer>

    <div class="content" ref="groupContent">
      <!-- 添加空状态显示 -->
      <el-empty 
        v-if="groupList.length == 0"
        description="暂无分组配置"
        class="no-group-empty"
      >
        <el-button type="primary" @click="clickAdd">
          新增分组
        </el-button>
      </el-empty>

      <!-- 分组列表显示 -->
      <div v-for="(item, groupIndex) in groupList" :key="item.id" class="group-wrapper" :id="'group-' + groupIndex">
        <el-collapse v-model="activeNames">
          <el-collapse-item :name="groupIndex">
            <template slot="title">
              <div class="collapse-header">
                <div class="collapse-header-left">
                  <i :class="activeNames.includes(groupIndex) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"></i>
                  <el-tag
                    :type="item.status == 1 ? 'success' : 'danger'"
                    size="mini"
                    effect="dark"
                  >
                    {{ item.status == 1 ? "开启" : "关闭" }}
                  </el-tag>
                  <span>{{ item.groupName }}</span>
                </div>
                <div class="header-actions">
                  <el-button type="text" @click.stop="clickEdit(item)">编辑</el-button>
                </div>
              </div>
            </template>
            <div class="group-content">
              <div class="platform-list">
                <div class="tag-list">
                  <el-tag 
                    v-for="product in JSON.parse(item.groupConfigJson)"
                    :key="`${product.platform}-${product.productId}`"
                    type="info"
                    class="platform-tag"
                  >
                    {{ product.platform }} - {{ product.productName }} - {{ product.productId }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <el-dialog
      :title="formData.id ? '修改' : '新增'"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="1200px"
      @close="handleclose"
      destroy-on-close
    >
      <div class="dialog-content">
        <el-form :model="formData" :rules="rules" ref="formData" label-position="top">
          <div class="form-row">
            <el-form-item label="分组名称" prop="groupName" class="form-item-name">
              <el-input
                v-model="formData.groupName"
                placeholder="请输入分组名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="状态" class="form-item-status">
              <el-radio-group v-model="formData.status">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="2">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="排序" class="form-item-sort">
              <el-input-number 
                v-model="formData.sort" 
                :min="0"
                :precision="0"
                placeholder="请输入排序"
              ></el-input-number>
            </el-form-item>
          </div>
          <el-form-item label="选择产品" prop="jsonData" class="form-item-products">
            <el-transfer
              v-model="formData.jsonData"
              :data="processedProductList"
              filterable
              :titles="['待选产品', '已选产品']"
              :props="{
                key: 'key',
                label: 'label',
                disabled: 'disabled'
              }"
            ></el-transfer>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleclose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getHalfPlatformGroupConfig,
  getHalfPlatformProduct,
  addHalfPlatformProduct,
  editHalfPlatformProduct,
} from "@/api/distributeStatistics/halfProcess";
import { throttle } from 'lodash';

export default {
  data() {
    return {
      // 表单校验规则
      rules: {
        groupName: [
          { required: true, message: "请输入分组名称", trigger: "blur" },
        ],
        jsonData: [{ required: true, message: "请选择产品", trigger: "blur" }],
      },
      groupList: [], // 分组列表数据
      productList: [], // 产品列表数据
      formData: {
        groupName: "", // 分组名称
        id: "", // 分组ID，编辑时存在
        status: 1, // 分组状态：1-开启，2-关闭
        sort: 0, // 排序值
        jsonData: [], // 已选产品数据
      },
      dialogVisible: false, // 弹窗显示状态
      activeNames: [], // 当前展开的分组
      currentGroupIndex: 0, // 当前选中的分组索引
      searchQuery: '', // 分组搜索关键词
      showNav: false, // 导航抽屉显示状态
      isManualScrolling: false, // 是否正在手动滚动
    };
  },
  computed: {
    // 根据搜索关键词过滤后的分组列表
    filteredGroups() {
      if (!this.searchQuery) {
        return this.groupList
      }
      const query = this.searchQuery.toLowerCase()
      return this.groupList.filter(group => 
        group.groupName.toLowerCase().includes(query)
      )
    },
    
    // 处理后的产品列表数据，用于穿梭框展示
    // 包含了禁用状态的处理：其他分组已选的产品会被禁用
    processedProductList() {
      if (!this.productList.length) return []

      // 获取其他分组已选择的产品
      const selectedProducts = new Set()
      this.groupList.forEach(group => {
        if (group.id !== this.formData.id) {
          JSON.parse(group.groupConfigJson).forEach(product => {
            selectedProducts.add(`${product.platformType}-${product.productId}`)
          })
        }
      })

      // 处理产品列表
      return this.productList.map(item => ({
        key: `${item.platformType}-${item.productId}`,
        label: `${item.platform}-${item.productName}-${item.productId}`,
        disabled: selectedProducts.has(`${item.platformType}-${item.productId}`) && 
          !this.formData.jsonData.includes(`${item.platformType}-${item.productId}`)
      }))
    }
  },
  mounted() {
    // 初始化数据和滚动监听
    this.getGroupList();
    this.throttledHandleScroll = throttle(this._handleScroll, 200)
    this.initGroupScroll()
  },
  beforeDestroy() {
    // 组件销毁前移除滚动监听
    this.destroyGroupScroll()
  },
  methods: {
    // 初始化分组滚动监听
    initGroupScroll() {
      const el = this.$refs.groupContent
      el.addEventListener('scroll', this.throttledHandleScroll)
    },

    // 处理内容区域滚动，更新当前选中的分组
    _handleScroll() {
      if (this.isManualScrolling) return

      const container = this.$refs.groupContent;
      const containerTop = container.getBoundingClientRect().top;
      let closestGroup = null;
      let minDistance = Infinity;

      Array.from(container.children).forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        const distance = Math.abs(rect.top - containerTop);
        
        if (distance < minDistance) {
          minDistance = distance;
          closestGroup = index;
        }
      });

      if (closestGroup !== null && this.currentGroupIndex !== closestGroup) {
        this.currentGroupIndex = closestGroup;
      }
    },

    // 移除滚动监听
    destroyGroupScroll() {
      const el = this.$refs.groupContent
      if (el) {
        el.removeEventListener('scroll', this.throttledHandleScroll)
      }
      if (this.throttledHandleScroll && this.throttledHandleScroll.cancel) {
        this.throttledHandleScroll.cancel()
      }
    },

    // 获取分组列表数据
    getGroupList() {
      getHalfPlatformGroupConfig({ type: 1 }).then((res) => {
        this.groupList = res.data;
        this.activeNames = res.data.map((_, index) => index);
      });
    },

    // 点击新增按钮
    clickAdd() {
      getHalfPlatformProduct({ type: 1 }).then((res) => {
        this.productList = res.data;
        this.dialogVisible = true;
      });
    },

    // 点击编辑按钮
    clickEdit(item) {
      getHalfPlatformProduct({ type: 1 }).then((res) => {
        this.productList = res.data;
        this.dialogVisible = true;
        let data = JSON.parse(item.groupConfigJson);
        let jsonData = [];
        data.forEach((item) => {
          jsonData.push(item.platformType + "-" + item.productId);
        });
        this.formData = {
          groupName: item.groupName,
          id: item.id,
          status: item.status,
          sort: item.sort,
          jsonData,
        };
      });
    },

    // 关闭弹窗，重置表单数据
    handleclose() {
      this.dialogVisible = false;
      this.formData = {
        groupName: "",
        id: "",
        status: 1,
        sort: 0,
        jsonData: [],
      };
    },

    // 提交表单数据
    handleSubmit() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          let groupConfigJson = [];
          this.productList.forEach((item) => {
            if (this.formData.jsonData.includes(`${item.platformType}-${item.productId}`)) {
              groupConfigJson.push(item);
            }
          });

          let data = {
            groupConfigJson: JSON.stringify(groupConfigJson),
            groupName: this.formData.groupName,
            status: this.formData.status,
            sort: this.formData.sort,
            type: 1
          };
          if (this.formData.id) {
            data.id = this.formData.id;
            editHalfPlatformProduct(data).then((res) => {
              this.$message({
                message: "修改成功",
                type: "success",
              });
              this.getGroupList();
              this.handleclose();
            });
          } else {
            addHalfPlatformProduct(data).then((res) => {
              this.$message({
                message: "新增成功",
                type: "success",
              });
              this.getGroupList();
              this.handleclose();
            });
          }
        }
      });
    },

    // 处理导航项点击，滚动到对应分组
    handleNavItemClick(index) {
      this.scrollToGroup(index)
      this.showNav = false
    },

    // 滚动到指定索引的分组
    scrollToGroup(index) {
      if (index == -1) return
      
      this.isManualScrolling = true
      this.currentGroupIndex = index

      const element = document.getElementById(`group-${index}`)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' })
        
        setTimeout(() => {
          this.isManualScrolling = false
        }, 1000)
      }
    },

    // 滚动到页面顶部
    scrollToTop() {
      const container = this.$refs.groupContent
      if (container) {
        container.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/**
 * 页面整体样式
 */
.half-process {
  height: calc(100vh - 90px);
  background-color: transparent;
  padding: 0;
  display: flex;
  gap: 20px;
  position: relative;

  &:has(.no-group-empty) {
    background-color: #fff;
  }

  /**
   * 右侧固定按钮样式
   */
  .fixed-actions {
    position: fixed;
    right: 24px;
    bottom: 24px;
    z-index: 99;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .action-btn {
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.25s ease;
      background-color: #fff;
      color: #909399;
      border-radius: 50%;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      i {
        font-size: 18px;
      }

      &:hover {
        color: #fff;
        background-color: #004DAB;
        box-shadow: 0 6px 16px rgba(103, 194, 58, 0.3);
      }
    }
  }

  /**
   * 导航抽屉内容样式
   */
  .drawer-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px;

    .search-box {
      margin-bottom: 16px;

      .el-input {
        ::v-deep .el-input__inner {
          border-radius: 4px;
          
          &:hover {
            border-color: #DCDFE6;
          }
          
          &:focus {
            border-color: #004DAB;
          }
        }
      }
    }

    .nav-list {
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 8px;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #E4E7ED;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      .nav-item {
        padding: 8px 12px;
        cursor: pointer;
        font-size: 14px;
        color: #606266;
        border-radius: 4px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        user-select: none;

        &:hover {
          color: #004DAB;
          background-color: #ecf5ff;
        }

        &.active {
          color: #fff;
          background-color: #004DAB;
          font-weight: 500;
        }
      }
    }
  }

  /**
   * 主内容区域样式
   */
  .content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;

    ::v-deep .no-group-empty {
      margin-top: 120px;

      .el-empty__description {
        cursor: pointer;
        color: #606266;
        
        &:hover {
          color: #004DAB;
        }
      }

      .el-button {
        margin-top: 20px;
      }
    }

    .group-wrapper {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      ::v-deep .el-collapse-item__header {
        padding: 12px 20px;
        background-color: #fff;
        border: none;
        
        .collapse-header {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .collapse-header-left {
            display: flex;
            align-items: center;
            gap: 8px;

            .el-tag {
              margin: 0;
            }
          }
        }

        .el-collapse-item__arrow {
          display: none;
        }
      }

      ::v-deep .el-collapse-item__wrap {
        background-color: #fff;
        border: none;
      }

      ::v-deep .el-collapse-item__content {
        padding: 20px;
      }
    }

    .status-info {
      margin-bottom: 16px;
      font-size: 14px;
    }

    .platform-list {
      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .platform-tag {
          margin-right: 0;
          max-width: 100%;

          &.el-tag {
            display: inline-flex;
            align-items: center;
            height: auto;
            padding: 6px 10px;
            line-height: 1.4;
            white-space: normal;
            word-break: break-all;
          }
        }
      }
    }
  }
}

/**
 * 弹窗内容样式
 */
.dialog-content {
  padding: 20px 20px 0;

  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .form-item-name {
      flex: 2;
    }

    .form-item-status {
      flex: 1;
      min-width: 160px;
    }

    .form-item-sort {
      flex: 1;
      min-width: 160px;

      ::v-deep .el-input-number {
        width: 100%;
      }
    }
  }

  .form-item-products {
    margin-bottom: 0;
  }

  ::v-deep .el-form-item__label {
    padding-bottom: 8px;
    line-height: 20px;
    font-weight: normal;
    color: #606266;
  }

  ::v-deep .el-transfer {
    display: flex;
    justify-content: center;
    align-items: center;
    
    .el-transfer-panel {
      flex: 1;
      width: auto;

      .el-transfer-panel__header {
        background: #f5f7fa;
        border-bottom: 1px solid #EBEEF5;
      }
    }

    .el-transfer__buttons {
      padding: 0 20px;
    }
  }
}
</style>
