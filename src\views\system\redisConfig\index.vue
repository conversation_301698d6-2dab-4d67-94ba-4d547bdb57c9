<template>
  <div class="redis-config-page">
    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧配置列表 -->
      <ConfigSidebar
        :config-list="configList"
        @refresh="loadConfigList"
        @select="handleConfigSelect"
      />

      <!-- 右侧配置详情 -->
      <div class="config-workspace">
        <!-- 空状态 -->
        <WorkspaceEmpty v-if="!selectedConfig" />

        <!-- 配置详情内容 -->
        <div v-else class="workspace-content">
          <!-- 配置信息头部 -->
          <ConfigHeader
            :config="selectedConfig"
            :config-key="selectedConfigKey"
          />

          <!-- 配置说明面板 -->
          <ConfigDescription :content="selectedConfig.content" />

          <!-- 平台配置标签页 -->
          <PlatformTabs
            :platforms="selectedConfig.list"
            :active-platform="activePlatform"
            @tab-change="handleTabChange"
            @config-update="handleConfigUpdate"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ConfigSidebar from './components/ConfigSidebar.vue'
import ConfigHeader from './components/ConfigHeader.vue'
import ConfigDescription from './components/ConfigDescription.vue'
import PlatformTabs from './components/PlatformTabs.vue'
import WorkspaceEmpty from './components/WorkspaceEmpty.vue'
import { getConfigList, getConfigByKey } from '@/api/system/redisConfig'

export default {
  name: 'RedisConfig',
  components: {
    ConfigSidebar,
    ConfigHeader,
    ConfigDescription,
    PlatformTabs,
    WorkspaceEmpty
  },
  
  data() {
    return {
      // 配置列表相关
      configList: [],

      // 配置详情相关
      selectedConfig: null,
      selectedConfigKey: '',
      activePlatform: '',
      originalData: {}
    }
  },

  created() {
    this.loadConfigList()
  },

  methods: {
    // 加载配置列表
    async loadConfigList() {
      try {
        const response = await getConfigList()
        this.configList = response.data || []
      } catch (error) {
        this.$message.error('加载配置列表失败')
        this.configList = []
      }
    },

    // 选择配置
    async handleConfigSelect(config) {
      if (!config.key) return

      try {
        const response = await getConfigByKey({ key: config.key })
        this.selectedConfig = response.data
        this.selectedConfigKey = config.key
        this.originalData = JSON.parse(JSON.stringify(response.data))

        // 默认选中第一个平台
        if (this.selectedConfig.list && this.selectedConfig.list.length > 0) {
          this.activePlatform = this.selectedConfig.list[0].platformType.toString()
        }
      } catch (error) {
        this.$message.error('加载配置详情失败')
        this.selectedConfig = null
        this.selectedConfigKey = ''
      }
    },

    // 处理标签页切换
    handleTabChange(tabName) {
      this.activePlatform = tabName
    },

    // 处理配置更新
    handleConfigUpdate(updateData) {
      // 子组件通知配置已更新，这里可以做一些处理
      console.log('配置已更新:', updateData)
    }
  }
}
</script>

<style lang="scss" scoped>
// CSS 变量定义
:root {
  --redis-primary-color: #dc382d;
  --redis-primary-light: #ff6b5a;
  --redis-primary-dark: #b71c1c;
  --redis-secondary-color: #2196f3;
  --redis-accent-color: #4caf50;
}

.redis-config-page {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 90px);
  width: 100%;
  background: #ffffff;
  font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;

  // 主内容区域
  .main-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    // 右侧工作区域
    .config-workspace {
      flex: 1;
      background: transparent;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .workspace-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }
}
</style>