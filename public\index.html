<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <meta name="version" id="BPMVersion" content="<%= htmlWebpackPlugin.options.version%>" />
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <title>
    <%= webpackConfig.name %>
  </title>
  <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    .loading-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    .loading-box .loading-wrap {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 98px;
    }

    .dot {
      position: relative;
      box-sizing: border-box;
      display: inline-block;
      width: 32px;
      height: 32px;
      font-size: 32px;
      transform: rotate(45deg);
      animation: ant-rotate 1.2s infinite linear;
    }

    .dot i {
      position: absolute;
      display: block;
      width: 14px;
      height: 14px;
      background-color: #004DAB;
      border-radius: 100%;
      opacity: 0.3;
      transform: scale(0.75);
      transform-origin: 50% 50%;
      animation: ant-spin-move 1s infinite linear alternate;
    }

    .dot i:nth-child(1) {
      top: 0;
      left: 0;
    }

    .dot i:nth-child(2) {
      top: 0;
      right: 0;
      animation-delay: 0.4s;
    }

    .dot i:nth-child(3) {
      right: 0;
      bottom: 0;
      animation-delay: 0.8s;
    }

    .dot i:nth-child(4) {
      bottom: 0;
      left: 0;
      animation-delay: 1.2s;
    }

    @keyframes ant-rotate {
      to {
        transform: rotate(405deg);
      }
    }

    @keyframes ant-spin-move {
      to {
        opacity: 1;
      }
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="loading-box">
      <div class="loading-wrap">
        <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
      </div>
    </div>
  </div>
</body>

</html>
