import request from '@/utils/request'
//获取商户列表
export const getPartyAlist = (data) => {
  return request({
    url: '/loan/partya/list',
    method: 'get',
    params: data
  })
}
//获取商务列表
export const getBusinessList = () => {
  return request({
    url: "/loan/partya/userList/permission",
    method: "get",

  })
}
//添加商户信息
export const addPartyAOne = (data) => {
  return request({
    url: "/loan/partya/add",
    method: "post",
    data
  })
}
//修改商户信息
export const editPartyAOne = (data) => {
  return request({
    url: "/loan/partya/edit",
    method: "post",
    data
  })
}
//获取详情商户信息
export const getPartyAOne = (id) => {
  return request({
    url: `/loan/partya/query/${id}`,
    method: "get",
  })
}
//商户入账
export const PartyArecharg = (data) => {
  return request({
    url: "/loan/partya/recharge",
    method: "post",
    data
  })
}

export const partyRefund = (data) => {
  return request({
    url: "/loan/partya/refund",
    method: "post",
    data
  })
}
//查询收款主体
export const getSubjectAll = () => {
  return request({
    url: "/loan/partya/subjectAll",
    method: "get"
  })
}


//收款主体列表
export const getSubjectList = () => {
  return request({
    url: "/loan/subject/list",
    method: "get"
  })
}

//添加主体
export const addSubjectOne = (data) => {
  return request({
    url: "/loan/subject/add",
    method: "post",
    data
  })
}
//修改主体列表
export const editSubjectOne = (data) => {
  return request({
    url: "/loan/subject/edit",
    method: "post",
    data
  })
}
//获取收款主体
export const getSubjectOne = (id) => {
  return request({
    url: `/loan/subject/${id}`,
    method: "get",
  })
}
//删除收款主体
export const delSubjectOne = (id) => {
  return request({
    url: `/loan/subject/del/${id}`,
    method: "post",
  })
}

//新增商户用户
export const addPartyaUser = (data) => {
  return request({
    url: "/loan/partya/addPartyaUser",
    method: "post",
    data
  })
}
//获取用户列表
export const getPartyaUserList = (id) => {
  return request({
    url: `/loan/partya/partyaUserList/${id}`,
    method: "get",
  })
}

//修改商户用户状态
export const aupdateUserStatus = (data) => {
  return request({
    url: "/loan/partya/updateUserStatus",
    method: "post",
    data
  })
}
//修改商户用户状态
export const updateUserPassword = (data) => {
  return request({
    url: "/loan/partya/updateUserPassword",
    method: "post",
    data
  })
}
//获取商户详情总共
export const getPartyDetail = (id) => {
  return request({
    url: `/loan/partya/detail/detailTotal/${id}`,
    method: "get"
  })
}
//获取商户收益明细
export const getProfitList = (id, data) => {
  return request({
    url: `/loan/partya/detail/profitList/${id}`,
    method: "get",
    params: data
  })
}
//获取商户返点明细
export const getRebateList = (id, data) => {
  return request({
    url: `/loan/partya/detail/rebateList/${id}`,
    method: "get",
    params: data
  })
}
//获取商户充值明细
export const getRechargeList = (id, data) => {
  return request({
    url: `/loan/partya/detail/rechargeList/${id}`,
    method: "get",
    params: data
  })
}
//获取商户退款明细
export const getrefundList = (id, data) => {
  return request({
    url: `/loan/partya/detail/refundList/${id}`,
    method: "get",
    params: data

  })
}
//修改商户用户信息
export const updatePartyaUser = (data) => {
  return request({
    url: "/loan/partya/updatePartyaUser",
    method: "post",
    data
  })
}
//获取商户产品
export const getAllProducts = (data) => {
  return request({
    url: "/loan/partya/getPartyaEditProduct",
    method: "get",
    params: data
  })
}
//获取商户用户权限列表
export const getpartyUserContronl = (data) => {
  return request({
    url: "/loan/partya/queryUserRole",
    method: 'get',
    params: data
  })
}

//添加商户保证金
export const getDepositAdd = (data) => {
  return request({
    url: "/loan/partya/detail/deposit/add",
    method: "post",
    data
  })
}
//获取保证金列表
export const getDepositList = (id, data) => {
  return request({
    url: `/loan/partya/detail/depositList/${id}`,
    method: "get",
    params: data
  })
}

//获取商户登录白名单
export const getCityBlankList = () => {
  return request({
    url: '/loan/partya/getCity',
    method: "get"
  })
}
//修改用户权限
export const updatePartyaUserRole = (data) => {
  return request({
    url: '/loan/partya/updatePartyaUserRole',
    method: "post",
    data
  })
}

//获取商户合同类容
export const getContractInfo = (data) => {
  return request({
    // url: `/loan/partya/getContractInfo/${data}`,
    url: `/loan/partya/contract/getContractInfo/${data}`,
    method: 'get'
  })
}
//审核商户
export const checkContractInfo = (data) => {
  return request({
    url: '/loan/partya/contract/checkContract',
    method: "post",
    data
  })
}

//修改商户登录状态
export const editLoginStatus = (data) => {
  return request({
    url: '/loan/partya/updateLoginStatus',
    method: "post",
    data
  })
}

//获取商户子账号信息
export const getSonAdminInfo = (id) => {
  return request({
    url: `/loan/partya/subUserList/${id}`,
    method: "get"
  })
}



//撤销返点
export const cancaleRevoke = (data) => {
  return request({
    url: "/loan/partya/detail/rebate/revoke",
    method: "post",
    data
  })
}

//获取商务退款列表
export const getPartyaRefundList = (data) => {
  return request({
    url: '/loan/partya/refund/list/business',
    method: "get",
    params: data
  })
}
//商务拒绝退款
export const rejectPartyaRefundOne = (data) => {
  return request({
    url: '/loan/partya/refund/doReject/business',
    method: "post",
    data

  })
}
//商务同意退款
export const passPartyaRefundOne = (data) => {
  return request({
    url: '/loan/partya/refund/pass/business',
    method: "post",
    data

  })
}
//修改合同编号
export const updateContractNo = (data) => {
  return request({
    url: "/loan/partya/updateContractNo",
    method: "post",
    data
  })
}

//修改视频
export const updateContractVideo = (data, id) => {
  return request({
    url: `/loan/partya/contract/updateContractVideo/${id}`,
    method: 'post',
    data
  })
}
//拉黑商户
export const partyaBlock = (data) => {
  return request({
    url: '/loan/partya/block',
    method: "post",
    data
  })
}

//获取商户黑名单列表
export const getPartyablackList = (data) => {
  return request({
    url: "/loan/partyablack/getlist",
    method: "get",
    params: data
  })
}

//查询商户是否在黑名单里面
export const checkListPartyName = (data) => {
  return request({
    url: "/loan/partyablack/getListPartyName",
    method: 'get',
    params: data
  })
}


//解除商户黑名单
export const partyablackRemove = (data) => {
  return request({
    url: "/loan/partyablack/remove",
    method: "post", data
  })
}

//商户黑名单列表拉黑
export const partyablackByName = (data) => {
  return request({
    url: "/loan/partyablack/black",
    method: "post",
    data
  })
}

//删除甲方主账号
export const delPartyAUserAdmin = (data) => {
  return request({
    url: "/loan/partya/deleteprimary",
    method: 'post',
    data
  })
}
//删除甲方子帐号
export const delPartySubAdmin = (data) => {
  return request({
    url: "/loan/partya/deletesub",
    method: 'post',
    data
  })
}

//甲方设置提醒余额
export const editBalanceTip = (data) => {
  return request({
    url: "/loan/partya/update/balance/tip",
    method: "post",
    data
  })
}


//获取公海客户列表
export const getPartyFirstHighSeasList = (data) => {
  return request({
    url: "/loan/partyFirst/highSeas/list",
    method: "get",
    params: data
  })
}
//获取公海客户列表
export const getHighSeasList = (data) => {
  return request({
    url: "/loan/partyFirst/highSeas/party/list",
    method: "get",
    params: data
  })
}
//领取公海客户
export const getPartyFirstHighSeasreceive = (data) => {
  return request({
    url: "/loan/partyFirst/highSeas/receive",
    method: "get",
    params: data
  })
}
//领取公海客户
export const partyFirstHighSeasBlack = (data) => {
  return request({
    url: "/loan/partyFirst/highSeas/black",
    method: "post",
    data
  })
}


//设置白名单
export const changeWhithStatus = (data) => {
  return request({
    url: '/loan/partya/set/white',
    method: "get",
    params: data
  })
}

//获取商户手机号
export const getPartyaPhone = (data) => {
  return request({
    url: "/loan/partya/getTel",
    method: "get",
    params: data
  })
}
//移交到公海
export const transferToHighSeas = (data) => {
  return request({
    url: "/loan/partya/toHighSeas",
    method: "get",
    params: data
  })
}
//公海移交审核资料
export const checkToHighSeas = (data) => {
  return request({
    url: "/loan/partya/toLegalAffairs",
    method: "get",
    params: data
  })
}
//公海移交审核资料
export const getHighSeasLog = (data) => {
  return request({
    url: "/loan/partyFirst/highSeas/receive/log",
    method: "get",
    params: data
  })
}

//获取公海商务列表
export const getHighSeasUser = () => {
  return request({
    url: "/loan/partyFirst/highSeas/business/pullDown",
    method: "get"
  })
}
//获取公海商务列表
export const getHighListSeasUser = () => {
  return request({
    url: "/loan/partyFirst/highSeas/business/receive/pullDown",
    method: "get"
  })
}

//分配公海用户
export const getHighSeasAppoint = (data) => {
  return request({
    url: "/loan/partyFirst/highSeas/appoint",
    method: 'get',
    params: data

  })
}


//获取所有商务
export const gethighSeasUserList = () => {
  return request({
    url: "/loan/partyFirst/highSeas/party/all/list",
    method: "get",
  })
}

//上传
export const upLoadFileUserQuality = (data) => {
  return request({
    url: "/loan/userQuality/feedback/upload",
    method: 'post',
    data
  })
}
//上传
export const productAnalyseQuality = (data) => {
  return request({
    url: "/loan/userQuality/feedback/analyse/product",
    method: 'get',
    params:data
  })
}

export const getProductList=()=>{
  return request({
    url:"/loan/userQuality/feedback/product/list",
    method:"get"
  })
}
