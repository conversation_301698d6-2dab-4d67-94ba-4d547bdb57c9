<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="订单号">
        <el-input
          size="mini"
          v-model="queryParams.orderNo"
          clearable
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="list">
      <el-table-column label="订单号" prop="orderNo" align="center" />
      <el-table-column label="充值时间" prop="orderTime" align="center" />
      <el-table-column label="充值金额" prop="orderAmount" align="center" />
      <el-table-column label="订单状态" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ statusType[row.status * 1] }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getChargeOrderList } from "@/api/partyManage";
export default {
  data() {
    return {
      value1: [],
      list: [],
      total: 0,
      statusType: {
        1: "支付中",
        2: "取消支付",
        3: "支付成功",
        4: "支付过期",
      },
      queryParams: {
        startTime: "",
        stopTime: "",
        orderNo: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    getList() {
      getChargeOrderList(this.queryParams).then((res) => {
        this.list = res.rows;
        this.total=res.total
      });
    },
    handleQuery() {
      if (this.value1 !== null) {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.value1[0];
        this.queryParams.stopTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.stopTime = "";
      }
      this.getList();
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
