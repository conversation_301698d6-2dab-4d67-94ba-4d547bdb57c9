<template>
  <div class="api-switcher" v-if="isDev">
    <!-- 触发按钮 -->
    <el-tooltip content="切换API地址" placement="left">
      <el-button
        class="api-switcher-trigger"
        type="primary"
        size="mini"
        circle
        icon="el-icon-connection"
        @click="openDialog"
      />
    </el-tooltip>

    <!-- 切换对话框 -->
    <el-dialog
      title="API地址切换"
      :visible.sync="visible"
      width="650px"
      :close-on-click-modal="false"
      append-to-body
    >
      <!-- 当前状态 -->
      <div class="current-status" v-if="currentEndpoint">
        <div class="status-item">
          <span class="label">当前环境：</span>
          <span class="value">{{ currentEndpoint.name }}</span>
        </div>
        <div class="status-item">
          <span class="label">API地址：</span>
          <span class="value">{{ currentEndpoint.url }}</span>
        </div>
      </div>

      <!-- 环境选择 -->
      <div class="endpoint-list">
        <el-radio-group v-model="currentKey" @change="switchEndpoint">
          <el-row :gutter="16">
            <el-col :span="12" v-for="(endpoint, key) in allEndpoints" :key="key">
              <div class="endpoint-item" :class="{ 'is-selected': currentKey === key }">
                <el-radio :label="key" class="endpoint-radio">
                  <div class="endpoint-content">
                    <div class="endpoint-name">{{ endpoint.name }}</div>
                    <div class="endpoint-url">{{ endpoint.url }}</div>
                  </div>
                </el-radio>
              </div>
            </el-col>
          </el-row>
        </el-radio-group>
      </div>

      <div slot="footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { apiManager, API_ENDPOINTS } from '@/utils/apiManager'

export default {
  name: 'ApiSwitcher',
  data() {
    return {
      visible: false,
      currentKey: '',
      currentEndpoint: null
    }
  },
  computed: {
    isDev() {
      return process.env.NODE_ENV === 'development'
    },
    allEndpoints() {
      return { ...API_ENDPOINTS, ...apiManager.customEndpoints }
    }
  },
  watch: {
    // 监听对话框状态，确保每次打开时状态是最新的
    visible(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.updateCurrentStatus()
        })
      }
    }
  },
  created() {
    this.updateCurrentStatus()
    // 监听API变化
    apiManager.addListener(this.onApiChange)

    // 开发环境下输出调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log(`%c[API切换器] 组件初始化，当前端点: ${this.currentKey}`, 'color: #909399;')
    }
  },
  beforeDestroy() {
    apiManager.removeListener(this.onApiChange)
  },
  methods: {
    updateCurrentStatus() {
      // 获取当前端点key
      this.currentKey = apiManager.currentEndpoint

      // 获取当前端点详细信息
      this.currentEndpoint = apiManager.getCurrentEndpoint()

      // 验证状态一致性
      if (!this.currentEndpoint) {
        console.warn(`%c[API切换器] 当前端点 "${this.currentKey}" 无效，尝试重新获取`, 'color: #e6a23c;')
        // 重新验证并获取
        apiManager.validateCurrentEndpoint()
        this.currentKey = apiManager.currentEndpoint
        this.currentEndpoint = apiManager.getCurrentEndpoint()
      }

      // 开发环境下输出状态信息
      if (process.env.NODE_ENV === 'development') {
        console.log(`%c[API切换器] 状态更新 - 端点: ${this.currentKey}, 名称: ${this.currentEndpoint?.name}, URL: ${this.currentEndpoint?.url}`, 'color: #909399;')
      }
    },

    switchEndpoint(key) {
      if (apiManager.switchEndpoint(key)) {
        this.updateCurrentStatus()
        this.$message.success(`已切换到: ${apiManager.getCurrentEndpoint().name}`)
      }
    },

    onApiChange() {
      this.updateCurrentStatus()
    },

    openDialog() {
      // 打开对话框前刷新状态
      this.updateCurrentStatus()
      this.visible = true

      if (process.env.NODE_ENV === 'development') {
        console.log(`%c[API切换器] 对话框打开，当前状态已刷新`, 'color: #909399;')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.api-switcher {
  position: fixed;
  top: 60px;
  right: 20px;
  z-index: 1000;

  .api-switcher-trigger {
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  }
}

.current-status {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .status-item {
    flex: 1;

    .label {
      color: #909399;
      font-size: 14px;
      margin-right: 8px;
    }

    .value {
      color: #303133;
      font-weight: 500;
      font-size: 14px;
    }
  }
}

.endpoint-list {
  .endpoint-item {
    margin-bottom: 16px;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.2s;

    &:hover {
      border-color: #c6e2ff;
      background-color: #f5f9ff;
    }

    // 选中状态的样式
    &.is-selected {
      border-color: #409eff;
      background-color: #ecf5ff;
    }

    .endpoint-radio {
      width: 100%;
      margin: 0;
      display: flex;
      align-items: flex-start;

      .endpoint-content {
        width: 100%;
        flex: 1;

        .endpoint-name {
          font-weight: 500;
          color: #303133;
          font-size: 14px;
          margin-bottom: 4px;
          line-height: 1.4;
        }

        .endpoint-url {
          font-size: 12px;
          color: #909399;
          word-break: break-all;
          line-height: 1.4;
        }
      }
    }
  }
}

::v-deep .el-radio {
  display: flex !important;
  align-items: flex-start !important;
  width: 100% !important;
  margin: 0 !important;

  .el-radio__input {
    margin-top: 2px;
    flex-shrink: 0;
  }

  .el-radio__label {
    width: calc(100% - 20px);
    padding-left: 8px;
    flex: 1;
    line-height: 1.4;
  }

  .el-radio__input.is-checked + .el-radio__label {
    color: #409eff;
  }
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
}
</style>
