<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="平台名称" prop="platformName">
        <el-input v-model.number="queryParams.platformName" placeholder="请输入平台名称" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableList" border>
      <el-table-column label="流量平台ID" width="130" align="center" prop="allotPlatformId" />
      <el-table-column label="平台名称" align="center" prop="platformName" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="状态" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-switch v-model="row.status"  active-text="正常"
              inactive-text="禁用" :active-value="0" :inactive-value="1" @change="changeStatus($event, row)" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="接入方式" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ joinType[row.joinType] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark">
      </el-table-column>
      <el-table-column label="操作">
        <template  slot-scope="{row}">
          <div>
            <el-button icon="el-icon-edit" @click="handleEdit(row)" type="text">修改</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="isAdd ? '新增平台' : '修改平台'" :visible.sync="show" append-to-body center width="600px"
      :close-on-click-modal="false" @colse="cancel">
      <el-form ref="formData" :model="formData" :rules="rules" label-width="130px">
        <el-form-item label="流量平台名称" prop="platformName">
          <el-input v-model="formData.platformName" placeholder="请输入流量平台名称" />
        </el-form-item>
        <el-form-item label="接入方式" prop="joinType">
          <el-select v-model="formData.joinType" placeholder="请选择接入方式">
            <el-option label="推送" :value="0"></el-option>
            <el-option label="开放平台" :value="1"></el-option>
            <el-option label="导入" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio v-model="formData.status" :label="0">正常</el-radio>
          <el-radio v-model="formData.status" :label="1">禁用</el-radio>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormData">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAllotList,
  addAllotListOne,
  editAllotListOne,
  changeAllotListOne,
} from "@/api/saas/flow";
export default {
  data() {
    return {

      joinType: {
        0: '推送',
        1: "开放平台",
        2: '导入',
      },
      tableList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        platformName: "",
      },
      show: false,
      isAdd: true,
      rules: {
        platformName: [
          { required: true, message: "流量平台名称不能为空", trigger: "blur" },
        ],
        joinType: [
          { required: true, message: "接入方式不能为空", trigger: "change" },
        ],
        status: [
          {
            required: true,
            message: "状态不能为空",
            trigger: "change",
          },
        ],
      },
      formData: {
        platformName: "",
        joinType: "",
        status: 0,
        remark: "",
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      getAllotList(this.queryParams).then((res) => {
        this.tableList = res.rows;
        this.total = res.total;
      });
    },
    handleAdd() {
      this.show = true;
      this.isAdd = true;
      if (this.formData.allotPlatformId) {
        delete this.formData.allotPlatformId;
      }
    },
    handleEdit(row) {
      this.show = true;
      this.isAdd = false;
      this.formData.platformName = row.platformName;
      this.formData.joinType = row.joinType;
      this.formData.status = row.status;
      this.formData.remark = row.remark;
      this.formData.allotPlatformId = row.allotPlatformId;
    },
    submitFormData() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addAllotListOne(this.formData).then((res) => {
              this.$message.success("新增成功");
              this.cancel();
              this.getList();
            });
          } else {
            editAllotListOne(this.formData).then((res) => {
              this.$message.success("修改成功");
              this.cancel();
              this.getList();
            });
          }
        }
      });
    },
    changeStatus(e, row) {
      this.$confirm("确定修改状态吗?", { type: "warning" })
        .then(() => {
          changeAllotListOne({
            allotPlatformId: row.allotPlatformId,
            status: e,
          })
            .then((res) => {
              this.getList();
              this.$message.success("修改成功");
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch((err) => {
          e == 0 ? (row.status = 1) : (row.status = 0);
        });
    },
    cancel() {
      this.show = false;
      this.formData = {
        platformName: "",
        joinType: "",
        status: 0,
        remark: "",
      };
      this.$refs.formData.resetFields();
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
