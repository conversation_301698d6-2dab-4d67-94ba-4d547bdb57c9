import request from '@/utils/request'

/**
 * 获取贷超渠道城市配置列表
 * @param {Object} query 查询参数
 * @returns {Promise}
 */
export function getDcCityList(query) {
  return request({
    url: '/loan/PartyB/dcChannelControl',
    method: 'get',
    params: query
  })
}

/**
 * 修改贷超渠道忽略城市
 * @param {Object} data 包含id和text的对象
 * @returns {Promise}
 */
export function editDcCityText(data) {
  return request({
    url: '/loan/PartyB/channel/setDcIgnoreCity',
    method: 'post',
    data: data
  })
}

/**
 * 修改贷超渠道城市忽略状态
 * @param {Object} data 包含id和status的对象
 * @returns {Promise}
 */
export function editDcCityStatus(data) {
  return request({
    url: '/loan/PartyB/channel/dcCityStatus',
    method: 'post',
    data: data
  })
} 