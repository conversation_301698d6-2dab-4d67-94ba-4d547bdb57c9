
import request from '@/utils/request'
//查询流量平台列表
export function getAllotList(data) {
    return request({
        url: "/saas/allot/platform/getList",
        method: "get",
        params: data
    })
}
//新增流量平台列表
export function addAllotListOne(data) {
    return request({
        url: "/saas/allot/platform/addAllotPlatform",
        method: "post",
        data
    })
}
//修改流量平台列表
export function editAllotListOne(data) {
    return request({
        url: "/saas/allot/platform/updateAllotPlatform",
        method: "post",
        data
    })
}
//修改状态
export function changeAllotListOne(data) {
    return request({
        url: "/saas/allot/platform/updateAllotPlatformStatus",
        method: "post",
        data
    })
}