<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="推广名称">
        <el-input
          size="mini"
          @keyup.enter.native="handleQuery"
          clearable
          placeholder="请输入推广名称"
          v-model="queryParams.productName"
        ></el-input>
      </el-form-item>
      <el-form-item label="产品是否在线">
        <el-select
          v-model="queryParams.productStatus"
          size="small"
          clearable
          style="width: 140px"
        >
          <el-option :value="1" label="在线"></el-option>
          <el-option :value="2" label="不在"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="平台类型">
        <el-select
          v-model="queryParams.platformType"
          size="small"
          clearable
          multiple
          collapse-tags
          @change="handleQuery"
          style="width: 240px"
        >
          <el-option
            v-for="item in platformList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border>
      <el-table-column label="平台名称" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-tag
              :style="{ backgroundColor: getTagType(row.platformType), color: '#fff', border: 'none' }"
              size="mini"
            >
              {{ getPlatformName(row.platformType) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="productId"
        label="产品ID"
        align="center"
      />
      <el-table-column
        prop="productName"
        label="产品名称"
        align="center"
      />
      <el-table-column
        prop="sysUserNickName"
        label="商务名称"
        align="center"
      />
      <el-table-column
        label="推广价格"
        align="center"
        sortable
        prop="cooperationCost"
      >
        <template slot-scope="scope">
          <div>{{ scope.row.cooperationCost || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="比价价格"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.comparePrice"
            :min="0"
            :max="999999"
            :precision="2"
            :controls="false"
            style="width: 130px"
            size="mini"
            @change="handleSavePrice(scope.row)"
            @focus="handlePriceFocus(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :pageSizes="[20, 30, 50, 100]"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getPlatformList } from "@/api/xmxrChannelManage/channelList";
import { getPlatformProductCompareList, updateProductComparePrice } from "@/api/distributeStatistics/loanComparisonConfig";

export default {
  data() {
    return {
      platformList: [],
      dataList: [],
      queryParams: {
        productName: "",
        productStatus: 1,
        platformType: [],
        pageNum: 1,
        pageSize: 100,
      },
      total: 0,
      type: {
        1: "银行机构",
        2: "线上-贷超",
        3: "线上持牌机构",
        4: "一级机构",
        5: "二级机构",
        6: "三级机构",
        7: "四级机构",
      },
      currentComparePrice: 0
    };
  },
  methods: {
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.queryParams = {
        productName: "",
        productStatus: 1,
        platformType: [],
        pageNum: 1,
        pageSize: 100,
      }
      this.getList();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      getPlatformProductCompareList(this.queryParams).then(res => {
        this.dataList = res.rows || [];
        this.total = res.total;
      });
    },
    handlePriceFocus(row) {
      this.currentComparePrice = row.comparePrice;
    },
    handleSavePrice(row) {
      if (row.comparePrice == this.currentComparePrice) return;

      this.$confirm('确定修改比价价格吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          productId: row.productId,
          platformType: row.platformType,
          comparePrice: row.comparePrice
        };
        updateProductComparePrice(params).then(() => {
          this.$message.success('保存成功');
          this.getList();
        });
      }).catch(() => {
        row.comparePrice = this.currentComparePrice;
      });
    },
    getTagType(platformType) {
      const baseColors = [
        '#52C41A',  // 清新绿
        '#4B7BE5',  // 优雅蓝
        '#FA8C16',  // 温暖橙
        '#F15A5A',  // 玫瑰红
        '#13A8A8',  // 青蓝色
        '#597EF7',  // 靛蓝色
        '#2F9688',  // 翠绿色
        '#E8943C',  // 琥珀色
        '#3B7EC9',  // 宝蓝色
        '#D48806'   // 赭石色
      ];
      const colorIndex = (platformType - 1) % baseColors.length;
      return baseColors[colorIndex];
    },
    getPlatformName(platformType) {
      const platform = this.platformList.find(item => item.id === platformType);
      return platform ? platform.name : '';
    }
  },
  mounted() {
    this.getList();
    getPlatformList().then(res => {
      this.platformList = res.data || [];
    });
  }
};
</script>

<style lang="scss">
input[aria-hidden="true"] {
  display: none !important;
}
::v-deep .el-input {
  input[type="number"] {
    padding-right: 0px;
    appearance: textfield;
    line-height: 1px !important;
    text-align: center;
  }
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    appearance: none;
    margin: 0;
  }
}
</style>
