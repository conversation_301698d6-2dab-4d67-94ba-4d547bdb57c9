<template>
  <div class="app-container">
    <el-form :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dataRange"
          :clearable="false"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="平台">
        <el-select v-model="queryParams.platformId" placeholder="请选择平台" clearable size="small" @change="handleQuery">
          <el-option
            v-for="item in platformList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序">
        <el-select v-model="queryParams.orderBy" placeholder="请选择排序" size="small" @change="handleQuery">
          <el-option label="充值排序" :value="1"></el-option>
          <el-option label="消耗排序" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <div class="content-box">
      <div class="chart">
        <div class="chart-box" ref="echartUv"></div>
      </div>
      <el-table border :data="dataList">
        <el-table-column label="商务名" prop="nickName" align="center" />
        <el-table-column label="充值金额" prop="recharge" align="center" />
        <el-table-column label="消耗金额" prop="profit" align="center" />
      </el-table>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { getCrmUserRecharge } from "@/api/crmReport";
import elementResizeDetectorMaker from "element-resize-detector";
import { getPlatformList } from "@/api/xmxrChannelManage/channelList";

let userName = ["马春梅", "方敏"];
export default {
  data() {
    return {
      dataRange: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      dataList: [],
      isPhone: false,
      queryParams: {
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
        orderBy: 1,
        platformId: null
      },
      platformList: [], // 平台列表
    };
  },
  methods: {
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    //搜索
    handleQuery() {
      if (this.dataRange !== null) {
        this.queryParams.startTime = this.dataRange[0];
        this.queryParams.endTime = this.dataRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },

    // 千分位格式化
    formatNumber(num) {
      return new Intl.NumberFormat('zh-CN').format(num);
    },

    getTotal(arr, type) {
      return arr.reduce((pre, data) => {
        return pre + data[type];
      }, 0);
    },

    getPlatformList() {
      getPlatformList().then(res => {
        this.platformList = res.data || [];
      });
    },

    getList() {
      getCrmUserRecharge(this.queryParams).then((res) => {
        this.dataList = res.data.filter(
          (item) => !userName.includes(item.nickName)
        );
        this.dataList.push({
          nickName: "合计",
          recharge: this.getTotal(this.dataList, "recharge"),
          profit: this.getTotal(this.dataList, "profit"),
        });
        let data = res.data.filter((item) => !userName.includes(item.nickName));

        let rechargeDta = data.map((item) => item.recharge);
        let profitgeDta = data.map((item) => item.profit);
        let nameData = data.map((item) => item.nickName);
        // 动态计算柱宽
        const containerWidth = this.$refs.echartUv.offsetWidth;
        const dataLength = nameData.length;
        const barWidth = Math.min(Math.max(containerWidth / (dataLength * 4), 5), 30);

        let option = {
          backgroundColor: "#fff",
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            formatter: (params) => {
              let result = `${params[0].axisValue}<br/>`;
              params.forEach(param => {
                result += `${param.seriesName}: ${this.formatNumber(param.value)}元<br/>`;
              });
              return result;
            }
          },
          title: {
            show: true,
            text: "单位(元)",
          },
          toolbox: {
            feature: {
              saveAsImage: {
                name: '充值消耗统计',
                type: 'png'
              },
              dataZoom: {
                yAxisIndex: 'none'
              },
              dataView: {
                readOnly: true
              }
            }
          },
          legend: {
            show: true,
            type: 'scroll',
            bottom: 30,
            left: 'center',
            itemWidth: this.isPhone ? 12 : 20,
            itemHeight: this.isPhone ? 12 : 20,
            itemGap: this.isPhone ? 50 : 100,
            icon: "circle",
            data: ["充值", "消耗"],
            textStyle: {
              color: "#414957",
              fontStyle: "normal",
              fontFamily: "微软雅黑",
              fontSize: 14,
            },
          },
          grid: {
            left: "10%",
            containLabel: true,
            right: "10%",
            bottom: '20%'  // 为底部图例和缩放条留出空间
          },
          xAxis: {
            type: "category",
            data: nameData,
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              interval: 0,
              rotate: 45,
              textStyle: {
                color: "#777",
                fontSize: 12,
              },
              formatter: function(value) {
                if(value.length > 6) {
                  return value.substring(0,6) + '...';
                }
                return value;
              }
            },
          },
          yAxis: {
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#777",
                fontSize: 13,
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#F1F3F5",
                type: "solid",
              },
            },
          },
          dataZoom: [
            {
              type: 'slider',
              show: true,
              bottom: 0,
              height: 20,
              left: '10%',   // 与grid对齐
              right: '10%',  // 与grid对齐
              startValue: 0,
              endValue: this.isPhone ? 5 : 20
            },
            {
              type: "inside",
              zoomOnMouseWheel: true,
            },
          ],
          series: [
            {
              name: "充值",
              type: "bar",
              barGap: 0.4,
              data: rechargeDta,
              barCategoryGap: "2%",
              barWidth: barWidth,
              itemStyle: {
                normal: {
                  barBorderRadius: [10, 10, 0, 0],
                  color: "#3FA7DC",
                },
              },
            },
            {
              name: "消耗",
              type: "bar",
              barGap: 0.4,
              data: profitgeDta,
              barCategoryGap: "2%",
              barWidth: barWidth,
              itemStyle: {
                normal: {
                  barBorderRadius: [10, 10, 0, 0],
                  color: "#7091C4",
                },
              },
            },
          ],
        };
        var echartUv = echarts.init(this.$refs.echartUv);
        echartUv.setOption(option);
        let erd = elementResizeDetectorMaker();
        erd.listenTo(this.$refs.echartUv, (element) => {
          this.$nextTick(() => {
            echartUv.resize();
          });
        });
      });
    },
  },
  mounted() {
    this.isPhone = document.body.clientWidth < 750;
    this.getPlatformList();
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.content-box {
  .chart {
    width: 100%;

    min-height: clamp(400px, calc(100vh - 200px), calc(100vh - 200px));

    display: flex;
    flex-direction: column;
    padding: 0 30px 10px;
    box-sizing: border-box;

    .chart-box {
      margin-top: 40px;
      flex: 1;
    }
  }
}

@media only screen and (max-width: 1400px) {
  .content-box {
    display: flex;
    flex-direction: column;

    .chart {
      width: 100%;
      margin-bottom: 20px;

      .chart-box {
        width: 100%;
      }
    }
  }
}

@media only screen and (max-width: 750px) {
  .content-box {
    display: flex;

    .chart {
      padding: 0;

      .chart-title {
        font-size: 18px;
        color: #333;
        font-weight: bold;
      }
    }
  }
}
</style>
