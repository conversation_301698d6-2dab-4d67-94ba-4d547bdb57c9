<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dataRange"
          :clearable="false"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="商务">
        <el-select
          size="small"
          v-model="queryParams.userId"
          placeholder="请选择商务"
          clearable
        >
          <el-option
            v-for="(item, index) in userList"
            :key="index"
            :label="item.nickName"
            :value="item.userId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="checked">隐藏余额</el-checkbox>
      </el-form-item>
    </el-form>
    <el-table :data="dataList">
      <el-table-column label="商务" prop="nickName" align="center" />
      <el-table-column label="名下商务总额" prop="price" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ checked ? "0" : row.price }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="新增数量" prop="addNum" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button type="text" @click="hanldeShow(row)"
              >查看新增商户</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-drawer
      title="新增详情"
      :visible.sync="drawerShow"
      direction="rtl"
      size="800px"
    >
      <el-table :data="addDataList">
        <el-table-column
          property="name"
          show-overflow-tooltip
          label="新增商户"
          align="center"
        ></el-table-column>
        <el-table-column
          property="price"
          label="充值金额"
          align="center"
        ></el-table-column>
        <el-table-column
          property="createTime"
          label="新增时间"
          align="center"
        ></el-table-column>
        <el-table-column
          property="rechargeDate"
          label="首次充值时间"
          align="center"
        ></el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryAdd.page"
        :limit.sync="queryAdd.size"
        @pagination="getAddList"
      />
    </el-drawer>
  </div>
</template>

<script>
import {
  getToDayAddInfo,
  getpartyFirstUser,
  getAddInfoDetail,
} from "@/api/crmReport";

export default {
  data() {
    return {
      useName: ["方敏", "马春梅","甘志戎","李丽","邹盼"],
      dataRange: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      dataList: [],
      userList: [],
      addDataList: [],
      drawerShow: false,
      checked: false,
      row: {},
      total: 0,
      queryParams: {
        userId: "",

        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
      },
      queryAdd: {
        userId: "",
        page: 1,
        size: 10,
        startTime: "",
        endTime: "",
      },
    };
  },
  methods: {
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    //搜索
    handleQuery() {
      if (this.dataRange !== null) {
        // this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.dataRange[0];
        this.queryParams.endTime = this.dataRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },

    getList() {
      getToDayAddInfo(this.queryParams).then((res) => {
        this.dataList = res.data.filter(
          (item) => !this.useName.includes(item.nickName)
        );
      });
    },
    getAddList() {
      this.queryAdd.userId = this.row.userId;
      this.queryAdd.startTime = this.queryParams.startTime;
      this.queryAdd.endTime = this.queryParams.endTime;
      getAddInfoDetail(this.queryAdd).then((res) => {
        this.addDataList = res.rows;
        this.total = res.total;
      });
    },
    hanldeShow(row) {
      this.drawerShow = true;
      this.row = row;
      this.getAddList();
    },
  },
  mounted() {
    this.getList();
    getpartyFirstUser().then((res) => {
      this.userList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped></style>
