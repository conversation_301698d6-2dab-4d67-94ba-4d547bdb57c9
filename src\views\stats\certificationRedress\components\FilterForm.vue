<template>
  <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
    <el-form-item label="渠道" prop="channelId">
      <el-select v-model="queryParams.channelId" placeholder="请选择渠道" filterable>
        <el-option
          v-for="item in channelOptions"
          :key="item.id"
          :label="`${item.id}-${item.channelName}`"
          :value="item.id"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="时间范围" prop="dateRange">
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        value-format="yyyy-MM-dd HH:mm:ss"
        :default-time="['00:00:00', '23:59:59']"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="handleDateChange"
        :clearable="false"
      ></el-date-picker>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
      <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { getAllChannelList } from '@/api/channeManage/channelList';
import dayjs from 'dayjs';

export default {
  name: 'FilterForm',
  data() {
    const today = dayjs();
    const start = today.startOf('day').format('YYYY-MM-DD HH:mm:ss');
    const end = today.endOf('day').format('YYYY-MM-DD HH:mm:ss');
    return {
      queryParams: { 
        startTime: start, 
        endTime: end,
        channelId: undefined
      },
      dateRange: [start, end],
      channelOptions: []
    };
  },
  created() {
    this.getChannelList();
  },
  methods: {
    getChannelList() {
      getAllChannelList().then(response => {
        this.channelOptions = response.data || [];
      });
    },
    getQueryParams() {
      return { ...this.queryParams };
    },
    handleQuery() {
      this.$emit('query', this.getQueryParams());
    },
    resetQuery() {
      const today = dayjs();
      const start = today.startOf('day').format('YYYY-MM-DD HH:mm:ss');
      const end = today.endOf('day').format('YYYY-MM-DD HH:mm:ss');
      this.dateRange = [start, end];
      this.queryParams.startTime = start;
      this.queryParams.endTime = end;
      this.queryParams.channelId = undefined;
      this.$emit('reset');
    },
    handleDateChange() {
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }
      this.handleQuery();
    }
  }
};
</script> 