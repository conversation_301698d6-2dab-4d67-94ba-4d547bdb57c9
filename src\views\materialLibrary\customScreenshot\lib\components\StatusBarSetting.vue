<template>
  <div class="status-bar-setting">
    <el-divider>手机状态栏</el-divider>
    <div class="form-row">
      <el-form-item label="时间" class="form-item-half">
        <el-time-picker
          v-model="statusBarTime"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          @change="handleStatusBarTimeChange"
        >
        </el-time-picker>
      </el-form-item>
      <el-form-item label="电池电量" class="form-item-half" v-if="form.showBattery">
        <el-input-number
          v-model="form.batteryLevel"
          :min="40"
          :max="100"
          @change="handleConfigChange"
          controls-position="right"
        ></el-input-number>
        <span class="unit-label">%</span>
      </el-form-item>
    </div>
    <el-form-item label="状态栏模式">
      <el-radio-group v-model="form.mode" @change="handleConfigChange">
        <el-radio label="light">亮色</el-radio>
        <el-radio label="dark">暗色</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="显示图标">
      <div class="status-bar-icons">
        <el-select
          v-model="statusBarIcons"
          multiple
          placeholder="请选择显示的图标"
          @change="handleStatusBarIconsChange"
        >
          <el-option
            v-for="item in iconOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'StatusBarSetting',
  props: {
    statusBarConfig: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      form: this.deepClone(this.statusBarConfig),
      statusBarTime: this.statusBarConfig.time,
      statusBarIcons: this.getInitialStatusBarIcons(),
      iconOptions: [
        { value: 'signal', label: '信号' },
        { value: 'wifi', label: 'WiFi' }
      ]
    }
  },
  watch: {
    statusBarConfig: {
      handler(newVal) {
        this.form = this.deepClone(newVal);
        this.statusBarTime = newVal.time;
        this.statusBarIcons = this.getInitialStatusBarIcons();
      },
      deep: true
    }
  },
  methods: {
    getInitialStatusBarIcons() {
      const icons = [];
      if (this.statusBarConfig.showSignal) icons.push('signal');
      if (this.statusBarConfig.showWifi) icons.push('wifi');
      return icons;
    },
    handleStatusBarTimeChange(time) {
      this.form.time = time;
      this.handleConfigChange();
    },
    handleStatusBarIconsChange(selectedIcons) {
      this.form.showSignal = selectedIcons.includes('signal');
      this.form.showWifi = selectedIcons.includes('wifi');
      // 电池图标默认显示
      this.form.showBattery = true;
      this.handleConfigChange();
    },
    handleConfigChange() {
      this.$emit('update:statusBar', this.deepClone(this.form));
    },
    // 深拷贝函数
    deepClone(obj) {
      if (obj === null || typeof obj !== 'object') {
        return obj;
      }
      
      // 处理Date
      if (obj instanceof Date) {
        return new Date(obj.getTime());
      }
      
      // 处理Array
      if (Array.isArray(obj)) {
        return obj.map(item => this.deepClone(item));
      }
      
      // 处理Object
      const clonedObj = {};
      Object.keys(obj).forEach(key => {
        clonedObj[key] = this.deepClone(obj[key]);
      });
      
      return clonedObj;
    }
  }
}
</script>

<style scoped lang="scss">
.status-bar-setting {
  .form-row {
    display: flex;
    gap: 20px;
  }

  .form-item-half {
    width: calc(50% - 10px);
  }

  .status-bar-icons {
    display: flex;
    align-items: center;

    .el-select {
      width: 100%;
    }
  }

  .unit-label {
    margin-left: 5px;
    color: #606266;
  }
}
</style> 