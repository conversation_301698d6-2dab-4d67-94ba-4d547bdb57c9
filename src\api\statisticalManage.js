import request from '@/utils/request'
//获取渠道统计
export const getChannelStatisticsList = (data) => {
  return request({
    url: "/loan/statistics/channelStatisticsList",
    method: 'get',
    params: data
  })
}

//获取助贷统计
export const getProductLoan = (data) => {

  return request({
    url: "/loan/statistics/productStatisticsLists",
    method: "get",
    params: data
  })
}
//获取助贷统计
export const getProductList = (data) => {
  return request({
    url: "/loan/statistics/productStatisticsList",
    method: "get",
    params: data
  })
}
//获取经营统计
export const getManagementList = (data) => {
  return request({
    url: "/loan/statistics/newBusinessStatistics",
    method: "get",
    params: data
  })
}


//获取不准入统计
export const getForNonAdmittance = (data) => {
  return request({
    url: "/loan/statistics/statisticsOfReasonsForNonAdmittance",
    method: "get",
    params: data
  })
}
//获取不准入统计
export const getForNonAdmittanceDeatils = (data) => {
  return request({
    url: "/loan/statistics/nonAdmittanceReasonsDetails",
    method: "get",
    params: data
  })
}

//获取新渠道统计
export const getNewChannelStatisticsList = (data) => {
  return request({
    url: "/loan/statistics/channelStatsList",
    method: 'get',
    params: data
  })
}

//获取新渠道统计
export const getChannelStatByChannelIds = (data) => {
  return request({
    url: "/loan/statistics/channelStatChannelIds",
    method: 'get',
    params: data
  })
}

//获取新渠道统计
export const getChannelStatByxy = (data) => {
  return request({
    url: "/loan/statistics/infoFlow/xyt/channelStatName",
    method: 'get',
    params: data
  })
}
//获取新渠道统计
export const getChannelStatName = (data) => {
  return request({
    url: "/loan/statistics/channelStatName",
    method: 'get',
    params: data
  })
}

// 获取App渠道统计
export const getAppChannelStatisticsList = (data) => {
  return request({
    url: "/loan/statistics/appChannelStatsList",
    method: 'get',
    params: data
  })
}

//获取新助贷统计
export const getNewProductLoan = (data) => {
  return request({
    url: "/loan/statistics/productMidStatsList",
    method: "get",
    params: data
  })
}
//获取产品统计
export const getNewProductList = (data) => {
  return request({
    url: "/loan/statistics/productStatsList",
    method: "get",
    params: data
  })
}

//获取流量统计
export const getUrbanTrafficDistributionStatistics = (data) => {
  return request({
    url: '/loan/statistics/urbanTrafficDistributionStatistics',
    method: "post",
    data
  })
}
//获取助贷统计
export const getMidProductCount = () => {
  return request({
    url: '/loan/statistics/getMidProductCount',
    method: "get"
  })
}

//获取产品失败列表
export const getMatchingStatisticslist = (data) => {
  return request({
    url: '/loan/productRulefail/matchingStatisticsList',
    method: 'get',
    params: data
  })
}
//获取产品失败列表
export const getMatchingDeatil = (data) => {
  return request({
    url: '/loan/productRulefail/resonList',
    method: 'get',
    params: data
  })
}

//导出助贷统计
export const exportData = (data) => {
  return request({
    url: "/loan/statistics/export",
    method: "get",
    responseType: "arraybuffer",
    params: data
  })
}


//获取没有全国的城市
export const getCityList = () => {
  return request({
    url: "/loan/partya/getCityOtherAll",
    method: 'get'
  })
}
//查询数据业务统计
export const getBusinessDataList = (data) => {
  return request({
    url: "/stats/business/getBusinessDataList",
    method: "get",
    params: data
  })
}

//查询数据业务统计
export const getBusinessChannelList = () => {
  return request({
    url: "/stats/business/getChannelList",
    method: "get",
  })
}


//查询产品转化率
export const getProductStatsList = (data) => {
  return request({
    url: "/stats/product/conversion/getProductStatsList",
    method: "get",
    params: data
  })
}
//查询产品转化率详情
export const getProductDetailStatsList = (data, productId) => {
  return request({
    url: `/stats/product/conversion/getProductDetailStatsList/${productId}`,
    method: "get",
    params: data
  })
}
//查询产品渠道
export const getProdChannelList = () => {
  return request({
    url: "/stats/product/conversion/getChannelList",
    method: "get"
  })
}

//查询产品列表
export const getProdProductList = (data = {}) => {
  return request({
    url: "/stats/product/conversion/getProductList",
    method: "get",
    params: data
  })
}

//查询api注册成功数
export const getApiRegister = (data = {}) => {
  return request({
    url: "/api/channel/stats/register",
    method: "get",
    params: data
  })
}
//查询api注册成功数
export const getApiapply = (data = {}) => {
  return request({
    url: "/api/channel/stats/apply/product",
    method: "get",
    params: data
  })
}


//查询渠道匹配统计
export const getChannelMatchInfoStats = (data = {}) => {
  return request({
    url: "/loan/statistics/channelMatchInfoStats",
    method: "get",
    params: data
  })
}

//导出产品
export const exportChannelProduct = (data = {}) => {
  return request({
    url: "/loan/statistics/export/channel/proportion",
    method: "get",
    responseType: "arraybuffer",
    params: data
  })
}
//导出产品
export const exportChannelProductList = (data = {}) => {
  return request({
    url: "/loan/statistics/channel/proportion/list",
    method: "get",

    params: data
  })
}
//导出产品
export const exportChannelexportProductList = (data = {}) => {
  return request({
    url: "/loan/statistics/channel/proportion/list/export",
    method: "get",

    params: data
  })
}

export const getChannelbev = (data = {}) => {
  return request({
    url: "/api/channel/stats/apply/analyse",
    method: "get",
    params: data
  })
}

export const getFeedbackChannel = (data = {}) => {
  return request({
    url: "/loan/userQuality/feedback/analyse/channel",
    method: "get",
    params: data
  })
}
export const getProductChannel = (data = {}) => {
  return request({
    url: "/loan/userQuality/feedback/analyse/product",
    method: "get",
    params: data
  })
}
export const getFeedbackChannelList = (data = {}) => {
  return request({
    url: "/loan/userQuality/feedback/getChannelList",
    method: "get",

  })
}
export const getFeedbackProductList = (data = {}) => {
  return request({
    url: "/loan/userQuality/feedback/getProductList",
    method: "get",

  })
}

// 获取出量渠道统计
export const getOutputChannelStatistics = (data) => {
  return request({
    url: "/loan/output/channelStatsList",
    method: 'get',
    params: data
  })
}

// 获取出量消耗统计
export const getOutputConsumptionStatistics = (data) => {
  return request({
    url: "/loan/output/channel/consumeStatsList",
    method: 'get',
    params: data
  })
}

// 获取出量消耗统计
export const getPlatformProductList = (data) => {
  return request({
    url: `/loan/product/queryPlatformProductList?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    method: 'post',
    data
  })
}

//获取小程序打开记录统计
export const getOpenRecord = (params) => {
  return request({
    url: '/loan/xm/applet/openrecord',
    method: 'get',
    params
  })
}

//导出小程序打开数据
export const exportAppletOpenData = (params) => {
  return request({
    url: '/loan/xm/applet/export',
    method: 'get',
    responseType: 'blob',
    params
  })
}


// 获取新的平台产品列表
export const getNewPlatformProductList = (data) => {
  return request({
    url: `/loan/xm/access/product/queryPlatformProductList?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    method: 'post',
    data
  })
}
