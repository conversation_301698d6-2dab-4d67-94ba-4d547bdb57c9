<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="商户名称" prop="partyFirstName">
        <el-input v-model="queryParams.partyFirstName" size="small" clearable placeholder="请输入商户名称"></el-input>
      </el-form-item>
      <el-form-item label="商务名称" prop="username">
        <el-input v-model="queryParams.username" size="small" clearable placeholder="请输入商务名称"></el-input>
      </el-form-item>

      <el-form-item label="审核状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择审核状态" @change="handleQuery" clearable size="small">
          <el-option :value="-1" label="全部"></el-option>
          <el-option :value="0" label="待确认"></el-option>
          <el-option :value="1" label="已确认"></el-option>

        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableList">
      <el-table-column label="提交时间" prop="date" align="center" />
      <el-table-column label="商户名称" prop="partyFirstName" align="center" />
      <el-table-column label="提交人" prop="username" align="center" />
      <el-table-column label="类型" prop="type" align="center" />
      <el-table-column label="扣除金额" prop="price" align="center" />
      <el-table-column label="扣除原因" prop="reason" align="center"  show-overflow-tooltip/>
      <!-- <el-table-column align="center" label="缴纳凭证">
        <template slot-scope="{row}">
          <div v-if="row.file">
            <el-image style="width: 30px; height: 30px" :src="row.file" :preview-src-list="[row.file]">
            </el-image>
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="审核状态" align="center">
        <template slot-scope="{row}">
          <el-tag :type="colorType[row.status]" size="small" effect="plain">
            {{ typeStatus[row.status] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="phone" align="center">
        <template slot-scope="{row}">
          <div>
            <span class="c-p f_c005" v-hasPermi="['loan:partyadetail:deposit']" @click="handleAudit(row)">{{ row.isAction
              ? '审核' : '查看'
            }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-drawer title="保证金退款详情" :visible.sync="showBond" size="665px">
      <div class="drawer-conatiner">
        <div class="drawer-title">退款信息</div>
        <div class="drawer-wrap">
          <div class="flex">
            <span class="drawer-label">商户名称</span>
            <span class="drawer-value">{{ formData.partyFirstName }} <span class="f_c005 c-p"
                @click="handleToPartyA">查看商户详情>></span></span>
          </div>
          <div class="flex">
            <span class="drawer-label">金额</span>
            <span class="drawer-value">{{ formData.price }}</span>
          </div>
          <div class="flex" v-if="isShowHandle">
            <span class="drawer-label">审批意见</span>
            <span class="drawer-value"> <el-input v-model="infoText" placeholder="请输入审批意见" maxlength="20"
                style="width:300px" show-word-limit type="textarea" /></span>
          </div>

          <div class="flex" v-if="isShowHandle">
            <span class="drawer-label"></span>
            <span class="drawer-value flex  ">
              <span class="submit-btn flex justify-content-c align-items-c c-p " style="border: 1px solid #DCDCDC;"
                @click.stop="submitReject" v-hasPermi="['loan:partyadetail:execute_deposit']">驳 回</span>
              <span class="submit-btn flex justify-content-c align-items-c c-p b_primay" style="color:#fff"
                @click.stop="submitPass" v-hasPermi="['loan:partyadetail:execute_deposit']">通 过</span>
            </span>
          </div>
        </div>
        <div class="drawer-title">审核进度</div>
        <div class="drawer-process">
          <div v-for="(item, index) in processList" :key="index">
            <div class="flex align-items-c">
              <span
                :class="['iconfont f-suceess drawer-process-icon', iconList[item.status], colorList[item.status]]"></span>
              <span class="drawer-process-user">{{ item.userRemark }} </span>
              <span :class="['drawer-process-status', tagList[item.status]]" v-if="item.status != 0"> {{
                statusJson[item.status] || "" }}</span>
            </div>
            <div :class="['drawer-process-line', item.status == 0 ? 'boder-none' : '']">
              <div class="drawer-process-time">{{ item.checkTime || "-" }}</div>
              <div :class="['drawer-process-remark', item.status == 2 ? 'fail' : '']"
                v-if="item.checkRemark && item.status != 3">{{
                  item.checkRemark || "-" }}</div>
            </div>
          </div>
        </div>

      </div>
    </el-drawer>

  </div>
</template>

<script>
import { getDepositList, getDepositProcess, depositDoPass, depositDoReject } from "@/api/auditFinance/bond"
export default {
  data() {
    return {
      total: 0,
      showBond: false,
      isShowHandle: true,
      infoText: "",
      typeStatus: {
        0: "审核中",
        1: "已通过",
        2: "已拒绝",
      },
      colorType: {
        0: "warning",
        1: "success",
        2: "danger",
      },
      statusJson: {
        0: "发起",
        1: "已通过",
        2: "已驳回",
        3: "结束",
        '-1': '审核中'
      },
      iconList: {
        0: 'icon-a-chaji6',
        1: 'icon-a-paichu3',
        2: 'icon-a-paichu2',
        3: 'icon-a-paichu3',
        '-1': 'icon-a-paichu1',
      },

      colorList: {
        0: 'f-suceess',
        1: 'f-suceess',
        2: 'f-danger',
        3: 'f-suceess',
        '-1': 'f-info',
      },
      tagList: {
        0: 'success',
        1: 'success',
        2: 'danger',
        3: 'success',
        '-1': 'info',
      },

      processList: [],
      formData: {},
      tableList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: 2
      }
    }
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    getList() {
      getDepositList(this.queryParams).then(res => {
        this.tableList = res.rows
        this.total = res.total
      })
    },
    handleAudit(row) {
      this.formData = row
      if (row.status == 0 && row.isAction) {
        this.isShowHandle = true
      } else {
        this.isShowHandle = false
      }
      this.infoText = ""
      getDepositProcess({ depositId: row.depositId }).then(res => {
        this.processList = res.data
      })
      this.showBond = true
    },
    submitPass() {
      depositDoPass({
        "depositId": this.formData.depositId,
        "remark": this.infoText,
      }).then(res => {
        this.getList()
        this.showBond = false

        this.$message.success("操作成功")
      })
    },
    submitReject() {
      if (!this.infoText) return this.$message.error("审批意见不能为空")
      depositDoReject({
        "depositId": this.formData.depositId,
        "remark": this.infoText,
      }).then(res => {
        this.getList()
        this.showBond = false
        this.$message.success("操作成功")
      })
    },
    handleToPartyA() {
      let url = this.$router.resolve('/partyA/list?name=' + this.formData.partyFirstName)
      window.open(url.href, '_blank')
    },

  },
  mounted() {
    this.getList()
  }

}
</script>

<style lang="scss" scoped>
.drawer-conatiner {
  padding: 20px;
}

.drawer-title {
  font-size: 18px;
  font-weight: 400;
  color: #3D3D3D;
  position: relative;
  padding-left: 20px;

  &::after {
    content: "";
    display: block;
    position: absolute;
    width: 4px;
    height: 20px;
    background: #e37318;
    top: 4px;
    left: 0;
  }
}

.drawer-wrap {
  box-sizing: border-box;
  padding-top: 20px;

  .drawer-label {
    width: 250px;
    font-size: 16px;
    text-align: right;
    padding-right: 100px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 10px;
  }

  .drawer-value {
    font-size: 16px;
    color: #000000;
    margin-bottom: 10px;
  }
}

.drawer-process {
  padding: 20px;

  &-icon {
    margin-right: 10px;
  }

  &-user {
    font-size: 16px;
    color: #181716;
    width: 250px;
  }

  &-status {
    margin-left: 100px;
    font-size: 12px;
    padding: 2px 4px;
    background: #E5F9E9;
    border-radius: 2px;

    &.success {
      background: #E5F9E9;
      color: #3FA372;
      border: 1px solid #3FA372;
    }

    &.danger {
      color: #FF0000;
      background: #FFECEC;
      border: 1px solid #f00;
    }

    &.info {
      color: #FF8F1F;
      background: #FFE8D1;
      border: 1px solid #FF8F1F;
    }
  }

  &-line {
    margin: 3px 0px 5px 8px;
    border-left: 1px dashed #D8D8D8;
    padding-left: 20px;
    padding-bottom: 10px;

  }

  &-time {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 10px;
  }

  &-remark {
    width: 100%;
    background: #FAFAFA;
    border-radius: 4px 4px 4px 4px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    padding: 20px;

    &.fail {
      background: #FFECEC;
      color: #f00;
      border: 1px solid #f00;
    }
  }
}

.submit-btn {
  width: 90px;
  height: 32px;
  font-size: 12px;
  margin-right: 20px;
  border-radius: 5px;
}

.boder-none {
  border: none;
}
</style>
