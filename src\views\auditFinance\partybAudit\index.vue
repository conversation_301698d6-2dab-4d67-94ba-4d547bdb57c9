<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="乙方名称" prop="name">
        <el-input v-model="queryParams.partybName" size="small" placeholder="请输入乙方名称"></el-input>
      </el-form-item>
      <el-form-item label="申请类型" prop="type">
        <el-select v-model="queryParams.cashOutType" placeholder="请选择类型" clearable size="small">
          <el-option value="" label="全部"></el-option>
          <el-option value="0" label="请款"></el-option>
          <el-option value="1" label="退款"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-select v-model="queryParams.examineStatus" placeholder="请选择审核状态" clearable size="small">
          <el-option value="" label="全部"></el-option>
          <el-option value="0" label="待审核"></el-option>
          <el-option value="1" label="已驳回"></el-option>
          <el-option value="2" label="已通过"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table border v-loading="loading" :data="tableList" :row-key="(row) => row.examineId">
      <el-table-column label="提交时间" prop="submitTime" align="center" />
      <el-table-column label="乙方名称" prop="partybName" align="center" />
      <el-table-column label="申请类型" prop="cashOutType" align="center">
        <template slot-scope="{ row }">
          <div>
            <strong v-show="row.cashOutType == 1" style="color: green;">退款</strong>
            <strong v-show="row.cashOutType != 1" style="color: red;">请款</strong>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="申请金额" prop="money" align="center" />
      <el-table-column label="审核状态" prop="examineStatus" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ row.examineStatus == 1 ? "已驳回" : (row.examineStatus == 0 ? '待审核' : '已通过') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="申请人" prop="userName" align="center" />
      <el-table-column label="我方主体" prop="subjectName" align="center" />
      <el-table-column label="操作" prop="phone" align="center">
        <template slot-scope="{row}">
          <div>
            <el-button type="text" @click.stop="handleAudit(row)" v-hasPermi="['loan:partybeexamine:financecashout']"
              v-if="row.seeoredit"> 审核
            </el-button>
            <el-button type="text" @click.stop="handleAudit(row)" v-hasPermi="['loan:partybeexamine:financeinfo']"
              v-if="!row.seeoredit"> 查看
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <!-- 请款审核 -->

    <el-dialog title="乙方请款详情" :visible.sync="showApply" width="800px" append-to-body center
      :close-on-click-modal="false" @close="applyCancel">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form label-width="100px">
            <el-form-item label="乙方名称">
              <div class="flex">
                <el-input v-model="formData.partyName" disabled />
                <el-button size="mini" type="primary" @click="handlePartyB">查看详情</el-button>
              </div>
            </el-form-item>
            <el-form-item label="乙方类型">
              <el-input v-model="formData.partyTypes" disabled />
            </el-form-item>
            <el-form-item label="收款主体名称">
              <el-input v-model="formData.proceedsAccount" disabled />
            </el-form-item>
            <el-form-item label="收款开户行">
              <el-input v-model="formData.openingBank" disabled />
            </el-form-item>
            <el-form-item label="收款银行卡号">
              <el-input v-model="formData.proceedsNumber" disabled />
            </el-form-item>
            <el-form-item label="请款金额">
              <el-input v-model="formData.money" disabled />
            </el-form-item>
            <el-form-item label="请款事项">
              <el-input v-model="formData.matterStr" disabled />
            </el-form-item>
            <el-form-item label="请款原因">
              <el-input v-model="formData.description" disabled />
            </el-form-item>
            <el-form-item label="金额大写">
              <strong>{{ handleCashMony(formData.money) }}</strong>
              <!-- <el-input :v-model="handleCashMony(formData.money)" /> -->
            </el-form-item>
            <el-form-item label="我方打款主体">
              <el-input v-model="formData.subject" disabled />
            </el-form-item>
            <el-form-item label="是否开发票">
              <el-radio-group v-model="formData.invoiceType" disabled>
                <el-radio :label="1">对公无发票</el-radio>
                <el-radio :label="2">对公有发票</el-radio>
                <el-radio :label="3">对私有发票</el-radio>
                <el-radio :label="4">对私无发票</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="凭证上传" v-if="formData.iscashier">
              <el-upload class="avatar-uploader" action="" :show-file-list="false" :auto-upload="false"
                :on-change="changeUpImg" drag>
                <img v-if="imageFile" style="vertical-align: middle; max-height: 100px" :src="imageFile" />
                <template v-else>
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </template>
<!--                <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
                <div class="el-upload__tip" slot="tip">通过须上传凭证</div>
              </el-upload>
            </el-form-item>
            <el-form-item label="审批意见" v-show="statusCode">
              <el-input type="textarea" :rows="2" v-model="remark" maxlength="20" show-word-limit
                placeholder="请输入审批意见,驳回必填">
              </el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <div>
            <div class="check-title">审核流程</div>
            <div class="check-info1" v-if="refundStep.length">
              <el-timeline>
                <el-timeline-item v-for="(item, index) in refundStep" :key="index" :color="refundStepColor(item)">
                  <div :class="[item.status == 2 ? 'c_red' : '']">
                    <div>

                      <div v-if="item.status != 3"> {{ item.isCreateBy ? "" : item.codeName + '-' }} {{ item.userName }}
                        <label for="">({{
                        item.isCreateBy ?
                        '发起人'
                        : '审核人'
                        }})</label>
                      </div>
                      <div v-else>流程结束</div>
                    </div>
                    <div>时间:{{ item.checkTime || "-" }}</div>
                    <div v-if="item.status != 3">状态：
                      {{ statusJson[item.status] || "-" }}</div>
                    <div v-if="item.status != 3">备注：{{ item.checkRemark || "-" }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
            <div class="check-center" v-else>暂无数据</div>
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer" v-show="statusCode">
        <el-button type="primary" @click.stop="applyBtn(0)">通 过</el-button>
        <el-button @click.stop="applyBtn(1)">驳 回</el-button>
      </div>
    </el-dialog>

    <!-- 退款审核 -->
    <el-dialog title="乙方退款详情" :visible.sync="showRefound" width="800px" append-to-body center
      :close-on-click-modal="false" @close="refundCancel">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form label-width="100px">
            <el-form-item label="乙方名称">
              <div class="flex">
                <el-input v-model="formData.partyName" disabled />
                <el-button size="mini" type="primary" @click="handlePartyB">查看详情</el-button>
              </div>
            </el-form-item>
            <el-form-item label="乙方类型">
              <el-input v-model="formData.partyTypes" disabled />
            </el-form-item>
            <el-form-item label="当前在线渠道">
              <el-input v-model="formData.partybId" disabled />
            </el-form-item>
            <el-form-item label="我方收款主体">
              <el-input v-model="formData.subject" disabled />
            </el-form-item>
            <el-form-item label="收款开户行">
              <el-input v-model="formData.openingBank" disabled />
            </el-form-item>
            <el-form-item label="收款银行卡号">
              <el-input v-model="formData.proceedsNumber" disabled />
            </el-form-item>
            <el-form-item label="退款金额">
              <el-input v-model="formData.refundMoney" disabled />
            </el-form-item>

            <el-form-item label="退款原因">
              <el-input v-model="formData.refundCause" disabled />
            </el-form-item>

            <el-form-item label="退款凭证" prop="file">
              <el-image style="width: 100px; height: 100px" :src="imageUrl" :preview-src-list="srcList">
              </el-image>
            </el-form-item>

            <el-form-item label="审批意见" v-show="statusCode">
              <el-input type="textarea" :rows="2" v-model="remark" maxlength="20" show-word-limit
                placeholder="请输入审批意见,驳回必填">
              </el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <div>
            <div class="check-title">审核流程</div>
            <div class="check-info1" v-if="refundStep.length">
              <el-timeline>
                <el-timeline-item v-for="(item, index) in refundStep" :key="index" :color="refundStepColor(item)">
                  <div :class="[item.status == 2 ? 'c_red' : '']">
                    <div>

                      <div v-if="item.status != 3"> {{ item.isCreateBy ? "" : item.codeName + '-' }} {{ item.userName }}
                        <label for="">({{
                        item.isCreateBy ?
                        '发起人'
                        : '审核人'
                        }})</label>
                      </div>
                      <div v-else>流程结束</div>
                    </div>
                    <div>时间:{{ item.checkTime || "-" }}</div>
                    <div v-if="item.status != 3">状态：
                      {{ statusJson[item.status] || "-" }}</div>
                    <div v-if="item.status != 3">备注：{{ item.checkRemark || "-" }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
            <div class="check-center" v-else>暂无数据</div>
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer" v-show="statusCode">
        <el-button type="primary" @click.stop="refundBtn(0)">通 过</el-button>
        <el-button @click.stop="refundBtn(1)">驳 回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getFinanceList, financecashout, financerefund, financerefundinfo, financeDetailfs, } from "@/api/partyB";

export default {
  data() {
    return {
      showApply: false,
      showRefound: false,
      imageUrl: "",
      imageFile: "",
      tableList: [],
      file: "",
      total: 0,
      loading: false,
      queryParams: {
        partybName: "",
        cashOutType: "",
        examineStatus: "",
        pageNum: 1,
        pageSize: 10,
      },
      formData: {},
      refundStep: [

      ],
      statusJson: {
        0: "发起",
        1: "通过",
        2: "驳回",
        3: "结束",
      },
      //原因
      remark: "",
      //默认
      milepostActive: 2,
      // 动态添加类名
      stepActive: 'stepActive',
      //上传凭证
      imageUrl: "",
      //放大图片
      srcList: [],

      //点击状态
      statusCode: ""
    }
  },
  // 0发起/1通过/2驳回/3结束/4待审核
  computed: {
    refundStepColor: () => {
      return (item) => {
        if (item.status == 4) {
          return ''
        } else if (item.status == 2) {
          return "#ff0000"
        } else {
          return "#00a607"
        }
      };
    },
  },
  mounted() {
    this.loading = false;
    this.getList();
  },
  methods: {
    //清空数据
    clearHanle() {
      this.formData = {};
      this.imageUrl = '';
      this.file = ""
      this.remark = '';
      this.imageFile = ""
    },
    //上传凭证
    changeUpImg(e) {
      const isJPG =
        e.raw.type === "image/jpeg" ||
        e.raw.type === "image/jpg" ||
        e.raw.type === "image/png";
      const isLt2M = e.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传图片只能是 JPG/JPEG/PNG 格式!");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
        return;
      }
      this.imageFile = URL.createObjectURL(e.raw);
      this.file = e.raw;
    },
    handleCashMony(viade) {
      if (viade > 0) {
        var money = viade;
        var fraction = ["角", "分"];
        var digit = [
          "零",
          "壹",
          "贰",
          "叁",
          "肆",
          "伍",
          "陆",
          "柒",
          "捌",
          "玖",
        ];
        var unit = [
          ["元", "万", "亿"],
          ["", "拾", "佰", "仟"],
        ];
        var head = money < 0 ? "欠" : "";
        money = Math.abs(money);
        var s = "";
        for (var i = 0; i < fraction.length; i++) {
          s += (
            digit[Math.floor(money * 10 * Math.pow(10, i)) % 10] + fraction[i]
          ).replace(/零./, "");
        }
        s = s || "整";
        money = Math.floor(money);
        for (var i = 0; i < unit[0].length && money > 0; i++) {
          var p = "";
          for (var j = 0; j < unit[1].length && money > 0; j++) {
            p = digit[money % 10] + unit[1][j] + p;
            money = Math.floor(money / 10);
          }
          s = p.replace(/(零.)*零$/, "").replace(/^$/, "零") + unit[0][i] + s;
        }

        return head + s.replace(/(零.)*零元/, "元")
          .replace(/(零.)+/g, "零")
          .replace(/^整$/, "零元整");
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handlePartyB() {
      let url = this.$router.resolve('/partyB/partyblist?name=' + this.formData.partyName)
      window.open(url.href, '_blank')

    },
    getList() {
      this.loading = true;
      getFinanceList(this.queryParams).then(res => {
        this.tableList = res.rows;
        this.total = res.total;
        this.loading = false;
      })
    },


    //操作审核
    handleAudit(data) {
      this.statusCode = data.seeoredit;

      if (data.cashOutType == 0) {
        let obj = {
          cashOutId: data.cashOutId
        }
        this.cashoutHanle(obj);
      } else {
        let obj = {
          refundId: data.refundId
        }
        this.refundHanle(obj);
      }
    },
    //请款审核详情
    cashoutHanle(obj) {
      financeDetailfs(obj).then(res => {
        this.showApply = true;
        this.formData = JSON.parse(JSON.stringify(res.data));
        this.refundStep = this.formData.list;

      })

    },
    //乙方请款同意/或拒绝
    applyBtn(_type) {
      let obj = {
        cashoutRemark: this.remark || '',
        cashoutType: _type || 0,
        id: this.formData.id || '',
        file: this.file || ''
      }
      let data = new FormData();
      for (let i in obj) {
        data.append(i, obj[i]);
      }
      if (_type == 1 && !this.remark) {
        this.$message.error("请输入审批意见");
        return;
      }
      if (_type == 0 && this.formData.iscashier && !this.file) {
        this.$message.error("请上传凭证");
        return;
      }
      financecashout(data).then(res => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.showApply = false;
          this.getList();
          this.clearHanle();
        }
      })
    },
    //乙方请款取消
    applyCancel() {
      this.showApply = false;
      this.clearHanle();
      this.getList();
    },
    //乙方退款取消
    refundCancel() {
      this.showRefound = false;
      this.clearHanle();
      this.getList();
    },
    //退款审核详情
    refundHanle(obj) {
      financerefundinfo(obj).then(res => {
        this.showRefound = true
        this.formData = JSON.parse(JSON.stringify(res.data));
        this.refundStep = this.formData.list;
        this.imageUrl = this.formData.fileName;
        this.srcList = [this.imageUrl];
      })
    },
    //退款确认/拒绝 操作
    refundBtn(_type) {
      let obj = {
        cashoutRemark: this.remark || '',
        cashoutType: _type || 0,
        id: this.formData.id
      }
      if (_type == 1 && !this.remark) {
        this.$message.error("请输入审批意见");
        return;
      }
      financerefund(obj).then(res => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.showRefound = false;
          this.getList();
          this.clearHanle();
        }

      })
    },

  }
}
</script>

<style lang="scss" scoped>
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.flex {
  display: flex;
}

.check-info1 {
  margin-top: 10px;
  overflow: auto;
  max-height: 560px;
}
</style>
