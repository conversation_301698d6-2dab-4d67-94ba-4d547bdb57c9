import request from '@/utils/request'

// 获取薪源对账统计数据
export function getXinyuanReconciliationStatistics(query) {
  return request({
    url: '/loan/xm/stat/xy/channel/bill/detail',
    method: 'get',
    params: query
  })
} 

// 获取未录入数据的产品列表
export function getNotEnterProductList(query) {
  return request({
    url: '/loan/xm/stat/xy/listDcNotEnterProduct',
    method: 'get',
    params: query
  })
} 