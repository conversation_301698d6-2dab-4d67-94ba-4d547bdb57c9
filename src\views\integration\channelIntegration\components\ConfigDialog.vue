<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="600px"
    append-to-body
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="configForm"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="整合渠道名称" prop="integrationChannelName">
        <el-input
          v-model="form.integrationChannelName"
          placeholder="请输入整合渠道名称"
        />
      </el-form-item>
      <el-form-item label="是否整合渠道" prop="integrationStatus">
        <el-radio-group v-model="form.integrationStatus">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="关联渠道" prop="channelIds">
        <el-select
          v-model="form.channelIds"
          multiple
          placeholder="请选择关联渠道"
          filterable
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.id"
            :label="`${item.id}-${item.channelName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关联乙方" prop="partyIds">
        <el-select
          v-model="form.partyIds"
          multiple
          placeholder="请选择关联乙方"
          filterable
        >
          <el-option
            v-for="item in partyOptions"
            :key="item.id"
            :label="`${item.id}-${item.name}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  addIntegrationConfig,
  updateIntegrationConfig,
} from "@/api/integration";

export default {
  name: "ConfigDialog",
  data() {
    return {
      // 内部状态管理
      dialogVisible: false,
      title: "",
      operationType: "add",
      channelOptions: [],
      partyOptions: [],
      // 表单数据
      form: {
        id: null,
        integrationChannelName: null,
        integrationStatus: 1,
        channelIds: [],
        partyIds: [],
      },
      // 表单验证规则
      rules: {
        integrationChannelName: [
          { required: true, message: "整合渠道名称不能为空", trigger: "blur" },
        ],
        integrationStatus: [
          { required: true, message: "请选择是否整合渠道", trigger: "change" },
        ],
        channelIds: [
          { required: true, message: "请选择关联渠道", trigger: "change" },
        ],
        partyIds: [
          { required: true, message: "请选择关联乙方", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    /** 打开新增配置弹窗 */
    openAdd(channelOptions, partyOptions) {
      this.title = "新增渠道关联关系";
      this.operationType = "add";
      this.channelOptions = channelOptions;
      this.partyOptions = partyOptions;
      
      // 重置表单数据
      this.form = {
        id: null,
        integrationChannelName: null,
        integrationStatus: 1,
        channelIds: [],
        partyIds: [],
      };
      
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.clearValidate();
        }
      });
      
      this.dialogVisible = true;
    },
    
    /** 打开编辑配置弹窗 */
    openEdit(configData, channelOptions, partyOptions) {
      this.title = "修改渠道关联关系";
      this.operationType = "edit";
      this.channelOptions = channelOptions;
      this.partyOptions = partyOptions;
      
      // 回填表单数据
      this.form = {
        id: configData.id,
        integrationChannelName: configData.integrationChannelName,
        integrationStatus: configData.integrationStatus || 1,
        channelIds: configData.channelIds || [],
        partyIds: configData.partyIds || [],
      };
      
      this.dialogVisible = true;
    },

    /** 提交表单 */
    handleSubmit() {
      this.$refs["configForm"].validate((valid) => {
        if (valid) {
          const formData = {
            ...this.form,
            channelIds: this.form.channelIds.join(","),
            partyIds: this.form.partyIds.join(","),
          };

          // 根据操作类型调用对应接口
          if (this.operationType === "edit") {
            updateIntegrationConfig(formData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.handleClose();
              this.$emit("success");
            });
          } else {
            addIntegrationConfig(formData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.handleClose();
              this.$emit("success");
            });
          }
        }
      });
    },
    
    /** 取消 */
    handleCancel() {
      this.handleClose();
    },
    
    /** 关闭弹窗 */
    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
    },
    
    /** 重置表单 */
    resetForm() {
      this.form = {
        id: null,
        integrationChannelName: null,
        integrationStatus: 1,
        channelIds: [],
        partyIds: [],
      };
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.resetFields();
        }
      });
    },
  },
};
</script> 