<template>
  <el-dialog title="失败记录" :visible.sync="visible" width="650px" :close-on-click-modal="false" @close="handleClose">
    <div class="operation-bar">
      <el-button type="primary" size="small" @click="handleSaveMD5" :disabled="!selection.length">
        保存选中MD5为txt
      </el-button>
      <el-button type="primary" size="small" @click="handleCopyMD5" :disabled="!selection.length">
        复制选中MD5
      </el-button>
    </div>
    <el-table 
      ref="failTable"
      :data="failRecords" 
      border 
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="md5Phone" label="md5手机号" align="center" />
      <el-table-column prop="date" label="申请时间" align="center" />
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getFailMd5Records } from "@/api/platformProductManagement/productCompare";
import { saveAs } from 'file-saver';
import clipboard from '@/utils/clipboard';

export default {
  name: "FailRecordDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    queryParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      failRecords: [],
      selection: []
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.getFailRecords();
      } else {
        this.$refs.failTable.clearSelection();
        this.selection = [];
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false);
    },
    getFailRecords() {
      const params = {
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime,
        productName: this.queryParams.productName,
        partyName: this.queryParams.partyName
      };
      getFailMd5Records(params).then(res => {
        this.failRecords = res.data || [];
      });
    },
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    handleSaveMD5() {
      const md5Phones = this.selection.map(item => item.md5Phone).join('\n');
      const blob = new Blob([md5Phones], { type: 'text/plain;charset=utf-8' });
      saveAs(blob, 'md5_phones.txt');
      this.$message.success('文件保存成功');
    },
    handleCopyMD5() {
      const md5Phones = this.selection.map(item => item.md5Phone).join('\n');
      if (md5Phones.length > 7000) {
        this.$message.warning('选中的MD5内容过长，请使用保存txt方式');
        return;
      }
      
      clipboard.copyText(md5Phones).then(() => {
        this.$message.success('复制成功');
      }).catch(() => {
        this.$message.error('复制失败');
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.operation-bar {
  margin-bottom: 16px;
}
</style> 