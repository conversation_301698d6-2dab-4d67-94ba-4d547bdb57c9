<template>
  <div class="half-platform-group">
    <el-card shadow="never">
      <div slot="header" class="card-header">
        <span>半流程平台</span>
        <el-button type="text" icon="el-icon-plus" @click="handleAddGroup">新增分组</el-button>
      </div>
      <div class="card-content">
        <div class="group-items-wrapper">
          <div v-if="!value.length" class="empty-content">
            <el-empty :image-size="60">
              <template slot="description">
                <el-button type="text" @click="handleAddGroup">暂无分组，点击新增分组</el-button>
              </template>
            </el-empty>
          </div>
          <draggable 
            v-else 
            v-model="groups" 
            :group="{ name: `halfPlatformGroup-${groupKey}` }"
            @end="onDragEnd"
          >
            <div 
              v-for="(group, index) in groups" 
              :key="group.renderKey"
              class="group-item"
            >
              <div class="group-title">
                <span>{{ group.groupName }}</span>
                <div class="group-actions">
                  <el-button type="text" @click="handleEditGroup(index)">编辑</el-button>
                  <el-button type="text" @click="handleDeleteGroup(index)">删除</el-button>
                </div>
              </div>
              <div class="group-content">
                <div class="platform-list">
                  <el-tag 
                    v-for="platform in group.productList" 
                    :key="`${platform.platform}-${platform.productId}`" 
                    closable 
                    type="info" 
                    class="platform-tag"
                    @close="handleRemoveProduct(index, platform)"
                  >
                    {{ platform.platform }} - {{ platform.productName }} - {{ platform.productId }}
                  </el-tag>
                </div>
              </div>
            </div>
          </draggable>
        </div>
      </div>
    </el-card>

    <!-- 半流程平台分组编辑弹窗 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible" 
      width="1200px" 
      :close-on-click-modal="false"
      @close="handleDialogClose" 
      destroy-on-close
    >
      <div class="dialog-content">
        <el-form 
          :model="groupForm" 
          :rules="rules" 
          ref="groupForm" 
          :validate-on-rule-change="false"
          label-position="top"
        >
          <el-form-item label="分组名称" prop="groupName">
            <el-input v-model="groupForm.groupName" placeholder="请输入分组名称"></el-input>
          </el-form-item>
          <el-form-item label="选择平台" prop="productList">
            <div class="filter-box">
              <el-checkbox v-model="onlyShowOnline">只看在线</el-checkbox>
            </div>
            <el-transfer
              v-model="groupForm.productList"
              :data="processedHalfProductList"
              filterable
              :titles="['待选平台', '已选平台']"
              :props="{
                key: 'renderKey',
                label: 'renderLabel',
                disabled: 'disabled'
              }"
            ></el-transfer>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveGroup">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
import draggable from 'vuedraggable'

export default {
  name: 'HalfPlatformGroup',
  
  components: {
    draggable
  },

  props: {
    // 分组数据
    value: {
      type: Array,
      default: () => []
    },
    // 分组的唯一标识，用于拖拽分组
    groupKey: {
      type: String,
      required: true
    },
    // 半流程产品列表
    halfProductList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      groups: this.value,
      dialogVisible: false,
      dialogTitle: '',
      currentGroupIndex: -1,
      onlyShowOnline: false,
      groupForm: {
        groupName: '',
        renderKey: '',
        sort: 0,
        productList: []
      },
      rules: {
        groupName: [
          { required: true, message: '请输入分组名称', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ],
        productList: [
          { required: true, message: '请至少选择一个平台', trigger: 'blur' }
        ]
      }
    }
  },

  computed: {
    // 处理半流程平台列表，禁用已选择的平台
    processedHalfProductList() {
      if (!this.halfProductList.length) return []

      // 获取当前平台下其他分组已选择的平台
      const selectedProducts = new Set()
      this.groups.forEach((group, index) => {
        if (index !== this.currentGroupIndex) {
          group.productList.forEach(product => {
            // 使用四个字段组合作为唯一标识
            selectedProducts.add(JSON.stringify({
              platform: product.platform,
              platformType: product.platformType,
              productName: product.productName,
              productId: product.productId
            }))
          })
        }
      })

      // 获取当前已选择的产品
      const currentSelectedKeys = new Set(this.groupForm.productList)

      // 处理列表数据
      return this.halfProductList.map(item => {
        const isSelected = currentSelectedKeys.has(item.renderKey)
        return {
          ...item,
          disabled: selectedProducts.has(JSON.stringify({
            platform: item.platform,
            platformType: item.platformType,
            productName: item.productName,
            productId: item.productId
          })),
          // 判断是否显示该项：
          // 1. 已选中的项始终显示
          // 2. 未选中的项：
          //    - 不筛选时显示全部
          //    - 筛选时只显示在线的
          show: isSelected || !this.onlyShowOnline || item.status == 1
        }
      }).filter(item => item.show)
    }
  },

  watch: {
    value: {
      handler(val) {
        this.groups = val
      },
      deep: true
    },
    groups: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  },

  methods: {
    // 处理添加分组
    handleAddGroup() {
      this.dialogTitle = '新增分组'
      this.currentGroupIndex = -1
      this.groupForm = {
        groupName: '',
        renderKey: uuidv4(),
        sort: this.groups.length > 0 ? Math.max(...this.groups.map(item => item.sort || 0)) + 1 : 1,
        productList: []
      }
      this.dialogVisible = true
    },

    // 处理编辑分组
    handleEditGroup(index) {
      this.dialogTitle = '编辑分组'
      this.currentGroupIndex = index
      const group = this.groups[index]

      let productList = []
      group.productList.forEach(item => {
        const matchedProduct = this.halfProductList.find(p => 
          p.platform == item.platform && 
          p.platformType == item.platformType && 
          p.productName == item.productName && 
          p.productId == item.productId
        )
        if (matchedProduct) {
          productList.push(matchedProduct.renderKey)
        }
      })

      this.groupForm = {
        groupName: group.groupName,
        renderKey: group.renderKey,
        sort: group.sort || 0,
        productList
      }

      this.dialogVisible = true
    },

    // 处理删除分组
    handleDeleteGroup(index) {
      this.$confirm('确认删除该分组吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.groups.splice(index, 1)
      }).catch(() => {})
    },

    // 处理移除产品
    handleRemoveProduct(groupIndex, product) {
      const group = this.groups[groupIndex]
      if (!group) return

      // 如果只剩一个平台，不允许删除
      if (group.productList.length <= 1) {
        this.$message.warning('分组至少需要保留一个平台')
        return
      }

      this.$confirm('确认移除该平台吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = group.productList.findIndex(item =>
          item.platform == product.platform && 
          item.platformType == product.platformType && 
          item.productName == product.productName && 
          item.productId == product.productId
        )
        if (index > -1) {
          group.productList.splice(index, 1)
        }
      }).catch(() => {})
    },

    // 处理保存分组
    handleSaveGroup() {
      this.$refs.groupForm.validate(valid => {
        if (valid) {
          // 检查分组名是否重复
          const isDuplicate = this.groups.some((item, index) => {
            return item.groupName == this.groupForm.groupName && 
              index !== this.currentGroupIndex &&
              item.renderKey !== this.groupForm.renderKey
          })

          if (isDuplicate) {
            this.$message.error('该平台下已存在相同名称的分组')
            return
          }

          const platformGroup = this.currentGroupIndex == -1 ? {
            renderKey: this.groupForm.renderKey,
            groupName: this.groupForm.groupName,
            productList: [],
            sort: this.groups.length > 0 ? Math.max(...this.groups.map(item => item.sort || 0)) + 1 : 1
          } : this.groups[this.currentGroupIndex]

          if (platformGroup) {
            platformGroup.groupName = this.groupForm.groupName
            platformGroup.productList = this.processedHalfProductList
              .filter(item => this.groupForm.productList.includes(item.renderKey))
              .map(item => ({
                platform: item.platform,
                platformType: item.platformType,
                productName: item.productName,
                productId: item.productId
              }))

            if (this.currentGroupIndex == -1) {
              this.groups.push(platformGroup)
            }
          }

          this.dialogVisible = false
          this.$message.success(`${this.dialogTitle}成功`)
        }
      })
    },

    // 处理弹窗关闭
    handleDialogClose() {
      this.groupForm = {
        groupName: '',
        renderKey: uuidv4(),
        sort: 0,
        productList: []
      }
      this.$refs.groupForm?.resetFields()
      this.currentGroupIndex = -1
    },

    // 处理拖拽结束
    onDragEnd() {
      // 更新排序
      this.groups.forEach((item, index) => {
        item.sort = index + 1
      })

      // 根据sort字段重新排序
      this.groups.sort((a, b) => a.sort - b.sort)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-card__header {
  padding: 0;
}

.half-platform-group {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
  
    .el-button {
      padding: 0;
    }
  }

  .card-content {
    .empty-content {
      padding: 20px 0;
      
      ::v-deep .el-button {
        font-size: 14px;
        padding: 0;
      }
    }

    .group-items-wrapper {
      display: flex;
      flex-direction: column;
      margin: -10px;

      .group-item {
        width: calc(100% - 20px);
        margin: 10px;
        border: 1px solid #EBEEF5;
        border-radius: 4px;
        cursor: move;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        &.sortable-ghost {
          opacity: 0.5;
          background: #f5f7fa;
        }

        &.sortable-drag {
          background: #fff;
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .group-title {
          padding: 8px 12px;
          background-color: #f5f7fa;
          border-bottom: 1px solid #EBEEF5;
          font-size: 13px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .group-actions {
            .el-button {
              padding: 2px 6px;

              & + .el-button {
                margin-left: 4px;
              }
            }
          }
        }

        .group-content {
          padding: 12px;

          .platform-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding: 10px 0;

            .platform-tag {
              margin-right: 0;
              max-width: 100%;

              &.el-tag {
                display: inline-flex;
                align-items: center;
                height: auto;
                padding: 6px 10px;
                line-height: 1.4;
                white-space: normal;
                word-break: break-all;
              }
            }
          }
        }
      }
    }
  }

  .dialog-content {
    padding: 20px 20px 0;

    .filter-box {
      margin-bottom: 10px;
    }

    ::v-deep .el-transfer {
      display: flex;
      justify-content: center;
      align-items: center;

      .el-transfer-panel {
        flex: 1;
      }
    }
  }
}
</style> 