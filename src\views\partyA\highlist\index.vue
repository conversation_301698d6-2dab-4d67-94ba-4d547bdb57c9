<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dateRange"
          clearable
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="商户名称" prop="partyName">
        <el-input
          v-model="queryParams.partyName"
          size="small"
          clearable
          placeholder="请输入商户名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="商务" v-if="userList.length">
        <el-select
          size="small"
          v-model="queryParams.userId"
          placeholder="请选择商务"
          clearable
        >
          <el-option
            v-for="(item, index) in userList"
            :key="index"
            :label="item.userName"
            :value="item.userId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table :data="dataList">
      <el-table-column label="商户名称" prop="name" align="center" />
      <el-table-column label="电话号码" prop="tel" align="center" />
      <el-table-column
        label="商户类型"
        prop="type"
        align="center"
        :formatter="(row) => typeJosn[row.type]"
      />
      <el-table-column label="商务" prop="userName" align="center" />
      <el-table-column label="领取时间" prop="maxTime" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <el-button
            type="text"
            v-hasPermi="['loan:partya:usermanage']"
            @click="hanldeGetPhone(row)"
            >获取号码</el-button
          >
          <el-button
            type="text"
            v-hasPermi="['loan:partya:toLegalAffairs']"
            @click="hanldeCheck(row)"
            >移交审核</el-button
          >
          <el-button
            type="text"
            v-hasPermi="['loan:partya:toHighSeas']"
            @click="handleTransfer(row)"
            >移入公海</el-button
          >

        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="查看号码"
      width="600px"
      :visible.sync="showPhone"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <div class="info-item">商户名称:{{ row.name }}</div>
      <div class="info-item">手机号:{{ partyaPhone }}</div>
    </el-dialog>
    <el-dialog
      title="移入公海"
      width="600px"
      :visible.sync="showTransfer"
      append-to-body
      center
      @close="handleTransferCancel"
      :close-on-click-modal="false"
    >
      <div class="info-item" style="color: red">
        是否确认将{{ row.name }}移入公海？
      </div>
      <div class="info-item">
        当前余额: {{ row.availableAmount || 0 }}
        <span style="margin-left: 20px"
          >保证金: {{ row.depositAmount || 0 }}</span
        >
      </div>
      <div class="info-item" style="color: red">
        移入公海后商户所有账号将全部禁用
      </div>
      <div class="info-item">
        <div style="margin-bottom: 10px">备注</div>
        <el-input v-model="remark" type="textarea" placeholder="请输入备注">
        </el-input>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleTransferConfirm"
          >确 定</el-button
        >
        <el-button @click="handleTransferCancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  getHighSeasList,
  getPartyaPhone,
  transferToHighSeas,
  checkToHighSeas,
  getHighListSeasUser,
} from "@/api/partyA";
export default {
  data() {
    return {
      typeJosn: {
        0: "门店商户",
        1: "个人商户",
        2: "线上商户",
      },
      total: 0,
      remark: "",
      row: {},
      showPhone: false,
      showTransfer: false, //移交审核
      partyaPhone: "",
      userList: [],
      dateRange: [],
      dataList: [],
      queryParams: {
        userId: "",
        pageNum: 1,
        pageSize: 10,
        startTime: "",
        endTime: "",
      },
    };
  },
  methods: {
    handleQuery() {
      if (this.value1 !== null) {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },

    getList() {
      getHighSeasList(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total;
      });
    },
    hanldeGetPhone(row) {
      getPartyaPhone({
        partyFirstId: row.partyFirstId,
      }).then((res) => {
        this.row = row;
        if (res.code == 200) {
          this.partyaPhone = res.data;
          this.showPhone = true;
        }
      });
    },
    handleTransfer(row) {
      this.row = row;
      this.showTransfer = true;
    },
    handleTransferConfirm() {
      if (!this.remark) return this.$message.error("备注不能为空");
      transferToHighSeas({
        partyFirstId: this.row.partyFirstId,
        remark: this.remark,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getList();
        this.handleTransferCancel();
      });
    },
    handleTransferCancel() {
      this.remark = "";
      this.showTransfer = false;
    },
    hanldeCheck(row) {
      const h = this.$createElement;
      const newDatas = [];
      newDatas.push(h("p", null, `是否确认去审核    ${row.name} 资质？`));
      newDatas.push(
        h("p", null, [
          h("span", null, "当前余额"),
          h(
            "span",
            { style: "color: red;padding:0px 5px" },
            `${row.availableAmount}`
          ),
          h("span", null, "保证金"),
          h(
            "span",
            { style: "color: red;padding:0px 5px" },
            `${row.depositAmount}`
          ),
        ])
      );
      // newDatas.push(h('p', { style: 'color: red' }, "拉黑后商户投放产品将不可上线"))
      this.$confirm("提示", {
        type: "warning",
        message: h("div", null, newDatas),
      })
        .then(() => {
          checkToHighSeas({ partyFirstId: row.partyFirstId }).then((res) => {
            this.$message.success("操作成功");
            this.getList();
          });
        })
        .catch((err) => {});
    },


  },
  mounted() {
    getHighListSeasUser().then((res) => {
      this.userList = res.data;
    });
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.info-item {
  font-size: 18px;
  margin-bottom: 12px;
  color: #222;
}
</style>
