---
description: 
globs: 
alwaysApply: true
---
# 项目概述

这是"小马新融"后台管理系统，基于 Vue 2 和 Element-UI 开发。

## 技术栈
- **框架**: Vue 2
- **UI组件库**: Element-UI
- **状态管理**: Vuex
- **路由**: Vue Router
- **网络请求**: Axios
- **构建工具**: Webpack

## 主要目录结构
- `src/api`: API 接口定义
- `src/assets`: 静态资源（图片、样式、字体等）
- `src/components`: 全局通用组件
- `src/layout`: 布局相关组件
- `src/router`: 路由配置
- `src/store`: Vuex 状态管理
- `src/utils`: 工具函数
- `src/views`: 页面组件

## 环境配置
项目有以下环境配置：
- 开发环境: `.env.development`
- 生产环境: `.env.production`
- 预发布环境: `.env.staging`

