import request from '@/utils/request'

// 获取开放短信账户列表
export function getAccountList(params) {
  return request({
    url: '/loan/open/sms/account/page',
    method: 'get',
    params
  })
}

// 保存开放短信账户
export function saveAccount(data) {
  return request({
    url: '/loan/open/sms/account/save',
    method: 'post',
    data
  })
}

// 测试短信发送
export function testSendSms(data) {
  return request({
    url: '/loan/open/sms/account/test/send',
    method: 'post',
    data
  })
}