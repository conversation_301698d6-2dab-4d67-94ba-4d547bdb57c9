---
description: 
globs: 
alwaysApply: true
---
# 常用组件

项目中有许多自定义组件和第三方组件包装，这些组件位于 `src/components` 目录下。

## 分页组件
```js
// 路径：@/components/Pagination/index.vue
import Pagination from "@/components/Pagination";

// 使用示例：
<pagination
  v-show="total>0"
  :total="total"
  :page.sync="queryParams.pageNum"
  :limit.sync="queryParams.pageSize"
  @pagination="getList"
/>

// script 部分
export default {
  components: {
    Pagination
  },
  data() {
    return {
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  methods: {
    // 获取列表数据
    getList() {
      // 请求接口...
    }
  }
}
```

## 文件上传组件
```js
// 路径：@/components/FileUpload/index.vue
import FileUpload from "@/components/FileUpload";

// 使用示例：
<file-upload
  :fileList.sync="fileList"
  :limit="1"
  :multiple="false"
  :disabled="false"
/>

// script 部分
export default {
  components: {
    FileUpload
  },
  data() {
    return {
      fileList: []
    }
  }
}
```

## 图片上传组件
```js
// 路径：@/components/ImageUpload/index.vue
import ImageUpload from "@/components/ImageUpload";

// 使用示例：
<image-upload v-model="form.avatar"/>

// script 部分
export default {
  components: {
    ImageUpload
  },
  data() {
    return {
      form: {
        avatar: ""
      }
    }
  }
}
```

## 富文本编辑器
```js
// 路径：@/components/Editor/index.vue
import Editor from "@/components/Editor";

// 使用示例：
<editor v-model="form.content" :min-height="192"/>

// script 部分
export default {
  components: {
    Editor
  },
  data() {
    return {
      form: {
        content: ""
      }
    }
  }
}
```

## SVG 图标组件
```js
// 路径：@/components/SvgIcon/index.vue
import SvgIcon from '@/components/SvgIcon'

// 使用示例：
<svg-icon icon-class="user" />

// script 部分
export default {
  components: {
    SvgIcon
  }
}
```

## 字典标签组件
```js
// 路径：@/components/DictTag/index.vue
import DictTag from '@/components/DictTag'

// 使用示例：
<dict-tag :options="statusOptions" :value="scope.row.status" />

// script 部分
export default {
  components: {
    DictTag
  },
  data() {
    return {
      // 状态数据字典
      statusOptions: []
    }
  },
  created() {
    this.getDicts("sys_normal_disable").then(response => {
      this.statusOptions = response.data;
    });
  }
}
```
