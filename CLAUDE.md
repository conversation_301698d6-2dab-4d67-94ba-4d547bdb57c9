# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是"小马信融"后台管理系统，基于 Vue 2 和 Element-UI 开发的企业级管理平台。

## 开发环境和构建命令

### 开发命令
```bash
# 启动开发服务器
npm run dev

# 代码检查
npm run lint

# 构建生产环境
npm run build:prod

# 构建预发布环境
npm run build:stage

# 构建所有环境
npm run build:all
```

### 环境配置
- 开发环境：`.env.development`
- 生产环境：`.env.production`
- 预发布环境：`.env.staging`

## 核心架构

### 技术栈
- **Vue 2** + **Element-UI** - 主要开发框架
- **Vuex** - 状态管理（模块：app、user、tagsView、permission、settings、yxqb）
- **Vue Router** - 路由管理
- **Axios** - HTTP 请求库（已统一配置拦截器）
- **Webpack** - 构建工具

### 目录结构
```
src/
├── api/                    # API 接口定义，按业务模块分类
├── assets/                 # 静态资源
├── components/             # 全局通用组件
├── layout/                 # 布局组件
├── router/                 # 路由配置
├── store/                  # Vuex 状态管理
├── utils/                  # 工具函数库
└── views/                  # 页面组件
```

### 核心特性
1. **动态路由** - 基于权限的路由控制
2. **API 管理** - 统一的 API 接口管理和动态切换
3. **全局组件** - 预注册的常用组件（Pagination、RightToolbar、DictTag 等）
4. **版本管理** - 自动版本检查和更新提示
5. **构建优化** - 代码分割、压缩打包

## 开发规范

### Vue 组件规范
- 组件名使用 PascalCase
- Props 定义应指定类型和默认值
- 避免在模板中使用复杂表达式
- 使用 `v-if` 代替 `v-show`

### 网络请求规范
- 使用预配置的 axios 实例，无需处理 catch 错误
- 无需判断 `res.code === 200`（拦截器已处理）
- 无需使用 try-catch 包裹请求
- 页面无需手动设置 loading

### Element-UI 使用规范
- 筛选表单使用 `el-form`，每个控件使用 `el-form-item` 并设置 `prop`
- 使用全局注册的 `Pagination` 组件进行分页
- 状态显示使用 `el-tag` 组件
- 模态框宽度设置在 400px-1200px 之间
- 关闭模态框时重置表单和校验

### 代码约定
- 使用 `==` 进行值比较，避免 `===`
- 页面结构简洁，避免不必要的 `el-card` 包裹
- 避免使用 `undefined` 和 `null`
- 日期时间处理使用 dayjs
- 日期选择器默认选中当天，格式：`yyyy-MM-dd HH:mm:ss`

## 工具函数

### 常用工具
- `@/utils/request` - HTTP 请求工具
- `@/utils/auth` - Token 管理
- `@/utils/permission` - 权限检查
- `@/utils/validate` - 数据验证
- `@/utils/ruoyi` - 业务工具函数

### 专用依赖
- **vuedraggable** - 拖拽功能
- **dayjs** - 日期时间处理
- **uuid** - 唯一标识生成
- **qrcode.vue** - 二维码生成
- **wangeditor** - 富文本编辑器
- **lodash** - 工具函数库
- **xlsx** - Excel 处理

## 构建流程

项目构建包含以下自动化步骤：
1. 执行 `version.js` 生成版本文件
2. 根据环境执行相应构建命令
3. 执行 `build/zip.js` 自动打包为带时间戳的 zip 文件
4. 清理旧的压缩包文件

## 权限系统

- 基于角色的权限控制（RBAC）
- 动态菜单生成
- 页面级和按钮级权限控制
- 使用 `hasPermi` 和 `hasRole` 进行权限判断

## 全局配置

- `settings.js` - 系统设置
- `permission.js` - 权限控制
- `src/main.js` - 全局组件注册和方法挂载
- `vue.config.js` - Webpack 配置和开发服务器设置