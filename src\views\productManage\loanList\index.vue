<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
          size="small"
        />
      </el-form-item>
      <el-form-item label="唯一标识" prop="adminKey">
        <el-input
          v-model="queryParams.adminKey"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
          size="small"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          v-hasPermi="['loan:loanMode:query']"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          v-hasPermi="['loan:loanMode:add']"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="addLoanOne"
          >新增</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="hanldeRoundStr"
          >生成随机字符</el-button
        >
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" border :data="loanList">
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="名称" prop="name" align="center" />
      <el-table-column label="唯一标识" prop="adminKey" align="center" />
      <el-table-column label="是否需要资质" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ row.isAssets ? "是" : "否" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              v-hasPermi="['loan:loanMode:edit']"
              @click="hanlEdit(row)"
              >修改</el-button
            >
            <el-button
              v-hasPermi="['loan:loanMode:delete']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              v-if="row.status == 1"
              @click="hanlDel(row)"
              >删除</el-button
            >
            <el-button
              v-hasPermi="['loan:loanMode:edit_param']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="hanldeDeail(row)"
              >查看详情</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="isAdd ? '新增对接' : '修改对接'"
      :visible.sync="loanAvisible"
      @close="cancel"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="唯一标识" prop="adminKey">
          <el-input
            v-model="formData.adminKey"
            maxlength="50"
            placeholder="请输入唯一标识"
          />
        </el-form-item>
        <el-form-item label="是否需要资质" prop="isAssets">
          <el-radio v-model="formData.isAssets" :label="false">否</el-radio>
          <el-radio v-model="formData.isAssets" :label="true">是</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="配置信息"
      width="900px"
      :visible.sync="detailAvisible"
      append-to-body
      center
      :close-on-click-modal="false"
      @click="cancelKey"
    >
      <el-form label-width="200px">
        <el-form-item
          :label="item[0]"
          v-for="(item, index) in paramData"
          :key="index"
        >
          <div class="hanlde">
            <el-input v-model="paramJson[item[0]]" placeholder="请输入" />
            <el-button
              type="info"
              size="small"
              @click="hanldeDatail(index, item[0])"
              >删除</el-button
            >
            <el-button
              @click="hanldeAddKey"
              type="primary"
              v-if="paramData.length - 1 == index"
              size="small"
              >添加key
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="hanldeAddKey"
          type="primary"
          v-if="!paramData.length"
          size="small"
          >添加key
        </el-button>
        <el-button type="primary" @click="submitJson">确 定</el-button>
        <el-button @click="cancelKey">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="添加key"
      width="900px"
      :visible.sync="keyvisible"
      append-to-body
      center
      :close-on-click-modal="false"
      @close="keyName = ''"
    >
      <el-form label-width="200px">
        <el-form-item label="key名称">
          <el-input v-model="keyName" placeholder="请输入key名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitJsonKey">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 生成随机字符串 -->
    <el-dialog
      title="生成随机字符"
      :visible.sync="strAvisible"
      width="600px"
      append-to-body
      center
      @close="cancelCopy"
      :close-on-click-modal="false"
    >
      <el-input
        v-model.number="roundStrs"
        clearable=""
        placeholder="请输入字符"
      />
      <div class="handle-str">
        <el-button type="primary" @click="handleStr">生成</el-button>
        <el-button type="primary" @click="handleCopy">复制</el-button>
      </div>
      <div class="handle-str">{{ str }}</div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getloanModeList,
  addloanModeOne,
  editloanModeOne,
  // delloanModeOne,
  updateParam,
  delLoanModeOne,
} from "@/api/productManage/load";

export default {
  name: "LoanList",
  data() {
    return {
      total: 0,
      isAdd: true,
      loading: false,
      loanAvisible: false,
      detailAvisible: false,
      keyvisible: false,
      strAvisible: false,
      keyName: "",
      paramJson: {},
      paramData: [],
      keyId: "",
      roundStrs: "",
      str: "",
      formData: {
        name: "",
        adminKey: "",
        isAssets: false,
      },
      loanList: [],
      rules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        adminKey: [
          { required: true, message: "请输入唯一标识", trigger: "blur" },
        ],
      },
      queryParams: {
        name: "",
        adminKey: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    hanlEdit(row) {
      this.isAdd = false;
      this.loanAvisible = true;
      this.formData.id = row.id;
      this.formData.name = row.name;
      this.formData.adminKey = row.adminKey;
      this.formData.isAssets = row.isAssets;
    },
    hanlDel(row) {
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delLoanModeOne({ id: row.id }).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    addLoanOne() {
      this.loanAvisible = true;
      this.isAdd = true;
      this.formData.name = "";
      this.formData.adminKey = "";
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    getList() {
      this.loading = true;
      getloanModeList(this.queryParams).then((res) => {
        this.loanList = res.rows;
        this.total = res.total;
        this.loading = false;
        if (this.loanList.length == 0 && this.queryParams.pageNum > 1) {
          this.queryParams.pageNum = this.queryParams.pageNum - 1;
          this.loading = false;
          this.getList();
          return;
        }
      });
    },
    cancel() {
      this.formData.name = "";
      this.formData.adminKey = "";
      this.formData.isAssets = false;
      this.$refs.formData.resetFields();
      this.loanAvisible = false;
    },
    //提交表单
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addloanModeOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.getList();
                this.cancel();
              }
            });
          } else {
            editloanModeOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.getList();
                this.cancel();
              }
            });
          }
        }
      });
    },
    hanldeDeail(row) {
      // if (!row.paramJson) return this.$message.error("暂无信息")
      this.detailAvisible = true;
      this.keyId = row.id;
      this.paramData = Object.entries(row.paramJson || {});
      this.paramJson = row.paramJson || {};
    },
    submitJson() {
      updateParam({ id: this.keyId, apiJson: this.paramJson }).then((res) => {
        this.cancelKey();
        this.getList();
        this.$message.success("修改成功");
      });
    },
    cancelKey() {
      this.paramData = [];
      this.paramJson = {};
      this.keyId = "";
      this.detailAvisible = false;
    },
    hanldeAddKey() {
      this.keyvisible = true;
    },
    hanldeDatail(index, key) {
      this.paramData.splice(index, 1);
      delete this.paramJson[key];
    },
    submitJsonKey() {
      if (!this.keyName) return this.$message.error("key不能为空");
      this.paramData.push([this.keyName, ""]);
      this.keyvisible = false;
    },
    //生成随机字符
    hanldeRoundStr() {
      this.strAvisible = true;
    },
    handleCopy() {
      let oInput = document.createElement("input");
      oInput.value = this.str;
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象;

      document.execCommand("Copy"); // 执行浏览器复制命令
      this.$message({
        message: "复制成功",
        type: "success",
      });
      oInput.remove();
    },
    handleStr() {
      let strarr = [
        "0",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n",
        "o",
        "p",
        "q",
        "r",
        "s",
        "t",
        "u",
        "v",
        "w",
        "x",
        "y",
        "z",
      ];
      this.str = "";

      for (var i = 0; i < this.roundStrs; i++) {
        this.str += strarr[parseInt(Math.random() * strarr.length)];
      }
    },
    cancelCopy() {
      this.str = "";
      this.roundStrs = "";
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.hanlde {
  display: flex;
}

.handle-str {
  display: flex;
  justify-content: center;

  margin-top: 10px;
}
</style>
