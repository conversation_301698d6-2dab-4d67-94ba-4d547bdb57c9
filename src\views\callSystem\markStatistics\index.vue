<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="渠道" prop="channelIds">
        <el-select v-model="queryParams.channelIds" multiple placeholder="请选择渠道" clearable filterable>
          <el-option v-for="channel in channelList" :key="channel.id" :label="`${channel.id} - ${channel.channelName}`" :value="channel.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="话务来源" prop="outboundSource">
        <el-select v-model="queryParams.outboundSource" placeholder="请选择话务来源" clearable filterable>
          <el-option v-for="item in outboundSourceOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="标记类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择标记类型" clearable>
          <el-option label="异常用户" :value="0" />
          <el-option label="优质用户" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="客户手机号" prop="consumerPhone">
        <el-input v-model="queryParams.consumerPhone" placeholder="请输入客户手机号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标记人名称" prop="outboundName">
        <el-input v-model="queryParams.outboundName" placeholder="请输入标记人名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标记人手机号" prop="outboundPhone">
        <el-input v-model="queryParams.outboundPhone" placeholder="请输入标记人手机号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标记时间">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="list" border @sort-change="sortChange">
      <el-table-column label="渠道ID" align="center" prop="channelId">
        <template slot-scope="scope">
          <span v-if="!scope.row.isSummary">{{ scope.row.channelId }}</span>
          <span v-else class="summary-label">合计</span>
        </template>
      </el-table-column>
      <el-table-column label="渠道名称" align="center" prop="channelName">
        <template slot-scope="scope">
          <span v-if="!scope.row.isSummary">{{ scope.row.channelName }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="异常用户数量"
        align="center"
        prop="riskUsers"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
      >
        <template slot-scope="scope">
          <span class="risk-users">{{ scope.row.riskUsers || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="优质用户数量"
        align="center"
        prop="qualityUsers"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
      >
        <template slot-scope="scope">
          <span class="quality-users">{{ scope.row.qualityUsers || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="总计"
        align="center"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="totalUsers"
      >
        <template slot-scope="scope">
          <span class="total-users">{{ scope.row.totalUsers || ((scope.row.riskUsers || 0) + (scope.row.qualityUsers || 0)) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button v-if="!scope.row.isSummary" type="text" size="small" @click="handleViewDetail(scope.row)">
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 详情抽屉 -->
    <el-drawer
      title="标记详情"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="60%"
      :before-close="handleDrawerClose"
    >
      <div class="drawer-content">
        <el-table :data="detailList" border>
          <el-table-column label="客户手机号" align="center" prop="phone" />
          <el-table-column label="话务系统来源" align="center" prop="outboundSource" />
          <el-table-column label="话务系统标记人姓名" align="center" prop="outboundName" />
          <el-table-column label="话务系统标记人手机号" align="center" prop="outboundPhone" />
          <el-table-column label="标记类型" align="center" prop="type">
            <template slot-scope="scope">
              <el-tag :type="scope.row.type === 1 ? 'success' : 'danger'">{{ scope.row.type === 1 ? '优质用户' : '异常用户' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="标记原因" align="center" prop="reason" show-overflow-tooltip />
        </el-table>
        
        <div class="pagination-wrapper">
          <pagination
            v-show="detailTotal > 0"
            :total="detailTotal"
            :page.sync="detailQueryParams.page"
            :limit.sync="detailQueryParams.size"
            :auto-scroll="false"
            @pagination="getDetailList"
          />
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getFlagStatistics, getOutboundSources, getFlagDetails } from '@/api/callSystem'
import { getAllChannelList } from '@/api/channeManage/channelList'
import { getDefaultDateRange } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'MarkStatistics',
  components: { Pagination },
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
      // 表格数据
      list: [],
      // 渠道列表
      channelList: [],
      // 话务来源选项
      outboundSourceOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        channelIds: [],
        consumerPhone: '',
        outboundName: '',
        outboundPhone: '',
        outboundSource: '',
        type: '',
        startTime: '',
        endTime: ''
      },
      // 抽屉相关
      drawerVisible: false,
      detailList: [],
      detailTotal: 0,
      // 保存当前统计数据的查询参数
      savedQueryParams: {},
      // 当前选中行的渠道ID
      currentChannelId: null,
      detailQueryParams: {
        page: 1,
        size: 10
      }
    }
  },
  created() {
    // 设置默认日期范围（当天）
    this.dateRange = getDefaultDateRange()

    // 初始化时设置查询参数中的时间
    this.queryParams.startTime = this.dateRange[0]
    this.queryParams.endTime = this.dateRange[1]

    // 获取数据
    this.fetchChannelList()
    this.getOutboundSources()
    this.getList()
  },
  methods: {
    /** 获取话务来源列表 */
    async getOutboundSources() {
      try {
        const res = await getOutboundSources()
        this.outboundSourceOptions = res.data
      } catch (error) {
        console.error('获取话务来源失败:', error)
      }
    },

    /** 查询统计数据 */
    async getList() {
      try {
        // 确保每次查询前都更新时间范围参数
        if (this.dateRange && this.dateRange.length > 0) {
          this.queryParams.startTime = this.dateRange[0]
          this.queryParams.endTime = this.dateRange[1]
        }

        // 保存当前查询参数，用于详情查询
        this.savedQueryParams = { ...this.queryParams }

        const { data } = await getFlagStatistics(this.queryParams)
        const originalData = data || []

        // 处理数据，添加totalUsers字段
        originalData.forEach(item => {
          item.totalUsers = (item.riskUsers || 0) + (item.qualityUsers || 0)
        })

        this.list = originalData

        // 如果有数据，添加合计行
        if (this.list.length > 0) {
          this.list.unshift(this.createSummaryRow(originalData))
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.list = []
      }
    },

    /** 创建合计行 */
    createSummaryRow(data) {
      const summaryRow = {
        channelId: '',
        channelName: '',
        riskUsers: 0,
        qualityUsers: 0,
        totalUsers: 0,
        isSummary: true
      }

      data.forEach(item => {
        summaryRow.riskUsers += item.riskUsers || 0
        summaryRow.qualityUsers += item.qualityUsers || 0
        summaryRow.totalUsers += item.totalUsers || 0
      })

      return summaryRow
    },

    /** 表格排序处理 */
    sortChange({ prop, order }) {
      if (!this.list.length) {
        return
      }

      // 分离合计行和数据行
      const summaryRow = this.list[0]
      const sortRows = this.list.slice(1)

      if (!order) {
        // 如果没有排序，恢复原始顺序
        return
      }

      switch (order) {
        // 正序
        case 'ascending':
          sortRows.sort((a, b) => {
            const aVal = a[prop] || 0
            const bVal = b[prop] || 0
            return aVal - bVal
          })
          break
        // 倒序
        case 'descending':
          sortRows.sort((a, b) => {
            const aVal = a[prop] || 0
            const bVal = b[prop] || 0
            return bVal - aVal
          })
          break
      }

      // 将合计行重新放到第一行
      sortRows.unshift(summaryRow)
      this.list = sortRows
    },

    /** 获取渠道列表 */
    async fetchChannelList() {
      try {
        const { data } = await getAllChannelList()
        this.channelList = data
      } catch (error) {
        console.error('获取渠道列表失败:', error)
      }
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      // 重置为当天日期范围
      this.dateRange = getDefaultDateRange()

      // 重置表单
      this.resetForm('queryForm')

      // 重置查询参数
      this.queryParams = {
        channelIds: [],
        consumerPhone: '',
        outboundName: '',
        outboundPhone: '',
        outboundSource: '',
        type: '',
        startTime: this.dateRange[0],
        endTime: this.dateRange[1]
      }

      this.handleQuery()
    },

    /** 查看详情 */
    handleViewDetail(row) {
      this.drawerVisible = true
      
      // 重置分页参数
      this.detailQueryParams = {
        page: 1,
        size: 10
      }
      
      // 保存当前行的渠道ID，用于详情查询
      this.currentChannelId = row.channelId
      
      this.getDetailList()
    },

    /** 获取详情列表 */
    async getDetailList() {
      try {
        // 使用保存的查询参数，但channelIds使用当前行的渠道ID
        const params = {
          ...this.savedQueryParams,
          ...this.detailQueryParams,
          channelIds: [this.currentChannelId]
        }
        
        const { data } = await getFlagDetails(params)
        this.detailList = data.records
        this.detailTotal = data.total
      } catch (error) {
        console.error('获取详情数据失败:', error)
        this.detailList = []
        this.detailTotal = 0
      }
    },

    /** 关闭抽屉 */
    handleDrawerClose() {
      this.drawerVisible = false
      this.detailList = []
      this.detailTotal = 0
    }
  }
}
</script>

<style scoped>
.risk-users {
  color: #f56c6c;
  font-weight: bold;
}

.quality-users {
  color: #67c23a;
  font-weight: bold;
}

.total-users {
  color: #409eff;
  font-weight: bold;
}

.summary-label {
  color: #303133;
  font-weight: bold;
  font-size: 14px;
}

.drawer-content {
  padding: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

/* 合计行样式 */
::v-deep .el-table__body tr:first-child {
  background-color: #f5f7fa;
}

::v-deep .el-table__body tr:first-child td {
  font-weight: bold;
}
</style>
