{"name": "ruoyi", "version": "3.7.0", "description": "管理后台", "author": "后台", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "node version.js && vue-cli-service build --mode production && node build/zip.js production", "build:stage": "node version.js && vue-cli-service build --mode staging && node build/zip.js staging", "build:all": "npm run build:prod && npm run build:stage", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@riophae/vue-treeselect": "0.4.0", "axios": "0.21.0", "bignumber.js": "^9.1.2", "clipboard": "2.0.6", "codemirror": "^5.65.16", "core-js": "3.8.1", "dayjs": "^1.11.10", "echarts": "^4.9.0", "element-resize-detector": "^1.2.4", "element-ui": "2.15.6", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html-webpack-plugin": "4.5.2", "html2canvas": "^1.4.1", "js-beautify": "1.13.0", "js-cookie": "2.2.1", "jsencrypt": "3.0.0-rc.1", "lodash": "^4.17.21", "nprogress": "0.2.0", "qrcode.vue": "^1.7.0", "qs": "^6.12.1", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "uuid": "^10.0.0", "validator": "^13.15.0", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "^2.4.0", "vue-pdf": "^4.3.0", "vue-router": "3.4.9", "vue-virtual-scroller": "^1.1.2", "vuedraggable": "2.24.3", "vuex": "3.6.0", "wangeditor": "^4.7.9", "xlsx": "^0.18.2"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "archiver": "^5.3.2", "babel-eslint": "10.1.0", "chalk": "4.1.0", "code-inspector-plugin": "^0.18.2", "compression-webpack-plugin": "^6.1.1", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12", "yorkie": "^2.0.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}