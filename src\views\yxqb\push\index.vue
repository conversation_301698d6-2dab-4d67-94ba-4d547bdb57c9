<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="handleQuery"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表部分，留空 -->
    <div class="table-container">
      <el-table
        v-loading="false"
        :data="list"
        border
        style="width: 100%"
      >
      <el-table-column
          prop="name"
          label="姓名"
          align="center"
        />
        <el-table-column
          prop="phone"
          label="手机号"
          align="center"
        />
        <el-table-column
          prop="pushTime"
          label="推送时间"
          align="center"
        />
      </el-table>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { getPushList } from '@/api/yxqb/push.js'
import Pagination from '@/components/Pagination'

export default {
  name: 'YxqbPush',
  components: {
    Pagination
  },
  data() {
    return {
      queryParams: {
        dateRange: [
          dayjs().startOf('day').format('YYYY-MM-DD 00:00:00'),
          dayjs().endOf('day').format('YYYY-MM-DD 23:59:59'),
        ],
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      list: []
    };
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const token = this.$store.state.yxqb.token
      if (!token) {
        this.$message.error('请先登录')
        this.$router.replace('/yxqbLogin')
        return
      }

      if (!this.queryParams.dateRange || this.queryParams.dateRange.length !== 2) {
        this.$message.error('请选择时间范围')
        return
      }

      const params = {
        startTime: this.queryParams.dateRange[0],
        endTime: this.queryParams.dateRange[1],
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        token: token
      }
          
      const res = await getPushList(params)
      this.list = res.data.rows || []
      this.total = res.data.total
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.queryParams = {
        dateRange: [
          dayjs().startOf('day').format('YYYY-MM-DD 00:00:00'),
          dayjs().endOf('day').format('YYYY-MM-DD 23:59:59'),
        ],
        pageNum: 1,
        pageSize: 10
      }
      this.getList()
    }
  },
};
</script>

<style scoped>
.table-container {
  margin-top: 20px;
}
</style>
