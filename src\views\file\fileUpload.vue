<script>
import { uploadFileToOSS } from "@/api/file";

export default {
  name: "fileUpload",

  data() {
    return {
      fileList: [],
    };
  },

  methods: {
    handleSuccess(response, file, fileList) {
      this.fileList = [...fileList];
    },

    async httpRequest({ file }) {
      const formData = new FormData();
      formData.append("file", file);
      const { url } = await uploadFileToOSS(formData);
      return url;
    },

    handleCopy(text) {
      navigator.clipboard.writeText(text).then(
        () => {
          this.$message({
            message: "复制成功",
            type: "success",
          });
        },
        () => {
          this.$message({
            message: "复制失败",
            type: "error",
          });
        }
      );
    },

    handleClear() {
      this.fileList = [];
      this.$refs.upload.clearFiles();
    },
  },
};
</script>

<template>
  <div class="app-container">
    <el-upload
      ref="upload"
      class="upload-demo"
      drag
      action=""
      multiple
      :on-success="handleSuccess"
      :http-request="httpRequest"
      :show-file-list="false"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
    </el-upload>

    <el-button type="primary" @click="handleClear">清空上传列表</el-button>

    <el-table :data="fileList" border>
      <el-table-column
        label="文件名"
        align="center"
        prop="name"
      ></el-table-column>
      <el-table-column label="预览" align="center">
        <template v-slot="{ row }">
          <el-image
            style="width: 100px"
            :src="row.response"
            :preview-src-list="[row.response]"
          ></el-image>
        </template>
      </el-table-column>
      <el-table-column
        label="地址"
        align="center"
        prop="response"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot="{ row }">
          <el-button type="text" @click="handleCopy(row.response)"
            >复制地址</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped lang="scss"></style>
