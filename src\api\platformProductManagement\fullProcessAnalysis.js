import request from '@/utils/request'

// 全流程分析统计
export function getFullProcessAnalysis(params) {
  return request({
    url: '/loan/xm/flow/output/stat',
    method: 'post',
    data: {
      ...params,
      type: '0' // 0: 全流程分析
    }
  })
}

// 全流程分析-渠道统计
export function getFullProcessAnalysisByChannel(params) {
  return request({
    url: '/loan/xm/flow/output/stat',
    method: 'post',
    data: {
      ...params,
      type: '1' // 1: 全流程渠道分布
    }
  })
}

// 全流程分析-失败记录列表
export function getFullProcessFailList(params) {
  return request({
    url: '/loan/xm/flow/output/failRecord',
    method: 'post',
    data: params
  })
}
