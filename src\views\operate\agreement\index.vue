<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="协议名称" prop="orderNo">
        <el-input
          size="small"
          v-model="queryParams.name"
          placeholder="请输入协议名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="协议归属" prop="">
        <el-select
          clearable
          v-model="queryParams.protocolSig"
          filterable
          size="small"
        >
          <el-option
            v-for="item in sigList"
            :key="item.protocolSig"
            :value="item.protocolSig"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="协议KEY" prop="">
        <el-input
          size="small"
          v-model="queryParams.key"
          placeholder="请输入协议KEY"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="editAgreement" size="small"
          >新增协议</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="list" border>
      <!-- <el-table-column label="协议类型" align="center" prop="typeName" /> -->
      <el-table-column label="协议id" align="center" prop="protocolId" />
      <el-table-column label="协议名称" align="center" prop="name" />
      <el-table-column label="协议归属" align="center" prop="name">
        <template slot-scope="{ row }">
          <div>
            {{ row.sigNameList.join(",") }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="添加时间" prop="createTime" align="center" />
      <el-table-column label="版本号" prop="versionNo" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button type="text" @click="handleEdit(row)" icon="el-icon-edit"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getProtocolList,
  delProtocolOne,
  getListSig,
} from "@/api/operate/agreement";

export default {
  data() {
    return {
      total: 0,
      list: [],
      sigList: [],
      total: 0,
      queryParams: {
        name: "",
        key: "",
        protocolSig: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      if (this.queryParams.key) {
        this.queryParams.protocolSig = this.queryParams.key;
      }

      getProtocolList(this.queryParams).then((res) => {
        this.list = res.rows;
        this.total = res.total;

        if (this.list.length == 0 && this.queryParams.pageNum > 1) {
          this.queryParams.pageNum = this.queryParams.pageNum - 1;
          this.getList();
          return;
        }
      });
    },
    editAgreement() {
      this.$router.push("/operate/agreementAdd");
    },
    handleEdit(row) {
      this.$router.push("/operate/agreementAdd?id=" + row.protocolId);
    },
    handleDelete(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delProtocolOne(row.protocolId).then((res) => {
            if (res.code == 200) {
              this.$message.success("删除成功");
              this.getList();
            }
          });
        })
        .catch((err) => {});
    },
  },
  mounted() {
    this.getList();
    getListSig().then((res) => {
      this.sigList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped></style>
