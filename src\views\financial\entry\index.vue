<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="提交时间">
        <el-date-picker
          size="small"
          v-model="value1"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="收款主体">
        <el-select
          v-model="queryParams.payeeSubjectId"
          filterable
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="i in subjectAllList"
            :key="i.id"
            :label="i.subjectName"
            :value="i.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="入账商务">
        <el-select
          v-model="queryParams.userId"
          filterable
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="i in affairsList"
            :key="i.userId"
            :label="i.nickName"
            :value="i.userId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          v-model="queryParams.status"
          filterable
          clearable
          @change="handleQuery"
        >
          <el-option :value="0" label="待确定"></el-option>
          <el-option :value="1" label="已确定"></el-option>
          <el-option :value="2" label="已作废"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >筛选
        </el-button
        >
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" border :data="replayList">
      <el-table-column label="入账日期" prop="entryTime" align="center"/>
      <el-table-column label="入账金额" prop="price" align="center"/>
      <el-table-column label="商户名称" prop="partyFirstName" align="center"/>

      <el-table-column label="收款主体" prop="subjectName" align="center"/>
      <el-table-column label="开户行" prop="bankName" align="center"/>
      <el-table-column label="入账商务" prop="nickName" align="center"/>
      <el-table-column label="提交时间" prop="rechargeDate" align="center"/>
      <el-table-column label="凭证" align="center">
        <template slot-scope="{ row }">
          <div>
            <!-- <el-popover>
              <el-button slot="reference" type="text">查看凭证</el-button>
              <img class="replayImage" :src="row.filename" alt=""/>
            </el-popover> -->

            <el-image
              style="width: 100px; height: 100px"
              :src="row.filename"
              :preview-src-list="[row.filename]">

            </el-image>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              v-if="row.status == 0"
              icon="el-icon-check"
              type="text"
              @click="handleConfire(row)"
            >待确认
            </el-button
            >
            <el-button v-if="row.status == 1" type="text"> 已确认</el-button>
            <el-button
              v-if="row.status == 0 || row.status == 1"
              icon="el-icon-close"
              type="text"
              style="color: red"
              @click="handleReject(row)"
            >作废
            </el-button
            >

            <el-button v-if="row.status == 2" type="text" style="color: red">
              已作废
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="作废"
      :visible.sync="rejectAvisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="作废理由" prop="cancellationReason">
          <el-input
            type="textarea"
            v-model="formData.cancellationReason"
            placeholder="请输入作废理由"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getEntryList, entryUpdateOne,getSubjectAll } from '@/api/financial'
import { getAffairsList } from '@/api/partyB'

export default {
  name: 'Reply',
  data() {
    return {
      rejectAvisible: false,
      formData: {
        cancellationReason: '',
        id: '',
        status: 2
      },
      value1: [],
      affairsList: [],
      subjectAllList: [],
      rules: {
        cancellationReason: [
          { required: true, message: '请输入作废理由', trigger: 'blur' }
        ]
      },
      total: 0,
      loading: false,
      replayList: [],
      queryParams: {
        userId: '',
        stopTime: '',
        startTime: '',
        payeeSubjectId: '',
        pageNum: 1,
        pageSize: 10,
        status: 0
      }
    }
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1
      if (this.value1 != null) {
        this.queryParams.startTime = this.value1[0]
        this.queryParams.stopTime = this.value1[1]
      } else {
        this.queryParams.startTime = ''
        this.queryParams.stopTime = ''
      }
      this.getList()
    },
    //确认入账
    handleConfire(row) {
      this.$confirm('确定入账吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          entryUpdateOne({
            status: 1,
            id: row.id
          }).then((res) => {
            if (res.code == 200) {
              this.getList()
              this.$message.success('操作成功')
            }
          })
        })
        .catch(() => {
        })
    },

    handleReject(row) {
      this.formData.id = row.id
      this.rejectAvisible = true
    },
    cancel() {
      this.rejectAvisible = false
      this.formData.cancellationReason = ''
      this.formData.id = ''
      this.formData.status = 2
      this.$refs.formData.resetFields()
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          entryUpdateOne(this.formData).then((res) => {
            if (res.code == 200) {
              this.$message.success('操作成功')
              this.cancel()
              this.getList()
            }
          })
        }
      })
    },
    getList() {
      this.loading = true
      getEntryList(this.queryParams).then((res) => {
        this.replayList = res.rows
        this.total = res.total
        this.loading = false
      })
    }
  },
  mounted() {
    this.getList()
    //查询商务
    getAffairsList().then((res) => {
      this.affairsList = res.data
    })
    //查询主体
    getSubjectAll().then((res) => {
      this.subjectAllList = res.data
    })
  }
}
</script>

<style lang="scss" scoped>
.replayImage {
  border: 0;
  max-height: 100vh;
  width: 80vw;
}
</style>
