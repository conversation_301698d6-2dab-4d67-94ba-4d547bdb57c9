<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dataRange"
          :clearable="false"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="筛选渠道">
        <el-select
          ref="select"
          size="small"
          v-model="checkValue"
          filterable
          collapse-tags
          placeholder="请选择"
        >
          <el-option
            v-for="item in optionsList"
            :key="item.id"
            :label="`id:${item.id}--${item.channelName}`"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item label="埋点开关" v-hasPermi="['loan:point:pointSwitch']">
        <el-switch
          v-model="switchStatus"
          @change="handleSwitchStatus"
          inactive-text="关"
          active-text="开"
          inactive-value="0"
          active-value="1"
        ></el-switch>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleSeach"
          >查询用户操作详情</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="dataList">
      <el-table-column label="渠道" prop="channel" align="center" />
      <el-table-column
        label="留资页点击数量"
        prop="retainCapitalKeyCount"
        align="center"
      />
      <el-table-column
        label="线下产品申请数量"
        prop="applyOfflineCount"
        align="center"
      />
      <el-table-column
        label="线上申请数量"
        prop="applyOnlineCount"
        align="center"
      />
      <el-table-column
        label="留资点击到申请产品点击转化率"
        prop="retainCapitalToProductShow"
        :formatter="
          (row) => (row.retainCapitalToProductShow * 100).toFixed(2) + '%'
        "
        align="center"
      />
      <el-table-column
        label="线下产品申请协议点击关闭数量"
        prop="productShowProtocolCloseKeyCount"
        align="center"
      />
      <el-table-column
        label="线下申请产品展示到关闭协议转化率"
        :formatter="
          (row) => (row.productShowToProtocolClose * 100).toFixed(2) + '%'
        "
        prop="productShowToProtocolClose"
        align="center"
      />
      <el-table-column
        label="线下产品页面点击同意数量"
        prop="productShowProtocolOkKeyCount"
        align="center"
      />
      <el-table-column
        label="申请产品展示到同意协议转化率"
        :formatter="
          (row) => (row.productShowToProtocolOk * 100).toFixed(2) + '%'
        "
        prop="productShowToProtocolOk"
        align="center"
      />
      <el-table-column
        label="企微挽留页点击数量"
        prop="wechatRetentionOkKeyCount"
        align="center"
      />
      <el-table-column
        label="点击申请产品到企微同意转化率"
        :formatter="(row) => (row.productShowToWeChat * 100).toFixed(2) + '%'"
        prop="productShowToWeChat"
        align="center"
      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 操作详情 -->
    <el-dialog
      title="用户操作详情"
      :visible.sync="isShow"
      width="1000px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <div class="row">
        <el-input
          placeholder="请输入手机号"
          maxlength="11"
          v-model.trim="phone"
        ></el-input>
        <el-button type="primary" @click="handleSeachPhone">搜索</el-button>
      </div>
      <div
        v-for="(item, index) in infoList"
        :key="index"
        style="margin-top: 20px"
      >
        <div>{{ item.channel }}</div>
        <div v-for="(citem, i) in item.detailVos" :key="i" class="step-wrap">
          <div class="step-index">{{ citem.step }}</div>
          <div>{{ citem.descText }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPointList,
  editPointSwitch,
  getPointSwitch,
  getPointDetailInfo,
} from "@/api/crmReport";
import { getChannelList } from "@/api/productManage/product";

export default {
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now() + 3600 * 23 * 1000 ||
            time.getTime() < Date.now() - 3600 * 3 * 24 * 1000
          );
        },
      },
      dataRange: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      dataList: [],
      optionsList: [],
      infoList: [],
      total: 0,
      switchStatus: "",
      isShow: false,
      checkValue: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        channelId: "",
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
      },
      phone: "",
    };
  },
  methods: {
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    //搜索
    handleQuery() {
      if (this.dataRange !== null) {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.dataRange[0];
        this.queryParams.endTime = this.dataRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },

    getList() {
      this.queryParams.channelId = this.checkValue;
      getPointList(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total || 0;
      });
    },
    handleSwitchStatus() {
      editPointSwitch().then((res) => {
        this.$message.success("操作成功");
      });
    },
    handleSeach() {
      this.isShow = true;
      this.phone = "";
      this.infoList = [];
    },
    handleSeachPhone() {
      if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(this.phone))
        return this.$message.error("请输入正确的手机号");
      getPointDetailInfo(this.phone).then((res) => {
        this.infoList = res.data;
      });
    },
  },
  mounted() {
    this.getList();
    getChannelList().then((res) => {
      this.optionsList = res.data;
    });
    getPointSwitch().then((res) => {
      this.switchStatus = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
.row {
  display: flex;
  align-items: center;
}
.step-wrap {
  display: flex;
  margin-top: 10px;
  color: #333;
}
.step-index {
  font-size: 16px;
  color: #333;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 1px solid #333;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}
</style>
