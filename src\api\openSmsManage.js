import request from '@/utils/request'

// ... existing code ...

// 获取短信发送报表
export function getSmsReports(query) {
  return request({
    url: '/loan/open/sms/statistics/send/reports',
    method: 'get',
    params: query
  })
}

// 获取所有短信平台账号列表
export function getSmsAccountList(query) {
  return request({
    url: '/loan/open/sms/account/list/all',
    method: 'get',
    params: query
  })
}

// 获取短信报表详情列表
export function getSmsReportDetails(query) {
  return request({
    url: '/loan/open/sms/statistics/send/report/details',
    method: 'get',
    params: query
  })
}

// ... existing code ... 