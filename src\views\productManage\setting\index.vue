<template>
  <div class="app-container" style="overflow: auto">
    <div class="title">{{ productName }}投放规则</div>
    <div class="tab">
      <div :class="tabIndex == 0 ? 'active' : ''">基础规则</div>
      <div :class="tabIndex == 1 ? 'active' : ''">订单规则</div>
    </div>
    <div v-if="tabIndex == 0">
      <el-form
        :inline="true"
        :rules="baseRule"
        ref="baseForm"
        :model="baseForm"
      >
        <!-- <div style="display: flex">
          <div>
            <el-form-item label="渠道筛选方式" prop="channelStatus">
              <el-select
                clearable
                size="small"
                v-model="baseForm.channelStatus"
                placeholder="请选择渠道筛选方式"
              >
                <el-option value="0" label="全部"> </el-option>
                <el-option value="1" label="仅展示配置渠道"> </el-option>
                <el-option value="2" label="不展示配置渠道"> </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div>
            <el-form-item
              label="渠道"
              v-if="
                baseForm.channelStatus == '1' || baseForm.channelStatus == '2'
              "
            >
              <el-select
                clearable
                size="small"
                v-model="baseForm.channelIds"
                placeholder="请选择渠道筛选方式"
                filterable
                multiple
              >
                <el-option
                  :value="item.id"
                  :label="item.id +'--'+ item.channelName"
                  v-for="item in channelList"
                  :key="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div> -->
        <div style="display: flex">
          <div>
            <el-form-item label="城市筛选方式" prop="cityStatus">
              <el-select
                clearable
                size="small"
                v-model="baseForm.cityStatus"
                placeholder="请选择城市筛选方式"
              >
                <el-option value="0" label="全部"> </el-option>
                <el-option value="1" label="仅展示配置城市"> </el-option>
                <!-- <el-option value="2" label="不展示配置城市"> </el-option> -->
              </el-select>
            </el-form-item>
          </div>
          <div>
            <el-form-item
              label="城市"
              v-if="baseForm.cityStatus == '1' || baseForm.cityStatus == '2'"
            >
              <BaseCascader
                :options="cityList"
                :is_deep="true"
                :has_all_select="true"
                @getOptions="disableEare"
                :back_options="baseForm.district"
              />
            </el-form-item>
          </div>
        </div>
        <!--
        <div>
          <el-form-item label="端型筛选" prop="deviceTypeStatus">
            <el-select
              clearable
              size="small"
              v-model="baseForm.deviceTypeStatus"
              placeholder="请选择端型筛选"
            >
              <el-option value="0" label="全部"> </el-option>
              <el-option value="1" label="安卓"> </el-option>
              <el-option value="2" label="苹果"> </el-option>
            </el-select>
          </el-form-item>
        </div> -->
        <!-- <div>
          <el-form-item label="运营商类型" prop="mobileTypes">
            <el-select
              clearable
              size="small"
              v-model="baseForm.mobileTypes"
              placeholder="请选择运营商类型"
              multiple
              @change="changeMobileTypes"
            >
              <el-option value="0" label="全部"> </el-option>
              <el-option
                value="1"
                label="移动"
                :disabled="baseForm.mobileTypes == '0'"
              >
              </el-option>
              <el-option
                value="2"
                label="联通"
                :disabled="baseForm.mobileTypes == '0'"
              >
              </el-option>
              <el-option
                value="3"
                label="电信"
                :disabled="baseForm.mobileTypes == '0'"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div> -->
        <div>
          <el-form-item>
            <el-button type="primary" @click="submitBaseForm">下一步</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div v-if="tabIndex == 1">
      <el-form>
        <el-form-item>
          <el-radio v-model="formData.status" label="0">启用</el-radio>
          <el-radio v-model="formData.status" label="1">停用</el-radio>
        </el-form-item>
        <div class="tags">基本筛选</div>
        <el-form-item label="年龄范围" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小年龄"
              v-model="formData.startAge"
              type="number"
              size="small"
            ></el-input>
            <div>至</div>
            <el-input
              placeholder="请输入最大年龄"
              v-model="formData.endAge"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="需求额度范围" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小范围"
              v-model="formData.startQuota"
              type="number"
              size="small"
            ></el-input>
            <div>至</div>
            <el-input
              placeholder="请输入最大范围"
              v-model="formData.endQuota"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item
          style="height: 20px"
          v-for="item in basic_typeList"
          :key="item.dictId"
          :label="item.dictName"
        >
          <el-checkbox-group v-model="checkList" style="display: flex">
            <el-checkbox
              v-for="citem in item.dataRules"
              :label="
                item.paramName.split('_')[
                  item.paramName.split('_').length - 1
                ] +
                'Ids-' +
                citem.dictCode
              "
              :key="citem.dictCode"
              >{{ citem.dictLabel }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <hr />
        <div class="tags" style="margin-top: 10px">基本筛选</div>
        <el-form-item
          style="height: 20px"
          v-for="item in assets_typeList"
          :key="item.dictId"
          :label="item.dictName"
        >
          <el-checkbox-group v-model="checkList" style="display: flex">
            <el-checkbox
              v-for="citem in item.dataRules"
              :label="
                item.paramName.split('_')[
                  item.paramName.split('_').length - 1
                ] +
                'Ids-' +
                citem.dictCode
              "
              :key="citem.dictCode"
              >{{ citem.dictLabel }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item>
          <el-radio v-model="formData.ruleStatus" label="0"
            >选中的满足任意一条即可</el-radio
          >
          <el-radio v-model="formData.ruleStatus" label="1"
            >选中的都需要满足</el-radio
          >
        </el-form-item>
        <hr />
 <div v-if="false">
          <div class="tags" style="margin-top: 10px">新颜报告筛选</div>
        <el-form-item
          v-for="item in xy_typeList"
          :key="item.dictId"
          :label="item.dictName"
        >
          <el-checkbox-group v-model="checkList">
            <el-checkbox
              v-for="citem in item.dataRules"
              :label="
                item.paramName.split('_')[
                  item.paramName.split('_').length - 1
                ] +
                'Ids-' +
                citem.dictCode
              "
              :key="citem.dictCode"
              >{{ citem.dictLabel }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="近6个月M0+逾期笔数" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小笔数"
              v-model="formData.overdue6M0Min"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>

        <el-form-item label="近3个月贷款笔数" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小笔数"
              v-model="formData.overdue3Min"
              type="number"
              size="small"
            ></el-input>
            <div>至</div>
            <el-input
              placeholder="请输入最大笔数"
              v-model="formData.overdue3Max"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="近6个月贷款笔数" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小笔数"
              v-model="formData.overdue6Min"
              type="number"
              size="small"
            ></el-input>
            <div>至</div>
            <el-input
              placeholder="请输入最大笔数"
              v-model="formData.overdue6Max"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="贷款行为分" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小贷款行为分"
              v-model="formData.loanScoreMin"
              type="number"
              size="small"
            ></el-input>
            <div>至</div>
            <el-input
              placeholder="请输入最大贷款行为分"
              v-model="formData.loanScoreMax"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item
          label="正常还款订单数占贷款总订单数比例(%)"
          style="height: 20px"
        >
          <div class="input">
            <el-input
              placeholder="请输入正常还款订单数占贷款总订单数比例(%)"
              v-model="formData.repaymentRateMin"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="贷款已结清最小单数" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入贷款已结清最小单数"
              v-model="formData.b22170052Min"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="近1个月履约贷款最小次数" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入近1个月履约贷款最小次数"
              v-model="formData.b22170045Min"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
 </div>

        <div class="tags" style="margin-top: 10px; margin-bottom: 10px">
          匹配价格
        </div>
        <el-form-item label="价格" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入价格"
              v-model="formData.matchingPriceSort"
              type="number"
              size="small"
            ></el-input>
            元
          </div>
        </el-form-item>
      </el-form>
      <el-button type="primary" @click="submit">提交</el-button>
      <el-button type="primary" @click="previousStep">上一步</el-button>
    </div>
  </div>
</template>

<script>
import {
  getProductRule,
  addProductRule,
  getChannelList,
  getProductCity,
  baseProductRule,
} from "@/api/productManage/product";
import BaseCascader from "@/components/cascader";
export default {
  name: "Setting",
  data() {
    return {
      tabIndex: 0,
      basic_typeList: [],
      assets_typeList: [],
      checkList: [],
      cityList: [],
      channelList: [],
      productName: "",
      baseForm: {
        channelStatus: 0,
        deviceTypeStatus: 0,
        cityStatus: null,
        mobileTypes: 0,
        channelIds: [],
        district: [],
      },
      baseRule: {
        channelStatus: [
          { required: true, message: "请选择渠道筛选方式", trigger: "blur" },
        ],
        deviceTypeStatus: [
          { required: true, message: "请选择设备筛选方式", trigger: "blur" },
        ],
        cityStatus: [
          { required: true, message: "请选择城市筛选方式", trigger: "blur" },
        ],
        mobileTypes: [
          { required: true, message: "请选择运营商类型", trigger: "blur" },
        ],
      },
      formData: {
        status: "0",
        ruleStatus: "0",
        startAge: "",
        endAge: "",
        startQuota: "",
        endQuota: "",
        overdue6Min: "",
        overdue6Max: "",
        overdue3Min: "",
        overdue3Max: "",
        productId: "",
        loanScoreMin: "",
        loanScoreMax: "",
        overdue6M0Min: "",
        matchingPriceSort: "",
        repaymentRateMin: "",
        b22170052Min: "",
        b22170045Min: "",
      },

      xy_typeList: [],
    };
  },
  methods: {
    disableEare(e) {
      this.baseForm.district = e;
    },
    //手机归属全选
    changeMobileTypes() {
      if (this.baseForm.mobileTypes.includes("0")) {
        this.baseForm.mobileTypes = ["0"];
      }
    },
    submitBaseForm() {
      this.$refs.baseForm.validate((valid) => {
        if (valid) {
          this.baseForm.productId = this.$route.query.id;
          this.baseForm.district = this.baseForm.district.map((item) => {
            if (item.length > 1) {
              return item[1];
            } else {
              return item[0];
            }
          });
          this.tabIndex = 1;
          // baseProductRule(this.baseForm).then((res) => {
          //   if (res.code == 200) {
          //     this.$message.success("添加成功");
          //     this.$router.push("/productManage/productList");
          //   }
          // });
        }
      });
    },
    // 上一步
    previousStep() {
      this.tabIndex = 0;
      let arr = [];
      if (this.baseForm.district[0] == 1) {
        arr = [[1]];
      }
      this.baseForm.district.forEach((item) => {
        this.cityList.forEach((i) => {
          if (i.children) {
            i.children.forEach((citem) => {
              if (citem.value == item) {
                arr = [...arr, [i.value, item]];
              }
            });
          }
        });
      });
      this.baseForm.district = JSON.parse(JSON.stringify(arr));
    },
    submit() {
      this.$confirm("您是否要修改配置，确认后推广可能进入审核中", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let arr = {};
          this.checkList.forEach((item) => {
            item = item.split("-");
            if (arr[item[0]]) {
              arr[item[0]] = [...arr[item[0]], item[1]];
            } else {
              arr[item[0]] = [];
              arr[item[0]] = [item[1]];
            }
          });
          // baseProductRule(this.baseForm).then((res) => {
          //   if (res.code == 200) {

          //   }
          // });
          addProductRule({ ...this.formData, ...arr, ...this.baseForm }).then(
            (res) => {
              if (res.code == 200) {
                this.$message.success("添加成功");
                this.$store.dispatch("tagsView/delView", this.$route);
                this.$router.replace({ path: "/productManage/productList" });
              }
            }
          );
        })
        .catch((err) => {});
    },
    toggleTab(e) {
      this.tabIndex = e;
    },
  },
  components: {
    BaseCascader,
  },
  mounted() {
    this.formData.productId = this.$route.query.id;

    getProductCity().then((res) => {
      let data = null;
      data = res.data.map((item) => {
        if (item.citys) {
          {
            return {
              value: item.code,
              label: item.name,
              children: [
                ...item.citys.map((citem) => {
                  return {
                    value: citem.code,
                    label: citem.name,
                  };
                }),
              ],
            };
          }
        } else {
          return {
            value: item.code,
            label: item.name,
          };
        }
      });
      this.cityList = JSON.parse(JSON.stringify(data));
      getProductRule(this.$route.query.id).then((res) => {
        this.basic_typeList = res.basic_typeList;
        this.assets_typeList = res.assets_typeList;
        this.xy_typeList = res.xy_typeList;
        this.productName = res.productName;
        if (res.rule) {
          this.formData.status = res.rule.status;
          this.formData.ruleStatus = res.rule.ruleStatus;
          this.formData.startAge = res.rule.startAge;
          this.formData.endAge = res.rule.endAge;
          this.formData.startQuota = res.rule.startQuota;
          this.formData.endQuota = res.rule.endQuota;
          this.formData.overdue6Min = res.rule.overdue6Min;
          this.formData.overdue6Max = res.rule.overdue6Max;
          this.formData.overdue3Max = res.rule.overdue3Max;
          this.formData.overdue3Min = res.rule.overdue3Min;
          this.formData.loanScoreMin = res.rule.loanScoreMin;
          this.formData.loanScoreMax = res.rule.loanScoreMax;
          this.formData.overdue6M0Min = res.rule.overdue6M0Min;
          this.formData.matchingPriceSort = res.rule.matchingPriceSort;
          this.formData.repaymentRateMin = res.rule.repaymentRateMin;
          this.formData.b22170052Min = res.rule.b22170052Min;
          this.formData.b22170045Min = res.rule.b22170045Min;
        }
        if (res.ruleBasics) {
          this.baseForm = {
            ...res.ruleBasics,
          };

          if (res.ruleBasics.district[0] != 1) {
            let arr = [];
            res.ruleBasics.district.forEach((item) => {
              this.cityList.forEach((i) => {
                if (i.children) {
                  i.children.forEach((citem) => {
                    if (citem.value == item) {
                      arr = [...arr, [i.value, item]];
                    }
                  });
                }
              });
            });

            this.baseForm.district = arr;
          } else {
            this.baseForm.district = [[1]];
          }
        }

        let obj = {};
        let ids = Object.keys(res.rule);
        let newIds = ids.filter((item) => item.includes("Ids"));
        newIds.forEach((item) => {
          obj[item] = res.rule[item];
        });
        for (let key in obj) {
          if (obj[key]) {
            obj[key].split(",").forEach((i) => {
              this.checkList.push(key + "-" + i);
            });
          }
        }
      });
    });

    getChannelList().then((res) => {
      this.channelList = res.data;
    });
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  padding-left: 40px;
}
.title {
  font-size: 20px;
  font-weight: 800;
  margin-left: 20px;
  margin-bottom: 20px;
  text-align: center;
}
.tags {
  width: 200px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: #999;
  opacity: 0.8;
  margin-top: -20px;
}
::v-deep .input {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  div {
    margin: 0 10px;
  }
}
::v-deep .el-input--small {
  width: 200px;
}
.tab {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  div {
    width: 150px;
    height: 40px;
    text-align: center;
    margin-right: 40px;
    line-height: 40px;
    border: 1px solid black;
    &:hover {
      cursor: pointer;
    }
  }
  .active {
    background: #004DAB;
    color: #fff;
    border: 1px solid #004DAB;
  }
}
</style>
