<script>
import {
  batchCheckPhone,
  batchPushProduct,
  getProductCity,
} from "@/api/productManage/product";

export default {
  name: "productMatchingTest",

  data() {
    return {
      form: {
        age: "",
        cityCode: [],
        demandAmount: "",
        isOverdue: false,
        loanMonth: "",
        name: "",
        other: [],
        phone: "",
        // 想要匹配的产品id，多个用逗号隔开
        productIds: "",
        sesameId: 115,
        sex: "",
      },
      areaList: [],
      // 芝麻分选项
      sesameScoreOptions: [
        [
          {
            dictCode: 115,
            dictLabel: "700分以上",
            dictValue: "8",
            dictType: "product_rule_sesame",
          },
          {
            dictCode: 113,
            dictLabel: "650-700分",
            dictValue: "5",
            dictType: "product_rule_sesame",
          },
          {
            dictCode: 112,
            dictLabel: "600-650分",
            dictValue: "4",
            dictType: "product_rule_sesame",
          },
          {
            dictCode: 110,
            dictLabel: "600分以下",
            dictValue: "2",
            dictType: "product_rule_sesame",
          },
        ],
      ],
      // 其他选项
      otherOptions: [
        {
          key: "isProvident",
          label: "有公积金",
        },
        {
          key: "isHouse",
          label: "有房",
        },
        {
          key: "isVehicle",
          label: "有车",
        },
        {
          key: "isSocial",
          label: "有社保",
        },
        {
          key: "isInsure",
          label: "有保险单",
        },
        {
          key: "isBusiness",
          label: "有营业执照",
        },
      ],
      rules: {
        phone: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          // 仅校验是否为11位数字
          {
            pattern: /^1\d{10}$/,
            message: "请输入正确的手机号",
            trigger: "blur",
          },
        ],
        cityCode: [
          { required: true, message: "请选择城市", trigger: "change" },
        ],
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        age: [{ required: true, message: "请输入年龄", trigger: "blur" }],
        sex: [{ required: true, message: "请选择性别", trigger: "change" }],
        sesameId: [
          { required: true, message: "请选择芝麻分", trigger: "change" },
        ],
        isOverdue: [
          { required: true, message: "请选择是否有逾期", trigger: "change" },
        ],
        demandAmount: [
          { required: true, message: "请输入需求金额", trigger: "blur" },
        ],
        loanMonth: [
          { required: true, message: "请选择借款时间", trigger: "change" },
        ],
        productIds: [
          { required: true, message: "请输入产品id", trigger: "blur" },
        ],
      },
      // 撞库结果
      matchingResult: [],
      // 当前撞库结果使用的请求参数，推送时需要用到
      currentRequestParams: {},
    };
  },

  mounted() {
    this.getAreaList();
  },

  methods: {
    async getAreaList() {
      const res = await getProductCity();
      this.areaList = res.data.filter((item) => item.name !== "全国");
    },

    getRequestParams() {
      const params = { ...this.form };

      params.cityCode = params.cityCode[1];

      // 其他资质，选了就为true，没选就为false
      this.otherOptions.forEach((option) => {
        params[option.key] = params.other.includes(option.key);
      });
      delete params.other;

      // 将产品id中的中文逗号替换为英文逗号，然后转换为数组，过滤掉空字符串
      params.productIds = params.productIds
        .replace(/，/g, ",")
        .split(",")
        .filter(Boolean)
        .map((id) => Number(id));
      return params;
    },

    async batchCheckPhone() {
      const params = this.getRequestParams();
      try {
        await this.$refs.form.validate();
        const res = await batchCheckPhone(params);
        this.currentRequestParams = {
          ...params,
          productAssetsVos: res.data,
        };
        this.matchingResult = res.data;
      } catch (e) {
        console.error("表单校验失败", e);
      }
    },

    async batchPushProduct() {
      if (Object.keys(this.currentRequestParams).length === 0) {
        this.$message.error("请先撞库");
        return;
      }
      if (!this.matchingResult.length) {
        this.$message.error("没有撞库结果，无法推送");
        return;
      }
      try {
        const res = await batchPushProduct(this.currentRequestParams);
        this.matchingResult = res.data;
      } catch (e) {
        console.error("表单校验失败", e);
      }
    },
  },
};
</script>

<template>
  <div class="app-container">
    <el-form
      inline
      ref="form"
      :model="form"
      size="small"
      label-width="6em"
      :rules="rules"
    >
      <el-form-item label="手机号" prop="phone">
        <el-input
          clearable
          v-model="form.phone"
          maxlength="11"
          placeholder="请输入手机号"
        ></el-input>
      </el-form-item>
      <el-form-item label="城市" prop="cityCode">
        <el-cascader
          clearable
          v-model="form.cityCode"
          :options="areaList"
          :props="{
            expandTrigger: 'hover',
            label: 'name',
            value: 'code',
            children: 'citys',
          }"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          clearable
          v-model="form.name"
          placeholder="请输入姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="年龄" prop="age">
        <el-input
          clearable
          type="number"
          v-model.number="form.age"
          placeholder="请输入年龄"
        ></el-input>
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="form.sex" clearable>
          <el-option label="男" :value="0"></el-option>
          <el-option label="女" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="芝麻分" prop="sesameId">
        <el-select v-model="form.sesameId" clearable>
          <el-option
            v-for="item in sesameScoreOptions[0]"
            :key="item.dictCode"
            :label="item.dictLabel"
            :value="item.dictCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="有无逾期" prop="isOverdue">
        <el-select v-model="form.isOverdue" clearable>
          <el-option label="无逾期" :value="false"></el-option>
          <el-option label="有逾期" :value="true"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="其他资质" prop="other">
        <el-select v-model="form.other" multiple clearable>
          <el-option
            v-for="item in otherOptions"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="需求金额" prop="demandAmount">
        <el-input
          type="number"
          clearable
          v-model.number="form.demandAmount"
          placeholder="请输入需求金额"
        ></el-input>
      </el-form-item>
      <el-form-item label="借款时间" prop="loanMonth">
        <el-select v-model="form.loanMonth" clearable>
          <el-option label="3个月" value="0"></el-option>
          <el-option label="6个月" value="1"></el-option>
          <el-option label="12个月" value="2"></el-option>
          <el-option label="24个月" value="3"></el-option>
          <el-option label="36个月" value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="产品id" prop="productIds">
        <el-input
          clearable
          v-model="form.productIds"
          placeholder="多个id用逗号隔开"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="batchCheckPhone">撞库</el-button>
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 18px">
      <el-button size="small" type="primary" @click="batchPushProduct"
        >推送
      </el-button>
    </div>

    <el-table :data="matchingResult">
      <el-table-column prop="id" label="产品id" fixed="left"></el-table-column>
      <el-table-column prop="name" label="产品名字"></el-table-column>
      <el-table-column label="推送结果" width="200">
        <template v-slot="{ row }">
          <el-tag v-if="row.pushSuccess === true" type="success" size="small"
            >推送成功
          </el-tag>
          <el-tag
            v-else-if="row.pushSuccess === false"
            size="small"
            type="danger"
            >推送失败
          </el-tag>
          <el-tag v-else type="info" size="small">待推送</el-tag>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped lang="scss"></style>
