
import request from '@/utils/request'
// 获取租户流量平台列表
export function getPlatformList(data) {
    return request({

        url: "/saas/tenant/platform/getList",
        method: "get",
        params: data
    })
}

//获取审核租户流量平台列表
export function getPlatformCheckList(data) {
    return request({
        url: "/saas/tenant/platform/getCheckList",
        method: "get",
        params: data
    })
}
// 通过
export function editUpdateCheck(data) {
    return request({
        url: `saas/tenant/platform/updateCheck/${data}`,
        method: "post",

    })
}
// 不通过
export function editUpdateCheckFail(data) {
    return request({
        url: `/saas/tenant/platform/updateCheckFail`,
        method: "post",
        data
    })
}

//分配租户流量平台
export function platformUpdateAllot(data){
    return request({
        url:`/saas/tenant/platform/updateAllot/${data}`,
        method:"post"
    })
}