import request from '@/utils/request'

/**
 * 获取留言板列表
 * @description 根据搜索条件获取平台留言板主题列表，支持分页查询和关键词搜索
 *
 * @param {Object} data - 搜索参数对象，必填参数
 * @param {number} data.page - 第几页，必填参数
 * @param {string} [data.searchText] - 搜索内容(title+content)，可选参数
 * @param {number} data.size - 每页多少条，必填参数
 *
 * @returns {Promise<Object>} 返回Promise对象，包含分页的留言板主题数据
 * @returns {Object} returns.code - 响应状态码
 * @returns {Object} returns.data - 分页数据对象
 * @returns {number} returns.data.current - 当前页码
 * @returns {boolean} returns.data.hitCount - 是否命中计数
 * @returns {number} returns.data.pages - 总页数
 * @returns {Array<Object>} returns.data.records - 留言板主题记录数组
 * @returns {number} returns.data.records[].adminReadable - 小马这边读取状态 (0: 未读, 1: 已读)
 * @returns {string} returns.data.records[].content - 主题内容
 * @returns {string} returns.data.records[].createBy - 创建人用户名
 * @returns {string} returns.data.records[].created - 创建时间
 * @returns {number} returns.data.records[].crmReadable - crm用户读取状态 (0: 未读, 1: 已读)
 * @returns {number} returns.data.records[].id - 主题ID
 * @returns {number} returns.data.records[].platformId - 平台ID
 * @returns {string} returns.data.records[].title - 主题标题
 * @returns {number} returns.data.records[].type - 留言板留言类型 (0: 产品建议, 1: 质量反馈)
 * @returns {number} returns.data.records[].userId - 用户ID
 * @returns {boolean} returns.data.searchCount - 是否搜索计数
 * @returns {number} returns.data.size - 每页大小
 * @returns {number} returns.data.total - 总记录数
 * @returns {string} returns.exMsg - 异常消息
 * @returns {Object} returns.extend - 扩展信息
 * @returns {boolean} returns.fail - 是否失败
 * @returns {string} returns.msg - 响应消息
 * @returns {string} returns.state - 响应状态
 * @returns {boolean} returns.success - 是否成功
 */
export const searchPlatformMessageBoard = (data) => {
  return request({
    url: '/platformMessageBoard/search',
    method: 'post',
    data
  })
}

/**
 * 获取留言板留言记录，按时间倒序排序
 * @description 根据留言板ID获取该留言板下的所有留言记录，支持分页查询，结果按创建时间倒序排列
 *
 * @param {number} id - 留言板ID，必填参数
 * @param {Object} [params] - 查询参数对象
 * @param {number} [params.page] - 第几页，可选参数
 * @param {number} [params.size] - 每页多少条，可选参数
 *
 * @returns {Promise<Object>} 返回Promise对象，包含分页的留言记录数据
 * @returns {Object} returns.code - 响应状态码
 * @returns {Object} returns.data - 分页数据对象
 * @returns {number} returns.data.current - 当前页码
 * @returns {boolean} returns.data.hitCount - 是否命中计数
 * @returns {number} returns.data.pages - 总页数
 * @returns {Array<Object>} returns.data.records - 留言记录数组
 * @returns {number} returns.data.records[].boardId - 留言板ID
 * @returns {string} returns.data.records[].content - 留言内容
 * @returns {string} returns.data.records[].createBy - 留言用户名
 * @returns {string} returns.data.records[].created - 创建时间
 * @returns {number} returns.data.records[].id - 留言记录ID
 * @returns {number} returns.data.records[].platformId - 平台ID
 * @returns {number} returns.data.records[].type - 留言方类型 (0: crm, 1: admin)
 * @returns {number} returns.data.records[].userId - 用户ID
 * @returns {boolean} returns.data.searchCount - 是否搜索计数
 * @returns {number} returns.data.size - 每页大小
 * @returns {number} returns.data.total - 总记录数
 * @returns {string} returns.exMsg - 异常消息
 * @returns {Object} returns.extend - 扩展信息
 * @returns {boolean} returns.fail - 是否失败
 * @returns {string} returns.msg - 响应消息
 * @returns {string} returns.state - 响应状态
 * @returns {boolean} returns.success - 是否成功

 */
export const getPlatformMessageBoardChat = (id, params) => {
  return request({
    url: `/platformMessageBoard/chat/${id}`,
    method: 'get',
    params
  })
}

/**
 * 在留言板上面留言
 * @description 在指定的留言板主题下添加新的留言内容
 *
 * @param {number} id - 留言板主题ID，必填参数
 * @param {string} content - 留言内容，可选参数
 *
 * @returns {Promise<Object>} 返回Promise对象，包含操作结果
 * @returns {Object} returns.code - 响应状态码
 * @returns {string} returns.exMsg - 异常消息
 * @returns {Object} returns.extend - 扩展信息
 * @returns {boolean} returns.fail - 是否失败
 * @returns {string} returns.msg - 响应消息
 * @returns {string} returns.state - 响应状态
 * @returns {boolean} returns.success - 是否成功
 */
export const addPlatformMessageBoardChat = (id, content) => {
  return request({
    url: `/platformMessageBoard/chat/${id}`,
    method: 'post',
    params: { content }
  })
}

/**
 * 删除留言记录，只能删除admin的留言
 * @description 根据留言ID删除指定的留言记录，仅限删除管理员(admin)类型的留言
 *
 * @param {number} id - 留言ID，必填参数
 *
 * @returns {Promise<Object>} 返回Promise对象，包含删除操作结果
 * @returns {Object} returns.code - 响应状态码
 * @returns {string} returns.exMsg - 异常消息
 * @returns {Object} returns.extend - 扩展信息
 * @returns {boolean} returns.fail - 是否失败
 * @returns {string} returns.msg - 响应消息
 * @returns {string} returns.state - 响应状态
 * @returns {boolean} returns.success - 是否成功
 */
export const deletePlatformMessageBoardChat = (id) => {
  return request({
    url: `/platformMessageBoard/chat/${id}`,
    method: 'delete'
  })
}
