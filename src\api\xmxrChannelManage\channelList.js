import request from '@/utils/request'
//获取所有乙方用户
export const queryAllPartyB = () => {
    return request({
        url: "/loan/PartyB/queryAllPartyB",
        method: "get"
    })
}

export const getChannelList = (data) => {
    return request({
        url: "/loan/PartyB/channelList",
        method: "get",
        params: data
    })
}
//新增渠道
export const addChannleOne = (data) => {
    return request({
        url: "/loan/xm/channel/add",
        method: "post",
        data
    })
}
//新增渠道链接
export const addedUrlOne = (data) => {
    return request({
        url: "/loan/PartyB/addedUrl",
        method: "post",
        data
    })
}
//复制渠道
export const copyChannleOne = (data) => {
    return request({
        url: '/loan/PartyB/channeDetailed',
        method: "get",
        params: data
    })
}

//修改当前链接的qq和微信状态
export const editUrlStatus = (data) => {
    return request({
        url: "/loan/PartyB/channeCondition",
        method: "post",
        data
    })
}
// 一键生成
export const autoCreateUrl = (data) => {
    return request({
        url: "/loan/PartyB/dNSPod",
        method: "post",
        data
    })
}
//修改渠道状态
export const editChannleStatus = (data) => {
    return request({
        url: "/loan/PartyB/channelStatus",
        method: "post",
        data
    })
}
//修改渠道状态
export const editChannleCheckStatus = (data) => {
    return request({
        url: "loan/PartyB/channel/checkStatus",
        method: "post",
        data
    })
}
//获取渠道详情信息
export const getDetailedChannel = (data) => {
    return request({
        url: '/loan/backstageChannel/detailedChannel',
        method: "get",
        params: data
    })
}
// 修改渠道详情信息
export const editDetailedChannel = (data) => {
    return request({
        url: '/loan/xm/channel/update',
        method: "post",
        data
    })
}
export const exportChannelFail=(data)=>{
    return request({
        url:'/loan/backstageChannel/exportChannelFail',
        method:"get",
        responseType:"arraybuffer",
        params:data
    })
}

// 获取平台列表
export const getPlatformList = () => {
    return request({
        url: '/loan/xm/platformConfig/list',
        method: "post"
    })
}

// 获取平台列表
export const updateChannelSort = (data) => {
    return request({
        url: 'loan/xm/channel/updateProcess',
        method: "post",
        data
    })
}

// 获取渠道ID参考数据
export function getChannelHeadMaxId() {
  return request({
    url: '/loan/xm/channel/listHeadMaxId',
    method: 'get'
  })
}
