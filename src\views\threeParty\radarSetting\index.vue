<template>
  <div class="app-container">
    <el-form
      :model="formData"
      :rules="ruels"
      ref="formData"
      label-position="left"
      label-width="130px"
    >
      <el-form-item label="请求地址" prop="url">
        <el-input
          size="small"
          placeholder="请输入请求地址"
          v-model="formData.url"
        ></el-input>
      </el-form-item>
      <el-form-item label="商户号" prop="memberId">
        <el-input
          size="small"
          placeholder="请输入商户号"
          v-model="formData.memberId"
        ></el-input>
      </el-form-item>
      <el-form-item label="终端号" prop="terminalId">
        <el-input
          size="small"
          placeholder="请输入终端号"
          v-model="formData.terminalId"
        ></el-input>
      </el-form-item>
      <el-form-item label="证书文件路径" prop="pfxFilePath">
        <el-input
          size="small"
          placeholder="请输入证书文件路径"
          v-model="formData.pfxFilePath"
        ></el-input>
      </el-form-item>
      <el-form-item label="版本号" prop="versions">
        <el-input
          size="small"
          placeholder="请输入版本号"
          v-model="formData.versions"
        ></el-input>
      </el-form-item>
      <el-form-item label="推广类型" prop="productType">
        <el-input
          size="small"
          placeholder="请输入推广类型"
          v-model="formData.productType"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitFormData">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getxyConfig, editxyConfig } from "@/api/threeParty/radersetting";
export default {
  name: "RadarSetting",
  data() {
    return {
      ruels: {
        url: [{ required: true, message: "请输入请求地址", trigger: "blur" }],
        memberId: [
          { required: true, message: "请输入商户号", trigger: "blur" },
        ],
        terminalId: [
          { required: true, message: "请输入终端号", trigger: "blur" },
        ],
        pfxFilePath: [
          { required: true, message: "请输入证书文件路径", trigger: "blur" },
        ],
        versions: [
          { required: true, message: "请输入版本号", trigger: "blur" },
        ],
        productType: [
          { required: true, message: "请输入推广类型", trigger: "blur" },
        ],
      },
      formData: {
        url: "",
        memberId: "",
        terminalId: "",
        pfxFilePath: "",
        versions: "",
        productType: "",
      },
    };
  },
  methods: {
    initPage() {
      getxyConfig().then((res) => {
        this.formData.url = res.data.url;
        this.formData.memberId = res.data.memberId;
        this.formData.terminalId = res.data.terminalId;
        this.formData.pfxFilePath = res.data.pfxFilePath;
        this.formData.versions = res.data.versions;
        this.formData.productType = res.data.productType;
      });
    },
    submitFormData() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          editxyConfig(this.formData).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.initPage();
            }
          });
        }
      });
    },
  },
  mounted() {
    this.initPage();
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input--small .el-input__inner {
  width: 500px;
}
</style>
