<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">


      <el-form-item label="日期类型" prop="dateType">
        <el-select v-model="queryParams.dateType" placeholder="日期类型" :disabled="!!dateRange&&dateRange.length>0"
          clearable size="small">
          <el-option value="1" label="今天"></el-option>
          <el-option value="2" label="昨天"></el-option>
          <el-option value="3" label="最近7天"></el-option>
          <el-option value="4" label="最近30天"></el-option>
          <el-option value="5" label="当月"></el-option>
          <el-option value="6" label="上月"></el-option>
          <el-option value="7" label="近半年"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd" :disabled="!!queryParams.dateType"
          type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="合作方名称" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="客户端" clearable size="small">
          <el-option :value="1" label="苹果"></el-option>
          <el-option :value="2" label="安卓"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="首复贷" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="客户端" clearable size="small">
          <el-option :value="1" label="苹果"></el-option>
          <el-option :value="2" label="安卓"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="客户端" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="客户端" clearable size="small">
          <el-option :value="1" label="苹果"></el-option>
          <el-option :value="2" label="安卓"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="渠道" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="客户端" clearable size="small">
          <el-option :value="1" label="苹果"></el-option>
          <el-option :value="2" label="安卓"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="指标筛选" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="客户端" clearable size="small">
          <el-option :value="1" label="苹果"></el-option>
          <el-option :value="2" label="安卓"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>
    <el-table border :data="dataList">
          <el-table-column label="日期" prop="queryDate" align="center" />
          <el-table-column label="资方名称" prop="queryDate" align="center" />
          <el-table-column label="收入" prop="queryDate" align="center" />
          <el-table-column label="申请成功单用户价值" prop="queryDate" align="center" />


          <el-table-column align="center" label="前筛撞库">

            <el-table-column label="发起前筛用户数" prop="queryDate" align="center" />
            <el-table-column label="前筛通过用户数" prop="queryDate" align="center" />
            <el-table-column label="前筛通过率" prop="queryDate" align="center" />
            <el-table-column label="撞库通过用户数" prop="queryDate" align="center" />
            <el-table-column label="撞库通过率" prop="queryDate" align="center" />
          </el-table-column>
          <el-table-column align="center" label="产品曝光">
            <el-table-column label="曝光用户数" prop="queryDate" align="center" />
            <el-table-column label="点击用户数" prop="queryDate" align="center" />
          </el-table-column>
          <el-table-column align="center" label="用户申请">

            <el-table-column label="申请页曝光用户数" prop="queryDate" align="center" />
            <el-table-column label="申请页授权弹窗用户数" prop="queryDate" align="center" />
            <el-table-column label="申请页授权确认用户数" prop="queryDate" align="center" />
            <el-table-column label="申请成功用户数" prop="queryDate" align="center" />
          </el-table-column>
          <el-table-column align="center" label="补件">

            <el-table-column label="需补件用户数" prop="queryDate" align="center" />
            <el-table-column label="补件成功用户数" prop="queryDate" align="center" />
            <el-table-column label="补件成功率" prop="queryDate" align="center" />
          </el-table-column>
          <el-table-column align="center" label="授信">

            <el-table-column label="授信用户数" prop="queryDate" align="center" />
            <el-table-column label="授信通过率" prop="queryDate" align="center" />
            <el-table-column label="授信金额" prop="queryDate" align="center" />
            <el-table-column label="授信件均" prop="queryDate" align="center" />
          </el-table-column>
          <el-table-column align="center" label="要款">
            <el-table-column label="要款用户数" prop="queryDate" align="center" />
            <el-table-column label="要款金额" prop="queryDate" align="center" />
            <el-table-column label="要款率" prop="queryDate" align="center" />
          </el-table-column>
          <el-table-column align="center" label="放款">
            <el-table-column label="放款用户数" prop="queryDate" align="center" />
            <el-table-column label="放款金额" prop="queryDate" align="center" />
            <el-table-column label="放款件均" prop="queryDate" align="center" />
            <el-table-column label="放款率" prop="queryDate" align="center" />
            <el-table-column label="平均期限" prop="queryDate" align="center" />
            <el-table-column label="放款中用户数" prop="queryDate" align="center" />
            <el-table-column label="放款中金额" prop="queryDate" align="center" />
          </el-table-column>
        </el-table>


  </div>
</template>
<script>
export default {
  data() {
    return {
      dataList: [],
      activeName: "one",
      queryParams: {
        productName: "",
        channelIds: '',

        deviceType: '',
        productType: ""
      }
    }
  },
  methods: {
    handleQuery() { }
  }
}
</script>

<style lang="scss" scoped>

</style>
