<template>
  <el-dialog
    title="渠道分布"
    :visible.sync="visible"
    width="50%"
    :before-close="handleClose"
  >
    <el-table :data="dataList" border style="width: 100%" size="small" max-height="500">
      <el-table-column prop="channelName" label="渠道名称" align="center" />
      <el-table-column prop="total" label="总量" align="center" />
      <el-table-column prop="proportion" label="占比" align="center">
        <template slot-scope="scope">
          {{ scope.row.proportion }}%
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
export default {
  name: "ChannelProportion",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dataList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleClose() {
      this.$emit("update:visible", false);
    }
  }
};
</script> 