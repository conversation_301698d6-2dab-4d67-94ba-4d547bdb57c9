<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="商户名称" prop="partyFirstName">
        <el-input
          v-model="queryParams.partyFirstName"
          placeholder="商户名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="拉黑时间段" prop="">
        <el-date-picker
          v-model="dateRange"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="是否解除黑名单" prop="type">
        <el-select
          v-model="queryParams.type"
          size="small"
          @change="handleQuery"
        >
          <el-option :value="0" label="全部"></el-option>
          <el-option :value="1" label="拉黑"></el-option>
          <el-option :value="2" label="解除"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="handleQuery"
          size="small"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <div class="add-btn">
      <el-button
        type="primary"
        icon="el-icon-plus"
        @click="handleBackList"
        v-hasPermi="['loan:partyablack:black']"
        size="small"
        >新增黑名单</el-button
      >
    </div>
    <el-table ref="table" :data="tableList">
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="商户名称" align="center" prop="partyFirstName" />
      <el-table-column label="拉黑时间" align="center" prop="blackTime" />
      <!-- <el-table-column label="合作商务" align="center" prop="nickName" />
      <el-table-column label="关联产品" align="center" prop="productName" /> -->
      <el-table-column label="拉黑人" align="center" prop="blackBy" />
      <el-table-column label="拉黑备注" align="center" prop="remark" />
      <el-table-column label="解除人" align="center" prop="removeBy" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <span
              class="f_c005 c-p"
              v-if="row.blackStatus == 1"
              @click="handleRemove(row)"
              v-hasPermi="['loan:partyablack:remove']"
              >解除拉黑</span
            >
            <span v-if="row.blackStatus == 2" class="c-92">已解除</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :visible.sync="blackListShow"
      width="30%"
      title="添加黑名单"
      append-to-body
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form label-position="left" label-width="80px">
        <el-form-item label="商户名称" prop="partyFirstName">
          <el-input
            placeholder="请输入商户名称"
            v-model="formData.partyFirstName"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer justify-content-c">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button type="primary" size="small" @click="submitFormData"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="showBlackPartya"
      width="400px"
      title="拉黑商户"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <div>是否拉黑{{ row.partyFirstName }}?</div>
      <div v-if="row.availableAmount">
        当前余额
        <span style="color: red">{{ row.availableAmount }} </span> 保证金余额
        <span style="color: red">{{ row.depositAmount }}</span>
      </div>
      <div style="color: red">拉黑后商户投放产品将不可上线</div>
      <span>备注：</span> <el-input v-model="remark" type="textarea"></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="hanldeBlackSubmit">确 定</el-button>
        <el-button @click="hanldeBlackCanlce">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPartyablackList,
  checkListPartyName,
  partyablackByName,
  partyablackRemove,
} from "@/api/partyA";
export default {
  data() {
    return {
      dateRange: [],
      total: 0,
      tableList: [],
      blackListShow: false,
      showBlackPartya: false,
      formData: {
        partyFirstName: "",
      },
      remark: "",
      queryParams: {
        partyFirstName: "",
        startTime: "",
        stopTime: "",
        type: 0,
      },
      row: {},
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.dateRange !== null && this.dateRange.length) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.stopTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.stopTime = "";
      }
      this.getList();
    },
    getList() {
      getPartyablackList(this.queryParams).then((res) => {
        this.tableList = res.rows;
        this.total = res.total;
      });
    },
    handleBackList() {
      this.blackListShow = true;
    },
    cancel() {
      this.formData.partyFirstName = "";
      this.blackListShow = false;
    },
    submitFormData() {
      if (!this.formData.partyFirstName)
        return this.$message.error("商户名称不能为空");
      this.row = {};
      checkListPartyName(this.formData).then((res) => {
        if (res.data) {
          this.row = res.data;
        } else {
          this.row = {
            partyFirstName: this.formData.partyFirstName,
          };
        }
        this.showBlackPartya = true;
        // if (res.data) {
        //   const h = this.$createElement;
        //   const newDatas = [];
        //   let data = res.data;
        //   newDatas.push(
        //     h("p", null, `是否确认拉黑    ${data.partyFirstName} ？`)
        //   );
        //   newDatas.push(
        //     h("p", null, [
        //       h("span", null, "当前余额"),
        //       h(
        //         "span",
        //         { style: "color: red;padding:0px 5px" },
        //         `${data.availableAmount}`
        //       ),
        //       h("span", null, "保证金"),
        //       h(
        //         "span",
        //         { style: "color: red;padding:0px 5px" },
        //         `${data.depositAmount}`
        //       ),
        //     ])
        //   );
        //   newDatas.push(
        //     h("p", { style: "color: red" }, "拉黑后商户投放产品将不可上线")
        //   );
        //   this.$confirm("提示", {
        //     type: "warning",
        //     message: h("div", null, newDatas),
        //   })
        //     .then(() => {
        //       partyablackByName({
        //         partyFirstId: data.partyFirstId,
        //         partyFirstName: this.formData.partyFirstName,
        //       })
        //         .then((res) => {
        //           this.getList();
        //           this.blackListShow = false;
        //           this.formData.partyFirstName = "";
        //           this.$message.success("操作成功");
        //         })
        //         .catch((err) => {});
        //     })
        //     .catch((err) => {});
        // } else {
        //   this.$confirm(`是否拉黑${this.formData.partyFirstName}？`, {
        //     type: "warning",
        //   })
        //     .then((res) => {
        //       partyablackByName({
        //         partyFirstId: "",
        //         partyFirstName: this.formData.partyFirstName,
        //       })
        //         .then((res) => {
        //           this.getList();
        //           this.blackListShow = false;
        //           this.formData.partyFirstName = "";
        //           this.$message.success("操作成功");
        //         })
        //         .catch((err) => {});
        //     })
        //     .catch((err) => {});
        // }
      });
    },
    hanldeBlackSubmit() {
      if (!this.remark) return this.$message.error("请填写备注");
      partyablackByName({
        partyFirstId: "",
        partyFirstName: this.formData.partyFirstName,
        remark: this.remark,
      })
        .then((res) => {
          this.getList();
          this.blackListShow = false;
          this.formData.partyFirstName = "";
          this.$message.success("操作成功");
          this.getList();
          this.hanldeBlackCanlce();
        })
        .catch((err) => {});
    },
    hanldeBlackCanlce() {
      this.showBlackPartya = false;
      this.remark = "";
      this.row = {};
    },
    handleRemove(row) {
      this.$confirm(`确认解除${row.partyFirstName}吗？`, {
        type: "warning",
      })
        .then((res) => {
          partyablackRemove({ id: row.id }).then((res) => {
            this.$message.success("操作成功");
            this.getList();
          });
        })
        .catch((err) => {});
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.c-92 {
  color: #929292;
}

.add-btn {
  // margin-bottom: 10px;   0
  transform: translateY(-12px);
}
</style>
