<template>
  <div class="status-bar" :class="`status-bar-${mode}`">
    <!-- 左侧区域 -->
    <div class="status-bar-left">
      <!-- 时间 -->
      <div class="status-bar-time">{{ time }}</div>
    </div>
    <!-- 右侧区域 -->
    <div class="status-bar-right">
      <!-- 移动信号 -->
      <img v-if="showSignal" class="status-bar-signal"
        :src="signalIconUrl">
      <!-- wifi -->
      <img v-if="showWifi" class="status-bar-wifi"
        :src="wifiIconUrl">
      <!-- 电量 -->
      <div v-if="showBattery" class="status-bar-battery">
        <img class="status-bar-battery-img"
          :src="batteryIconUrl">
        <div class="status-bar-battery-text" :style="batteryLevelStyle"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IOSStatusBar',
  props: {
    mode: {
      type: String,
      default: 'light',
      validator: (value) => ['light', 'dark'].includes(value)
    },
    time: {
      type: String,
      default: '9:41'
    },
    showSignal: {
      type: Boolean,
      default: true
    },
    showWifi: {
      type: Boolean,
      default: true
    },
    showBattery: {
      type: Boolean,
      default: true
    },
    batteryLevel: {
      type: Number,
      default: 100
    }
  },
  computed: {
    isDarkMode() {
      return this.mode === 'dark';
    },
    textColor() {
      return this.isDarkMode ? '#333333' : '#fff';
    },
    signalIconUrl() {
      return this.isDarkMode
        ? 'https://jst.oss-utos.hmctec.cn/common/path/852e418b67204ddeb5a066a208d7d5ba.png'
        : 'https://jst.oss-utos.hmctec.cn/common/path/001387862bb2401887542dc8ee7ced85.png';
    },
    wifiIconUrl() {
      return this.isDarkMode
        ? 'https://jst.oss-utos.hmctec.cn/common/path/b614fb4ccdb54ffd877268a6236e93c4.png'
        : 'https://jst.oss-utos.hmctec.cn/common/path/d1f5c01865804bf79eea9d974fd9b06c.png';
    },
    batteryIconUrl() {
      return this.isDarkMode
        ? 'https://jst.oss-utos.hmctec.cn/common/path/57d50d1b52ba422386863af3746aecc3.png'
        : 'https://jst.oss-utos.hmctec.cn/common/path/5342fe106cf84054ad7ba9ad09acb698.png';
    },
    batteryLevelStyle() {
      const maxWidth = 41; // 最大宽度(px)
      const height = 18; // 高度(px)

      // 确保电量在40-100之间
      const level = Math.max(40, Math.min(100, this.batteryLevel));

      // 根据电量百分比计算宽度
      const width = Math.max(0, Math.min(maxWidth, (level / 100) * maxWidth));

      return {
        width: `${width}px`,
        height: `${height}px`,
        backgroundColor: this.isDarkMode ? '#333333' : '#fff'
      };
    }
  }
}
</script>

<style scoped lang="scss">
.status-bar {
  position: relative;
  z-index: 1;
  width: 100%;
  padding: 43px 74px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: 'SanFranciscoText-Medium';

  .status-bar-left,
  .status-bar-right {
    display: flex;
    align-items: center;
    gap: 13px;
  }

  .status-bar-time {
    font-size: 32px;
  }

  .status-bar-signal {
    width: 36px;
    height: 22px;
  }

  .status-bar-wifi {
    width: 33px;
    height: 23px;
  }

  .status-bar-battery {
    position: relative;
    width: 51px;
    height: 24px;

    .status-bar-battery-img {
      width: 51px;
      height: 24px;
    }

    .status-bar-battery-text {
      position: absolute;
      top: 3px;
      left: 3px;
      width: 41px;
      height: 18px;
      border-radius: 3px;
    }
  }
}

.status-bar-dark {
  .status-bar-time {
    color: #333333;
  }
}

.status-bar-light {
  .status-bar-time {
    color: #fff;
  }
}
</style> 