<template>
  <div class="quota-structure">
    <div class="quota-title">总可用额度</div>
    <div class="quota-info">
      <div class="quota-value">{{ configData.quotaValue || '24,700.02' }}</div>
      <div class="quota-action">提升额度</div>
    </div>
    <div class="quota-base">
      <div class="quota-base-label">基础额度：</div>
      <div class="quota-base-value">{{ configData.baseValue || '25,200.00' }}</div>
      <img class="icon-arrow" src="https://jst.oss-utos.hmctec.cn/common/path/b47429fe352e4f4b8aa240a1b7166470.png"
        alt="arrow">
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuotaStructure',
  props: {
    configData: {
      type: Object,
      default: () => ({
        quotaValue: '24,700.02',
        baseValue: '25,200.00'
      })
    }
  }
}
</script>

<style scoped lang="scss">
.quota-structure {
  margin-top: 45px;
  padding: 0 40px;
  position: relative;

  .quota-title {
    font-weight: 500;
    font-size: 28px;
    color: #000000;
    line-height: 41px;
  }

  .quota-info {
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .quota-value {
      font-family: DIN, DIN;
      font-weight: 500;
      font-size: 80px;
      color: #000000;
      line-height: 97px;
    }

    .quota-action {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 196px;
      height: 68px;
      background: #FFFFFF;
      border-radius: 34px 34px 34px 34px;
      font-weight: 500;
      font-size: 26px;
      color: #FF4144;
      line-height: 38px;
    }
  }

  .quota-base {
    display: flex;
    align-items: center;

    .quota-base-label {
      font-weight: 400;
      font-size: 24px;
      color: #666666;
      line-height: 35px;
    }

    .quota-base-value {
      font-family: DIN, DIN;
      font-weight: 500;
      font-size: 24px;
      color: #666666;
      line-height: 29px;
    }

    .icon-arrow {
      margin-left: 6px;
      width: 8px;
      height: 15px;
      transform: translateY(3px);
    }
  }
}
</style> 