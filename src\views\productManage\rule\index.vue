<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      @submit.native.prevent
      :inline="true"
    >
      <el-form-item label="字典名称" prop="name">
        <el-input
          size="small"
          clearable
          placeholder="请输入字典名称"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.name"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" border :data="rolesList">
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="字典名称" prop="name" align="center" />
      <el-table-column label="字典类型" prop="dictType" align="center" />
      <el-table-column label=" 规则类型标识" prop="ruleKey" align="center" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  addRuleType,
  getRuleTypeList,
  getRuleDetail,
} from "@/api/productManage/rule";
export default {
  name: "Rule",
  data() {
    return {
      total: 0,
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: "",
      },
      rolesList: [],
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      this.loading = true;

      getRuleTypeList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.rolesList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>