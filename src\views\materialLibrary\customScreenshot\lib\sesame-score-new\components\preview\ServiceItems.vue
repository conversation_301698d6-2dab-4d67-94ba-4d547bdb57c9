<template>
  <div class="services-container">
    <!-- 芝麻粒 -->
    <div class="service-item">
      <div class="service-title">{{ services.sesameGrain.title }}</div>
      <div class="service-subtitle">{{ services.sesameGrain.subtitle }}
        <span v-if="services.sesameGrain.showDot" class="highlight-dot"></span>
        <img src="https://jst.oss-utos.hmctec.cn/common/path/1f914532710b45218af613d8597fca40.png"
          class="service-arrow">
      </div>
      <img class="icon-sesame-grain"
        src="https://jst.oss-utos.hmctec.cn/common/path/bb6435b7ebaf485d8784dfa6f0e13df0.png" alt="">
    </div>

    <!-- 我的额度 -->
    <div class="service-item my-quota">
      <div class="service-title">{{ services.quota.title }}</div>
      <div class="service-subtitle">{{ services.quota.subtitle }}
        <span v-if="services.quota.showDot" class="highlight-dot"></span>
        <img src="https://jst.oss-utos.hmctec.cn/common/path/1f914532710b45218af613d8597fca40.png"
          class="service-arrow">
      </div>
      <img class="icon-my-quota"
        src="https://jst.oss-utos.hmctec.cn/common/path/fce305e2c855440e9cd524c03a7c8a6e.png" alt="">
    </div>

    <!-- 芝麻名片 -->
    <div class="service-item sesame-card">
      <div class="service-title">{{ services.sesameCard.title }}</div>
      <div class="service-subtitle">{{ services.sesameCard.subtitle }}
        <span v-if="services.sesameCard.showDot" class="highlight-dot"></span>
        <img src="https://jst.oss-utos.hmctec.cn/common/path/1f914532710b45218af613d8597fca40.png"
          class="service-arrow">
      </div>
      <img class="icon-sesame-card"
        src="https://jst.oss-utos.hmctec.cn/common/path/719eb56b84f5428b92605286b08e619b.png" alt="">
    </div>
  </div>
</template>

<script>
export default {
  name: 'ServiceItems',
  props: {
    services: {
      type: Object,
      required: true,
      default: () => ({
        sesameGrain: {
          title: '芝麻粒',
          subtitle: '可兑权益',
          showDot: false
        },
        quota: {
          title: '我的额度',
          subtitle: '查额度',
          showDot: true
        },
        sesameCard: {
          title: '芝麻名片',
          subtitle: '快速授权',
          showDot: false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.services-container {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  margin: 30px 24px 20px;
  gap: 17px;

  .service-item {
    position: relative;
    width: 223px;
    height: 188px;
    background: linear-gradient(180deg, #C8E5FF 5%, #FFFFFF 31%);
    border-radius: 24px 24px 24px 24px;
    border: 2px solid #FFFFFF;

    .service-title {
      padding: 14px 23px 5px;
      font-size: 28px;
      line-height: 41px;
      color: #333333;
      font-family: 'SourceHanSansSC-Medium';
    }

    .service-subtitle {
      padding: 0 23px;
      font-size: 24px;
      line-height: 35px;
      color: #999999;
      font-family: 'SourceHanSansSC-Regular';
      display: flex;
      align-items: center;

      .highlight-dot {
        width: 16px;
        height: 16px;
        background-color: #FF411C;
        border-radius: 50%;
        margin-left: 3px;
        margin-top: 5px;
      }

      .service-arrow {
        margin-left: 7px;
        width: 11px;
        height: 18px;
      }
    }

    .icon-sesame-grain {
      position: absolute;
      right: 22px;
      bottom: 15px;
      width: 62px;
      height: 60px;
    }

    .icon-my-quota {
      position: absolute;
      right: 22px;
      bottom: 15px;
      width: 66px;
      height: 66px;
    }

    .icon-sesame-card {
      position: absolute;
      right: 22px;
      bottom: 15px;
      width: 65px;
      height: 61px;
    }
  }
}
</style> 