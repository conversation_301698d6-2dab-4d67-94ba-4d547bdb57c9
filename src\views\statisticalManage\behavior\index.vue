<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道" prop="channelIds">
        <el-select
          v-model="checkValue"
          placeholder="渠道"
          clearable
          filterable
          multiple
          collapse-tags
          size="small"
        >
          <el-option
            :value="item.id"
            :label="item.id + '----' + item.channelName"
            v-for="(item, index) in channelIdList"
            :key="index"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="dataList">
      <el-table-column
        label="渠道ID"
        prop="channelId"
        align="center"
        width="60"
      />
      <el-table-column
        label="渠道名称"
        prop="channelName"
        width="160"
        align="center"
      >
      </el-table-column>
      <el-table-column label="注册用户数" prop="registerNum" align="center">
      </el-table-column>
      <el-table-column label="表单提交人数" prop="formPushNum" align="center">
      </el-table-column>
      <el-table-column label="匹配成功机构" prop="registerNum" align="center">
        <el-table-column
          label="人数"
          prop="okNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请机构人数"
          width="100"
          prop="applyNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请机构数量"
          width="100"
          prop="organizationNum"
          align="center"
        ></el-table-column>

        <el-table-column
          label="申请机构人数 排除兜底易贝"
          width="100"
          prop="applyExcludeEBNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请机构数量 排除兜底易贝"
          width="100"
          prop="applyExcludeOrganizationEBNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请线上产品人数"
          width="160"
          prop="applyOnlineNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请线上产品数量"
          width="160"
          prop="applyOnlineProductNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请机构+线上产品人数"
          width="160"
          prop="totalNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请机构+线上产品数量"
          width="160"
          prop="onlineTotalNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请线上U产品人数(匹配前)"
          width="160"
          prop="uABeforeNum"
          align="center"
        ></el-table-column>

        <el-table-column
          label="申请线上U产品人数(匹配后)"
          width="160"
          prop="uAAfterNum"
          align="center"
        ></el-table-column>

        <el-table-column
          label="申请线上CPS产品人数（匹配前）"
          width="160"
          prop="cPSBeforeNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请线上CPS产品人数（匹配后)"
          width="160"
          prop="cPSAfterNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请线上U产品数量（匹配前）"
          width="160"
          prop="uAProductBeforeNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请线上U产品数量（匹配后）"
          width="160"
          prop="uAProductAfterNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请线上CPS产品数量（匹配后）"
          width="160"
          prop="cPSProductAfterNum"
          align="center"
        ></el-table-column>
      </el-table-column>
      <el-table-column label="匹配成功线上产品" align="center">
        <el-table-column
          label="人数"
          prop="okOnlineNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请人数"
          prop="applyOnlineZNum"
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请线上产品数量"
          width="160"
          prop="organizationZNum"
          align="center"
        ></el-table-column>
      </el-table-column>
      <el-table-column label="匹配失败" align="center">
        <el-table-column
          label="人数"
          prop="okXNum"
          align="center"
        ></el-table-column>
        <!-- <el-table-column
          label="申请线上产品人数"
          width="160"
          prop=""
          align="center"
        ></el-table-column>
        <el-table-column
          label="申请线上产品数量"
          width="160"
          prop=""
          align="center"
        ></el-table-column> -->
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getBusinessChannelList, getChannelbev } from "@/api/statisticalManage";
export default {
  data() {
    return {
      dateRange: [],
      dataList: [],
      channelIdList: [],
      queryParams: {
        endTime: "",
        startTime: "",
        channelIds: "",
      },
      checkValue: "",
    };
  },
  methods: {
    handleQuery() {

      this.queryParams.channelIds = this.checkValue.join(",");
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    getList() {

      getChannelbev(this.queryParams).then((res) => {
        this.dataList = res;
      });
    },
  },
  mounted() {
    // this.getList();
    getBusinessChannelList().then((res) => {
      this.channelIdList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped></style>
