<template>
  <div class="app-container">
    <div class="product">{{$route.query.productName}}</div>
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="渠道类型" prop="channelIds">
        <el-select v-model="queryParams.channelIds" placeholder="渠道类型" clearable filterable multiple collapse-tags
          size="small">
          <el-option :value="item.id" :label="item.id+'----'+item.channelName" v-for="(item,index) in channelIdList "
            :key="index">
          </el-option>

        </el-select>
      </el-form-item>

      <el-form-item label="日期类型" prop="dateType">
        <el-select v-model="queryParams.dateType" placeholder="日期类型" @change="handleQuery"
          :disabled="!!dateRange&&dateRange.length>0" clearable size="small">
          <el-option value="1" label="今天"></el-option>
          <el-option value="2" label="昨天"></el-option>
          <el-option value="3" label="最近7天"></el-option>
          <el-option value="4" label="最近30天"></el-option>
          <el-option value="5" label="当月"></el-option>
          <el-option value="6" label="上月"></el-option>
          <el-option value="7" label="近半年"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd" @change="handleQuery"
          :disabled="!!queryParams.dateType" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" size="small">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="客户端" prop="deviceType">
        <el-select v-model="queryParams.deviceType" @change="handleQuery" placeholder="客户端" clearable size="small">
          <el-option :value="1" label="苹果"></el-option>
          <el-option :value="2" label="安卓"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="流量场景" prop="positionStatus">
        <el-select v-model="queryParams.positionStatus" placeholder="流量场景" @change="handleQuery" clearable size="small">
          <el-option :value="1" label="H5"></el-option>
          <el-option :value="2" label="APP"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>

    <el-table border :data="dataList">
      <el-table-column label="日期" prop="queryDate" align="center" />

      <el-table-column label="曝光用户数" prop="seeUserNum" align="center" />
      <el-table-column label="点击用户数" prop="clickUserNum" align="center" />
      <el-table-column label="点击率" align="center">
        <template slot-scope="{row}">
          <div>
            {{row.seeProductClickRate}}%
          </div>
        </template>
      </el-table-column>
      <el-table-column label="成功进件用户数" prop="successUserNum" align="center">

      </el-table-column>
      <el-table-column label="进件率" align="center">
        <template slot-scope="{row}">
          <div>
            {{row.successProductRate}}%
          </div>
        </template>
      </el-table-column>
      <el-table-column label="预估收益" prop="profit" align="center" />
      <el-table-column label="单点击价值" prop="clickProductRate" align="center" />
    </el-table>
  </div>
</template>

<script>
import { getProductDetailStatsList, getProdChannelList } from "@/api/statisticalManage";
export default {
  data() {
    return {
      dateRange: [],
      dataList: [

      ],
      channelIdList: [],
      productId: "",
      queryParams: {
        channelIds: [],
        dateType: '3',
        deviceType: '',
        productType: "",
        positionStatus: "",
      }
    }
  },
  methods: {
    handleQuery() {
      if (this.dateRange && this.dataList.length > 0) {
        this.queryParams.startDate = this.dateRange[0]
        this.queryParams.endDate = this.dateRange[1]
      } else {
        this.queryParams.startDate = ""
        this.queryParams.endDate = ""
      }
      this.getList()
    },
    getList() {
      getProductDetailStatsList(this.queryParams, this.productId).then(res => {
        this.dataList = res.data
      })
    }
  },
  mounted() {
    getProdChannelList().then(res => {

      this.channelIdList = res.data
    })
    this.productId = this.$route.query.id
    this.getList()
  }
}
</script>

<style lang="scss" scoped>
.product{
  text-align: center;

  font-size: 18px;
  color: #222;
  font-weight: bold;
  margin: 5px;
}
</style>
