<template>
  <div class="login">
    <!-- <img src="../assets/images/logo-icon.png" alt="" class="logo-icon"> -->
    <div class="login-wrap">
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" size="default">
        <!-- <div class="name-logo">
          <img src="@/assets/logo/logo.png" alt="logo" class="logo-img">
          <span class="name">小马信融管理后台</span>
        </div> -->
<!--        <div class="login-welcome">欢迎使用</div>-->
        <div class="login-title">账号登录</div>
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="用户名">
            <svg-icon slot="prefix" icon-class="user1" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码"
            @keyup.enter.native="handleLogin">
            <svg-icon slot="prefix" icon-class="lock1" class="el-input__icon input-icon" />
          </el-input>
        </el-form-item>
        <el-form-item prop="code">
          <el-input oninput="value=value.replace(/[^0-9]/g,'')" v-model="loginForm.code" auto-complete="off"
            placeholder="验证码" maxlength="6" @keyup.enter.native="handleLogin">
            <svg-icon slot="prefix" icon-class="pwd" class="el-input__icon input-icon" />
            <el-button slot="suffix" type="text" class="code-btn" @click="getCode">{{
                codeText
              }}</el-button>
          </el-input>
<!--          <div class="login-code">-->
<!--            <el-button type="primary" style="width: 100%" @click="getCode">{{-->
<!--              codeText-->
<!--            }}</el-button>-->
<!--          </div>-->
        </el-form-item>
        <el-checkbox v-model="loginForm.rememberMe" class="rememberMe"><span style="color:#fff;">记住密码</span>
        </el-checkbox>
        <el-form-item style="width: 100%">
          <el-button :loading="loading" size="medium" type="primary" class="login-btn"
            @click.native.prevent="handleLogin">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <!-- <div class="argee">
          <el-checkbox v-model="isCheck"></el-checkbox>
          <span style="margin-left: 10px" @click="argreeFlag = true"
            >我已阅读并同意
            <span style="color: #608fff">《广告主行为规范规定》</span></span
          >
        </div> -->
        </el-form-item>
      </el-form>
    </div>
    <!--  底部  -->
    <div class="el-login-footer">
      <!-- <span>Copyright © 2018-2021 ruoyi.vip All Rights Reserved.</span> -->
    </div>
    <div class="agreement" v-if="argreeFlag">
      <div class="regulations">
        <div class="title1">广告主行为规范规定</div>
        <p>
          广告主后台网站、嗨森数科平台及相关网站、移动软件（以下简称或“平台”）由成都嗨森数科科技有限公司及其关联实体(以下简称“我们”)运营。为了建立健康和谐的平台秩序，我们与您签订《广告主行为规范规定》（以下简称“本规定”）。
        </p>

        <p>
          所有向我们提交广告发布申请的广告主（以下简称“您”），都视同已充分阅读理解并同意本行为规定约定：
        </p>
        <p>
          1.
          您保证在广告发布和提供服务的过程中，不应收取除其所属机构明确公示以外的任何其他费用。<br />
          2.
          严禁您在未经用户许可的情况下，将其个人信息向第三方泄露，如违反本条款，我们将取消您的合作资格。<br />
          <span class="blod">
            3.
            您理解并认可，我们只是作为您与用户进行信息对接的中介第三方，对您及您所属客户之间产生的争议无关，对之间产生的纠纷亦不承担任何责任。</span><br />

          4.
          您同意并允许我们对您与用户的对话，如有必要，可能会进行录音、截屏，仅用于判断是否有违规情况，我们对此不承担任何侵权责任。<br />
        </p>
        <p>
          若您有违规发布广告、传播嗨森数科及关联公司和平台的不实负面信息、伪冒接单及辱骂威胁骚扰用户、冒充嗨森数科及关联公司或平台工作人员、向用户收费、账户非本人使用等行为，视作您违反了本规定，我们将按照本规定约定的惩罚措施对您作出惩罚。各违规行为的定义及惩罚措施如下：
        </p>
        <p>一、违规发布广告</p>
        <p>
          定义：您无视双方已签署的相关合作协议，使用违禁语言、违禁手段、销售违禁产品等方式，在平台上违规发布广告或提供其他非双方合作协议项下约定的服务。
        </p>
        <p>惩罚机制：</p>
        <p>用户或其他渠道投诉：</p>

        <p>
          （1）投诉1次（核实认定），取消您的认证且不再参与及享受任何平台优惠政策活动及相关会员特权。<br />
          （2）被投诉2次（核实认定），终止合作。用户已提交的产品或服务使用申请不再反馈且不予退还服务费，账户余额不退费，取消全部优惠政策。如希望继续合作，需与平台签署《保证金协议》并缴纳保证金。
          <br />
          （3）被投诉3次（核实认定），除以上（1）和（2）惩罚外，保证金不予退回，您将纳入黑名单，永远不予合作。
        </p>
        <p>被平台发现违规：</p>

        <p>
          （1）发现1次，封禁您账户15天（含所有产品）。<br />
          （2）发现2次，永久封禁，如希望继续合作，申请特批解封，需与平台签署《保证金协议》并缴纳保证金。解封后您不再参与并享受平台任何优惠政策活动以及会员特权。<br />
          （3）发现3次，终止合作，用户已提交的产品或服务使用申请不再反馈且不予退还服务费，账户余额不退费，取消全部优惠政策，缴纳的保证金不予退回，您将纳入黑名单，永远不予合作。
        </p>
        <p>二、传播嗨森数科及关联公司或平台不实负面信息</p>

        <p>
          定义：
          制造或传播损害嗨森数科及关联公司和平台形象的信息，包括对嗨森数科及关联公司和平台的用户质量、收费规则等虚假不实信息。
        </p>
        <p>惩罚机制：</p>
        <p>
          （1）被投诉举报1次（核实认定）：要求传播或制造不实信息的广告主立即删除不实言论并对嗨森数科及关联公司和平台进行公开致歉，终止合作。用户已提交的产品或服务使用申请不再反馈且不予退还服务费，账户余额不退费，取消全部优惠政策。如希望继续合作，需与平台签署《保证金协议》并缴纳保证金。<br />
          （2）被投诉举报2次（核实认定）：除（1）的惩罚外，您将纳入黑名单且保证金不予退回，缴纳保证金不予退回，平台并将追究其法律责任。
        </p>
        <p>三、伪冒接单及辱骂威胁骚扰用户</p>
        <p>
          定义：冒用其他机构或广告主名义接单、态度恶劣辱骂威胁骚扰用户，损害嗨森数科及关联公司和平台形象。
        </p>
        <p>惩罚机制：</p>

        <p>
          （1）被平台发现或被投诉1次（核实认定）：取消您的认证，您须向用户致歉，核实后予以认证审核，但认证通过后不再参与及享受任何平台优惠政策活动及相关会员特权。
          <br />

          （2）被平台发现或被投诉2次（核实认定）：终止合作，用户已提交的产品或服务使用申请不再反馈且不予退还服务费，账户余额不退费，取消全部优惠政策，缴纳的保证金不予退回，并立即向贷款用户致歉。如希望继续合作，需与平台签署《保证金协议》并缴纳保证金。
          <br />

          （3）被平台发现或被投诉3次（核实认定），除（1）和（2）的惩罚外，缴纳保证金不予退还，纳入黑名单，永远不予合作。
        </p>

        <p>四、冒充嗨森数科及关联公司或平台工作人员与用户联系</p>
        <p>定义：擅自冒充嗨森数科及关联公司或平台工作人员与用户联系。</p>
        <p>惩罚机制：</p>
        <p>
          被平台发现或被投诉（核实认定）：终止合作，用户已提交的产品或服务使用申请不再反馈且不予退还服务费，账户余额不退费，取消全部优惠政策，缴纳的保证金不予退回，并立即向用户澄清其非动及关联公司或平台工作人员与用户联系。
          定义：擅自冒充嗨森数科及关联公司或平台工作人员。纳入黑名单，永久不予合作。
        </p>

        <p>五、向用户收费</p>
        <p>1.向用户收费且收费后您处于失联状态</p>
        <p>
          定义：您存在收费行为且在被投诉或平台发现收费行为后24小时内联系不上。
        </p>
        <p>惩罚机制：</p>
        <p>
          被用户投诉或被平台发现之后账号处于失联状态（核实认定）：封禁账号且无法查看历史订单信息，用户已提交的产品或服务使用申请不再反馈且不予退还服务费，账户余额不退费，您需主动联系平台，并说明情况。
        </p>
        <p>2.冒充嗨森数科及关联公司或平台名义向用户收取费用</p>
        <p>定义：冒充嗨森数科及关联公司或平台名义向贷款用户收取费用。</p>
        <p>惩罚机制：</p>
        <p>
          被用户举报或被平台发现（核实认定）：立即向用户归还收取的全部费用，终止合作，无法查看历史订单信息，用户已提交的产品或服务使用申请不再反馈且不予退还服务费，账户余额不退费，取消全部优惠政策，缴纳的保证金不予退回，纳入黑名单，永久不予合作。
        </p>
        <p>六、账户非本人使用</p>
        <p>定义：您将你的账户擅自转让或出售给第三方进行广告发布。</p>
        <p>惩罚机制：</p>
        <p>
          被用户举报或被平台发现（核实认定）：立即向用户归还收取的全部费用，终止合作，无法查看历史订单信息，用户已提交的产品或服务使用申请不再反馈且不予退还服务费，账户余额不退费，取消全部优惠政策，缴纳的保证金不予退回，纳入黑名单，永久不予合作。
        </p>
        <p class="blod">
          本规定以电子协议的方式签订，您在与我们合作期间，在您点击“阅读并同意《广告主行为规范规定》”后即视为您已同意本规定的全部内容，并愿意接受本规定的约束。若您违反本规定约定，我们将按照本规定的相应条款对您进行惩罚。
        </p>
      </div>
      <button @click="
        argreeFlag = false;
      isCheck = true;
                      ">
        我已知悉
      </button>
    </div>
    <el-dialog :visible.sync="showCode" title="修改密码" width="500px" center :close-on-click-modal="false" @close="cancelPwd"
      append-to-body>
      <el-form label-width="100px" :model="pwdForm" ref="pwdRef" :rules="padRule">
        <el-form-item prop="code" label="验证码">
          <el-input oninput="value=value.replace(/[^0-9]/g,'')" v-model="pwdForm.code" auto-complete="off"
            placeholder="验证码" style="width: 63%" maxlength="6">
          </el-input>
          <div class="login-code">
            <el-button type="primary" style="width: 100%" @click="getPwdCode">{{
              codePwdText
            }}</el-button>
          </div>
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input placeholder="8-20位，字母/数字/标点符号至少两种"
            oninput="value=value.replace(/[(\u4e00-\u9fa5)|\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]/g,'')"
            @blur="pwdForm.password = $event.target.value" v-model="pwdForm.password" maxlength="20" type="password"
            show-password>
          </el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="repeatPassword">
          <el-input placeholder="请输入确认密码" v-model="pwdForm.repeatPassword"
            oninput="value=value.replace(/[(\u4e00-\u9fa5)|\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]/g,'')"
            @blur="pwdForm.repeatPassword = $event.target.value" type="password" show-password maxlength="20">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPwdForm">提交</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserType, getCode, editRestPassword, resetSend } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";

export default {
  name: "Login",
  data() {
    var validateNewPwd = (rule, value, callback) => {
      if (!value) {
        callback(new Error("密码不能为空"));



      } else if (!/^(?![a-zA-Z]+$)(?!\d+$)(?![^\da-zA-Z\s]+$).{8,20}$/.test(value)) {
        callback(new Error("新密码不符合规范，请按照要求修改!"));
      } else {
        callback();
      }
    };
    var validateoldPwd = (rule, value, callback) => {
      if (!value) {
        callback(new Error("密码不能为空"));
      } else if (value != this.pwdForm.password) {
        callback(new Error("确认密码和新密码不一样"));
      } else {
        callback();
      }
    }
    return {
      codeUrl: "",
      codeText: "获取验证码",
      codePwdText: "获取验证码",
      codeFlag: true,
      codePwdFlag: true,
      argreeFlag: false,
      showCode: false,
      isCheck: false,
      cookiePassword: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      pwdForm: {
        "code": "",
        "password": "",
        "repeatPassword": "",
        "username": ""
      },
      padRule: {
        code: [
          { required: true, trigger: "blur", message: "请输入验证码" },
        ],
        password: [
          {
            required: true,
            validator: validateNewPwd,
            trigger: "blur",
          },
        ],
        repeatPassword: [
          {
            required: true,
            validator: validateoldPwd,
            trigger: "blur",
          },
        ],
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" },
        ],

        code: [{ required: true, trigger: "change", message: "请输入验证码" }],
      },
      loading: false,
      // 验证码开关
      captchaOnOff: true,
      // 注册开关
      register: false,
      redirect: undefined,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  created() {
    this.getCookie();
    // let that = this;
    // var script = document.createElement("script");
    // script.src =
    //   "https://webapi.amap.com/maps?v=1.4.15&key=a85bc9e98aa47e4c4cc3bee364257013&callback=mapInit";
    // document.body.appendChild(script);
    // window.mapInit = function () {
    //   AMap.plugin("AMap.Geolocation", function () {
    //     var geolocation = new AMap.Geolocation({
    //       // 是否使用高精度定位，默认：true
    //       enableHighAccuracy: true,
    //       // 设置定位超时时间，默认：无穷大
    //       timeout: 10000,
    //       // 定位按钮的停靠位置的偏移量，默认：Pixel(10, 20)
    //       buttonOffset: new AMap.Pixel(10, 20),
    //       //  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
    //       zoomToAccuracy: true,
    //       //  定位按钮的排放位置,  RB表示右下
    //       buttonPosition: "RB",
    //     });

    //     geolocation.getCurrentPosition();
    //     AMap.event.addListener(geolocation, "complete", onComplete);
    //     AMap.event.addListener(geolocation, "error", onError);

    //     function onComplete(data) {
    //       console.log(data);
    //       that.$set(that.loginForm, "lng", data.position.lng);
    //       that.$set(that.loginForm, "lat", data.position.lat);
    //     }

    //     function onError(data) {
    //       // alert("为正常使用后台功能，请授权位置信息权限");
    //     }
    //   });
    // };
  },

  destroyed() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    getCode() {
      if (this.codeFlag) {
        this.codeFlag = false;
        let that = this;
        var num = 61;
        num--;
        this.codeText = `${num} s`;
        this.timer = setInterval(() => {
          if (num > 0) {
            num--;
            this.codeText = `${num} s`;
          } else {
            clearInterval(this.timer);
            that.codeText = "重新获取";
            that.codeFlag = true;
            num = 61;
          }
        }, 1000);

        getCode({
          password: this.loginForm.password,
          username: this.loginForm.username,
        })
          .then((res) => {
            if (res.code == 200) {
              this.$notify({
                type: "success",
                title: "温馨提示",
                message: "验证码发送成功，请注意查收",
              });
              return
            }
            if (res.code == 903) {
              this.$confirm("您当前安全等级过低,请修改密码后重新登录", "温馨提示", {
                type: 'warning',
                confirmButtonText: '去修改',
                showCancelButton: false
              }).then(res => {
                this.showCode = true
              })
            }
          })
          .catch((err) => {
            // this.codeFlag = true;
            // this.codeText="获取验证码"
            // clearInterval(this.timer);
          });
      }
    },
    getPwdCode() {
      if (this.codePwdFlag) {
        this.codePwdFlag = false;
        let that = this;
        var num = 61;
        num--;
        this.codeText = `${num} s`;
        this.timer = setInterval(() => {
          if (num > 0) {
            num--;
            this.codePwdText = `${num} s`;
          } else {
            clearInterval(this.timer);
            that.codePwdText = "重新获取";
            that.codePwdFlag = true;
            num = 61;
          }
        }, 1000);

        resetSend({
          password: this.loginForm.password,
          username: this.loginForm.username,
        }).then((res) => {
          if (res.code == 200) {
            this.$notify({
              type: "success",
              title: "温馨提示",
              message: "验证码发送成功，请注意查收",
            });
            return
          }
        });
      }
    },
    cancelPwd() {
      this.pwdForm = {
        "code": "",
        "password": "",
        "repeatPassword": "",
        "username": ""
      }
      this.$refs.pwdRef.resetFields()
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      };
    },
    submitPwdForm() {
      this.$refs.pwdRef.validate((valid) => {
        editRestPassword({
          ...this.pwdForm,
          username: this.loginForm.username
        }).then(() => {
          this.showCode = false
          this.pwdForm = {
            "code": "",
            "password": "",
            "repeatPassword": "",
            "username": ""
          }
          this.$refs.pwdRef.resetFields()
          this.$message.success("修改成功,请重新登录")
          setTimeout(() => {
            window.location.reload()
          }, 0)
        })
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          // if (!this.isCheck) {
          //   this.$notify({
          //     title: "温馨提示",
          //     message: "请先同意广告主行为规范规定",
          //     type: "warning",
          //   });
          //   return;
          // }
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), {
              expires: 30,
            });
            Cookies.set("rememberMe", this.loginForm.rememberMe, {
              expires: 30,
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          this.$store
            .dispatch("Login", this.loginForm)
            .then(() => {
              getUserType().then((res) => {
                if (res.systemUserType) {
                  this.$router
                    .push({ path: this.redirect || "/" })
                    .catch(() => { });
                } else {
                  this.$router
                    .push({ path: this.redirect || "/" })
                    .catch(() => { });
                  // this.$router
                  //   .push({
                  //     path: "/partyManage/partuserlist",
                  //   })
                  //   .catch(() => {});
                }
              });
            })
            .catch(() => {
              this.loading = false;
              // if (this.captchaOnOff) {
              //   this.getCode();
              // }
            });
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.login {
  display: flex;
  justify-content: flex-end;
  height: 100%;
  background-image: url("../assets/images/bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center center;
}

.login-wrap {
  --width: 50%;
  --padding-l: 150px;
  width: var(--width);
  height: 100%;
  display: flex;
  padding-left: var(--padding-l);
  align-items: center;

  .login-welcome {
    font-size: 24px;
    color: #313131;
  }

  .login-title {
    position: relative;
    z-index: 10;
    margin-bottom: 50px;
    font-weight: 500;
    font-size: 38px;
    color: #FFFFFF;
    line-height: 55px;
    text-align: center;
  }

  .login-agree {
    color: #608fff;
    cursor: pointer;
  }

  .login-form {
    position: relative;
    z-index: 10;
    padding: 60px 80px;
    box-shadow: 0px 3px 18px 1px rgba(167, 83, 0, 0.19);
    width: 560px;
    background: linear-gradient( 180deg, rgba(68,179,248,0.9) 0%, rgba(65,109,245,0.9) 100%);
    border-radius: 16px 16px 16px 16px;

    .input-icon {
      height: 20px;
      width: 18px;
      margin-left: 2px;

    }

    .name-logo {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      transform: translateY(-100%);
      font-weight: 500;
      font-size: 38px;
      color: #FFFFFF;
      line-height: 42px;
      letter-spacing: 6px;

      .logo-img {
        width: 100px;
        height: 100px;
      }
    }

    &::before {
      position: absolute;
      content: "";
      top: 0;
      left: 0;
      width: 100%;
      height: 50%;
      background: url("../assets/images/login-form-bg.png") no-repeat;
      background-size: 100% 100%;
    }
  }

  .code-btn {
    padding-right: 20px;
    color: #3F73F0;
  }

  .login-code {
    width: 33%;
    height: 38px;
    float: right;
  }

  .rememberMe {
    margin-bottom: 20px;

    ::v-deep.el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #fff;
      &::after {
        border-color: #416DF5;
      }
    }
  }

  .login-btn {
    width: 100%;
    background-color: #46B6FC;
    color: #fff;
    border-color: #46B6FC;
  }
}





.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

::v-deep .el-form-item__error {
  color: #fff !important;
  font-weight: 800 !important;
}

::v-deep .el-form-item.is-error .el-input__inner,
.el-form-item.is-error .el-input__inner:focus,
.el-form-item.is-error {
  border-color: #fff !important;
}

::v-deep .el-input__prefix {
  display: flex;
  justify-content: center;
  align-items: center;
}


.agreement {
  z-index: 99;
  width: 650px;
  background-color: #fff;
  position: fixed;
  top: 17%;
  left: 50%;
  transform: translate(-50%);
  height: 550px;
  text-align: center;
  border-top: 4px solid #2e5486;
  box-shadow: 0 0 5px #5f7c9f;

  .title1 {
    margin-top: 20px;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
  }

  .regulations {
    width: 85%;
    height: 86%;
    margin: 10px auto;
    overflow: hidden;
    overflow: auto;

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      width: 20px;
      height: 10px;
      border-radius: 5px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background-color: #2e5486;
    }

    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background-color: #f3f3f3;
    }

    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 10px;
      /*高宽分别对应横竖滚动条的尺寸*/
      background-color: #f3f3f3;
    }

    p {
      padding: 0 10px;
      line-height: 30px;
      font-size: 14px;
      text-align: left;
    }
  }

  button {
    width: 90px;
    height: 30px;
    line-height: 30px;
    border-radius: 4px;
    color: #fff;
    background-color: #2e5486;
    border: none;
    outline: none;
  }
}

.blod {
  font-weight: 800;
  color: #000;
}

@media only screen and (max-width: 1300px) {
  .logo-icon {
    display: none;
  }
}

@media only screen and (max-width:750px) {
  .agreement-wrap {
    --width: 100vw;
  }

  .login-wrap {
    --width: 100%;
    --padding-l: 0px;

    .name-logo {
      .logo-img {
        width: 50px !important;
        height: 50px !important;
      }

      font-size: 24px !important;
    }

    .login-title {
      font-size: 24px !important;
      margin-bottom: 20px !important;
    }


    .login-form {
      padding: 30px 30px;
    }
  }

  .login {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: url("../assets/images/h5-bg.png");
    padding: 0px 10px;
    box-sizing: border-box;
  }

}
</style>
