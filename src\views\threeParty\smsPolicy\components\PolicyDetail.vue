<template>
  <el-dialog title="策略详情" :visible.sync="dialogVisible" width="1000px" :close-on-click-modal="false"
    @close="handleClose">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="策略名称">{{ detail.name }}</el-descriptions-item>
      <el-descriptions-item label="策略类型">{{ typeMap[detail.type] }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="detail.status == 1 ? 'success' : 'info'">
          {{ detail.status == 1 ? '启用' : '禁用' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="发送量">{{ detail.limit }}</el-descriptions-item>
      <el-descriptions-item label="描述" :span="2">{{ detail.remark }}</el-descriptions-item>
    </el-descriptions>

    <el-divider content-position="left">用户资质筛选</el-divider>
    <el-descriptions :column="2" border direction="vertical">
      <el-descriptions-item label="开始时间(分钟)">
        {{ getQualificationValue('intervalMinutes') }}
      </el-descriptions-item>
      <el-descriptions-item label="结束时间(分钟)">
        {{ getQualificationValue('intervalMinutesForNow') }}
      </el-descriptions-item>
      <el-descriptions-item label="芝麻分" :span="2">
        {{ formatSesameIds(getQualificationValue('sesameIds')) }}
      </el-descriptions-item>
      <el-descriptions-item label="信用情况" :span="2">
        {{ formatOverdues(getQualificationValue('overdues')) }}
      </el-descriptions-item>
      <el-descriptions-item label="包含城市" :span="2">
        <template v-if="getQualificationValue('inCities') && getQualificationValue('inCities').length > 0">
          <div style="display: flex; flex-wrap: wrap; gap: 5px;">
            <el-tag v-for="city in getQualificationValue('inCities')" :key="city" type="info">{{ city }}</el-tag>
          </div>
        </template>
        <template v-else>
          无
        </template>
      </el-descriptions-item>
      <el-descriptions-item label="排除城市" :span="2">
        <template v-if="getQualificationValue('notInCities') && getQualificationValue('notInCities').length > 0">
          <div style="display: flex; flex-wrap: wrap; gap: 5px;">
            <el-tag v-for="city in getQualificationValue('notInCities')" :key="city" type="info">{{ city }}</el-tag>
          </div>
        </template>
        <template v-else>
          无
        </template>
      </el-descriptions-item>
      <el-descriptions-item label="包含渠道" :span="2">
        <template v-if="getQualificationValue('inChannelIds') && getQualificationValue('inChannelIds').length > 0">
          <div style="display: flex; flex-wrap: wrap; gap: 5px;">
            <el-tag v-for="channel in getQualificationValue('inChannelIds')" :key="channel" type="info">{{ getChannelName(channel) }}</el-tag>
          </div>
        </template>
        <template v-else>
          无
        </template>
      </el-descriptions-item>
      <el-descriptions-item label="排除渠道" :span="2">
        <template v-if="getQualificationValue('notInChannelIds') && getQualificationValue('notInChannelIds').length > 0">
          <div style="display: flex; flex-wrap: wrap; gap: 5px;">
            <el-tag v-for="channel in getQualificationValue('notInChannelIds')" :key="channel" type="info">{{ getChannelName(channel) }}</el-tag>
          </div>
        </template>
        <template v-else>
          无
        </template>
      </el-descriptions-item>
      <el-descriptions-item label="年龄条件" :span="2">
        {{ formatAgeCondition(getQualificationValue('age')) }}
      </el-descriptions-item>
    </el-descriptions>

    <el-divider content-position="left">渠道控量配置</el-divider>
    <el-table :data="detail.channelLimitConfigs || []" border style="width: 100%">
      <el-table-column label="短信模板" prop="templateId" align="center">
        <template slot-scope="{row}">
          {{ getTemplateName(row.templateId) }}
        </template>
      </el-table-column>
      <el-table-column label="营销渠道" prop="channelId" align="center">
        <template slot-scope="{row}">
          {{ getChannelName(row.channelId) }}
        </template>
      </el-table-column>
      <el-table-column label="发送占比" prop="sendRate" align="center">
        <template slot-scope="{row}">
          {{ (row.sendRate * 100).toFixed(2) }}%
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="hasEnable" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.hasEnable ? 'success' : 'info'">
            {{ row.hasEnable ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { SMS_POLICY_TYPE_MAP } from '@/const/smsPolicy'

export default {
  name: 'PolicyDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => ({})
    },
    templateOptions: {
      type: Array,
      default: () => []
    },
    channelOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      typeMap: SMS_POLICY_TYPE_MAP
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('update:visible', false)
    },
    getQualificationValue(key) {
      if (!this.detail.qualificationCondition) return null
      return this.detail.qualificationCondition[key]
    },

    formatSesameIds(ids) {
      if (!ids || !ids.length) return '无'
      const sesameMap = {
        '108': '无',
        '109': '550-579',
        '110': '600分以下',
        '111': '600-619',
        '112': '600-650分',
        '113': '650-700分',
        '114': '700-749',
        '115': '700分以上'
      }
      return ids.map(id => sesameMap[id] || id).join('、')
    },
    formatOverdues(overdues) {
      if (!overdues || !overdues.length) return '无'
      const overdueMap = {
        '122': '信用良好，无逾期',
        '123': '无信用卡或贷款',
        '124': '近1年无逾期',
        '125': '1年内逾期少于3次且少于90天',
        '126': '1年内逾期超过3次或者90天'
      }
      return overdues.map(id => overdueMap[id] || id).join('、')
    },
    formatAgeCondition(age) {
      if (!age) return '无'
      const { type, vars } = age
      const typeMap = {
        0: '小于',
        1: '小于等于',
        2: '大于',
        3: '大于等于',
        4: '等于',
        5: '区间',
        6: '包含',
        7: '不包含'
      }

      if (!vars || !vars.length) return '无'

      if (type == 5) {
        return `${vars[0]}-${vars[1]}岁`
      } else if (type == 6 || type == 7) {
        return `${typeMap[type]}: ${vars.join('、')}岁`
      } else {
        return `${typeMap[type]} ${vars[0]}岁`
      }
    },
    getTemplateName(id) {
      const template = this.templateOptions.find(t => t.id == id)
      return template ? template.name : id
    },
    getChannelName(id) {
      const channel = this.channelOptions.find(c => c.id == id)
      return channel ? `${id} - ${channel.channelName}` : id
    }
  }
}
</script>