<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dataRange"
          :clearable="false"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="商户" prop="partyFirstName">
        <el-input
          v-model="queryParams.partyFirstName"
          size="small"
          placeholder="请输入商户名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="商务">
        <el-select
          v-model="queryParams.userId"
          size="small"
          placeholder="请选择商务"
          clearable
        >
          <el-option
            v-for="(item, index) in userList"
            :key="index"
            :label="item.nickName"
            :value="item.userId"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="dataList" @sort-change="handleSortChange">
      <el-table-column label="商户ID" prop="partyFirstId" align="center" />
      <el-table-column label="商户名称" prop="name" align="center" />
      <!-- <el-table-column label="合计充值总额" prop="recharge" align="center" /> -->
      <el-table-column
        label="账户可用余额"
        :sort-orders="['descending', 'ascending']"
        sortable="custom"
        prop="currentBalance"
        align="center"
      />
      <el-table-column label="商务" prop="nickName" align="center" />
    </el-table>
    <!-- <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.size"
      @pagination="getList" /> -->
  </div>
</template>

<script>
import { getpartyFirstUser, getUserProfitStats } from "@/api/crmReport";

export default {
  data() {
    return {
      dataRange: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      dataList: [],
      userList: [],
      total: 0,
      queryParams: {
        userId: "",
        page: 1,
        size: 1500,
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
      },
    };
  },
  methods: {
    //前端排序
    handleSortChange(column) {
      this.proptype = column.prop;
      let sortingType = column.order;

      if (sortingType == "ascending") {
        //正序
        this.dataList = this.dataList
          .splice(1)
          .sort((a, b) => b[this.proptype] - a[this.proptype]);

      }
      if (sortingType == "descending") {
        // 倒序
        this.dataList = this.dataList
          .splice(1)
          .sort((a, b) => a[this.proptype] - b[this.proptype]);

      }
    },
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    //搜索
    handleQuery() {
      if (this.dataRange !== null) {
        this.queryParams.page = 1;
        this.queryParams.startTime = this.dataRange[0];
        this.queryParams.endTime = this.dataRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },

    getList() {
      getUserProfitStats(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total || 0;
      });
    },
  },
  mounted() {
    this.getList();
    getpartyFirstUser().then((res) => {
      this.userList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped></style>
