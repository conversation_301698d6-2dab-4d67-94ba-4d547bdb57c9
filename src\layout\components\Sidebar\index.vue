<template>
    <div :class="{'has-logo':showLogo}" :style="{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
        <logo v-if="showLogo" :collapse="isCollapse" />
        <div style="padding: 10px;">
            <el-input
                v-if="!isCollapse"
                placeholder="搜索菜单"
                v-model.trim="searchQuery"
                clearable
                @clear="handleClear"
            />
        </div>
        <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
            <el-menu
                :default-active="activeMenu"
                :collapse="isCollapse"
                :background-color="settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground"
                :text-color="settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
                :unique-opened="true"
                :active-text-color="settings.theme"
                :collapse-transition="false"
                mode="vertical"
            >
                <sidebar-item
                    v-for="(route, index) in filteredSidebarRouters"
                    :key="route.path  + index"
                    :item="route"
                    :base-path="route.path"
                />
            </el-menu>
        </el-scrollbar>
    </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.scss";

export default {
    components: { SidebarItem, Logo },
    computed: {
        ...mapState(["settings"]),
        ...mapGetters(["sidebarRouters", "sidebar"]),
        activeMenu() {
            const route = this.$route;
            const { meta, path } = route;
            // if set path, the sidebar will highlight the path you set
            if (meta.activeMenu) {
                return meta.activeMenu;
            }
            return path;
        },
        showLogo() {
            return this.$store.state.settings.sidebarLogo;
        },
        variables() {
            return variables;
        },
        isCollapse() {
            return !this.sidebar.opened;
        },

        // 只搜索菜单，不搜索目录
        filteredSidebarRouters() {
            // 如果没有搜索关键词,直接返回原始路由
            if (!this.searchQuery) {
                return this.sidebarRouters;
            }

            const baseRoutes = JSON.parse(JSON.stringify(this.sidebarRouters));
            const keywords = this.searchQuery.toLowerCase();

            // 递归匹配路由
            const matchRoutes = (routes) => {
                return routes.filter(route => {
                    // 过滤隐藏路由
                    if (route.hidden) {
                        return false;
                    }

                    const hasChildren = route.children;
                    const titleMatch = route.meta?.title?.toLowerCase().includes(keywords);

                    // 如果有子路由,递归匹配
                    if (hasChildren) {
                        route.children = matchRoutes(route.children);
                        return route.children.length > 0;
                    }

                    // 没有子路由,只匹配标题
                    return titleMatch;
                });
            }

            return matchRoutes(baseRoutes);
        }
    },

    data() {
        return {
            searchQuery: ''
        }
    },

    methods: {
        handleClear() {
            this.searchQuery = '';
        }
    }
};
</script>
