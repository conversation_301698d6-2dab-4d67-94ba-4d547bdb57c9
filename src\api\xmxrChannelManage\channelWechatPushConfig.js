import request from '@/utils/request'

// 获取渠道企微推送配置列表
export function getChannelWechatPushConfigList(query) {
  return request({
    url: '/loan/xm/channelWechatPushConfig/pageList',
    method: 'get',
    params: query
  })
}

// 新增渠道企微推送配置
export function addChannelWechatPushConfig(data) {
  return request({
    url: '/loan/xm/channelWechatPushConfig/add',
    method: 'post',
    data
  })
}

// 修改渠道企微推送配置
export function updateChannelWechatPushConfig(data) {
  return request({
    url: '/loan/xm/channelWechatPushConfig/update',
    method: 'post',
    data
  })
}

// 更新状态
export function updateChannelWechatPushConfigStatus(data) {
  return request({
    url: '/loan/xm/channelWechatPushConfig/updateStatus',
    method: 'post',
    data
  })
} 