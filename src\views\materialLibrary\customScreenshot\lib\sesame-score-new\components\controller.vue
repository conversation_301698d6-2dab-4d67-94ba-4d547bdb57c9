<template>
  <div class="controller-container">
    <!-- 使用截图按钮组件 -->
    <capture-button :preview-url="previewUrl" :device-os="form.device.os"
      @capture-screenshot="$emit('capture-screenshot')" />

    <el-form :model="form" label-width="90px" size="small">
      <!-- 使用平台类型设置组件 -->
      <platform-type-setting :deviceConfig="form.device" @update:platform="updatePlatform" />

      <!-- 使用统一的状态栏设置组件 -->
      <status-bar-setting :statusBarConfig="form.statusBar" @update:statusBar="updateStatusBar" />

      <!-- 用户信息 -->
      <el-divider>用户信息</el-divider>
      <el-form-item label="用户名">
        <el-input v-model="form.userName" @change="handleConfigChange" clearable></el-input>
      </el-form-item>

      <div class="form-row">
        <el-form-item label="芝麻分" class="form-item-half">
          <el-input-number v-model="form.score" :min="350" :max="950" @change="handleConfigChange"
            controls-position="right"></el-input-number>
        </el-form-item>

        <el-form-item label="新消息数" class="form-item-half">
          <el-input-number v-model="form.messageCount" :min="0" @change="handleConfigChange"
            controls-position="right"></el-input-number>
        </el-form-item>
      </div>

      <!-- 芝麻分字体设置 -->
      <el-divider>芝麻分字体设置</el-divider>
      <div class="form-row">
        <el-form-item label="字体大小" class="form-item-half">
          <el-input-number v-model="form.scoreFont.fontSize" :min="60" :max="200" @change="handleConfigChange"
            controls-position="right">
            <template slot="append">px</template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="字体粗细" class="form-item-half">
          <el-select v-model="form.scoreFont.fontWeight" @change="handleConfigChange" placeholder="选择字体粗细">
            <el-option label="100 (极细)" :value="100"></el-option>
            <el-option label="200 (超细)" :value="200"></el-option>
            <el-option label="300 (细)" :value="300"></el-option>
            <el-option label="400 (正常)" :value="400"></el-option>
            <el-option label="500 (中等)" :value="500"></el-option>
            <el-option label="600 (半粗)" :value="600"></el-option>
            <el-option label="700 (粗)" :value="700"></el-option>
            <el-option label="800 (超粗)" :value="800"></el-option>
            <el-option label="900 (极粗)" :value="900"></el-option>
          </el-select>
        </el-form-item>
      </div>

      <el-form-item label="进度条控制">
        <el-switch v-model="form.arcSettings.linkedToScore" @change="handleArcLinkChange" active-text="自动计算"
          inactive-text="手动控制"></el-switch>
        <div class="tip-text">注意：自动计算的圆弧进度可能不准确，建议手动控制进度值</div>
      </el-form-item>

      <el-form-item label="圆弧进度" v-if="!form.arcSettings.linkedToScore">
        <el-slider v-model="form.arcSettings.percentage" :min="0.25" :max="0.9" :step="0.01"
          :format-tooltip="formatTooltip" @input="handleConfigChange"></el-slider>
      </el-form-item>

      <!-- 服务配置 -->
      <el-divider>服务配置</el-divider>
      <div class="service-config-container">
        <!-- 芝麻粒配置 -->
        <div class="service-config-item">
          <div class="service-title">芝麻粒</div>
          <el-input v-model="form.services.sesameGrain.title" placeholder="标题" @change="handleConfigChange" size="small"
            clearable></el-input>
          <el-input v-model="form.services.sesameGrain.subtitle" placeholder="副标题" @change="handleConfigChange"
            size="small" clearable></el-input>
          <el-checkbox v-model="form.services.sesameGrain.showDot" @change="handleConfigChange">显示红点</el-checkbox>
        </div>

        <!-- 额度配置 -->
        <div class="service-config-item">
          <div class="service-title">我的额度</div>
          <el-input v-model="form.services.quota.title" placeholder="标题" @change="handleConfigChange" size="small"
            clearable></el-input>
          <el-input v-model="form.services.quota.subtitle" placeholder="副标题" @change="handleConfigChange" size="small"
            clearable></el-input>
          <el-checkbox v-model="form.services.quota.showDot" @change="handleConfigChange">显示红点</el-checkbox>
        </div>

        <!-- 芝麻名片配置 -->
        <div class="service-config-item">
          <div class="service-title">芝麻名片</div>
          <el-input v-model="form.services.sesameCard.title" placeholder="标题" @change="handleConfigChange" size="small"
            clearable></el-input>
          <el-input v-model="form.services.sesameCard.subtitle" placeholder="副标题" @change="handleConfigChange"
            size="small" clearable></el-input>
          <el-checkbox v-model="form.services.sesameCard.showDot" @change="handleConfigChange">显示红点</el-checkbox>
        </div>
      </div>

      <!-- 广告图配置 -->
      <el-divider>广告图配置</el-divider>
      <el-form-item label="广告图">
          <div class="ad-options">
            <div class="ad-option" v-for="(item,index) in adImageSrcList" :key="index" @click="handleSelectAdImage(item)">
              <img :src="item" :alt="`广告图${index+1}`"
                class="ad-thumbnail" :class="{'ad-selected':form.adImage.url === item}"/>
            </div>
          </div>
      </el-form-item>

      <!-- 消息列表 -->
      <el-divider>消息列表</el-divider>
      <el-form-item label="消息助手">
        <div v-for="(message, index) in form.messages" :key="index" class="message-item">
          <div class="message-icon-selector">
            <el-select v-model="message.iconType" placeholder="选择图标类型" @change="handleConfigChange" size="small">
              <el-option v-for="iconOption in iconOptions" :key="iconOption.value" :label="iconOption.label"
                :value="iconOption.value">
                <div class="icon-option-content">
                  <!-- <img :src="iconOption.icon" class="icon-preview" alt=""> -->
                  <span>{{ iconOption.label }}</span>
                </div>
              </el-option>
            </el-select>
          </div>
          <el-input v-model="message.title" placeholder="消息标题" @change="handleConfigChange" clearable>
          </el-input>
          <el-input v-model="message.date" placeholder="日期" @change="handleConfigChange" clearable>
          </el-input>
          <el-button type="danger" icon="el-icon-delete" circle @click="removeMessage(index)"></el-button>
        </div>
        <el-button type="primary" icon="el-icon-plus" @click="addMessage" size="small">添加消息</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import StatusBarSetting from '../../components/StatusBarSetting.vue'
import PlatformTypeSetting from '../../components/PlatformTypeSetting.vue'
import CaptureButton from '../../components/CaptureButton.vue'
// 导入图标图片
import barbaraFarmIcon from '@/assets/images/icon/barbaraFarm.png'
import taobaoIcon from '@/assets/images/icon/taobao.png'

export default {
  name: 'SesameScoreController',
  components: {
    StatusBarSetting,
    PlatformTypeSetting,
    CaptureButton
  },
  props: {
    previewUrl: {
      type: String,
      default: null
    },
    configData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      form: this.deepClone(this.configData),
      // 图标选项配置
      iconOptions: [
        {
          value: 'monthly',
          label: '月报',
          icon: 'https://jst.oss-utos.hmctec.cn/common/path/81b56869b302404dbb988305c8a08ce4.png'
        },
        {
          value: 'barbaraFarm',
          label: '芭芭农场',
          icon: barbaraFarmIcon
        },
        {
          value: 'taobao',
          label: '淘宝天猫',
          icon: taobaoIcon
        }
      ],
      // 广告图 src 数组
      adImageSrcList:[
        "https://jst.oss-utos.hmctec.cn/common/path/c226a93f56d545afa90124387838ed85.png",
        "https://jst.oss-utos.hmctec.cn/common/path/4545b0034f24431383db5cc0620b4846.png",
        "https://jst.oss-utos.hmctec.cn/common/path/0a97ad4638354546826f06b61bb888e4.png",
        "https://cdn.oss-unos.hmctec.cn/common/path/8e9ff51ab4d340d888d351c65c7c4013.png",
        "https://cdn.oss-unos.hmctec.cn/common/path/da54fdb48056428993b3e82009730e4c.png",
        "https://cdn.oss-unos.hmctec.cn/common/path/55b6032d490a4709b073497f9c1bf205.png",
        "https://cdn.oss-unos.hmctec.cn/common/path/8ca46c05c69a44c487b2c41e36aaae22.png",
        "https://cdn.oss-unos.hmctec.cn/common/path/8b4e3eb3aa254be9836e365bfe9da401.png",
        "https://cdn.oss-unos.hmctec.cn/common/path/c87cb78169894573b1da329b7b2ebf1c.png"
      ],
    }
  },
  watch: {
    configData: {
      handler(newVal) {
        this.form = this.deepClone(newVal);
      },
      deep: true
    }
  },
  methods: {
    updatePlatform(deviceConfig) {
      this.form.device = deviceConfig;
      this.handleConfigChange();
    },
    updateStatusBar(statusBarConfig) {
      this.form.statusBar = statusBarConfig;
      this.handleConfigChange();
    },
    handleSelectAdImage(url){
      this.form.adImage.url = url
      this.handleConfigChange()
    },
    handleConfigChange() {
      this.$emit('update:config', this.deepClone(this.form));
    },
    addMessage() {
      this.form.messages.push({
        title: '',
        date: '',
        iconType: 'monthly' // 默认图标类型
      });
      this.handleConfigChange();
    },
    removeMessage(index) {
      this.form.messages.splice(index, 1);
      this.handleConfigChange();
    },
    handleArcLinkChange() {
      this.handleConfigChange();
    },
    formatTooltip(value) {
      return `${Math.round(value * 100)}%`;
    },
    // 深拷贝函数
    deepClone(obj) {
      if (obj === null || typeof obj !== 'object') {
        return obj;
      }

      // 处理Date
      if (obj instanceof Date) {
        return new Date(obj.getTime());
      }

      // 处理Array
      if (Array.isArray(obj)) {
        return obj.map(item => this.deepClone(item));
      }

      // 处理Object
      const clonedObj = {};
      Object.keys(obj).forEach(key => {
        clonedObj[key] = this.deepClone(obj[key]);
      });

      return clonedObj;
    }
  }
}
</script>

<style scoped lang="scss">
.controller-container {
  .message-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;

    .el-input {
      margin-right: 10px;
    }

    .message-icon-selector {
      min-width: 120px;

      .el-select {
        width: 100%;
      }
    }
  }

  // 图标选择器下拉选项样式
  .icon-option-content {
    display: flex;
    align-items: center;
    gap: 8px;

    .icon-preview {
      width: 20px;
      height: 20px;
      object-fit: cover;
    }
  }

  .el-form-item {
    margin-bottom: 22px;

    .el-input+.el-input {
      margin-top: 10px;
    }

    .el-checkbox {
      margin-top: 10px;
      display: block;
    }
  }

  .status-bar-icons {
    display: flex;
    align-items: center;

    .el-select {
      width: 100%;
    }
  }

  .unit-label {
    margin-left: 5px;
    color: #606266;
  }

  .action-buttons {
    margin: 20px 0;
    display: flex;
    gap: 10px;
  }

  .ad-options {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .ad-option {
    text-align: center;
  }

  .ad-thumbnail {
    width: 140px;
    height: 110px;
    object-fit: cover;
    border: 1px solid #fff;
    border-radius: 4px;
    margin-top: 5px;
    cursor: pointer;
    padding: 5px;
  }
  .ad-selected{
    border: 1px solid #409EFF;
  }

  .form-row {
    display: flex;
    gap: 20px;
  }

  .form-item-half {
    width: calc(50% - 10px);
  }

  .service-config-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .service-config-item {
    width: calc(33.33% - 15px);
    margin-bottom: 15px;
  }

  .service-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #409EFF;
  }

  .tip-text {
    margin-top: 5px;
    font-size: 12px;
    color: #E6A23C;
    line-height: 1.4;
  }
}
</style>