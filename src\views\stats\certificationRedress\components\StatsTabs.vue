<template>
  <el-tabs v-model="activeTab" @tab-click="handleTabChange">
    <el-tab-pane label="芝麻分统计" name="zhima">
      <el-radio-group v-model="zhimaType" @change="handleZhimaTypeChange">
        <el-radio-button :label="2">芝麻分650+</el-radio-button>
        <el-radio-button :label="3">芝麻分650-</el-radio-button>
      </el-radio-group>
    </el-tab-pane>
    <el-tab-pane label="逾期统计" name="overdue">
      <el-radio-group v-model="overdueType" @change="handleOverdueTypeChange">
        <el-radio-button :label="0">无逾期</el-radio-button>
        <el-radio-button :label="1">有逾期</el-radio-button>
      </el-radio-group>
    </el-tab-pane>
    <el-tab-pane label="年龄统计" name="age">
      <el-radio-group v-model="ageType" @change="handleAgeTypeChange">
        <el-radio-button :label="4">年龄 < 20</el-radio-button>
        <el-radio-button :label="5">年龄 22-45</el-radio-button>
        <el-radio-button :label="6">年龄 50+</el-radio-button>
      </el-radio-group>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
export default {
  name: 'StatsTabs',
  data() {
    return {
      activeTab: 'zhima',
      zhimaType: 2,
      overdueType: 0,
      ageType: 4
    };
  },
  methods: {
    handleTabChange() {
      this.$emit('tab-change', this.activeTab);
    },
    handleZhimaTypeChange(val) {
      if (this.activeTab === 'zhima') {
        this.$emit('type-change', val);
      }
    },
    handleOverdueTypeChange(val) {
      if (this.activeTab === 'overdue') {
        this.$emit('type-change', val);
      }
    },
    handleAgeTypeChange(val) {
      if (this.activeTab === 'age') {
        this.$emit('type-change', val);
      }
    },
    getCurrentType() {
      switch (this.activeTab) {
        case 'zhima':
          return this.zhimaType;
        case 'overdue':
          return this.overdueType;
        case 'age':
          return this.ageType;
        default:
          return null;
      }
    },
    reset() {
      this.activeTab = 'zhima';
      this.zhimaType = 2;
      this.overdueType = 0;
      this.ageType = 4;
    }
  }
};
</script> 