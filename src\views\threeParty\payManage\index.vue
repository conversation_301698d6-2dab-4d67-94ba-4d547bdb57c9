<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="模块名称" prop="status">
        <el-input
          placeholder="请输入模块明白"
          clearable
          v-model="queryParams.paymentName"
          size="mini"
        >
        </el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="addPayName"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-table border :data="payList">
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="模块名称" prop="paymentName" align="center" />
      <el-table-column label="支付标题" prop="headers" align="center" />
      <el-table-column label="支付现价" prop="novPrice" align="center" />
      <el-table-column label="支付原价" prop="protPrice" align="center" />
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              type="text"
              icon="el-icon-edit-outline"
              @click="editPay(row)"
              >编辑</el-button
            >
            <el-button type="text" icon="el-icon-delete" @click="handleDel(row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="isadd ? '新增支付模块' : '修改支付模块'"
      :visible.sync="payAvisible"
      @close="cancel"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="支付标题" prop="headers">
          <el-input v-model="formData.headers" placeholder="请输入支付标题" />
        </el-form-item>
        <el-form-item label="模块名称" prop="paymentName">
          <el-input
            v-model="formData.paymentName"
            placeholder="请输入模块名称"
          />
        </el-form-item>
        <el-form-item label="支付原来价格" prop="protPrice">
          <el-input
            v-model="formData.protPrice"
            type="number"
            placeholder="支付原来价格"
          />
        </el-form-item>
        <el-form-item label="支付现价格" prop="novPrice">
          <el-input
            type="number"
            v-model="formData.novPrice"
            placeholder="请输入支付现价格"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPayManageList,
  addPayManangeOne,
  editPayManangeOne,
  delPayManangeOne,
} from "@/api/threeParty/payManage";
export default {
  name:"PayManage",
  data() {
    return {
      payAvisible: false,
      isadd: true,
      formData: {
        headers: "",
        protPrice: null,
        novPrice: null,
        paymentName: "",
      },
      rules: {
        headers: [
          { required: true, message: "请输入支付标题", trigger: "blur" },
        ],
        protPrice: [
          { required: true, message: "支付原来价格", trigger: "blur" },
        ],
        novPrice: [
          { required: true, message: "请输入支付现价格", trigger: "blur" },
        ],
        paymentName: [
          { required: true, message: "请输入模块名称", trigger: "blur" },
        ],
      },
      total: 0,

      payList: [],
      queryParams: {
        paymentName: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    editPay(row) {
      this.formData = row;
      this.payAvisible = true;
      this.isadd = false;
    },
    addPayName() {
      this.isadd = true;
      this.payAvisible = true;
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    cancel() {
      this.payAvisible = false;
      this.formData = {
        headers: "",
        protPrice: "",
        novPrice: "",
        paymentName: "",
      };
      this.$refs.formData.resetFields();
    },
    handleDel(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delPayManangeOne({ id: row.id })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {});
        })
        .catch((err) => {});
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.protPrice = this.formData.protPrice * 1;
          this.formData.novPrice = this.formData.novPrice * 1;
          if (this.isadd) {
            addPayManangeOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.cancel();
                this.getList();
              }
            });
          } else {
            editPayManangeOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.cancel();
                this.getList();
              }
            });
          }
        }
      });
    },
    getList() {
      getPayManageList(this.queryParams).then((res) => {
        this.payList = res.rows;
        this.total = res.total;
        if (this.payList.length == 0 && this.queryParams.pageNum > 1) {
          this.queryParams.pageNum = this.queryParams.pageNum - 1;
          this.getList();
          return;
        }
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
