<template>
  <div class="app-container">
    <div class="title">手机状态标识统计</div>
    <!-- 筛选表单 -->
    <el-form :inline="true" :model="queryParams">
      <el-form-item label="日期">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="handleQuery"
          :clearable="false"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="statusName" label="状态名称"></el-table-column>
      <el-table-column prop="statusCount" label="状态数量"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { getPhoneStatusList } from "@/api/distributeStatistics/phoneStatus";

export default {
  name: "PhoneStatusStatistics",
  data() {
    return {
      dateRange: null,
      queryParams: {
        startDateTime: undefined,
        endDateTime: undefined
      },
      tableData: []
    };
  },
  created() {
    this.setDefaultDate();
    this.getList();
  },
  methods: {
    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format("YYYY-MM-DD 00:00:00");
      const endTime = today.format("YYYY-MM-DD 23:59:59");

      this.dateRange = [startTime, endTime];
      this.queryParams.startDateTime = startTime;
      this.queryParams.endDateTime = endTime;
    },
    handleQuery() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        this.$message.warning('请选择日期范围');
        return;
      }
      this.queryParams.startDateTime = this.dateRange[0];
      this.queryParams.endDateTime = this.dateRange[1];
      this.getList();
    },
    resetQuery() {
      this.setDefaultDate();
      this.getList();
    },
    async getList() {
      const res = await getPhoneStatusList({
        startDateTime: this.queryParams.startDateTime,
        endDateTime: this.queryParams.endDateTime
      });
      if (res.code === 200) {
        this.tableData = res.data || [];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
  }
}
</style> 