<!-- 渠道企微推送配置 -->
<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="渠道ID" prop="channelId">
        <el-input
          v-model="queryParams.channelId"
          placeholder="请输入渠道ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="渠道名称" prop="channelName">
        <el-input
          v-model="queryParams.channelName"
          placeholder="请输入渠道名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column label="渠道ID" prop="channelId" width="80" />
      <el-table-column label="渠道名称" prop="channelName" width="120" show-overflow-tooltip />
      <el-table-column label="推送比例" prop="pushRatio" width="80">
        <template slot-scope="scope">
          {{ scope.row.pushRatio }}%
        </template>
      </el-table-column>
      <el-table-column label="生效时间段" prop="timeSlot" width="180">
        <template slot-scope="scope">
          <div v-if="scope.row.timeSlot">
            <div v-for="(item, index) in JSON.parse(scope.row.timeSlot)" :key="index">
              {{ item.startPoint }} - {{ item.endPoint }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="固定推送手机号" prop="fixedPhone" width="140" show-overflow-tooltip />
      <el-table-column label="收益渠道" prop="profitChannelName" width="300" show-overflow-tooltip />
      <el-table-column label="收益渠道ID" prop="profitChannelId" width="140" show-overflow-tooltip />
      <el-table-column label="忽略产品" prop="ignoreProduct" min-width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.ignoreProduct">
            <el-tag
              v-for="item in JSON.parse(scope.row.ignoreProduct)"
              :key="`${item.platformType}-${item.productId}`"
              style="margin-right: 5px; margin-bottom: 5px;"
            >
              {{ getProductName(`${item.platformType}-${item.productId}`) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="150">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="2"
            active-text="启用"
            inactive-text="停用"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="80" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body @close="handleClose">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="渠道" prop="channelId">
          <el-select
            v-model="form.channelId"
            placeholder="请选择渠道"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in channelOptions"
              :key="item.id"
              :label="`${item.id} - ${item.channelName}`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="推送比例" prop="pushRatio">
          <el-input-number
            v-model="form.pushRatio"
            :min="0"
            :max="100"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="生效时间段" prop="timeSlot">
          <div v-for="(item, index) in timeSlots" :key="index" style="margin-bottom: 10px; display: flex; align-items: center;">
            <el-time-picker
              v-model="item.startPoint"
              placeholder="开始时间"
              format="HH:mm:ss"
              value-format="HH:mm:ss"
              style="width: 180px; margin-right: 10px"
              @change="handleTimeChange(index)"
            />
            <span style="margin: 0 10px">至</span>
            <el-time-picker
              v-model="item.endPoint"
              placeholder="结束时间"
              format="HH:mm:ss"
              value-format="HH:mm:ss"
              style="width: 180px"
              @change="handleTimeChange(index)"
            />
          </div>
        </el-form-item>
        <el-form-item label="固定推送手机号" prop="fixedPhone">
          <el-input v-model="form.fixedPhone" maxlength="11" placeholder="请输入固定推送手机号" />
        </el-form-item>
        <el-form-item label="收益渠道" prop="profitChannelId">
          <el-input
            v-model.number="form.profitChannelId"
            placeholder="请输入收益渠道ID"
            type="number"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="渠道ID参考">
          <el-select
            v-model="channelHeadMaxIdValue"
            filterable
            placeholder="仅供参考，不会实际提交"
            style="width: 100%"
          >
            <el-option
              v-for="item in channelHeadMaxIdList"
              :key="item.channelHead"
              :label="`${item.channelHead} - 最大ID: ${item.maxChannelId}`"
              :value="item.channelHead"
            >
              <span>渠道头: {{ item.channelHead }}</span>
              <span style="float: right; color: #8492a6;">最大ID: {{ item.maxChannelId }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="忽略产品" prop="ignoreProduct">
          <el-select
            v-model="form.ignoreProduct"
            multiple
            collapse-tags
            placeholder="请选择忽略产品"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="item in productOptions"
              :key="`${item.platformType}-${item.productId}`"
              :label="`${item.productName}(${item.platform})`"
              :value="`${item.platformType}-${item.productId}`"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="form.status"
            :active-value="1"
            :inactive-value="2"
            active-text="启用"
            inactive-text="停用"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAllChannelList } from '@/api/channeManage/channelList'
import {
  getChannelWechatPushConfigList,
  addChannelWechatPushConfig,
  updateChannelWechatPushConfig,
  updateChannelWechatPushConfigStatus
} from '@/api/xmxrChannelManage/channelWechatPushConfig'
import { getChannelPlatformGroupProductList } from '@/api/platformProductManagement/platformProductConfig'
import { getChannelHeadMaxId } from '@/api/xmxrChannelManage/channelList'

export default {
  name: 'ChannelWechatPushConfig',
  data() {
    return {
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 渠道选项
      channelOptions: [],
      // 产品列表选项
      productOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        channelId: null,
        channelName: ''
      },
      // 表单参数
      form: {
        id: null,
        channelId: null,
        pushRatio: 0,
        timeSlot: undefined,
        fixedPhone: '',
        profitChannelId: null,
        status: 1,
        ignoreProduct: undefined
      },
      // 时间段数组
      timeSlots: [
        { startPoint: '', endPoint: '' },
        { startPoint: '', endPoint: '' },
        { startPoint: '', endPoint: '' }
      ],
      // 表单校验
      rules: {
        channelId: [
          { required: true, message: '请选择渠道', trigger: 'change' }
        ],
        pushRatio: [
          { required: true, message: '请输入推送比例', trigger: 'blur' },
          { type: 'number', min: 0, max: 100, message: '推送比例必须在0到100之间', trigger: 'blur' }
        ],
        fixedPhone: [
          { required: true, message: '请输入固定推送手机号', trigger: 'blur' }
        ],
        profitChannelId: [
          { required: true, message: '请输入收益渠道ID', trigger: 'blur' },
          { type: 'number', message: '收益渠道ID必须为数字', trigger: 'blur' }
        ]
      },
      channelHeadMaxIdList: [], // 渠道ID参考数据
      channelHeadMaxIdValue: '' // 渠道ID参考选中值
    }
  },
  created() {
    this.getList()
    this.getChannelOptions()
    this.getProductOptions()
  },
  methods: {
    /** 查询列表 */
    getList() {
      getChannelWechatPushConfigList(this.queryParams).then(response => {
        this.tableData = response.rows
        this.total = response.total
      })
    },
    /** 查询渠道选项 */
    getChannelOptions() {
      getAllChannelList().then(response => {
        this.channelOptions = response.data || []
      }).catch(error => {
        console.error('获取渠道列表出错：', error)
        this.channelOptions = []
      })
    },
    /** 获取产品列表选项 */
    getProductOptions() {
      getChannelPlatformGroupProductList({ type: 1 }).then(response => {
        this.productOptions = response.data || []
      }).catch(error => {
        console.error('获取产品列表出错：', error)
        this.productOptions = []
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        channelId: null,
        channelName: ''
      }
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加渠道企微推送配置'
      this.getChannelHeadMaxIdList()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const form = {
        id: row.id,
        channelId: row.channelId,
        pushRatio: row.pushRatio,
        timeSlot: row.timeSlot,
        fixedPhone: row.fixedPhone,
        profitChannelId: row.profitChannelId,
        status: row.status || 1
      }
      
      // 处理忽略产品数据
      if (row.ignoreProduct) {
        try {
          const ignoreProducts = JSON.parse(row.ignoreProduct)
          form.ignoreProduct = ignoreProducts.map(item => `${item.platformType}-${item.productId}`)
        } catch (e) {
          console.error('解析ignoreProduct失败:', e)
          form.ignoreProduct = undefined
        }
      }
      
      this.form = form
      
      // 处理时间段数据
      if (row.timeSlot) {
        try {
          const parsedTimeSlots = JSON.parse(row.timeSlot)
          this.timeSlots = Array(3).fill(null).map((_, index) => {
            if (parsedTimeSlots[index]) {
              return {
                startPoint: parsedTimeSlots[index].startPoint,
                endPoint: parsedTimeSlots[index].endPoint
              }
            }
            return { startPoint: '', endPoint: '' }
          })
        } catch (e) {
          console.error('解析timeSlot失败:', e)
          this.timeSlots = [
            { startPoint: '', endPoint: '' },
            { startPoint: '', endPoint: '' },
            { startPoint: '', endPoint: '' }
          ]
        }
      }
      
      this.open = true
      this.title = '修改渠道企微推送配置'
      this.getChannelHeadMaxIdList()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        channelId: null,
        pushRatio: 0,
        timeSlot: undefined,
        fixedPhone: '',
        profitChannelId: null,
        status: 1,
        ignoreProduct: undefined
      }
      this.timeSlots = [
        { startPoint: '', endPoint: '' },
        { startPoint: '', endPoint: '' },
        { startPoint: '', endPoint: '' }
      ]
      this.channelHeadMaxIdValue = '' // 重置渠道ID参考选中值
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 对话框关闭的回调 */
    handleClose() {
      this.reset()
    },
    /** 获取产品名称 */
    getProductName(value) {
      const product = this.productOptions.find(item => `${item.platformType}-${item.productId}` == value)
      return product ? `${product.productName}(${product.platform})` : ''
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const submitData = { ...this.form }

          // 处理时间段数据
          const validTimeSlots = this.timeSlots.filter(
            item => item.startPoint && item.endPoint
          )
          submitData.timeSlot = JSON.stringify(validTimeSlots)

          // 处理忽略产品数据
          if (submitData.ignoreProduct && Array.isArray(submitData.ignoreProduct) && submitData.ignoreProduct.length > 0) {
            submitData.ignoreProduct = JSON.stringify(submitData.ignoreProduct.map(value => {
              const [platformType, productId] = value.split('-')
              return {
                platformType: Number(platformType),
                productId: Number(productId)
              }
            }))
          } else {
            submitData.ignoreProduct = '[]'
          }

          if (this.form.id != null) {
            updateChannelWechatPushConfig(submitData).then(response => {
              this.$message.success('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addChannelWechatPushConfig(submitData).then(response => {
              this.$message.success('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 状态修改 */
    handleStatusChange(row) {
      const text = row.status == 1 ? '启用' : '停用'
      this.$confirm('确认要"' + text + '""' + row.channelName + '"吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return updateChannelWechatPushConfigStatus({
          id: row.id,
          status: row.status
        })
      }).then(() => {
        this.$message.success(text + '成功')
      }).catch(() => {
        row.status = row.status == 1 ? 2 : 1
      })
    },
    /** 处理时间变化 */
    handleTimeChange(index) {
      const item = this.timeSlots[index]
      if (item.startPoint && item.endPoint && item.startPoint >= item.endPoint) {
        this.$message.warning('结束时间必须大于开始时间')
        item.endPoint = ''
      }
    },
    /** 获取渠道ID参考数据 */
    async getChannelHeadMaxIdList() {
      try {
        const res = await getChannelHeadMaxId()
        if (res.code == 200) {
          this.channelHeadMaxIdList = res.data
        }
      } catch (error) {
        console.error('获取渠道ID参考数据失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style> 