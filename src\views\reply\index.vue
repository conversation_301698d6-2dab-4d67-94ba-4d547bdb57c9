<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="筛选" prop="commerce">
        <el-date-picker v-model="queryParams.createTime" type="date" value-format="yyyy-MM-dd" placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="筛选" prop="status">
        <el-select v-model="queryParams.status" placeholder="状态" clearable size="small">
          <el-option value="0" label="未处理"></el-option>
          <el-option value="1" label="已处理"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" border :data="replayList">
      <el-table-column label="联系方式" prop="phone" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.phone }} <el-button type="text" @click="handleGetUserPhone(row)" v-hasPermi="['loan:reply:getphone']">联系方式</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="留言内容" prop="content" align="center" />
      <el-table-column label="留言时间" prop="createTime" align="center" />
      <el-table-column label="图片" width="320">
        <template  slot-scope="{row}">
          <div class="priview">
            <el-popover v-for="(item, index) in row.urls" :key="index">
              <img slot="reference" :src="item" alt="" />
              <img class="replayImage" :src="item" alt="" />
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-tag type="success" v-if="row.status == '1'">已处理</el-tag>
            <el-tag type="danger" v-if="row.status == '0'">未处理</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button type="text" @click="replay(row)" :disabled="row.status == '1'">处理</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="回答内容" prop="replyContent" align="center" />
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog title="回复留言" :visible.sync="replayAvisible" @close="cancel" width="600px" append-to-body center
      :close-on-click-modal="false">
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="回复留言" prop="replyContent">
          <el-input type="textarea" v-model="formData.replyContent" placeholder="请输入留言内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="投诉信息" width="600px" :visible.sync="showPhone" append-to-body center :close-on-click-modal="false">
      <div class="phone">投诉号码： {{ userPhone || '-' }}</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showPhone = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getReplyList, replyOne, getUserPhone } from "@/api/reply";
export default {
  name: "Reply",
  data() {
    return {
      replayAvisible: false,
      showPhone: false,
      userPhone: "",
      formData: {
        replyContent: "",
        id: "",
      },
      rules: {
        replyContent: [
          { required: true, message: "请输入回复内容", trigger: "blur" },
        ],
      },
      total: 0,
      loading: false,
      replayList: [],
      queryParams: {
        createTime: "",
        status: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    replay(row) {
      this.replayAvisible = true;
      this.formData.id = row.id;
    },
    cancel() {
      this.replayAvisible = false;
      this.formData.replyContent = "";
      this.formData.id = "";
      this.$refs.formData.resetFields();
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          replyOne(this.formData).then((res) => {
            if (res.code == 200) {
              this.$message.success("回复成功");
              this.cancel();
              this.getList();
            }
          });
        }
      });
    },
    handleGetUserPhone(row) {

      getUserPhone({ userReplyId: row.id }).then(res => {
        this.showPhone = true
        this.userPhone = res.data.fullPhone

      })
    },
    getList() {
      this.loading = true;
      getReplyList(this.queryParams).then((res) => {
        this.replayList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.priview {
  width: 300px;

  img {
    width: 85px;
    height: 50px;
    padding: 0 10px;
  }
}

.replayImage {
  border: 0;
  max-width: 400px;
  max-height: 100vh;
}

.phone {
  font-size: 20px;
  color: #222;
}
</style>
