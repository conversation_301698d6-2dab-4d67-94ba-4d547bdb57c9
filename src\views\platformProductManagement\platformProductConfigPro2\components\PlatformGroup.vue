<template>
  <el-card shadow="never">
    <div slot="header" class="card-header">
      <span>{{ title }}</span>
    </div>
    <div class="card-content">
      <div class="group-items-wrapper">
        <template v-for="(group, index) in groups">
          <div 
            :key="group.renderKey"
            class="group-item"
          >
            <div class="group-title">
              <span>{{ group.groupName || '默认分组' }}</span>
              <div class="group-actions">
                <el-button type="text" @click="handleEditGroup(index)">编辑</el-button>
              </div>
            </div>
            <div class="group-content">
              <div v-if="!group.productList || group.productList.length === 0" class="empty-content">
                <el-empty :image-size="60">
                  <template slot="description">
                    <span>暂无产品数据</span>
                  </template>
                </el-empty>
              </div>
              <div v-else class="platform-list">
                <el-tag 
                  v-for="platform in group.productList" 
                  :key="`${platform.platformType}-${platform.productId}`" 
                  closable 
                  type="info" 
                  class="platform-tag"
                  @close="handleRemoveProduct(index, platform)"
                >
                  {{ platform.platform }}-{{ platform.productName }}
                </el-tag>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 使用平台分组编辑弹窗组件 -->
    <platform-group-dialog
      :visible.sync="dialogVisible"
      :title="dialogTitle"
      :product-list="processedProductList"
      :edit-data="currentEditData"
      @save="handleSaveGroup"
    />
  </el-card>
</template>

<script>
import PlatformGroupDialog from './PlatformGroupDialog.vue'

export default {
  name: 'PlatformGroup',

  components: {
    PlatformGroupDialog
  },

  props: {
    // 分组数据
    value: {
      type: Array,
      default: () => []
    },
    // 分组的唯一标识，用于拖拽分组
    groupKey: {
      type: String,
      required: true
    },
    // 平台类型
    type: {
      type: String,
      required: true
    },
    // 平台标题
    title: {
      type: String,
      required: true
    },
    // 产品列表数据
    productList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      groups: this.value,
      dialogVisible: false,
      dialogTitle: '',
      currentEditIndex: -1,
      currentEditData: {}
    }
  },

  computed: {
    // 处理产品列表，禁用已选择的产品
    processedProductList() {
      if (!this.productList || !this.productList.length) return []

      // 获取当前平台下其他分组已选择的产品
      const selectedProducts = new Set()
      if (this.groups && this.groups.length) {
        this.groups.forEach((group, index) => {
          if (index !== this.currentEditIndex && group.productList && group.productList.length) {
            group.productList.forEach(product => {
              if (product && product.platformType && product.productId) {
                selectedProducts.add(`${product.platformType}-${product.productId}`)
              }
            })
          }
        })
      }

      // 更新产品列表的禁用状态并预处理transfer组件需要的字段
      return this.productList.map(product => {
        if (!product) return null
        
        // 确保所有必要的字段都存在
        const platformType = product.platformType
        const productId = product.productId
        const platformName = product.platform || ''
        const productName = product.productName || ''
        
        // 如果缺少关键字段，跳过此项
        if (!platformType || !productId) {
          console.warn('产品数据缺少必要字段:', product)
          return null
        }
        
        return {
          ...product,
          disabled: selectedProducts.has(`${platformType}-${productId}`),
          // 为transfer组件准备的字段
          key: `${platformType}-${productId}`,
          label: `${platformName}-${productName}`
        }
      }).filter(item => item !== null)
    }
  },

  watch: {
    value: {
      handler(val) {
        if (!val || val.length === 0) {
          // 如果没有数据，创建默认分组
          this.groups = [{
            groupName: '默认分组',
            renderKey: Math.random().toString(36).substring(2, 9),
            productList: [],
            sort: 1
          }]
        } else {
          this.groups = val
        }
      },
      deep: true,
      immediate: true
    },
    groups: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  },

  methods: {
    // 处理编辑分组
    handleEditGroup(index) {
      this.dialogTitle = '编辑分组'
      this.currentEditIndex = index
      this.currentEditData = { ...this.groups[index] }
      this.dialogVisible = true
    },

    // 处理移除产品
    handleRemoveProduct(groupIndex, product) {
      const group = this.groups[groupIndex]
      if (!group) return

      // 如果只剩一个平台，不允许删除
      if (group.productList.length <= 1) {
        this.$message.warning('分组至少需要保留一个平台')
        return
      }

      this.$confirm('确认移除该平台吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = group.productList.findIndex(item => 
          item.platformType == product.platformType && item.productId == product.productId
        )
        if (index > -1) {
          group.productList.splice(index, 1)
        }
      }).catch(() => {})
    },

    // 处理保存分组
    handleSaveGroup(formData, callback) {
      // 检查分组名是否重复
      const isDuplicate = this.groups.some((item, index) => {
        return item.groupName == formData.groupName && 
          index !== this.currentEditIndex &&
          item.renderKey !== formData.renderKey
      })

      if (isDuplicate) {
        this.$message.error('该平台下已存在相同名称的分组')
        callback(false)
        return
      }

      // 编辑现有分组
      Object.assign(this.groups[this.currentEditIndex], formData)
      callback(true)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-card__header {
  padding: 0;
}

.card-header {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .el-button {
    padding: 0;
  }
}

.card-content {
  .empty-content {
    padding: 20px 0;
    
    ::v-deep .el-button {
      font-size: 14px;
      padding: 0;
    }
  }

  .group-items-wrapper {
    display: flex;
    flex-direction: column;
    margin: -10px;

    .group-item {
      width: calc(100% - 20px);
      margin: 10px;
      border: 1px solid #EBEEF5;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      &.sortable-ghost {
        opacity: 0.5;
        background: #f5f7fa;
      }

      &.sortable-drag {
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .group-title {
        padding: 8px 12px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #EBEEF5;
        font-size: 13px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .group-actions {
          .el-button {
            padding: 2px 6px;

            & + .el-button {
              margin-left: 4px;
            }
          }
        }
      }

      .group-content {
        padding: 12px;

        .platform-list {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          padding: 10px 0;

          .platform-tag {
            margin-right: 0;
            max-width: 100%;

            &.el-tag {
              display: inline-flex;
              align-items: center;
              height: auto;
              padding: 6px 10px;
              line-height: 1.4;
              white-space: normal;
              word-break: break-all;
            }
          }
        }
      }
    }
  }
}
</style> 