<template>
  <div v-if="content" class="config-description-section">
    <el-collapse v-model="expandedDescriptions" accordion>
      <el-collapse-item name="description">
        <template slot="title">
          <div class="description-header">
            <i class="fas fa-info-circle"></i>
            <span>配置说明</span>
          </div>
        </template>
        <div v-html="content" class="description-content"></div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
export default {
  name: 'ConfigItemDescription',
  props: {
    content: {
      type: String,
      default: ''
    },
    configKey: {
      type: String,
      required: true
    }
  },
  
  data() {
    return {
      expandedDescriptions: []
    }
  }
}
</script>

<style lang="scss" scoped>
.config-description-section {
  margin-top: 16px;

  ::v-deep .el-collapse {
    border: 1px solid var(--border-light);
    border-radius: 6px;

    .el-collapse-item__header {
      background: var(--bg-tertiary);
      border-bottom: 1px solid var(--border-light);
      padding: 0 16px;
      font-weight: 500;
      color: var(--text-primary);

      .description-header {
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: var(--redis-secondary-color);
        }
      }
    }

    .el-collapse-item__content {
      padding: 16px;
      background: var(--bg-primary);

      .description-content {
        color: var(--text-secondary);
        line-height: 1.6;
        font-size: 14px;

        ::v-deep p {
          margin: 8px 0;
        }

        ::v-deep ul, ::v-deep ol {
          margin: 8px 0;
          padding-left: 20px;
        }

        ::v-deep li {
          margin: 4px 0;
        }
      }
    }
  }
}
</style>