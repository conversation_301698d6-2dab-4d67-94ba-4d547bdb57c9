import request from '@/utils/request'


export const getAppUserBlackList = (data) => {
  return request({
    url: "/loan/appUserBlackList/list",
    method: "get",
    params: data
  })
}
//恢复
export const getAppUserBlackOne = (data) => {
  return request({
    url: "/loan/appUserBlackList/recover/" + data,
    method: "post"
  })
}

export const getAppUserList = (data) => {
  return request({
    url: "/loan/appUserList/list",
    method: 'get',
    params: data
  })
}
//拉黑
export const getrecoverOne = (data) => {
  return request({
    url: "/loan/appUserList/recover/" + data,
    method: 'post',
  })
}
//拉黑名字
export const addBlackByName = (data) => {
  return request({
    url: "/loan/appUserBlackList/addBlackName",
    method: "post",
    params: data
  })
}

//导出未推送的用户
export const exportData = (data) => {
  return request({
    url: '/loan/appUserList/export',
    method: "get",
    responseType: "arraybuffer",
    params: data,
    timeout: 180000
  })
}
//导出城市机构表单推送统计
export const exportFormAndCity = (data) => {
  return request({
    url: "/loan/appUserList/exportFormAndCity",
    method: "get",
    responseType: "arraybuffer",
    params: data,
    timeout: 180000
  })
}
//导出申请成功城市机构表单
export const exportSuccesscity = (data) => {
  return request({
    url: "/loan/appUserList/exportapplysuccesscityform",
    method: "get",
    responseType: "arraybuffer",
    params: data,
    timeout: 180000
  })
}
//导出申请成功城市机构表单
export const exportApplyForm = (data) => {
  return request({
    url: "/loan/appUserList/export/notApply",
    method: "get",
    responseType: "arraybuffer",
    params: data,
    timeout: 180000
  })
}


//获取用户状态统计
export const getStarRatingStat = (data) => {
  return request({
    url: "/loan/partya/push/starRatingStat",

    method: "get",
    params: data
  })
}


export const getPushStatistics = (data) => {
  return request({
    url: "/loan/partya/push/statistics",

    method: "get",
    params: data
  })
}
//获取用户状态统计详情
export const getPushstatisticsDetails = (data) => {
  return request({
    url: "/loan/partya/push/statisticsDetails",
    method: "get",
    params: data
  })
}

export const getStarRatingStatDetails = (data) => {
  return request({
    url: "/loan/partya/push/starRatingStatDetails",
    method: "get",
    params: data
  })
}


//获取用户状态搜索
export const getUserPhoneStatusOption = () => {
  return request({
    url: "/loan/appUserList/abnormal/status",
    method: 'get'
  })
}
//获取用户状态列表
export const getUserPhoneStatusList = (data) => {
  return request({
    url: "/loan/appUserList/abnormal/list",
    method: 'get',
    params:data
  })
}
//获取用户状态搜索
export const getUserPhoneStatusChannel = (data) => {
  return request({
    url: "/loan/appUserList/abnormal/channel",
    method: 'get',
    params:data
  })
}

// 导出渠道用户状态统计数据
export const exportChannelUserStatus = (data) => {
  return request({
    url: "/loan/partya/push/statistics/export",
    method: "get",
    responseType: "arraybuffer",
    params: data,
    timeout: 180000
  })
}
