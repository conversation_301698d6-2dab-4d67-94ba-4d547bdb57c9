<template>
  <el-dialog
    title="渠道分布"
    :visible="visible"
    width="80%"
    append-to-body
    @open="getChannelData"
    :before-close="handleClose"
  >
    <div class="dialog-content">
      <el-table
        :data="tableData"
        border
        max-height="500"
        style="width: 100%"
        @sort-change="handleSortChange"
        ref="tableRef"
      >
        <el-table-column
          prop="channelName"
          label="渠道名称"
          min-width="150"
          fixed="left"
          align="center"
        >
          <template slot-scope="{row}">
            <span v-if="!row.isSummary">{{ row.channelName }}</span>
            <span v-else>合计</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="channelId"
          label="渠道ID"
          min-width="100"
          align="center"
        >
          <template slot-scope="{row}">
            <span v-if="!row.isSummary">{{ row.channelId }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="successTotalPrice"
          label="成功总价"
          min-width="120"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.successTotalPrice || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="successAvePrice"
          label="成功均价"
          min-width="120"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="!scope.row.isSummary">{{ scope.row.successAvePrice || 0 }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="showNum"
          label="曝光数"
          min-width="100"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.showNum || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="successNum"
          label="成功数"
          min-width="100"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.successNum || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="successRate"
          label="成功率"
          min-width="100"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="!scope.row.isSummary">{{ scope.row.successRate ? `${scope.row.successRate}%` : '0%' }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="callbackNum"
          label="回调数"
          min-width="100"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.callbackNum || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="callbackRate"
          label="回调率"
          min-width="100"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="!scope.row.isSummary">{{ scope.row.callbackRate ? `${scope.row.callbackRate}%` : '0%' }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="failNum"
          label="失败数"
          min-width="100"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.failNum || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="checkNum"
          label="撞库数"
          min-width="100"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.checkNum || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="checkSuccessNum"
          label="撞库成功数"
          min-width="120"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.checkSuccessNum || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="checkSuccessRate"
          label="撞库成功率"
          min-width="120"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="!scope.row.isSummary">{{ scope.row.checkSuccessRate ? `${scope.row.checkSuccessRate}%` : '0%' }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import { getHalfProcessAnalysisByChannel } from "@/api/platformProductManagement/halfProcessAnalysis";
import { sum } from "@/utils/calculate";

export default {
  name: 'ChannelDistributionDialog',

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      tableData: []
    }
  },

  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },

    createSummaryRow(data) {
      return {
        channelName: '合计',
        successTotalPrice: sum(data.map(item => Number(item.successTotalPrice || 0))),
        showNum: sum(data.map(item => Number(item.showNum || 0))),
        successNum: sum(data.map(item => Number(item.successNum || 0))),
        callbackNum: sum(data.map(item => Number(item.callbackNum || 0))),
        failNum: sum(data.map(item => Number(item.failNum || 0))),
        checkNum: sum(data.map(item => Number(item.checkNum || 0))),
        checkSuccessNum: sum(data.map(item => Number(item.checkSuccessNum || 0))),
        isSummary: true
      }
    },

    async getChannelData() {
      const res = await getHalfProcessAnalysisByChannel(this.params)
      const list = res.data || []
      if (list.length > 0) {
        this.tableData = [this.createSummaryRow(list), ...list]
      } else {
        this.tableData = list
      }
      this.$nextTick(() => {
        this.$refs.tableRef.clearSort()
      })
    },

    handleSortChange({ prop, order }) {
      if (!this.tableData.length) {
        return
      }

      // 取出合计行
      const summaryRow = this.tableData[0]
      const sortRows = this.tableData.slice(1)

      switch (order) {
        case "ascending":
          sortRows.sort((a, b) => Number(a[prop] || 0) - Number(b[prop] || 0))
          break
        case "descending":
          sortRows.sort((a, b) => Number(b[prop] || 0) - Number(a[prop] || 0))
          break
        default:
          break
      }

      // 将合计行放回首位
      this.tableData = [summaryRow, ...sortRows]
    }
  }
}
</script>

<style scoped>
.dialog-content {
  min-height: 300px;
}

.el-table {
  margin: 10px 0;
}
</style>
