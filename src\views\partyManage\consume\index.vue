<template>
  <div class="app-container">
    <div class="header-moeny">
      <div class="header-text">
        当前账户可用余额：<span class="header-available">{{
          availableAmount
        }}</span>
        元
      </div>
      <el-button
        type="primary"
        icon="el-icon-money"
        size="mini"
        v-hasPermi="['partyaAdmin:order:recharge']"
        @click="handRecharge"
        >立即充值</el-button
      >
      <el-button
        type="primary"
        icon="el-icon-delete"
        size="mini"
        v-hasPermi="['partyaAdmin:order:refund']"
        @click="handRefund"
        >退款</el-button
      >
    </div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item label="日期">
        <el-date-picker
          v-model="queryParams.productDate"
          type="date"
          size="small"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          placeholder="选择日期"
          @change="getList"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="推广名称" prop="productId">
        <el-select
          v-model="queryParams.productId"
          placeholder="请输入选择推广名称"
          clearable
          size="small"
          filterable
          @change="getList"
        >
          <el-option
            :key="i.id"
            :value="i.id"
            :label="i.name"
            v-for="i in productList"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
    </el-form>

    <el-table border :data="consumeData">
      <el-table-column label="日期" prop="productDate" align="center" />
      <el-table-column label="推广名称" prop="productName" align="center" />
      <!-- <el-table-column label="合作价格" prop="cooperationCost" align="center" /> -->
      <el-table-column label="线索量" prop="userNum" align="center" />
      <el-table-column label="消耗" prop="profit" align="center" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="账户充值"
      :visible.sync="payVisible"
      center
      width="35%"
      append-to-body
      :close-on-click-modal="false"
      @close="closePayStuats"
      @submit.native.prevent
    >
      <div v-if="ispay" class="payForm">
        <el-form ref="payForm" :inline="true" :model="payForm" :rules="rules">
          <el-form-item label="请输入充值金额 :" prop="orderAmount">
            <el-input
              v-model="payForm.orderAmount"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              maxlength="6"
            ></el-input>
          </el-form-item>

          <div class="pay-moeny">
            应付金额：<span
              >{{
                payForm.orderAmount
                  ? (payForm.orderAmount * 1).toFixed(2)
                  : "0.00"
              }}元</span
            >
          </div>
        </el-form>

        <div class="handPay">
          <el-button type="primary" @click="payStatus">立即支付</el-button>
        </div>
      </div>
      <div class="payResult" v-if="respay">
        <div class="pay-num">支付单号：{{ orderNo }}</div>
        <div><code-vue :size="size" :value="qrCode" /></div>
      </div>
    </el-dialog>

    <el-dialog
      title="退款"
      :visible.sync="refundVisible"
      center
      width="35%"
      append-to-body
      @close="cancleRefund"
      :close-on-click-modal="false"
    >
      <el-form
        ref="refund"
        label-position="right"
        label-width="180px"
        :model="refundForm"
        :rules="refoundRules"
      >
        <el-form-item label="请输入退款金额" prop="refundAmount">
          <el-input
            v-model="refundForm.refundAmount"
            oninput="value=value.replace(/[^0-9.]/g,'')"
            maxlength="6"
          ></el-input>
        </el-form-item>
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="refundForm.orderNo"></el-input>
        </el-form-item>
        <el-button
          type="primary"
          style="margin-left: 200px"
          @click="refundSubmit"
          >退款</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPartAconsumeList,
  getPartyFirstInfoList,
  partyaAdminreCharge,
  getPartypayStatus,
  partyRefund,
  orderPayProgress,
} from "@/api/partyManage";
import codeVue from "qrcode.vue";
export default {
  data() {
    const validateMoney = (rule, value, callback) => {
      if (!value) {
        callback(new Error("金额不能为空"));
      } else if (value.indexOf(".") != -1 && value.split(".").length > 2) {
        callback(new Error("请输入正确格式的金额")); //防止输入多个小数点
      } else if (value.indexOf(".") != -1 && value.split(".")[1].length > 2) {
        callback(new Error("请输入正确的小数位数")); //小数点后两位
      } else {
        callback();
      }
    };
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      orderNo: "",
      qrCode: "",
      total: 0,
      size: 180,

      payForm: {
        orderAmount: null,
        // payType: "",
      },
      refundForm: {
        orderNo: "",
        refundAmount: "",
      },
      queryParams: {
        productDate: this.getNowDate(),
        productId: null,
        pageNum: 1,
        pageSize: 10,
      },
      consumeData: [],
      productList: [],
      availableAmount: 0,
      payVisible: false,
      refundVisible: false,

      respay: false,
      ispay: true,
      refoundRules: {
        orderNo: [{ required: true, message: "请输入订单号", trigger: "blur" }],
        refundAmount: [
          { required: true, validator: validateMoney, trigger: "blur" },
        ],
      },
      rules: {
        orderAmount: [
          { required: true, validator: validateMoney, trigger: "blur" },
        ],
        payType: [
          { required: true, message: "请选择支付方式", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    refundSubmit() {
      this.$refs.refund.validate((valid) => {
        if (valid) {
          this.refundForm.refundAmount = this.refundForm.refundAmount * 1;
          partyRefund(this.refundForm).then((res) => {

          });
        }
      });
    },

    getNowDate() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    getList() {
      getPartAconsumeList(this.queryParams).then((response) => {
        this.consumeData = response.rows;
        this.total = response.total;
      });
      //获取产品列表和可用余额
      getPartyFirstInfoList().then((res) => {
        this.productList = res.productList;
        this.availableAmount = res.availableAmount;
      });
    },
    payStatus() {
      this.$refs.payForm.validate((valid) => {
        if (valid) {
          partyaAdminreCharge({
            orderAmount: this.payForm.orderAmount * 1,
          }).then((res) => {
            this.ispay = false;
            this.respay = true;
            this.qrCode = res.qrCode;
            this.orderNo = res.orderNo;
            if (this.timer) {
              clearInterval(this.timer);
            }
            this.timer = setInterval(() => {
              getPartypayStatus({ orderNo: res.orderNo }).then((res) => {
                if (res.status == "3") {
                  this.payVisible = false;
                  this.$message.success("支付成功");
                  this.getList();
                  clearInterval(this.timer);
                  this.closePayStuats();
                }
              });
            }, 2000);
          });
        }
      });
    },
    closePayStuats() {
      this.ispay = true;
      this.respay = false;
      this.payForm = {
        orderAmount: null,
      };

      this.$refs.payForm.resetFields();
    },
    handRecharge() {
      this.payVisible = true;
      // orderPayProgress().then((res) => {
      //   if (res.code == 200 && res.status == 0) {
      //     this.respay = false;
      //     this.ispay = true;
      //   } else {
      //     this.$notify({
      //       title: "温馨提示",
      //       message: "请支付未完成订单",
      //       type: "warning",
      //     });
      //     this.respay = true;
      //     this.ispay = false;
      //     this.qrCode = res.qrCode;
      //     this.orderNo = res.orderNo;
      //     if (this.timer) {
      //       clearInterval(this.timer);
      //     }
      //     this.timer = setInterval(() => {
      //       getPartypayStatus({ orderNo: res.orderNo }).then((res) => {
      //         if (res.status == "3") {
      //           this.payVisible = false;
      //           this.getList();
      //           this.$message.success("支付成功");
      //           clearInterval(this.timer);
      //           this.closePayStuats();
      //         }
      //       });
      //     }, 2000);
      //   }
      // });
    },
    handRefund() {
      this.refundVisible = true;
    },
    cancleRefund() {
      this.refundForm = {
        orderNo: "",
        refundAmount: "",
      };
      this.$refs.refund.resetFields();
      this.refundVisible = false;
    },
  },
  components: {
    codeVue,
  },
  watch: {
    payVisible: function (newValue) {
      if (!newValue) {
        if (this.timer) {
          clearInterval(this.timer);
        }
      }
    },
  },

  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.link {
  cursor: pointer;
  text-decoration: underline;
  color: #1e90ff;
}
.header-moeny {
  display: flex;
  margin: 20px 0;
}
.header-text {
  font-size: 24px;
  font-weight: 600;
  margin-right: 20px;
}
.header-available {
  color: red;
}
.payForm {
  height: 260px;
}
.pay-moeny {
  font-size: 20px;
  span {
    color: red;
    font-weight: 800;
  }
}
.pay-type {
  margin-top: 30px;
  font-size: 20px;
  display: flex;
  align-items: center;
  span {
    margin-right: 20px;
  }
}
.handPay {
  margin: 40px 120px;
}
.active {
  border: 1px dashed #1890ff;
}
::v-deep .el-form-item__label {
  font-size: 20px;
  color: #606266;
  font-weight: normal;
}
.payResult {
  height: 260px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.pay-num {
  margin-bottom: 20px;
  font-size: 20px;
}
</style>
