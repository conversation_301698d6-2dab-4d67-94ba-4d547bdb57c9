import request from '@/utils/request'
export const getQwCityList = (params) => {
  return request({
    url: "/loan/PartyB/qwChannelControl",
    method: "get",
    params
  })
}
export const editQwCityStatus = (data) => {
  return request({
    url: "/loan/PartyB/channel/qwCityStatus",
    method: "post",
    data
  })
}
export const editQwCityText = (data) => {
  return request({
    url: "/loan/PartyB/channel/setQwIgnoreCity",
    method: "post",
    data
  })
}
