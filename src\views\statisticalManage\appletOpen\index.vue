<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams">
        <el-form-item label="选择日期">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            :clearable="false"
            @change="getList"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="false"
      :data="tableData"
      style="width: 100%"
      border
    >
      <el-table-column
        prop="channelId"
        label="渠道ID"
        align="center"
      />
      <el-table-column
        prop="channelName"
        label="渠道名称"
        align="center"
      />
      <el-table-column
        prop="appletOpenCount"
        label="小程序打开数"
        align="center"
      />
      <el-table-column
        prop="totalCount"
        label="总数"
        align="center"
      />
      <el-table-column
        prop="percentage"
        label="占比"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.percentage }}%
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleExport(scope.row)"
          >导出</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getOpenRecord, exportAppletOpenData } from '@/api/statisticalManage'
import dayjs from 'dayjs'
import { saveAs } from 'file-saver'

export default {
  name: 'AppletOpen',
  data() {
    return {
      queryParams: {
        dateRange: [dayjs().format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')]
      },
      tableData: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      if (!this.queryParams.dateRange) {
        this.$message.error('请选择日期')
        return
      }
      const [startTime, endTime] = this.queryParams.dateRange;
      getOpenRecord({ startTime, endTime }).then(res => {
        this.tableData = res.data || []
      })
    },
    handleExport(row) {
      if (!this.queryParams.dateRange) {
        this.$message.error('请选择日期')
        return
      }
      
      const [startTime, endTime] = this.queryParams.dateRange
      const channelId = row.channelId
      const channelName = row.channelName
      
      // 调用API文件中定义的导出方法
      exportAppletOpenData({
        startTime,
        endTime,
        channelId,
        channelName
      }).then(res => {
        const blob = new Blob([res])
        saveAs(blob, `${channelName}.txt`)
        this.$message.success('导出成功')
      }).catch(err => {
        this.$message.error('导出失败：' + err.message)
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
</style>
