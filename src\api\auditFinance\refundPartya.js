import request from '@/utils/request'

//获取甲方退款列表
export const getPartyaRefundList = (data) => {
  return request({
    url: '/loan/partya/refund/list',
    method: "get",
    params: data
  })
}
//甲方拒绝退款
export const rejectPartyaRefundOne = (data) => {
  return request({
    url: '/loan/partya/refund/doReject',
    method: "post",
    data

  })
}
//甲方同意退款
export const passPartyaRefundOne = (data) => {
  return request({
    url: '/loan/partya/refund/pass',
    method: "post",
    data
  })
}
//获取甲方审核流程
export const getRefundProcess = (data) => {
  return request({
    url: "/loan/partya/refund/detail",
    method: "get",
    params: data
  })
}
