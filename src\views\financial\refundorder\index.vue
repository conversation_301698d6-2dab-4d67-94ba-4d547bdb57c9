<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="商家订单号" prop="merchantOrderId">
        <el-input size="small" clearable v-model="queryParams.merchantOrderId" placeholder="请输入商家订单号"></el-input>
      </el-form-item>
      <el-form-item label="智付订单号" prop="orderNo">
        <el-input size="small" clearable v-model="queryParams.orderNo" placeholder="请输入智付订单号"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleRefund"
          v-hasPermi="['loan:din_pay:refund:add']">退款</el-button>
      </el-form-item>
    </el-form>

    <el-table border :data="tableList">
      <el-table-column label="创建人" prop="createBy" align="center" />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="平台商ID" prop="merchantCode" align="center" />
      <el-table-column label="商家订单号" prop="merchantOrderId" align="center" />
      <el-table-column label="智付订单号" prop="orderNo" align="center" />
      <el-table-column label="商家退款金额" prop="refundAmount" align="center" />
      <el-table-column label="退款时间" prop="refundTime" align="center" />
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog title="申请分账订单退款" :visible.sync="showRefund" width="700px" append-to-body center
      :close-on-click-modal="false" @close="cancel">
      <el-form ref="formData" label-width="120px" :model="formData" :rules="rules">
        <el-form-item label="商家订单号" prop="merchantOrderId">
          <el-input v-model="formData.merchantOrderId" maxlength="30" oninput="value=value.replace(/[^0-9]/g,'')"
            @blur="formData.merchantOrderId = $event.target.value" placeholder="请输入商家订单号"></el-input>
        </el-form-item>
        <el-form-item label="回退金额" prop="refundAmount">
          <el-input v-model="formData.refundAmount" oninput="value=value.replace(/[^0-9.]/g,'')"
            @blur="formData.refundAmount = $event.target.value" placeholder="请输入回退金额"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRefundMerchantOrderList, applyStbRefund } from "@/api/financial"
export default {
  name: "refundorder",
  data() {
    const validateNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error("金额不能为空"));
      } else if (!/^(([1-9]{1}\d{0,3})|(0{1}))(\.\d{1,2})?$/.test(value)) {
        callback(new Error("输入不合法"));
      } else {
        callback();
      }
    };
    return {
      total: 0,
      showRefund: false,
      tableList: [],
      queryParams: {
        merchantOrderId: "",
        orderNo: "",
        pageNum: 1,
        pageSize: 10,
      },
      formData: {
        merchantOrderId: "",
        refundAmount: ""
      },
      rules: {
        merchantOrderId: [
          { required: true, message: "商家订单号", trigger: "blur" },
        ],
        refundAmount: [
          { required: true, validator: validateNumber, trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    //查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleRefund() {
      this.showRefund = true
    },
    //确认退款
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          applyStbRefund(this.formData).then(res => {
            this.cancel()
            this.getList()
            this.$message.success("操作成功")
          })
        }
      })
    },
    //取消退款
    cancel() {
      this.formData = {
        merchantOrderId: "",
        refundAmount: ""
      }
      this.showRefund = false
      this.$refs.formData.resetFields()
    },

    getList() {
      getRefundMerchantOrderList(this.queryParams).then(res => {
        this.tableList = res.rows
        this.total = res.total
      })
    },

  },
  mounted() {
    this.getList();

  },
};
</script>

<style lang="scss" scoped>
.priview {
  width: 300px;

  img {
    width: 85px;
    height: 50px;
    padding: 0 10px;
  }
}

.replayImage {
  border: 0;
  max-height: 100vh;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
</style>
