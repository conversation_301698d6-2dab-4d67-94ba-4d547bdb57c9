/**
* v-hasPermi 操作权限处理
* Copyright (c) 2019 ruoyi
*/

import store from '@/store'
export function hasBool(hasPermi) {

    if (hasPermi) {
        const all_permission = "*:*:*";
        const permissions = store.getters && store.getters.permissions
        return permissions.some(permission => {
            return all_permission === permission || hasPermi.includes(permission)
        })
    } else {
        return false
    }
}