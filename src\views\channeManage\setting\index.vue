<template>
  <div class="app-container">

    <div class="preview">
      <div>
        <div class="tips">渠道ID:{{ $route.query.id }} --- 渠道名称:{{ $route.query.name }}</div>
        <el-form :rules="rules" :model="formData" ref="formData" label-position="left" label-width="120px">
          <el-form-item label="渠道模板" prop="templateId">
            <el-select clearable v-model="formData.templateId" label="渠道模板" placeholder="请选择渠道模板"
              @change="handleChangePreview" filterable>
              <el-option v-for="item in channleType" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="IOS跳转地址" prop="iosurl">
            <el-input clearable placeholder="请输入IOS跳转地址" v-model="formData.iosurl"></el-input>
          </el-form-item>
          <el-form-item label="安卓跳转地址" prop="andoridurl">
            <el-input clearable placeholder="请输入安卓跳转地址" v-model="formData.andoridurl"></el-input>
          </el-form-item>
          <el-form-item label="自定义步骤" prop="process">
            <div style="display: flex">
              <el-input clearable placeholder="请输入自定义步骤（步骤一,步骤二）" v-model="formData.process"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="自定义HTML">
            <div id="editor" />
          </el-form-item>
          <el-form-item>
            <el-button @click="submitForm" type="primary">提交</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="iframe">
        <iframe v-if="src" :src="src" frameborder="0" class="iframe"></iframe>
      </div>
    </div>
  </div>
</template>

<script>
import editor from "wangeditor";
import {
  getConfigDetailed,
  addConfigDetailed,
  getChannelType,
} from "@/api/channeManage/domain";
import { uploadFile, delLoadFile } from "@/api/operate/encyclopedias"

export default {
  data() {
    return {
      imgNewSrc: [],
      imgOldSrc: [],
      fileLists: [],
      channleType: [],
      projectList: [],
      src: "",
      formData: {
        andoridurl: "",
        html: "",
        id: "",
        iosurl: "",
        process: "",
        templateId: "",
      },
      rules: {
        templateId: [
          {
            required: true,
            message: "请输入渠道模板",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {

    onchange(html) {
      this.formData.html = html
      this.imgOldSrc = this.imgNewSrc
      this.imgNewSrc = this.getSrc(html)
      if ((this.imgNewSrc.length <= this.imgOldSrc.length)) {
        if (!this.imgOldSrc.length) return
        let list = this.imgOldSrc.filter((n) => {
          return this.imgNewSrc.indexOf(n) === -1
        })
        if (list.length) {
          delLoadFile({ list: list })
        }
      }


    },
    /**
     * 取出区域内所有img的src
     */
    getSrc(html) {
      var imgReg = /<img.*?(?:>|\/>)/gi
      // 匹配src属性
      var srcReg = /src=[\\"]?([^\\"]*)[\\"]?/i
      var arr = html.match(imgReg)
      let imgs = []
      if (arr) {
        for (let i = 0; i < arr.length; i++) {
          var src = arr[i].match(srcReg)[1]
          imgs.push(src)
        }
      }
      return imgs
    },

    submitForm() {
      this.formData.id = this.$route.query.id;
      this.formData.html = this.editor.txt.html();

      //    let data=new FormData()
      //    for(let i in this.formData){
      //        data.append(i,this.formData[i])
      //    }
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.formData.process) {
            this.formData.process = this.formData.process.replace(/，/g, ",");
          }
          addConfigDetailed(this.formData).then((res) => {
            if (res.code == 200) {
              this.$router.push("/xmxrChanneManage/channeList");
              this.$message.success("设置成功");
            }
          });
        }
      });
    },
    handleChangePreview(id) {
      this.src = "";
      if (!id) return;
      let templateSig = this.channleType.filter((item) => item.id == id)[0]
        .templateSig;

      setTimeout(() => {
        this.src = process.env.VUE_APP_BASE_HTTP + `${this.$route.query.i}&templateSig=${templateSig}`
      }, 100);
    },
  },
  mounted() {
    this.editor = new editor("#editor");
    this.editor.config.height = 500;
    this.editor.config.zIndex = 10;
    this.editor.config.colors = ["#000000", "#333333", "#666666", "#999999", "#cccccc", "#aaa", , "#bbb", "#ccc", "#ddd", "#ffffff"];
    this.editor.config.fontNames = [
      "黑体",
      "仿宋",
      "楷体",
      "标楷体",
      "华文仿宋",
      "华文楷体",
      "宋体",
      "微软雅黑",
      "Arial",
      "Tahoma",
      "Verdana",
      "Times New Roman",
      "Courier New",
    ];
    this.editor.config.uploadImgMaxSize = 10 * 1024 * 1024
    this.editor.config.customUploadImg = function (resultFiles, insertImgFn) {
      let file = resultFiles[0]
      let data = new FormData()
      data.append('file', file)
      uploadFile(data).then(res => {
        insertImgFn(res.msg)
      })
    }
    this.editor.config.onchange = html => {
      this.onchange(html)
    };


    this.editor.create();
    getConfigDetailed({ id: this.$route.query.id }).then((res) => {
      this.editor.txt.html(res.data.html);
      this.formData.iosurl = res.data.iosurl;
      this.formData.andoridurl = res.data.andoridurl;
      this.formData.templateId = res.data.templateId;
      this.formData.process = res.data.process;
      if (!res.data.templateSig) return;
      this.src = process.env.VUE_APP_BASE_HTTP + `${this.$route.query.i}&templateSig=${res.data.templateSig}`
    });
    getChannelType().then((res) => {
      this.channleType = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
.tips {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
  font-weight: bold;
}

.iframe {
  width: 500px;
  padding: 20px;
  height: 800px;
  overflow: scroll;
  margin-left: 20px;

  &::-webkit-scrollbar {
    display: none;
  }

}

.preview {
  display: flex;
}
</style>
