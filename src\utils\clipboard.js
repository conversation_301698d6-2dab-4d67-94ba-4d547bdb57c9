function copyTextNewAPI(text) {
  return navigator.clipboard
    .writeText(text)
    .then(() => {
      console.log("文本已成功复制（使用新API）");
    })
    .catch((err) => {
      console.error("无法使用新API复制文本：", err);
      return Promise.reject(err);
    });
}

function copyTextOldAPI(text) {
  return new Promise((resolve, reject) => {
    const textarea = document.createElement("textarea");
    textarea.value = text;
    textarea.style.position = "fixed"; // 避免在页面上滚动
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();

    try {
      const successful = document.execCommand("copy");
      if (successful) {
        console.log("文本已成功复制（使用旧API）");
        resolve();
      } else {
        throw new Error("复制失败");
      }
    } catch (error) {
      console.error("无法使用旧API复制文本：", error);
      reject(error);
    } finally {
      document.body.removeChild(textarea);
    }
  });
}

function copyText(text) {
  if (navigator.clipboard && navigator.clipboard.writeText) {
    return copyTextNewAPI(text).catch(() => copyTextOldAPI(text));
  } else {
    return copyTextOldAPI(text);
  }
}

export default {
    copyText
}