import request from '@/utils/request'

// 获取平台产品对比列表
export const getPlatformProductCompareList = (data) => {
  return request({
    url: `/loan/product/queryPlatformProductCompareList?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    method: "post",
    data
  });
};

// 更新产品对比价格
export const updateProductComparePrice = (data) => {
  return request({
    url: "/loan/product/updateProductComparePrice",
    method: "post",
    data: data
  });
};
