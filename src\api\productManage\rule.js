
import request from '@/utils/request'
//新增商品规则类型
export const addRuleType = (data) => {
    return request({
        url: "/loan/ruleType/add",
        method: "post",
        data
    })
}
//商品规则列表
export const getRuleTypeList = (data) => {
    return request({
        url: "/loan/ruleType/list",
        method: "get",
        params: data
    })
}
//获取规则类型详信息
export const getRuleDetail = () => {
    return request({
        url: "/loan/ruleType/query",
        method: "get"
    })
}