<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="渠道ID" prop="name">
        <el-input
          size="small"
          clearable
          placeholder="请输入渠道ID"
          @keyup.enter.native="handleQuery"
          @change="handleQuery"
          v-model="queryParams.channelId"
        ></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="name">
        <el-input
          size="small"
          clearable
          placeholder="请输入备注"
           @change="handleQuery"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.remark"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          style="margin-bottom: 20px"
          @click="addAgreementType"
          size="small"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="list" border>
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="渠道ID" align="center" prop="channelId" />
      <el-table-column label="项目地址" align="center" prop="filename" />
      <el-table-column label="版本号" align="center" prop="versions" />
      <el-table-column label="更新类容" prop="updateContent" align="center" />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="修改时间" prop="updateTime" align="center" />
      <el-table-column label="备注" prop="remark" align="center" />
      <el-table-column label="状态" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.state == 1 ? "新版本" : "旧版本" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button type="text" @click="handleEdit(row)" icon="el-icon-edit"
              >修改</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="isadd ? '新增项目' : '修改项目'"
      :visible.sync="agreementTypeAvisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="渠道ID" prop="channelId">
          <el-input v-model="formData.channelId" placeholder="请输入渠道ID" />
        </el-form-item>
        <el-form-item label="更新类容" prop="updateContent">
          <el-input
            v-model="formData.updateContent"
            placeholder="请输入更新类容"
          />
        </el-form-item>
        <el-form-item label="项目地址" prop="filename">
          <el-input v-model="formData.filename" placeholder="请输入项目地址" />
        </el-form-item>
        <el-form-item label="版本号" prop="versions">
          <el-input v-model="formData.versions" placeholder="请输入版本号" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio v-model="formData.state" :label="0">旧版本</el-radio>
          <el-radio v-model="formData.state" :label="1">新版本</el-radio>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAppInfo,
  addAppInfo,
  editAppInfo,
  delProjectOne,
} from "@/api/operate/project";
export default {
  data() {
    return {
      agreementTypeAvisible: false,
      isadd: true,
      total: 0,
      list: [],
      formData: {
        channelId: "",
        filename: "",
        state: "",
        updateContent: "",
        versions: "",
        remark:""
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        channelId: "",
        remark: "",
      },
      rules: {
        channelId: [
          { required: true, message: "请输入渠道ID", trigger: "blur" },
        ],
        filename: [
          { required: true, message: "请输入文件路径", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    addAgreementType() {
      this.agreementTypeAvisible = true;
      this.isadd = true;
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    getList() {
      getAppInfo(this.queryParams).then((res) => {
        this.list = res.rows;
        this.total = res.total;
        if (this.list.length == 0 && this.queryParams.pageNum > 1) {
          this.queryParams.pageNum = this.queryParams.pageNum - 1;
          this.getList();
          return;
        }
      });
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isadd) {
            addAppInfo(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.cancel();
                this.getList();
              }
            });
          } else {
            editAppInfo(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.cancel();
                this.getList();
              }
            });
          }
        }
      });
    },
    cancel() {
      this.agreementTypeAvisible = false;
      this.formData = {
        channelId: "",
        filename: "",
        state: "",
        updateContent: "",
        versions: "",
        remark:""
      };
      this.$refs.formData.resetFields();
    },
    handleEdit(row) {
      this.agreementTypeAvisible = true;
      this.isadd = false;
      this.formData.id = row.id;
      this.formData.channelId = row.channelId;
      this.formData.filename = row.filename;
      this.formData.state = row.state * 1;
      this.formData.updateContent = row.updateContent;
      this.formData.versions = row.versions;
      this.formData.remark = row.remark;
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
