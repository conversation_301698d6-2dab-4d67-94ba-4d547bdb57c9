<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="value1"
          size="small"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="推广名称">
        <el-input
          size="mini"
          placeholder="请输入推广名称"
          v-model="queryParams.productName"
          @keyup.enter.native="handleQuery"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border>
      <el-table-column label="推广ID" prop="productId" align="center" />
      <el-table-column label="推广名称" prop="productName" align="center" />
      <el-table-column label="推广类型" align="center" prop="productTypeName">
      </el-table-column>
      <el-table-column
        label="合作方式"
        align="center"
        prop="cooperationModeName"
      >
      </el-table-column>
      <el-table-column label="商务" prop="sysUserNickName" align="center" />
      <el-table-column
        v-if="hasCooperationCost"
        label="推广价格"
        prop="cooperationCost"
        align="center"
      />
      <el-table-column
        label="匹配价格"
        prop="matchingPriceSort"
        align="center"
      />
      <el-table-column
        label="点击申请数"
        prop="applyBehaviorNum"
        align="center"
      />
      <el-table-column label="申请成功数" prop="applyNum" align="center" />
      <!-- <el-table-column label="进件数" prop="apiPushNum" align="center" />
      <el-table-column label="进件率" prop="productIntoARate" align="center">
        <template  slot-scope="{row}">
          <div>
            {{
              row.applyBehaviorNum
                ? ((row.apiPushNum / row.applyBehaviorNum) * 100).toFixed(2)
                : 0
            }}%
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="收益" prop="profit" align="center" />
      <el-table-column
        label="操作"
        align="center"
        v-if="checkPermi(['loan:productChannelProportion:export'])"
      >
        <template slot-scope="row">
          <div>
            <el-button v-if="row.$index" type="text" @click="hanldeExport(row)"
              >导出</el-button
            >
            <el-button v-if="row.$index" type="text" @click="hanldeDetail(row)"
              >查看详情</el-button
            >
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="H5收益" prop="h5Profit" align="center" /> -->
    </el-table>
    <!-- <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    /> -->
    <el-dialog
      :visible.sync="dialogVisible"
      title="详情"
      width="80%"
      append-to-body
    >
      <el-table :data="detailList" border>
        <el-table-column
          label="渠道ID"
          align="center"
          prop="channelId"
        ></el-table-column>
        <el-table-column
          label="渠道名称"
          align="center"
          prop="channelName"
        ></el-table-column>
        <el-table-column
          label="产品名称"
          align="center"
          prop="productName"
        ></el-table-column>
        <el-table-column
          label="总数"
          align="center"
          prop="total"
        ></el-table-column>
        <el-table-column
          label="占比"
          align="center"
          prop="proportion"
        ></el-table-column>

      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  getNewProductList,
  exportChannelProduct,
  exportChannelexportProductList,

} from "@/api/statisticalManage";
import { checkPermi } from "@/utils/permission";
export default {
  name: "Product",
  data() {
    return {
      dialogVisible: false,
      checkPermi,
      value1: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      type: {
        1: "线下-助贷",
        2: "线上-贷超",
        3: "线上-大额",
      },
      coopType: {
        1: "链接合作",
        2: "API",
        3: "撞库 + H5",
        4: "撞库 + 连登H5",
        5: "仅连登",
      },
      total: 0,
      queryParams: {
        productName: "",
        pageNum: 1,
        pageSize: 10,
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
      },
      dataList: [],
      detailList:[],
    };
  },

  computed: {
    // 有合作价格
    hasCooperationCost() {
      return this.dataList.some((item) => item.cooperationCost && item.cooperationCost > 0);
    }
  },

  methods: {
    getTotal(arr, type) {
      return arr.reduce((pre, data) => {
        return pre + data[type];
      }, 0);
    },
    handleQuery() {
      if (this.value1 !== null) {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    getList() {
      getNewProductList(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total;
        this.dataList.unshift({
          productId: "合计",
          applyNum: this.getTotal(this.dataList, "applyNum"),
          apiPushNum: this.getTotal(this.dataList, "apiPushNum"),
          applyBehaviorNum: this.getTotal(this.dataList, "applyBehaviorNum"),
          profit: this.getTotal(this.dataList, "profit"),
        });
      });
    },
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    hanldeExport(row) {
      if (!this.value1[0]) return this.$message.error("请选择时间");
      exportChannelProduct({
        productId: row.row.productId,
        startTime: this.value1[0],
        endTime: this.value1[1],
      }).then((res) => {
        let a = document.createElement("a");
        let blob = new Blob([res], { type: "application/vnd.ms-excel" });
        let objectUrl = URL.createObjectURL(blob);
        a.setAttribute("href", objectUrl);
        a.setAttribute("download", `${row.row.productName}.xlsx`);
        a.click();
        this.$message.success("导出成功");
      });
    },
    hanldeDetail(row) {
      if (!this.value1[0]) return this.$message.error("请选择时间");
      this.detailList=[]
      exportChannelexportProductList({
        productId: row.row.productId,
        startTime: this.value1[0],
        endTime: this.value1[1],
      }).then((res) => {

        this.detailList=res.data||[]
        this.dialogVisible = true;
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped></style>
