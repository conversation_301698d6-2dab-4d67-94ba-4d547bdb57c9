<template>
  <div class="config-sidebar">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <div class="redis-logo">
        <i class="fas fa-database" style="color: #dc382d; font-size: 24px;"></i>
        <span class="logo-text">Redis 配置管理</span>
      </div>
      <div class="header-actions">
        <el-tooltip content="刷新配置" placement="bottom">
          <i class="fas fa-sync-alt refresh-icon" @click="handleRefresh"></i>
        </el-tooltip>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索配置项..."
        size="small"
        class="search-input"
        @input="handleSearch"
        clearable
      >
        <i class="fas fa-search" style="font-size: 16px;" slot="prefix"></i>
      </el-input>
    </div>

    <!-- 配置列表头部 -->
    <div class="config-list-header">
      <span class="sidebar-title">配置项</span>
      <el-badge :value="filteredConfigList.length" class="config-count" />
    </div>

    <!-- 配置列表内容 -->
    <div class="sidebar-content">
      <el-tree
        ref="configTree"
        :data="filteredConfigList"
        :props="{ label: 'name', children: 'children' }"
        node-key="key"
        @node-click="handleConfigSelect"
        highlight-current
        :expand-on-click-node="false"
        class="config-tree"
      >
        <span class="tree-node" slot-scope="{ node, data }">
          <div class="node-icon">
            <i class="fas fa-cog" style="font-size: 16px;"></i>
          </div>
          <div class="node-content">
            <span class="node-label">{{ data.name }}</span>
            <span class="node-key">{{ data.key }}</span>
          </div>
        </span>
      </el-tree>

      <!-- 空状态 -->
      <div v-if="filteredConfigList.length === 0" class="empty-container">
        <div class="empty-icon">
          <i class="fas fa-folder-open" style="font-size: 48px;"></i>
        </div>
        <span class="empty-text">暂无配置数据</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConfigSidebar',
  props: {
    configList: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      searchKeyword: '',
      filteredConfigList: []
    }
  },
  
  watch: {
    configList: {
      handler(newList) {
        this.filteredConfigList = [...newList]
        this.handleSearch()
      },
      immediate: true
    }
  },
  
  methods: {
    // 处理搜索
    handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.filteredConfigList = [...this.configList]
        return
      }

      const keyword = this.searchKeyword.toLowerCase()
      this.filteredConfigList = this.configList.filter(config =>
        config.name.toLowerCase().includes(keyword) ||
        config.key.toLowerCase().includes(keyword)
      )
    },
    
    // 处理刷新
    handleRefresh() {
      this.$emit('refresh')
    },
    
    // 处理配置选择
    handleConfigSelect(config) {
      if (!config.key) return
      this.$emit('select', config)
    }
  }
}
</script>

<style lang="scss" scoped>
.config-sidebar {
  width: 320px;
  background: transparent;
  border-right: 1px solid #e8eaed;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;

  .sidebar-header {
    padding: 12px 20px;
    border-bottom: 1px solid #e8eaed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;

    .redis-logo {
      display: flex;
      align-items: center;
      gap: 8px;

      .logo-text {
        font-size: 18px;
        font-weight: 600;
        color: #202124;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;

      .refresh-icon {
        font-size: 16px;
        color: #5f6368;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:hover {
          background: #f1f3f4;
          color: #2196f3;
        }
      }
    }
  }

  .search-section {
    padding: 12px 16px;
    border-bottom: 1px solid #e8eaed;
    background: #ffffff;

    .search-input {
      width: 100%;

      ::v-deep .el-input__inner {
        background: #f1f3f4;
        border: 1px solid #e8eaed;
        color: #202124;
        border-radius: 20px;
        padding-left: 40px;
        transition: all 0.3s ease;

        &:focus {
          border-color: #2196f3;
          box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
        }
      }

      ::v-deep .el-input__prefix {
        left: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #9aa0a6;
        transition: color 0.3s ease;
      }
    }
  }

  .config-list-header {
    padding: 12px 20px;
    border-bottom: 1px solid #e8eaed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;

    .sidebar-title {
      font-size: 14px;
      font-weight: 600;
      color: #202124;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .config-count {
      ::v-deep .el-badge__content {
        background: #dc382d;
        border: none;
        font-size: 11px;
        height: 18px;
        line-height: 18px;
        min-width: 18px;
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;

    .config-tree {
      background: transparent;

      ::v-deep .el-tree-node {
        .el-tree-node__content {
          height: 44px;
          padding: 0 12px;
          border-radius: 6px;
          margin-bottom: 4px;
          border: 1px solid transparent;
          transition: all 0.2s ease;

          &:hover {
            background: #f1f3f4;
            border-color: #e8eaed;
          }

          &.is-current {
            background: linear-gradient(135deg, #dc382d10, #dc382d05);
            border-color: #dc382d;
            color: #dc382d;

            .node-icon {
              color: #dc382d;
            }
          }
        }
      }

      .tree-node {
        display: flex;
        align-items: center;
        width: 100%;

        .node-icon {
          width: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #9aa0a6;
          transition: color 0.2s ease;
        }

        .node-content {
          flex: 1;
          min-width: 0;

          .node-label {
            font-size: 14px;
            font-weight: 500;
            color: #202124;
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .node-key {
            font-size: 12px;
            color: #9aa0a6;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 2px;
          }
        }
      }
    }

    .empty-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #9aa0a6;

      .empty-icon {
        margin-bottom: 16px;
      }

      .empty-text {
        font-size: 14px;
      }
    }
  }
}

// 滚动条样式
.config-sidebar .sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.config-sidebar .sidebar-content::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

.config-sidebar .sidebar-content::-webkit-scrollbar-thumb {
  background: #e8eaed;
  border-radius: 3px;
  
  &:hover {
    background: #9aa0a6;
  }
}
</style>