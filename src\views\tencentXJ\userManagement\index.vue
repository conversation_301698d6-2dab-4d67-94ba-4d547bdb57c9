<script>
import { fetchUserList, moveToPending, getConfig, updateConfig } from '@/api/tencentXJ/userManagement'
import { getCityList } from '@/api/statisticalManage'
import dayjs from 'dayjs'
import Pagination from "@/components/Pagination";
import EditUser from './components/editUser.vue'
import MatchAndPush from './components/matchAndPush.vue'

import {
  HOUSE_TYPES,
  VEHICLE_TYPES,
  PROVIDENT_FUND_TYPES,
  SESAME_CREDIT_TYPES,
  SOCIAL_SECURITY_TYPES,
  CREDIT_STATUS_TYPES,
  VOCATION_TYPES,
  INSURANCE_TYPES,
  SEX_TYPES
} from '@/const/userQualification'
import { PLATFORM_TYPES } from '@/const/platformId';

export default {
  name: 'UserManagement',
  components: {
    Pagination,
    EditUser,
    MatchAndPush
  },
  data() {
    return {
      PLATFORM_TYPES,
      tableData: [],
      cityList: [],
      queryParams: {
        dateRange: [],
        city: [],
        phone: '',
        creativeName: '',
        status: '0', // 默认为"未推送"
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      editDialogVisible: false,
      currentUser: null,
      datePickerOptions: {
        disabledDate(time) {
          const end = dayjs().endOf('day');
          const start = end.subtract(2, 'day').startOf('day');
          return dayjs(time).isBefore(start) || dayjs(time).isAfter(end);
        }
      },
      statusOptions: [ // 更新推送状态选项
        { value: '0', label: '未推送' },
        { value: '1', label: '推送成功' },
        { value: '2', label: '推送失败' },
        { value: '3', label: '待处理' }
      ],
      selectedRows: [],
      ratioDialogVisible: false,
      ratioForm: {
        ratio: 0
      },
      ratioRules: {
        ratio: [
          { required: true, message: '请输入比例', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.fetchCityData();
    this.setDefaultDate();
    this.loadUserData();
  },
  methods: {
    async loadUserData() {
      if (!this.queryParams.dateRange ||!this.queryParams.dateRange.length) {
        this.$message.error('请选择日期范围');
        return;
      }
      const [startTime, endTime] = this.queryParams.dateRange;
      const city = this.queryParams.city.length ? this.queryParams.city[this.queryParams.city.length - 1] : '';
      const { pageNum, pageSize, phone, creativeName, status } = this.queryParams;
      const res = await fetchUserList({ startTime, endTime, city, pageNum, pageSize, phone, creativeName, status });
      this.tableData = res.rows;
      this.total = res.total;
    },
    async fetchCityData() {
      const res = await getCityList();
      this.cityList = res.data.map((item) => {
        if (item.citys) {
          return {
            value: item.name,
            label: item.name,
            children: item.citys.map((citem) => ({
              value: citem.name,
              label: citem.name
            }))
          };
        } else {
          return {
            value: item.name,
            label: item.name
          };
        }
      });
    },
    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format('YYYY-MM-DD 00:00:00');
      const endTime = today.format('YYYY-MM-DD 23:59:59');
      this.queryParams.dateRange = [startTime, endTime];
    },
    handleSearch() {
      this.loadUserData();
    },
    getDisplayValue(field, value) {
      const mappings = {
        houseId: HOUSE_TYPES,
        vehicleId: VEHICLE_TYPES,
        providentId: PROVIDENT_FUND_TYPES,
        sesameId: SESAME_CREDIT_TYPES,
        socialId: SOCIAL_SECURITY_TYPES,
        overdueId: CREDIT_STATUS_TYPES,
        vocationId: VOCATION_TYPES,
        insureId: INSURANCE_TYPES,
        sex: SEX_TYPES
      };
      return mappings[field] ? mappings[field][value] : value;
    },
    handleEdit(row) {
      this.$refs.editUserDialog.open(row);
    },
    handleMatchAndPush(row) {
      this.$refs.matchAndPushDialog.open(row);
    },
    handleEditSuccess() {
      this.loadUserData();
    },
    getStatusLabel(status) {
      const statusMap = {
        '0': '未推送',
        '1': '推送成功',
        '2': '推送失败'
      };
      return statusMap[status] || '未知状态';
    },
    getStatusType(status) {
      const statusTypeMap = {
        '0': 'info',
        '1': 'success',
        '2': 'danger'
      };
      return statusTypeMap[status] || 'info';
    },
    async moveToPending() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择至少一行数据');
        return;
      }
      const clueIds = this.selectedRows.map(row => row.clueId).join(',');
      await moveToPending({clueIds});
      this.$message.success('选中行已移入待处理');
      this.loadUserData();
    },
    handleRatioConfig() {
      this.fetchConfig();
      this.ratioDialogVisible = true;
    },
    async fetchConfig() {
      const res = await getConfig();
      if (res.data) {
        this.ratioForm.ratio = res.data;
      }
    },
    async handleRatioSubmit() {
      this.$refs.ratioForm.validate(async valid => {
        if (valid) {
          await updateConfig({ rate: this.ratioForm.ratio });
          this.$message.success('配置更新成功');
          this.ratioDialogVisible = false;
        }
      });
    },
    handleClose() {
      this.$refs.ratioForm.resetFields();
      this.ratioDialogVisible = false;
    }
  }
};
</script>

<template>
  <div class="app-container">
    <el-form inline :model="queryParams" size="small">
      <el-form-item label="日期" prop="time">
        <el-date-picker v-model="queryParams.dateRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          @change="handleSearch" :clearable="false" :picker-options="datePickerOptions">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="城市">
        <el-cascader v-model="queryParams.city" :options="cityList" @change="handleSearch" clearable filterable
          :show-all-levels="false">
        </el-cascader>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="queryParams.phone" placeholder="请输入手机号" @keyup.enter.native="handleSearch"
          clearable></el-input>
      </el-form-item>
      <el-form-item label="推送状态">
        <el-select v-model="queryParams.status" placeholder="请选择推送状态" @change="handleSearch">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button type="primary" @click="moveToPending">移入待处理</el-button>
        <el-button type="primary" @click="handleRatioConfig">接入比例配置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" style="width: 100%" @selection-change="selectedRows = $event" ref="userTable">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="createTime" label="线索时间"></el-table-column>
      <el-table-column prop="name" label="用户姓名"></el-table-column>
      <el-table-column prop="sex" label="性别"
        :formatter="(row, column, cellValue) => getDisplayValue('sex', cellValue)"></el-table-column>
      <el-table-column prop="age" label="年龄"></el-table-column>
      <el-table-column prop="city" label="城市"></el-table-column>
      <el-table-column prop="channelInfo" label="渠道"></el-table-column>
      <el-table-column label="最终申请结果">
        <template v-slot="{ row }">
          <div v-if="row.applyResult && row.applyResult.lastApplyResult">
            <el-tag size="mini" v-if="row.applyResult.lastApplyResult.applySuccess" type="success">成功</el-tag>
            <el-tag size="mini" v-else type="danger">失败</el-tag>
            {{ PLATFORM_TYPES[row.applyResult.lastApplyResult.platformTypes] }}-
            {{ row.applyResult.lastApplyResult.productId }}-
            {{ row.applyResult.lastApplyResult.productName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template slot-scope="scope">
          <!-- <el-button type="text" @click="handleEdit(scope.row)">修改</el-button> -->
          <el-button type="text" @click="handleMatchAndPush(scope.row)">操作</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="loadUserData" />
    <edit-user ref="editUserDialog" @success="handleEditSuccess" />
    <match-and-push ref="matchAndPushDialog" @success="handleEditSuccess" @close="handleEditSuccess" />
    <el-dialog title="接入比例配置" :visible.sync="ratioDialogVisible" width="400px" @closed="handleClose">
      <el-form :model="ratioForm" :rules="ratioRules" ref="ratioForm" label-width="80px">
        <el-form-item label="比例" prop="ratio">
          <el-input-number 
            v-model="ratioForm.ratio"
            :min="0"
            :max="100"
            :step="1"
            :precision="0"
            :controls="false"
            placeholder="请输入比例">
          </el-input-number>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleRatioSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
</style>
