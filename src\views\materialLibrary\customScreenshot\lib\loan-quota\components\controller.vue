<template>
  <div class="controller-container">
    <!-- 使用截图按钮组件 -->
    <capture-button 
      :preview-url="previewUrl"
      :device-os="form.device.os"
      @capture-screenshot="$emit('capture-screenshot')"
    />
    
    <el-form :model="form" label-width="90px" size="small">
      <!-- 使用平台类型设置组件 -->
      <platform-type-setting 
        :deviceConfig="form.device" 
        @update:platform="updatePlatform" 
      />
      
      <!-- 使用统一的状态栏设置组件 -->
      <status-bar-setting 
        :statusBarConfig="form.statusBar" 
        @update:statusBar="updateStatusBar" 
      />

      <!-- 贷款额度特定配置项 -->
      <el-divider>贷款额度配置</el-divider>
      
      <!-- 贷款额度 -->
      <el-form-item label="贷款金额" prop="loanAmount">
        <el-input v-model="form.loanAmount" placeholder="请输入贷款金额" @change="handleConfigChange"></el-input>
        <div class="tip-text">默认值：2,094.01</div>
      </el-form-item>
      
      <!-- 备用金配置 -->
      <el-form-item label="备用金描述" prop="reserveDesc">
        <el-input v-model="form.reserveDesc" placeholder="请输入备用金描述" @change="handleConfigChange"></el-input>
        <div class="tip-text">默认值：已获得额外专享额度</div>
      </el-form-item>
      
      <!-- 额度券配置 -->
      <el-form-item label="额度券描述" prop="couponDesc">
        <el-input v-model="form.couponDesc" placeholder="请输入额度券描述" @change="handleConfigChange"></el-input>
        <div class="tip-text">默认值：提升可用额度</div>
      </el-form-item>
      
      <!-- 其他额度配置 -->
      <el-divider>其他额度配置</el-divider>
      
      <div v-for="(item, index) in form.otherQuotas" :key="index" class="other-quota-item">
        <div class="other-quota-header">
          <span>额度项 {{ index + 1 }}</span>
          <el-button 
            type="danger" 
            size="mini" 
            icon="el-icon-delete" 
            circle 
            @click="removeOtherQuota(index)"
            v-if="form.otherQuotas.length > 1"
          ></el-button>
        </div>
        
        <el-form-item label="名称" :prop="`otherQuotas[${index}].name`">
          <el-input v-model="item.name" placeholder="请输入名称" @change="handleConfigChange"></el-input>
        </el-form-item>
        
        <el-form-item label="描述" :prop="`otherQuotas[${index}].desc`">
          <el-input v-model="item.desc" placeholder="请输入描述" @change="handleConfigChange"></el-input>
        </el-form-item>
        
        <el-form-item label="图标URL" :prop="`otherQuotas[${index}].iconUrl`">
          <el-input v-model="item.iconUrl" placeholder="请输入图标URL" @change="handleConfigChange"></el-input>
        </el-form-item>
      </div>
      
      <el-button type="primary" size="small" @click="addOtherQuota" style="margin-bottom: 20px;">添加额度项</el-button>

    </el-form>
  </div>
</template>

<script>
import StatusBarSetting from '../../components/StatusBarSetting.vue'
import PlatformTypeSetting from '../../components/PlatformTypeSetting.vue'
import CaptureButton from '../../components/CaptureButton.vue'

export default {
  name: 'LoanQuotaController', 
  components: {
    StatusBarSetting,
    PlatformTypeSetting,
    CaptureButton
  },
  props: {
    previewUrl: {
      type: String,
      default: null
    },
    configData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      form: this.deepClone(this.configData)
    }
  },
  watch: {
    configData: {
      handler(newVal) {
        this.form = this.deepClone(newVal);
      },
      deep: true
    }
  },
  methods: {
    updatePlatform(deviceConfig) {
      this.form.device = deviceConfig;
      this.handleConfigChange();
    },
    updateStatusBar(statusBarConfig) {
      this.form.statusBar = statusBarConfig;
      this.handleConfigChange();
    },
    handleConfigChange() {
      // 使用深拷贝确保传递的是新对象
      this.$emit('update:config', this.deepClone(this.form));
    },
    // 添加其他额度项
    addOtherQuota() {
      if (!this.form.otherQuotas) {
        this.form.otherQuotas = [];
      }
      
      this.form.otherQuotas.push({
        name: '',
        desc: '',
        iconUrl: ''
      });
      
      this.handleConfigChange();
    },
    // 删除其他额度项
    removeOtherQuota(index) {
      this.form.otherQuotas.splice(index, 1);
      this.handleConfigChange();
    },
    // 内部实现的深拷贝函数
    deepClone(obj) {
      if (obj === null || typeof obj !== 'object') {
        return obj;
      }
      
      // 处理Date
      if (obj instanceof Date) {
        return new Date(obj.getTime());
      }
      
      // 处理Array
      if (Array.isArray(obj)) {
        return obj.map(item => this.deepClone(item));
      }
      
      // 处理Object
      const clonedObj = {};
      Object.keys(obj).forEach(key => {
        clonedObj[key] = this.deepClone(obj[key]);
      });
      
      return clonedObj;
    }
  }
}
</script>

<style scoped lang="scss">
.controller-container {
  .el-form-item {
    margin-bottom: 22px;

    .el-input + .el-input {
      margin-top: 10px;
    }

    .el-checkbox {
      margin-top: 10px;
      display: block;
    }
  }

  .status-bar-icons {
    display: flex;
    align-items: center;

    .el-select {
      width: 100%;
    }
  }

  .unit-label {
    margin-left: 5px;
    color: #606266;
  }

  .form-row {
    display: flex;
    gap: 20px;
  }

  .form-item-half {
    width: calc(50% - 10px);
  }

  .action-buttons {
    margin: 20px 0;
    display: flex;
    gap: 10px;
  }

  .tip-text {
    margin-top: 5px;
    font-size: 12px;
    color: #E6A23C;
    line-height: 1.4;
  }
  
  .other-quota-item {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px dashed #DCDFE6;
    border-radius: 4px;
    
    .other-quota-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      font-weight: bold;
    }
  }
}
</style> 