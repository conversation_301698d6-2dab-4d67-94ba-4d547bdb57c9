<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" inline>
      <el-form-item label="时间范围" prop="timeRange">
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="handleTimeChange"
        />
      </el-form-item>
      <el-form-item label="渠道" prop="channelIds">
        <el-select
          v-model="queryParams.channelIds"
          multiple
          collapse-tags
          placeholder="请选择渠道"
          clearable
          style="width: 240px"
          filterable
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.id"
            :label="`${item.id}-${item.channelName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="tableData"
      border
      style="width: 100%; margin-top: 15px"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="channelId" label="渠道ID" width="100" align="center" sortable="custom" v-if="dataFields.length > 0" />
      <el-table-column prop="channelName" label="渠道名称" width="220" align="center" v-if="dataFields.length > 0" />

      <!-- 动态渲染表格列 -->
      <template v-for="(field, index) in dataFields">
        <el-table-column
          :key="index"
          :label="field.label"
          sortable="custom"
          :prop="'t1.' + field.key"
          align="center"
          min-width="90"
        >
          <template slot-scope="scope">
            {{ scope.row.t1 ? scope.row.t1[field.key] : '' }}
          </template>
        </el-table-column>
      </template>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getOutboundAnalysis, getOutboundChannelIds } from '@/api/distributionStatistics/outboundAnalysis'
import { getAllChannelList } from '@/api/channeManage/channelList'
import Pagination from '@/components/Pagination'
import dayjs from 'dayjs'

export default {
  name: 'OutboundAnalysis',
  components: {
    Pagination
  },
  data() {
    // 获取当前时间
    const now = dayjs()
    const currentTime = now.format('HH:mm:ss')
    const today = now.format('YYYY-MM-DD')
    const todayStart = today + ' 00:00:00'
    const todayEnd = today + ' ' + currentTime

    return {
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 渠道选项
      channelOptions: [],
      // 动态数据字段
      dataFields: [],
      // 排除的字段（不在表格中显示）
      excludeFields: ['渠道名称', '渠道id', '渠道ID'],
      // 时间范围
      timeRange: [todayStart, todayEnd],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        firstStartTime: todayStart,
        firstEndTime: todayEnd,
        secondStartTime: todayStart,
        secondEndTime: todayEnd,
        channelIds: [],
        channelName: '',
        sortAsc: {}
      }
    }
  },
  created() {
    this.getChannelList()
    this.getList()
  },
  methods: {
    // 获取渠道列表
    async getChannelList() {
      try {
        // 获取全部渠道列表
        const allChannelsResponse = await getAllChannelList()
        const allChannels = allChannelsResponse.data || []

        // 获取话务系统渠道ID列表
        const outboundChannelIdsResponse = await getOutboundChannelIds()
        const outboundChannelIds = outboundChannelIdsResponse.data || []

        // 筛选出包含在话务系统渠道ID列表中的渠道
        this.channelOptions = allChannels.filter(channel =>
          outboundChannelIds.includes(String(channel.id)) || outboundChannelIds.includes(channel.id)
        )
      } catch (error) {
        console.error('获取渠道列表失败:', error)
        this.channelOptions = []
      }
    },
    // 获取列表数据
    getList() {
      getOutboundAnalysis(this.queryParams).then(response => {
        // 处理接口返回的数据结构
        const responseData = response.data || {}
        this.tableData = responseData.records || []
        this.total = responseData.total || 0
        this.extractDataFields()
      }).catch(() => {
        this.tableData = []
        this.total = 0
      })
    },
    // 提取数据字段
    extractDataFields() {
      if (this.tableData.length > 0) {
        const firstItem = this.tableData[0]
        if (firstItem.t1) {
          // 提取所有字段，但排除指定字段
          this.dataFields = Object.keys(firstItem.t1)
            .filter(key => !this.excludeFields.includes(key))
            .map(key => ({
              key,
              label: key
            }))

          // 为表格数据添加渠道名称和ID字段方便展示
          this.tableData.forEach(item => {
            item.channelName = item.t1['渠道名称'] || ''
            item.channelId = item.t1['渠道id'] || item.t1['渠道ID'] || ''
          })
        }
      }
    },
    // 处理时间范围变化
    handleTimeChange(time) {
      if (time) {
        this.queryParams.firstStartTime = time[0]
        this.queryParams.firstEndTime = time[1]
        this.queryParams.secondStartTime = time[0]
        this.queryParams.secondEndTime = time[1]
      } else {
        this.queryParams.firstStartTime = ''
        this.queryParams.firstEndTime = ''
        this.queryParams.secondStartTime = ''
        this.queryParams.secondEndTime = ''
      }
    },
    // 查询按钮点击
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置按钮点击
    resetQuery() {
      this.$refs.queryForm.resetFields()
      const now = dayjs()
      const currentTime = now.format('HH:mm:ss')
      const today = now.format('YYYY-MM-DD')
      const todayStart = today + ' 00:00:00'
      const todayEnd = today + ' ' + currentTime

      this.timeRange = [todayStart, todayEnd]

      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        firstStartTime: todayStart,
        firstEndTime: todayEnd,
        secondStartTime: todayStart,
        secondEndTime: todayEnd,
        channelIds: [],
        channelName: '',
        sortAsc: {}
      }
      this.getList()
    },
    // 处理排序变化
    handleSortChange(column) {
      if (column.prop) {
        // 清空之前的排序
        this.queryParams.sortAsc = {}

        // 设置新的排序，仅当排序不为 null 时
        if (column.order) {
          // 获取排序字段名
          let key = column.prop

          // 如果是动态字段（带t1.前缀），去掉前缀
          if (key.startsWith('t1.')) {
            key = key.substring(3)
          }

          // 处理特殊字段名
          if (key === 'channelId') {
            key = '渠道id'
          }

          this.queryParams.sortAsc[key] = column.order === 'ascending'
        }

        this.getList()
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
