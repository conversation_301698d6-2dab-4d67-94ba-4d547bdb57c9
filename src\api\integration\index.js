import request from '@/utils/request'

// 新增-渠道及乙方关联关系
export function addIntegrationConfig(data) {
  return request({
    url: '/loan/xm/api/integration/config/add',
    method: 'post',
    data
  })
}

// 修改-渠道及乙方关联关系
export function updateIntegrationConfig(data) {
  return request({
    url: '/loan/xm/api/integration/config/update',
    method: 'post',
    data
  })
}

// 删除-渠道及乙方关联关系
export function deleteIntegrationConfig(data) {
  return request({
    url: '/loan/xm/api/integration/config/delete',
    method: 'post',
    data
  })
}

// 新增-渠道整合发票
export function addIntegrationInvoice(data) {
  return request({
    url: '/loan/xm/api/integration/invoice/add',
    method: 'post',
    data
  })
}

// 修改-渠道整合发票
export function updateIntegrationInvoice(data) {
  return request({
    url: '/loan/xm/api/integration/invoice/update',
    method: 'post',
    data
  })
}

// 删除-渠道整合发票
export function deleteIntegrationInvoice(data) {
  return request({
    url: '/loan/xm/api/integration/invoice/delete',
    method: 'post',
    data
  })
}

// 渠道账本列表
export function getIntegrationAccountList(data) {
  return request({
    url: '/loan/xm/api/integration/account/list',
    method: 'post',
    data
  })
}

// 更新账本余额
export function updateIntegrationAccount(data) {
  return request({
    url: '/loan/xm/api/integration/account/update',
    method: 'post',
    data
  })
} 