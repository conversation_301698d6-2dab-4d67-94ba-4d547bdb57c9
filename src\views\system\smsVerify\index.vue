<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="用户名/手机号" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户名或手机号" clearable style="width: 200px" />
      </el-form-item>
      <el-form-item label="商户类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择商户类型" clearable style="width: 200px">
          <el-option :key="1" label="商务" :value="1" />
          <el-option :key="2" label="商户" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleGetSms">获取验证码</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-if="smsCodeList.length > 0" :data="smsCodeList" style="width: 100%; margin-top: 20px" border>
      <el-table-column prop="platformName" label="平台" />
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="code" label="验证码" />
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button type="text" @click="handleCopy(scope.row.code)">复制</el-button>
          </template>
        </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getSmsByUserName } from '@/api/system/user'
import { getPlatformList } from '@/api/xmxrChannelManage/channelList'
import clipboard from '@/utils/clipboard'

export default {
  name: 'SmsVerify',
  data() {
    return {
      // 查询参数
      queryParams: {
        userName: '',
        platformId: '',
        type: 1
      },
      // 平台选项
      platformOptions: [],
      // 验证码列表
      smsCodeList: []
    }
  },
  created() {
    this.getPlatformList()
  },
  methods: {
    // 重置表单
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.smsCodeList = []
    },
    // 获取平台列表
    async getPlatformList() {
      const res = await getPlatformList()
      if (res.code == 200) {
        this.platformOptions = [
          {
            id: -1,
            name: '小马平台'
          },
          ...res.data
        ]
      }
    },
    // 获取验证码
    async handleGetSms() {
      if (!this.queryParams.userName) {
        this.$message.warning('请输入用户名或手机号')
        return
      }
      if (!this.queryParams.type) {
        this.$message.warning('请选择商户类型')
        return
      }
      const res = await getSmsByUserName(this.queryParams)
      if (res.data && res.data.length > 0) {
        this.smsCodeList = res.data
      } else {
        this.smsCodeList = []
      }
    },
    // 复制验证码
    async handleCopy(text) {
      try {
        await clipboard.copyText(text)
        this.$message.success('复制成功')
      } catch (error) {
        this.$message.error('复制失败')
      }
    }
  }
}
</script>

<style scoped></style>
