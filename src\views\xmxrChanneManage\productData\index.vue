<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" size="mini">
      <el-form-item label="时间范围" prop="timeRange">
        <el-date-picker
          v-model="queryParams.timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="handleQuery"
          :clearable="false"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="推广名称">
        <el-input
          size="mini"
          @keyup.enter.native="handleQuery"
          clearable
          placeholder="请输入推广名称"
          v-model="queryParams.productName"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="产品类型">
        <el-select
          v-model="prodType"
          size="small"
          clearable
          style="width: 140px"
        >
          <el-option :value="1" label="银行机构">银行机构</el-option>
          <el-option :value="4" label="一级机构">一级机构</el-option>
          <el-option :value="5" label="二级机构">二级机构</el-option>
          <el-option :value="6" label="三级机构">三级机构</el-option>
          <el-option :value="7" label="四级机构">四级机构</el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="产品是否在线">
        <el-select
          v-model="queryParams.productStatus"
          size="small"
          clearable
          style="width: 140px"
        >
          <el-option :value="1" label="在线"></el-option>
          <el-option :value="2" label="不在"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商户名称">
        <el-input
          v-model="queryParams.partyName"
          @keyup.enter.native="handleQuery"
          size="small"
          clearable
          placeholder="请输入商户名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="城市">
        <el-cascader
          v-model="cityArr"
          :options="cityList"
          @change="handleQuery"
          clearable
          filterable
          size="mini"
        >
        </el-cascader>
      </el-form-item>
      <el-form-item label="商务">
        <el-select
          v-model="queryParams.phoneNumber"
          size="small"
          clearable
          filterable
          style="width: 140px"
        >
          <el-option
            v-for="item in businessUserList"
            :key="item.phonenumber"
            :value="item.phonenumber"
            :label="item.nickName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="推广类型">
        <el-select
          v-model="queryParams.mid"
          size="small"
          clearable
          multiple
          collapse-tags
          @change="handleMidChange"
        >
          <el-option :value="1" label="银行机构"></el-option>
          <el-option :value="2" label="线上-贷超"></el-option>
          <el-option :value="3" label="线上持牌机构"></el-option>
          <el-option :value="4" label="一级机构"></el-option>
          <el-option :value="5" label="二级机构"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="线上贷超类型" prop="onlineType">
        <el-select
          v-model="queryParams.onlineType"
          size="small"
          clearable
          style="width: 140px"
          @change="handleOnlineTypeChange"
        >
          <el-option :value="1" label="企微"></el-option>
          <el-option :value="2" label="贷超"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="平台类型">
        <el-select
          v-model="queryParams.platformType"
          size="small"
          clearable
          multiple
          collapse-tags
          @change="handleQuery"
          style="width: 240px"
        >
          <el-option
            v-for="item in platformList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table
      :data="dataList"
      border
      :default-sort="{prop: 'cooperationCost', order: 'descending'}"
    >
      <el-table-column label="产品ID" prop="productId" align="center" width="80"></el-table-column>
      <el-table-column label="平台名称" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-tag
              :style="{ backgroundColor: getTagType(row.platformType), color: '#fff', border: 'none' }"
              size="mini"
            >
              {{ getPlatformName(row.platformType) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="推广名称" prop="productName" align="center" >
        <template slot-scope="{ row }">
          <div style="display: flex; align-items: center;">
            <span>{{ row.productName }}</span>
              <div>
                <el-tag v-if="row.accessQwFlag == 1" style="margin: 1px">接</el-tag>
                <el-tag v-if="getQwTagLabel(row)">{{ getQwTagLabel(row) }}</el-tag>
              </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="推广类型" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ type[row.mid * 1] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="投放城市" prop="cityNames" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.cityNames && row.cityNames.split(',').length > 2">
            <div>
              <el-popover
                placement="top-start"
                width="400"
                trigger="hover"
                :content="row.cityNames"
              >
                <el-button slot="reference" type="text">查看城市</el-button>
              </el-popover>
            </div>
          </div>
          <div v-else>
            {{ row.cityNames == "全国" ? "全国" : row.cityNames }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="商户名称"
        prop="partyName"
        align="center"
      ></el-table-column>
      <el-table-column label="接单类型" prop="productMidName" align="center" />
      <el-table-column label="商务" prop="sysUserNickName" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-tag v-if="row.isTransfer == 1">转</el-tag>
            {{ row.sysUserNickName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
      sortable
        label="最新推广价格"
        align="center"
        prop="cooperationCost"
      >
        <template slot-scope="{ row }">
          <div
            :class="[
              row.matchingPriceSort == null ||
              row.matchingPriceSort == row.cooperationCost
                ? ''
                : 'red',
            ]"
          >
            {{ row.cooperationCost }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="申请数量" align="center" prop="applyCount" sortable></el-table-column>
      <el-table-column label="7日累计金额" align="center" prop="weekProfit" sortable></el-table-column>
      <el-table-column label="合计金额" align="center" prop="amount" sortable></el-table-column>

      <!-- <el-table-column
        sortable="custom"
        label="推广价格"
        align="center"
        prop="matchingPriceSort"
        :sort-orders="['descending', 'ascending']"
      >
        <template slot-scope="{ row }">
          <div
            :class="[
              row.matchingPriceSort == null ||
              row.matchingPriceSort == row.cooperationCost
                ? ''
                : 'red',
            ]"
          >
            {{ row.matchingPriceSort }}
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="{ row }">
          <el-button
            type="text"
            size="mini"
            @click="handleRelease(row)"
          >投放规则</el-button>
          <el-button
            type="text"
            size="mini"
            @click="handleChannelLimit(row)"
          >渠道限制</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :pageSizes="[20, 30, 50, 100]"
      @pagination="getList"
    />
    <RuleDetail
      v-model="releaseVisible"
      :id="currentId"
      :platformType="currentPlatformType"
      @close="handleCloseRelease"
    />
    <ChannelLimit
      v-model="channelLimitVisible"
      :id="currentId"
      :platformId="currentPlatformType"
      :productName="currentProductName"
      :channelList="channelList"
      :width="screenWidth ? '50%' : '95%'"
      @close="handleCloseChannelLimit"
      @submit="submitChannelLimit"
    />
  </div>
</template>

<script>
import { getCityList, getPlatformProductList } from "@/api/statisticalManage";
import { getAllUser } from "@/api/system/user";
import { getPlatformList } from "@/api/xmxrChannelManage/channelList";
import { getSubPlatformChannels, updateSubPlatformChannel } from "@/api/productManage/product";
import RuleDetail from "./components/ruleDetail";
import ChannelLimit from "./components/ChannelLimit";
import dayjs from "dayjs";
import { getQwTypeFlag } from "@/api/addRegulationProducts/productRule";
import { isWeComLink } from '@/utils/validate';
export default {
  components: {
    RuleDetail,
    ChannelLimit
  },
  data() {
    return {
      type: {
        1: "银行机构",
        2: "线上-贷超",
        3: "线上持牌机构",
        4: "一级机构",
        5: "二级机构",
        6: "三级机构",
        7: "四级机构",
      },
      platformList: [],
      prodType: "",
      cityList: [],
      cityArr: [],
      dataList: [],
      businessUserList: [],
      queryParams: {
        partyName: "",
        productName: "",
        city: "",
        productStatus: 1,
        phoneNumber: "",
        platformType: [],
        mid: [2],
        onlineType: 1,
        pageNum: 1,
        pageSize: 100,
        timeRange: [dayjs().format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],
      },
      total: 0,
      releaseVisible: false,
      currentId: "",
      currentPlatformType: "",
      channelLimitVisible: false,
      channelList: [],
      channelForm: {
        productId: "",
        channelStatus: "0",
        channelIds: [],
        platformId: ""
      },
      currentProductName: "",
      screenWidth: true,
      qwTypeFlagList: [],
    };
  },
  methods: {
    handleQuery() {
      if (this.cityArr[1]) {
        this.queryParams.city = this.cityArr[1];
      } else {
        this.queryParams.city = "";
      }
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 监听推广类型变化
    handleMidChange() {
      // 如果推广类型中不包含"线上-贷超"(值为2)，则清空线上贷超类型
      if (!this.queryParams.mid || !this.queryParams.mid.includes(2)) {
        this.queryParams.onlineType = null;
      } else if (!this.queryParams.onlineType) {
        // 如果mid包含2，但onlineType没有值，则默认设置为企微(1)
        this.queryParams.onlineType = 1;
      }
      this.handleQuery();
    },
    // 监听线上贷超类型变化
    handleOnlineTypeChange() {
      // 如果线上贷超类型有值，则确保推广类型包含"线上-贷超"(值为2)
      if (this.queryParams.onlineType) {
        // 初始化mid数组（如果不存在）
        this.queryParams.mid = this.queryParams.mid || [];

        // 确保mid数组中包含值2
        if (!this.queryParams.mid.includes(2)) {
          this.queryParams.mid.push(2);
        }
      }
      this.handleQuery();
    },
    getList() {
      if (this.queryParams.timeRange && this.queryParams.timeRange.length === 2) {
        this.queryParams.startTime = this.queryParams.timeRange[0];
        this.queryParams.endTime = this.queryParams.timeRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";

        this.$message.warning("请选择时间范围");
        return;
      }
      getPlatformProductList(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total;
      });
    },
    // 添加获取标签类型的方法
    getTagType(platformType) {
      // 预定义的基础颜色数组 - 适中的配色
      const baseColors = [
        '#52C41A',  // 清新绿
        '#4B7BE5',  // 优雅蓝
        '#FA8C16',  // 温暖橙
        '#F15A5A',  // 玫瑰红
        '#13A8A8',  // 青蓝色
        '#597EF7',  // 靛蓝色
        '#2F9688',  // 翠绿色
        '#E8943C',  // 琥珀色
        '#3B7EC9',  // 宝蓝色
        '#D48806'   // 赭石色
      ];

      // 使用平台ID作为索引来选择颜色
      const colorIndex = (platformType - 1) % baseColors.length;
      return baseColors[colorIndex];
    },
    getPlatformName(platformType) {
      const platform = this.platformList.find(item => item.id === platformType);
      return platform ? platform.name : '';
    },
    handleRelease(row) {
      this.currentId = row.productId;
      this.currentPlatformType = row.platformType;
      this.currentProductName = row.productName;
      this.releaseVisible = true;
    },
    handleCloseRelease() {
      this.releaseVisible = false;
      this.currentId = "";
      this.currentPlatformType = "";
      this.currentProductName = "";
    },
    // 打开渠道限制抽屉
    handleChannelLimit(row) {
      this.currentId = row.productId;
      this.currentPlatformType = row.platformType;
      this.currentProductName = row.productName;
      this.getChannelList(row.platformType);
      this.channelLimitVisible = true;
    },

    // 获取渠道列表
    getChannelList(platformId) {
      getSubPlatformChannels(platformId).then(res => {
        if (res.code === 200) {
          this.channelList = res.data || [];
        }
      });
    },

    // 提交渠道限制
    submitChannelLimit(form) {
      updateSubPlatformChannel(form).then(res => {
        if (res.code === 200) {
          this.$message.success("设置成功");
          this.channelLimitVisible = false;
          this.getList();
        }
      });
    },

    // 关闭渠道限制抽屉
    handleCloseChannelLimit() {
      this.channelLimitVisible = false;
      this.currentId = "";
      this.currentPlatformType = "";
      this.currentProductName = "";
      this.channelList = [];
    },
    getQwTagLabel(row) {
      if (isWeComLink(row.cooperationLink)) {
        return this.getQwTypeFlagLabel(row.qwTypeFlag);
      }
      return '';
    },
    getQwTypeFlagLabel(flagValue) {
      if (flagValue == null || !this.qwTypeFlagList || this.qwTypeFlagList.length === 0) {
        return '';
      }
      const flagItem = this.qwTypeFlagList.find(item => item.value == flagValue);
      return flagItem ? flagItem.label : '';
    },
  },
  mounted() {
    this.screenWidth = document.body.clientWidth > 768 ? true : false;
    this.getList();
    getAllUser().then(res => {
      this.businessUserList = res.data || [];
    });
    getPlatformList().then(res => {
      this.platformList = res.data || [];
    });
    getCityList().then((res) => {
      let data = null;
      data = res.data.map((item) => {
        if (item.citys) {
          {
            return {
              value: item.code,
              label: item.name,
              disabled: false,
              children: [
                ...item.citys.map((citem) => {
                  return {
                    value: citem.code,
                    label: citem.name,
                    disabled: false,
                  };
                }),
              ],
            };
          }
        } else {
          return {
            value: item.code,
            label: item.name,
            disabled: false,
          };
        }
      });
      this.cityList = JSON.parse(JSON.stringify(data));
    });
    getQwTypeFlag().then(response => {
      if (response.code === 200 && response.data) {
        this.qwTypeFlagList = response.data;
      }
    });
  },
};
</script>

<style lang="scss">
input[aria-hidden="true"] {
  display: none !important;
}
</style>
