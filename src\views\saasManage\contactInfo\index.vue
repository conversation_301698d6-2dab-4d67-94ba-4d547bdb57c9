<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="联系情况" prop="contact">
        <el-input v-model.number="queryParams.contact" placeholder="请输入联系情况" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标签状态" prop="contactStatus">
        <el-select v-model="queryParams.contactStatus" clearable size="small">
          <el-option :value="0" label="禁用"></el-option>
          <el-option :value="1" label="启用"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableList" border>
      <el-table-column label="ID" align="center" prop="contactId" />
      <el-table-column label="联系情况" align="center" prop="contact" />
      <el-table-column label="状态" align="center" prop="contactStatus">
        <template  slot-scope="{row}">
          <div>
            <el-switch v-model="row.contactStatus"  :active-value="1"
              :inactive-value="0" active-text="启用" inactive-text="禁用" @change="changeStatus(row)"></el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" prop="label">
        <template  slot-scope="{row}">
          <div>
            <el-button type="text" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog title="新增联系情况" :visible.sync="showContact" width="600px" append-to-body @close="cancel">
      <el-form label-width="100px">
        <el-form-item label="联系情况">
          <el-input v-model="contact" maxlength="5" placeholder="请输入联系情况" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改联系情况" :visible.sync="showEditContact" width="600px" append-to-body>
      <el-form label-width="100px">
        <el-form-item label="标签名称">
          <el-input v-model="editForm.contact" maxlength="5" placeholder="请输入标签名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="showEditContact = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import { getContactList, addContactOne, changeContactStatus, editContactOne } from "@/api/saas/contactInfo"
export default {
  data() {
    return {
      tableList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        contact: "",
        contactStatus: ""
      },
      contact: "",
      editForm: {
       contact:"",
       contactId:"",
      },
      showEditContact: false,
      showContact: false
    }
  },
  methods: {
    getList() {
      getContactList(this.queryParams).then(res => {
        this.tableList = res.rows
        this.total = res.total
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleAdd() {
      this.showContact = true
    },
    changeStatus(row) {
      this.$confirm("确定要修改状态吗？", {
        type: 'warning'
      }).then(res => {
        changeContactStatus({ contactId: row.contactId }).then(res => {
          this.$message.success("状态修改成功")
        })
      }).catch(err => {

        row.contactStatus = row.contactStatus == 0 ? 1 : 0
      })
    },
    submitForm() {
      if (!this.contact) return this.$message.error("联系情况不能为空")
      addContactOne({
        contact: this.contact
      }).then(res => {
        this.$message.success('新增成功')
        this.getList()
        this.cancel()
      })
    },
    handleEdit(row) {

      this.showEditContact = true
      this.editForm.contactId = row.contactId
      this.editForm.contact = row.contact
    },
    submitEditForm() {
      if (!this.editForm.contact) return this.$message.error("联系情况不能为空")
      editContactOne(this.editForm).then(res => {
        this.showEditContact = false
        this.getList()
        this.$message.success('修改成功')
      })
    },
    cancel() {
      this.showContact = false
      this.contact = ""
    }
  },
  mounted() {
    this.getList()
  }
}
</script>

<style lang="scss" scoped>
</style>
