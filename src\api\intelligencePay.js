import request from '@/utils/request'
//获取分账支付列表
export function getStbAccountList() {
  return request({
    url: "/loan/dinPay/getStbAccountList",
    method: "get"
  })
}

//注册分账账号
export function stbMerchantRegister(data) {
  return request({
    url: '/loan/dinPay/stbMerchantRegister',
    method: "post",
    data
  })
}

//完善基本信息
export function addStbMerchantBasic(data) {
  return request({
    url: "/loan/dinPay/stbMerchantBasic",
    method: "post",
    data
  })
}

//获取城市
export function getCity(data) {
  return request({
    url: "/loan/dinPay/getAreaList",
    method: "get",
    params: data
  })
}
//修改结算信息
export function eidtStbSettleMer(data) {
  console.log(data);
  return request({
    url: "/loan/dinPay/stbSettleMer",
    method: "post",
    data
  })
}

//分账账号提交审核
export function checkAccount(data) {
  return request({
    url: "/loan/dinPay/stbSubmitMer",
    method: "post",
    data
  })
}
//修改联系方式
export function editStbContactMer(data) {
  return request({
    url: "/loan/dinPay/stbContactMer",
    method: "post",
    data
  })
}
//账号上传图片
export function editStbUpload(data) {
  return request({
    url: `/loan/dinPay/stbUpload`,
    method: "post",
    data: data
  })
}
//获取银行信息
export function getBankList(data) {
  return request({
    url: "/loan/dinPay/getBankList",
    method: "get",
    params: data
  })
}
//获取分账账号详细信息
export function getStbBasicInfo(data) {
  return request({
    url: "/loan/dinPay/getStbBasic",
    method: "get",
    params: data
  })
}
//查询账号提交审核结果
export function getSubmitMerStatus(data) {
  return request({
    url: "/loan/dinPay/getSubmitMerStatus",
    method: "get",
    params: data
  })
}
//获取支行信息
export function getUniteBankList(data) {
  return request({
    url: "/loan/dinPay/getUniteBankList",
    method: "get",
    params: data
  })
}

//查询商户余额信息
export function getStbQueryBalance(data) {
  return request({
    url: `/loan/dinPay/transfer/stbQueryBalance/${data}`,
    method: 'get'
  })
}

//分账提现
export function addStbTransfer(data) {
  return request({
    url: '/loan/dinPay/transfer/stbTransfer',
    method: "post",
    data
  })
}

//查询分账提现结果
export function getStbTransferQuery(data) {
  return request({
    url: `/loan/dinPay/transfer/stbTransferQuery/${data}`,
    method: 'get'
  })
}


//查询分账提现列表
export function getTransferList(data) {
  return request({
    url: "/loan/dinPay/transfer/getList",
    method: "get",
    params: data
  })
}
