<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px" size="small">
      <el-form-item label="申请时间">
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable />
      </el-form-item>
      <el-form-item label="申请人" prop="applyBy">
        <el-input v-model="queryParams.applyBy" placeholder="请输入申请人" clearable />
      </el-form-item>
      <el-form-item label="平台类型" prop="platformType">
        <el-select v-model="queryParams.platformType" placeholder="请选择平台类型" multiple>
          <el-option v-for="item in platformList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="rebateList" border style="width: 100%" size="small">
      <el-table-column label="申请时间" align="center" prop="createTime" />
      <el-table-column label="返点金额" align="center" prop="price" />
      <el-table-column label="返点日期" align="center" prop="rebateDate" />
      <el-table-column label="平台类型" align="center" prop="platformType">
        <template v-slot="{ row }">
          {{ formatPlatformName(row.platformType) }}
        </template>
      </el-table-column>
      <el-table-column label="推广产品名称" align="center" prop="productName" />
      <el-table-column label="推广合作价格" align="center" prop="cooperationCost" />
      <el-table-column label="申请原因" align="center" prop="reason" />
      <el-table-column label="申请人" align="center" prop="createBy" />
      <el-table-column label="返点审核" align="center" prop="twoExamineStatus">
        <template slot-scope="scope">
          <el-tag :type="scope.row.twoExamineStatus == 1 ? 'success' : scope.row.twoExamineStatus == 2 ? 'danger' : ''" v-if="auditStatusMap[scope.row.twoExamineStatus]">
            {{ auditStatusMap[scope.row.twoExamineStatus] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="文件审核" align="center" prop="auditStatus">
        <template slot-scope="scope">
          <el-tag :type="scope.row.auditStatus == 1 ? 'success' : scope.row.auditStatus == 2 ? 'danger' : ''" v-if="auditStatusMap[scope.row.auditStatus]">
            {{ auditStatusMap[scope.row.auditStatus] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审核备注" align="center" prop="twoExamineRemarks">
        <template slot-scope="scope">
          {{ formatRemarks(scope.row.twoExamineRemarks) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button size="small" type="text" @click="handleDetail(scope.row)">查看明细</el-button>

            <el-button v-if="hasBool('loan:partyfirst:rebate:apply') && scope.row.twoExamineStatus == 0" size="small"
              type="text" @click="handleAudit(scope.row)">审核</el-button>

            <el-button v-if="hasBool('loan:partyfirst:rebate:auditapply') && scope.row.auditStatus == 0"
              size="small" type="text" @click="handleAudit(scope.row, 'file')">文件审核</el-button>

            <el-button v-if="scope.row.fileName" size="small" type="text"
              @click="handleDownload(scope.row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 查看明细弹窗 -->
    <el-dialog title="返点明细" :visible.sync="detailDialog" width="600px" append-to-body>
      <div>
        <div class="detail-item">
          <span class="label">返点时间：</span>
          <span>{{ detailData.rebateDate }}</span>
        </div>
        <el-table :data="detailData.detailList" style="margin-top: 15px;" max-height="500" border size="small">
          <el-table-column label="渠道ID" prop="channelId" align="center" />
          <el-table-column label="返点金额" prop="rebateAmount" align="center" />
          <el-table-column label="原因" prop="reason" align="center" />
        </el-table>
      </div>
    </el-dialog>

    <!-- 审核弹窗 -->
    <el-dialog title="返点审核" :visible.sync="auditDialog" width="500px" append-to-body @close="resetAuditForm">
      <div class="audit-info">
        <div class="info-item"><span class="label">申请时间:</span> <span class="value">{{currentRow && currentRow.createTime}}</span></div>
        <div class="info-item"><span class="label">返点金额:</span> <span class="value red">{{currentRow && currentRow.price}}</span></div>
        <div class="info-item"><span class="label">返点日期:</span> <span class="value">{{currentRow && currentRow.rebateDate}}</span></div>
        <div class="info-item"><span class="label">平台类型:</span> <span class="value">{{currentRow && formatPlatformName(currentRow.platformType)}}</span></div>
        <div class="info-item"><span class="label">产品名称:</span> <span class="value">{{currentRow && currentRow.productName}}</span></div>
        <div class="info-item"><span class="label">合作价格:</span> <span class="value">{{currentRow && currentRow.cooperationCost}}</span></div>
        <div class="info-item"><span class="label">申请原因:</span> <span class="value">{{currentRow && currentRow.reason}}</span></div>
        <div class="info-item"><span class="label">申请人:</span> <span class="value">{{currentRow && currentRow.createBy}}</span></div>
      </div>
      <el-form ref="auditForm" :model="auditForm" label-width="100px" class="mt-20">
        <el-form-item label="审核意见" prop="remarks">
          <el-input type="textarea" v-model.trim="auditForm.remarks" placeholder="请输入审核意见，拒绝必须填写" :rows="3"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAuditWithType(0)">通 过</el-button>
        <el-button type="danger" @click="submitAuditWithType(1)">拒 绝</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRebateList, getRebateDetail, auditRebate, auditRebateFile } from '@/api/platformRebate'
import { getPlatformList } from '@/api/xmxrChannelManage/channelList'
import { hasBool } from '@/directive/permission/hasBool'

export default {
  name: 'PlatformRebateExamine',
  data() {
    return {
      // 审核状态映射
      auditStatusMap: {
        0: '待审核',
        1: '通过',
        2: '驳回'
      },
      // 总条数
      total: 0,
      // 返点列表
      rebateList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startTime: null,
        endTime: null,
        productName: null,
        applyBy: null,
        platformType: []
      },
      // 平台列表
      platformList: [],
      // 查看明细弹窗
      detailDialog: false,
      detailData: {
        rebateDate: '',
        detailList: []
      },
      // 审核弹窗
      auditDialog: false,
      auditForm: {
        id: null,
        type: null,
        remarks: '',
        platformType: null,
        auditType: null,
        rowRemark: ''
      },
      currentRow: null
    }
  },
  created() {
    this.getList()
    this.getPlatformList()
  },
  methods: {
    formatPlatformName(platformType) {
      return this.platformList.find(item => item.id == platformType).name
    },

    // 格式化备注
    formatRemarks(remarks) {
      if (!remarks || !remarks.length) return ''
      return remarks.map(item => `${item.name}：${item.remarks}`).join('\n')
    },

    /** 查询列表 */
    getList() {
      // 处理日期范围
      if (this.dateRange) {
        this.queryParams.startTime = this.dateRange[0]
        this.queryParams.endTime = this.dateRange[1]
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
      getRebateList(this.queryParams).then(res => {
        res.rows.forEach(item => {
          if (item.twoExamineRemarks) {
            item.twoExamineRemarks = JSON.parse(item.twoExamineRemarks)
          } else {
            item.twoExamineRemarks = []
          }
        })
        this.rebateList = res.rows
        this.total = res.total
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.$refs.queryForm.resetFields()
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.handleQuery()
    },
    /** 查看明细按钮操作 */
    handleDetail(row) {
      getRebateDetail({
        id: row.id,
        platformType: row.platformType
      }).then(res => {
        this.detailData = res.data
        this.detailDialog = true
      })
    },
    /** 审核按钮操作 */
    handleAudit(row, type = 'normal') {
      if (type === 'normal') {
        const statusText = row.auditStatus == 0 ? '待审核' : row.auditStatus == 1 ? '已通过' : '已驳回';
        this.$confirm(`当前文件审核状态：${statusText}，是否继续进行审核操作？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.openAuditDialog(row, type);
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消审核'
          });
        });
      } else {
        this.openAuditDialog(row, type);
      }
    },

    /** 打开审核弹窗 */
    openAuditDialog(row, type) {
      this.currentRow = row;
      this.auditForm.id = row.id;
      this.auditForm.platformType = row.platformType;
      this.auditForm.type = null;
      this.auditForm.remarks = '';
      this.auditForm.auditType = type;
      this.auditForm.rowRemark = [...row.twoExamineRemarks];
      this.auditDialog = true;
    },

    /** 获取平台列表 */
    getPlatformList() {
      getPlatformList().then(res => {
        this.platformList = res.data
      })
    },
    /** 提交审核 */
    submitAuditWithType(type) {
      this.auditForm.type = type;
      
      // 如果是拒绝且没有填写原因，则验证不通过
      if (type == 1 && !this.auditForm.remarks) {
        this.$message.warning('拒绝时必须填写审核意见');
        return;
      }
      
      // 将新备注添加到当前数据已有的备注中
      const rowRemark = this.auditForm.rowRemark
      rowRemark.push({
        name: this.$store.getters.userName,
        remarks: this.auditForm.remarks,
        auditType: this.auditForm.auditType
      })

      const { id, platformType, auditType } = this.auditForm

      const data = {
        id,
        type,
        remarks: JSON.stringify(rowRemark)
      }

      const auditMethod = auditType === 'file' ? auditRebateFile : auditRebate

      auditMethod(data, platformType).then(() => {
        this.$modal.msgSuccess('审核成功')
        this.auditDialog = false
        this.getList()
      })
    },
    /** 重置审核表单 */
    resetAuditForm() {
      this.$refs.auditForm.resetFields()
    },
    /** 下载文件 */
    async handleDownload(row) {
      const url = row.fileName
      const fileName = row.fileName.substring(row.fileName.lastIndexOf('/') + 1)

      fetch(url).then(res => {
        return res.blob()
      }).then(res => {
        this.$download.saveAs(res, fileName)
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.detail-item {
  margin-bottom: 15px;

  .label {
    display: inline-block;
    width: 80px;
    color: #606266;
  }
}

.mt-20 {
  margin-top: 20px;
}

.audit-info {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  
  .info-item {
    margin-bottom: 10px;
    font-size: 14px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      display: inline-block;
      width: 80px;
      color: #606266;
    }
    
    .value {
      font-weight: 500;
      color: #303133;
      
      &.red {
        color: #f56c6c;
      }
    }
  }
}
</style>