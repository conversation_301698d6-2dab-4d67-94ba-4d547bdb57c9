<template>
  <div class="error-monitor">
    <!-- GitHub 风格错误指示器 -->
    <div
      v-if="showIndicator"
      class="github-error-indicator"
      :class="indicatorClass"
      @click="togglePanel"
      :title="getIndicatorTooltip()"
    >
      <!-- 错误图标 -->
      <svg class="indicator-icon" viewBox="0 0 16 16" width="16" height="16">
        <path v-if="errorCount > 0" fill="currentColor" d="M8 1.5c-2.363 0-4 1.69-4 3.75 0 .984.424 1.625.984 2.304l.214.253c.223.264.47.556.673.848.284.411.537.896.621 1.49a.75.75 0 0 1-1.484.211c-.04-.282-.163-.547-.37-.847a8.456 8.456 0 0 0-.542-.68c-.084-.1-.173-.205-.268-.32C3.201 7.75 2.5 6.766 2.5 5.25 2.5 2.31 4.863 0 8 0s5.5 2.31 5.5 5.25c0 1.516-.701 2.5-1.328 3.259-.095.115-.184.22-.268.319-.207.245-.383.453-.541.681-.208.3-.33.565-.37.847a.751.751 0 0 1-1.485-.212c.084-.593.337-1.078.621-1.489.203-.292.45-.584.673-.848.075-.088.147-.173.213-.253.561-.679.985-1.32.985-2.304 0-2.06-1.637-3.75-4-3.75ZM5.75 12h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1 0-1.5ZM6 15.25a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Z"/>
        <path v-else-if="warningCount > 0" fill="currentColor" d="M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"/>
        <path v-else fill="currentColor" d="M13.78 4.22a.75.75 0 0 1 0 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L2.22 9.28a.75.75 0 0 1 1.06-1.06L6 10.94l6.72-6.72a.75.75 0 0 1 1.06 0Z"/>
      </svg>

      <!-- 错误计数徽章 -->
      <span v-if="errorList.length > 0" class="github-error-badge">
        {{ errorList.length > 99 ? '99+' : errorList.length }}
      </span>
    </div>

    <!-- GitHub 风格错误面板 -->
    <el-drawer
      title="错误监控"
      :visible.sync="panelVisible"
      direction="rtl"
      size="60%"
      :modal="false"
      :show-close="true"
      class="github-error-drawer"
    >
      <div class="github-error-panel">
        <!-- 操作栏 -->
        <div class="github-panel-actions">
          <div class="actions-left">
            <span class="error-summary">{{ errorList.length }} 个问题</span>
          </div>
          <div class="actions-right">
            <button class="github-btn github-btn-sm" @click="clearErrors" :disabled="errorList.length === 0">
              <svg viewBox="0 0 16 16" width="16" height="16">
                <path fill="currentColor" d="M11 1.75V3h2.25a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1 0-1.5H5V1.75C5 .784 5.784 0 6.75 0h2.5C10.216 0 11 .784 11 1.75ZM4.496 6.675l.66 6.6a.25.25 0 0 0 .249.225h5.19a.25.25 0 0 0 .249-.225l.66-6.6a.75.75 0 0 1 1.492.149l-.66 6.6A1.748 1.748 0 0 1 10.595 15h-5.19a1.748 1.748 0 0 1-1.741-1.575l-.66-6.6a.75.75 0 1 1 1.492-.15ZM6.5 1.75V3h3V1.75a.25.25 0 0 0-.25-.25h-2.5a.25.25 0 0 0-.25.25Z"/>
              </svg>
              清除全部
            </button>
          </div>
        </div>

        <!-- GitHub 风格筛选器 -->
        <div class="github-filters">
          <div class="filter-tabs">
            <button
              v-for="tab in filterTabs"
              :key="tab.key"
              class="filter-tab"
              :class="{ active: activeTab === tab.key }"
              @click="activeTab = tab.key"
            >
              <svg v-if="tab.icon" class="tab-icon" viewBox="0 0 16 16" width="16" height="16">
                <path fill="currentColor" :d="tab.icon"/>
              </svg>
              {{ tab.label }}
              <span class="tab-count">{{ tab.count }}</span>
            </button>
          </div>
        </div>

        <!-- GitHub 风格错误列表 -->
        <div class="github-error-list" v-if="filteredErrorList.length > 0">
          <!-- 批量操作栏 - 固定高度避免布局抖动 -->
          <div class="github-bulk-actions">
            <div class="bulk-actions-content" v-show="selectedErrors.length > 0">
              <div class="bulk-actions-left">
                <span class="selected-count">已选择 {{ selectedErrors.length }} 项</span>
              </div>
              <div class="bulk-actions-right">
                <button class="github-btn github-btn-sm" @click="copySelectedErrors">
                  <svg viewBox="0 0 16 16" width="16" height="16">
                    <path fill="currentColor" d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .138.112.25.25.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"/>
                    <path fill="currentColor" d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .138.112.25.25.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"/>
                  </svg>
                  复制选中
                </button>
              </div>
            </div>
          </div>

          <!-- 错误项列表 -->
          <div class="github-error-items">
            <div
              v-for="(error, index) in filteredErrorList"
              :key="index"
              class="github-error-item"
              :class="{ selected: selectedErrors.includes(error.originalIndex) }"
            >
              <!-- 错误卡片主体 -->
              <div class="error-item-main">
                <div class="error-item-header">
                  <div class="error-header-left">
                    <input
                      type="checkbox"
                      class="github-checkbox"
                      :checked="selectedErrors.includes(error.originalIndex)"
                      @change="toggleErrorSelection(error.originalIndex)"
                    >
                    <div class="error-status-icon" :class="getErrorStatusClass(error.type)">
                      <svg viewBox="0 0 16 16" width="16" height="16">
                        <path fill="currentColor" :d="getErrorIcon(error.type)"/>
                      </svg>
                    </div>
                    <span class="github-label" :class="getErrorLabelClass(error.type)">
                      {{ getErrorTypeLabel(error.type) }}
                    </span>
                  </div>
                  <div class="error-header-right">
                    <span class="error-timestamp">{{ formatRelativeTime(error.timestamp) }}</span>
                    <div class="error-actions">
                      <button class="github-btn-icon" @click="copyError(error)" title="复制错误">
                        <svg viewBox="0 0 16 16" width="16" height="16">
                          <path fill="currentColor" d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .138.112.25.25.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"/>
                          <path fill="currentColor" d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .138.112.25.25.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"/>
                        </svg>
                      </button>
                      <button class="github-btn-icon" @click="removeError(error.originalIndex)" title="删除错误">
                        <svg viewBox="0 0 16 16" width="16" height="16">
                          <path fill="currentColor" d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L9.06 8l3.22 3.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L8 9.06l-3.22 3.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>

                <div class="error-content">
                  <div class="error-message">{{ error.message }}</div>
                  <div class="error-meta" v-if="error.filename || error.component">
                    <span v-if="error.filename" class="error-file">
                      {{ error.filename }}:{{ error.lineno }}:{{ error.colno }}
                    </span>
                    <span v-if="error.component" class="error-component">
                      组件: {{ error.component }}
                    </span>
                  </div>
                </div>

                <!-- 堆栈信息 -->
                <div class="error-stack-section" v-if="error.stack">
                  <button
                    class="github-btn github-btn-sm stack-toggle"
                    @click="toggleStack(error.originalIndex)"
                  >
                    <svg viewBox="0 0 16 16" width="16" height="16">
                      <path fill="currentColor" d="M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z"/>
                    </svg>
                    查看堆栈信息
                  </button>
                  <div v-if="expandedStacks.includes(error.originalIndex)" class="github-code-block">
                    <pre><code>{{ error.stack }}</code></pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="github-empty-state">
          <div class="empty-state-icon">
            <svg viewBox="0 0 24 24" width="48" height="48">
              <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
          <h3 class="empty-state-title">没有发现错误</h3>
          <p class="empty-state-description">当前没有任何错误或警告信息</p>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: 'ErrorMonitor',
  data() {
    return {
      errorList: [],
      panelVisible: false,
      showIndicator: true,
      originalConsoleError: null,
      originalConsoleWarn: null,
      activeTab: 'all', // 默认显示全部，会根据错误情况动态调整
      selectedErrors: [], // 选中的错误索引数组
      selectAll: false, // 全选状态
      expandedStacks: [] // 展开的堆栈信息
    }
  },
  computed: {
    // 根据错误类型计算指示器样式
    indicatorClass() {
      if (this.errorList.length === 0) {
        return 'status-success' // 绿色 - 无错误
      }

      // 检查是否有严重错误（JavaScript错误、Promise错误、Vue错误、Console Error）
      const hasErrors = this.errorList.some(error =>
        ['JavaScript', 'Promise', 'Vue', 'Console Error'].includes(error.type)
      )

      if (hasErrors) {
        return 'status-error' // 红色 - 有错误
      }

      // 只有警告
      return 'status-warning' // 橙色 - 只有警告
    },

    // 计算错误数量
    errorCount() {
      return this.errorList.filter(error =>
        ['JavaScript', 'Promise', 'Vue', 'Console Error'].includes(error.type)
      ).length
    },

    // 计算警告数量
    warningCount() {
      return this.errorList.filter(error =>
        error.type === 'Console Warn'
      ).length
    },

    // GitHub 风格的筛选标签
    filterTabs() {
      return [
        {
          key: 'all',
          label: '全部',
          count: this.errorList.length,
          icon: 'M8 1.5c-2.363 0-4 1.69-4 3.75 0 .984.424 1.625.984 2.304l.214.253c.223.264.47.556.673.848.284.411.537.896.621 1.49a.75.75 0 0 1-1.484.211c-.04-.282-.163-.547-.37-.847a8.456 8.456 0 0 0-.542-.68c-.084-.1-.173-.205-.268-.32C3.201 7.75 2.5 6.766 2.5 5.25 2.5 2.31 4.863 0 8 0s5.5 2.31 5.5 5.25c0 1.516-.701 2.5-1.328 3.259-.095.115-.184.22-.268.319-.207.245-.383.453-.541.681-.208.3-.33.565-.37.847a.751.751 0 0 1-1.485-.212c.084-.593.337-1.078.621-1.489.203-.292.45-.584.673-.848.075-.088.147-.173.213-.253.561-.679.985-1.32.985-2.304 0-2.06-1.637-3.75-4-3.75ZM5.75 12h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1 0-1.5ZM6 15.25a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Z'
        },
        {
          key: 'error',
          label: '错误',
          count: this.errorCount,
          icon: 'M8 1.5c-2.363 0-4 1.69-4 3.75 0 .984.424 1.625.984 2.304l.214.253c.223.264.47.556.673.848.284.411.537.896.621 1.49a.75.75 0 0 1-1.484.211c-.04-.282-.163-.547-.37-.847a8.456 8.456 0 0 0-.542-.68c-.084-.1-.173-.205-.268-.32C3.201 7.75 2.5 6.766 2.5 5.25 2.5 2.31 4.863 0 8 0s5.5 2.31 5.5 5.25c0 1.516-.701 2.5-1.328 3.259-.095.115-.184.22-.268.319-.207.245-.383.453-.541.681-.208.3-.33.565-.37.847a.751.751 0 0 1-1.485-.212c.084-.593.337-1.078.621-1.489.203-.292.45-.584.673-.848.075-.088.147-.173.213-.253.561-.679.985-1.32.985-2.304 0-2.06-1.637-3.75-4-3.75ZM5.75 12h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1 0-1.5ZM6 15.25a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Z'
        },
        {
          key: 'warning',
          label: '警告',
          count: this.warningCount,
          icon: 'M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z'
        }
      ]
    },

    // 根据当前tab返回过滤后的错误列表
    filteredErrorList() {
      let filtered = []

      if (this.activeTab === 'error') {
        filtered = this.errorList.filter(error =>
          ['JavaScript', 'Promise', 'Vue', 'Console Error'].includes(error.type)
        )
      } else if (this.activeTab === 'warning') {
        filtered = this.errorList.filter(error =>
          error.type === 'Console Warn'
        )
      } else {
        filtered = this.errorList
      }

      // 为每个错误添加原始索引，用于删除操作
      return filtered.map((error) => ({
        ...error,
        originalIndex: this.errorList.indexOf(error)
      }))
    },

    // 全选状态的中间态
    isIndeterminate() {
      const filteredIndexes = this.filteredErrorList.map(error => error.originalIndex)
      const selectedInFiltered = this.selectedErrors.filter(index => filteredIndexes.includes(index))
      return selectedInFiltered.length > 0 && selectedInFiltered.length < filteredIndexes.length
    }
  },
  watch: {
    // 监听选中错误的变化，更新全选状态
    selectedErrors: {
      handler() {
        const filteredIndexes = this.filteredErrorList.map(error => error.originalIndex)
        const selectedInFiltered = this.selectedErrors.filter(index => filteredIndexes.includes(index))
        this.selectAll = selectedInFiltered.length === filteredIndexes.length && filteredIndexes.length > 0
      },
      deep: true
    },

    // 监听tab切换，清空选中状态
    activeTab() {
      this.selectedErrors = []
      this.selectAll = false
    }
  },
  mounted() {
    this.initErrorHandlers()
  },
  beforeDestroy() {
    this.removeErrorHandlers()
  },
  methods: {
    initErrorHandlers() {
      // 只在开发环境或配置允许时启用
      if (process.env.NODE_ENV === 'production' && !this.$store?.state?.settings?.showErrorMonitor) {
        this.showIndicator = false
        return
      }

      // 监听JavaScript运行时错误
      window.addEventListener('error', this.handleError)
      
      // 监听Promise未捕获的拒绝
      window.addEventListener('unhandledrejection', this.handlePromiseRejection)
      
      // 设置Vue错误处理器
      if (!this.$options._base.config.errorHandler) {
        this.$options._base.config.errorHandler = this.handleVueError
      }

      // 拦截console.error和console.warn
      this.interceptConsole()
    },

    removeErrorHandlers() {
      window.removeEventListener('error', this.handleError)
      window.removeEventListener('unhandledrejection', this.handlePromiseRejection)
      
      // 恢复原始console方法
      if (this.originalConsoleError) {
        console.error = this.originalConsoleError
      }
      if (this.originalConsoleWarn) {
        console.warn = this.originalConsoleWarn
      }
    },

    handleError(event) {
      const error = {
        type: 'JavaScript',
        message: event.message || '未知错误',
        filename: event.filename || '',
        lineno: event.lineno || 0,
        colno: event.colno || 0,
        stack: event.error?.stack || '',
        timestamp: Date.now()
      }
      this.addError(error)
    },

    handlePromiseRejection(event) {
      const error = {
        type: 'Promise',
        message: event.reason?.message || event.reason || '未捕获的Promise拒绝',
        stack: event.reason?.stack || '',
        timestamp: Date.now()
      }
      this.addError(error)
    },

    handleVueError(err, vm, info) {
      const error = {
        type: 'Vue',
        message: err.message || 'Vue组件错误',
        stack: err.stack || '',
        info: info || '',
        component: vm?.$options?.name || vm?.$options?._componentTag || '未知组件',
        timestamp: Date.now()
      }
      this.addError(error)
    },

    interceptConsole() {
      // 保存原始方法
      this.originalConsoleError = console.error
      this.originalConsoleWarn = console.warn

      // 拦截console.error
      console.error = (...args) => {
        this.originalConsoleError.apply(console, args)
        
        const message = args.map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ')

        const error = {
          type: 'Console Error',
          message: message,
          timestamp: Date.now()
        }
        this.addError(error)
      }

      // 拦截console.warn
      console.warn = (...args) => {
        this.originalConsoleWarn.apply(console, args)
        
        const message = args.map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ')

        const error = {
          type: 'Console Warn',
          message: message,
          timestamp: Date.now()
        }
        this.addError(error)
      }
    },

    addError(error) {
      // 避免重复添加相同错误
      const isDuplicate = this.errorList.some(existingError => 
        existingError.message === error.message && 
        existingError.type === error.type &&
        (Date.now() - existingError.timestamp) < 1000 // 1秒内的相同错误视为重复
      )

      if (!isDuplicate) {
        this.errorList.unshift(error)
        
        // 限制错误数量，避免内存泄漏
        if (this.errorList.length > 50) {
          this.errorList = this.errorList.slice(0, 50)
        }
      }
    },

    togglePanel() {
      this.panelVisible = !this.panelVisible
      
      // 如果打开面板，根据错误情况智能选择tab
      if (this.panelVisible) {
        this.setSmartActiveTab()
      }
    },

    clearErrors() {
      this.errorList = []
      this.selectedErrors = []
      this.selectAll = false
    },

    removeError(index) {
      this.errorList.splice(index, 1)
      // 更新选中状态
      const selectedIndex = this.selectedErrors.indexOf(index)
      if (selectedIndex > -1) {
        this.selectedErrors.splice(selectedIndex, 1)
      }
      // 更新其他选中项的索引
      this.selectedErrors = this.selectedErrors.map(selectedIndex =>
        selectedIndex > index ? selectedIndex - 1 : selectedIndex
      )
    },

    formatTime(timestamp) {
      const date = new Date(timestamp)
      return date.toLocaleTimeString()
    },

    // GitHub 风格的相对时间格式
    formatRelativeTime(timestamp) {
      const now = Date.now()
      const diff = now - timestamp
      const seconds = Math.floor(diff / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)
      const days = Math.floor(hours / 24)

      if (seconds < 60) {
        return '刚刚'
      } else if (minutes < 60) {
        return `${minutes} 分钟前`
      } else if (hours < 24) {
        return `${hours} 小时前`
      } else if (days < 7) {
        return `${days} 天前`
      } else {
        return new Date(timestamp).toLocaleDateString()
      }
    },

    // 获取指示器提示文本
    getIndicatorTooltip() {
      if (this.errorList.length === 0) {
        return '没有错误'
      }
      return `${this.errorCount} 个错误，${this.warningCount} 个警告`
    },

    // 获取错误状态类名
    getErrorStatusClass(type) {
      const errorTypes = ['JavaScript', 'Promise', 'Vue', 'Console Error']
      if (errorTypes.includes(type)) {
        return 'status-error'
      }
      return 'status-warning'
    },

    // 获取错误图标
    getErrorIcon(type) {
      const errorTypes = ['JavaScript', 'Promise', 'Vue', 'Console Error']
      if (errorTypes.includes(type)) {
        return 'M8 1.5c-2.363 0-4 1.69-4 3.75 0 .984.424 1.625.984 2.304l.214.253c.223.264.47.556.673.848.284.411.537.896.621 1.49a.75.75 0 0 1-1.484.211c-.04-.282-.163-.547-.37-.847a8.456 8.456 0 0 0-.542-.68c-.084-.1-.173-.205-.268-.32C3.201 7.75 2.5 6.766 2.5 5.25 2.5 2.31 4.863 0 8 0s5.5 2.31 5.5 5.25c0 1.516-.701 2.5-1.328 3.259-.095.115-.184.22-.268.319-.207.245-.383.453-.541.681-.208.3-.33.565-.37.847a.751.751 0 0 1-1.485-.212c.084-.593.337-1.078.621-1.489.203-.292.45-.584.673-.848.075-.088.147-.173.213-.253.561-.679.985-1.32.985-2.304 0-2.06-1.637-3.75-4-3.75ZM5.75 12h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1 0-1.5ZM6 15.25a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Z'
      }
      return 'M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z'
    },

    // 获取错误标签类名
    getErrorLabelClass(type) {
      const errorTypes = ['JavaScript', 'Promise', 'Vue', 'Console Error']
      if (errorTypes.includes(type)) {
        return 'label-danger'
      }
      return 'label-warning'
    },

    // 获取错误类型标签文本
    getErrorTypeLabel(type) {
      const labelMap = {
        'JavaScript': 'JS错误',
        'Promise': 'Promise',
        'Vue': 'Vue',
        'Console Error': '控制台错误',
        'Console Warn': '警告'
      }
      return labelMap[type] || type
    },

    // 切换错误选择状态
    toggleErrorSelection(index) {
      const selectedIndex = this.selectedErrors.indexOf(index)
      if (selectedIndex > -1) {
        this.selectedErrors.splice(selectedIndex, 1)
      } else {
        this.selectedErrors.push(index)
      }
    },

    // 切换堆栈信息展开状态
    toggleStack(index) {
      const expandedIndex = this.expandedStacks.indexOf(index)
      if (expandedIndex > -1) {
        this.expandedStacks.splice(expandedIndex, 1)
      } else {
        this.expandedStacks.push(index)
      }
    },

    // 处理全选
    handleSelectAll(value) {
      if (value) {
        this.selectedErrors = this.filteredErrorList.map(error => error.originalIndex)
      } else {
        this.selectedErrors = []
      }
    },

    // 复制单个错误
    copyError(error) {
      const errorText = this.formatErrorText(error)
      this.copyToClipboard(errorText)
      this.$message.success('错误信息已复制到剪贴板')
    },

    // 复制选中的错误
    copySelectedErrors() {
      const selectedErrorsData = this.errorList.filter((_, index) =>
        this.selectedErrors.includes(index)
      )
      const errorTexts = selectedErrorsData.map(error => this.formatErrorText(error))
      const combinedText = errorTexts.join('\n\n' + '='.repeat(50) + '\n\n')
      this.copyToClipboard(combinedText)
      this.$message.success(`已复制 ${selectedErrorsData.length} 个错误信息到剪贴板`)
    },

    // 格式化错误文本
    formatErrorText(error) {
      let text = `[${error.type}] ${this.formatTime(error.timestamp)}\n`
      text += `消息: ${error.message}\n`

      if (error.filename) {
        text += `文件: ${error.filename}:${error.lineno}:${error.colno}\n`
      }

      if (error.component) {
        text += `组件: ${error.component}\n`
      }

      if (error.info) {
        text += `信息: ${error.info}\n`
      }

      if (error.stack) {
        text += `堆栈:\n${error.stack}`
      }

      return text
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
      try {
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(text)
        } else {
          // 降级方案
          const textArea = document.createElement('textarea')
          textArea.value = text
          textArea.style.position = 'fixed'
          textArea.style.left = '-999999px'
          textArea.style.top = '-999999px'
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()
          document.execCommand('copy')
          textArea.remove()
        }
      } catch (err) {
        console.error('复制失败:', err)
        this.$message.error('复制失败，请手动复制')
      }
    },

    // 智能设置活跃tab
    setSmartActiveTab() {
      if (this.errorCount > 0) {
        // 有错误，显示错误tab
        this.activeTab = 'error'
      } else if (this.warningCount > 0) {
        // 无错误但有警告，显示警告tab
        this.activeTab = 'warning'
      } else {
        // 都没有，显示全部tab
        this.activeTab = 'all'
      }
    }
  }
}
</script>

<style scoped>
/* GitHub 风格的颜色变量 */
:root {
  --github-color-primary: #0969da;
  --github-color-success: #1a7f37;
  --github-color-warning: #bf8700;
  --github-color-danger: #cf222e;
  --github-color-neutral: #656d76;
  --github-color-bg-default: #ffffff;
  --github-color-bg-subtle: #f6f8fa;
  --github-color-border-default: #d0d7de;
  --github-color-border-muted: #d8dee4;
  --github-color-fg-default: #1f2328;
  --github-color-fg-muted: #656d76;
  --github-color-fg-subtle: #8c959f;
}

/* GitHub 风格错误指示器 */
.github-error-indicator {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--github-color-bg-default);
  border: 1px solid var(--github-color-border-default);
  color: var(--github-color-fg-default);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2);
  transition: all 0.2s cubic-bezier(0.3, 0, 0.5, 1);
  z-index: 99999;
}

.github-error-indicator:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 28px rgba(140, 149, 159, 0.3);
}

.github-error-indicator.status-success {
  color: var(--github-color-success);
  border-color: var(--github-color-success);
}

.github-error-indicator.status-warning {
  color: var(--github-color-warning);
  border-color: var(--github-color-warning);
  animation: github-pulse-warning 2s infinite;
}

.github-error-indicator.status-error {
  color: var(--github-color-danger);
  border-color: var(--github-color-danger);
  animation: github-pulse-error 2s infinite;
}

.indicator-icon {
  width: 20px;
  height: 20px;
}

.github-error-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: var(--github-color-danger);
  color: white;
  border-radius: 10px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  padding: 0 6px;
  border: 2px solid var(--github-color-bg-default);
}

@keyframes github-pulse-error {
  0% { box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2), 0 0 0 0 rgba(207, 34, 46, 0.4); }
  70% { box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2), 0 0 0 8px rgba(207, 34, 46, 0); }
  100% { box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2), 0 0 0 0 rgba(207, 34, 46, 0); }
}

@keyframes github-pulse-warning {
  0% { box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2), 0 0 0 0 rgba(191, 135, 0, 0.4); }
  70% { box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2), 0 0 0 8px rgba(191, 135, 0, 0); }
  100% { box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2), 0 0 0 0 rgba(191, 135, 0, 0); }
}

/* GitHub 风格抽屉 */
.github-error-drawer {
  --el-drawer-bg-color: var(--github-color-bg-default);
}

.github-error-drawer .el-drawer__header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--github-color-border-default);
  background: var(--github-color-bg-default);
}

.github-error-drawer .el-drawer__title {
  font-size: 20px;
  font-weight: 600;
  color: var(--github-color-fg-default);
  margin: 0;
}

.github-error-drawer .el-drawer__close-btn {
  color: var(--github-color-fg-muted);
  font-size: 18px;
}

.github-error-drawer .el-drawer__close-btn:hover {
  color: var(--github-color-fg-default);
}

.github-error-drawer .el-drawer__body {
  padding: 0;
}

/* GitHub 风格面板 */
.github-error-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--github-color-bg-default);
}

/* 操作栏 */
.github-panel-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--github-color-border-default);
  background: var(--github-color-bg-default);
}

.actions-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.error-summary {
  color: var(--github-color-fg-muted);
  font-size: 14px;
  font-weight: 500;
}

.actions-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* GitHub 风格按钮 */
.github-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid var(--github-color-border-default);
  border-radius: 6px;
  background-color: var(--github-color-bg-default);
  color: var(--github-color-fg-default);
  transition: all 0.2s cubic-bezier(0.3, 0, 0.5, 1);
  text-decoration: none;
}

.github-btn:hover:not(:disabled) {
  background-color: var(--github-color-bg-subtle);
  border-color: var(--github-color-border-muted);
}

.github-btn:disabled {
  color: var(--github-color-fg-subtle);
  background-color: var(--github-color-bg-subtle);
  border-color: var(--github-color-border-default);
  cursor: not-allowed;
}

.github-btn-sm {
  padding: 4px 12px;
  font-size: 12px;
  line-height: 18px;
}

.github-btn-icon {
  padding: 6px;
  background: transparent;
  border: none;
  color: var(--github-color-fg-muted);
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s cubic-bezier(0.3, 0, 0.5, 1);
}

.github-btn-icon:hover {
  background-color: var(--github-color-bg-subtle);
  color: var(--github-color-fg-default);
}

.github-btn svg {
  width: 16px;
  height: 16px;
}

.github-btn-sm svg {
  width: 14px;
  height: 14px;
}

.github-btn-icon svg {
  width: 16px;
  height: 16px;
}

/* GitHub 风格筛选器 */
.github-filters {
  padding: 0 24px;
  border-bottom: 1px solid var(--github-color-border-default);
}

.filter-tabs {
  display: flex;
  gap: 0;
  margin: 0;
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--github-color-fg-muted);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.3, 0, 0.5, 1);
  position: relative;
}

.filter-tab:hover {
  color: var(--github-color-fg-default);
}

.filter-tab.active {
  color: var(--github-color-fg-default);
  border-bottom-color: var(--github-color-primary);
}

.tab-icon {
  width: 16px;
  height: 16px;
}

.tab-count {
  background: var(--github-color-bg-subtle);
  color: var(--github-color-fg-default);
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.filter-tab.active .tab-count {
  background: var(--github-color-primary);
  color: white;
}

/* GitHub 风格错误列表 */
.github-error-list {
  flex: 1;
  overflow-y: auto;
  background: var(--github-color-bg-default);
}

/* 批量操作栏 - 固定高度避免布局抖动 */
.github-bulk-actions {
  min-height: 48px;
  background: var(--github-color-bg-subtle);
  border-bottom: 1px solid var(--github-color-border-default);
  transition: all 0.2s cubic-bezier(0.3, 0, 0.5, 1);
}

.bulk-actions-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  height: 48px;
}

.selected-count {
  font-size: 14px;
  color: var(--github-color-fg-default);
  font-weight: 500;
}

.bulk-actions-right {
  display: flex;
  gap: 8px;
}



/* 错误项容器 */
.github-error-items {
  padding: 0;
}

/* GitHub 风格错误项 */
.github-error-item {
  border-bottom: 1px solid var(--github-color-border-default);
  transition: background-color 0.2s cubic-bezier(0.3, 0, 0.5, 1);
}

.github-error-item:hover {
  background-color: var(--github-color-bg-subtle);
}

.github-error-item.selected {
  background-color: #f6f8fa;
}

.error-item-main {
  padding: 16px 24px;
}

.error-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.error-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.github-checkbox {
  width: 16px;
  height: 16px;
  margin: 0;
  cursor: pointer;
}

.error-status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.error-status-icon.status-error {
  color: var(--github-color-danger);
}

.error-status-icon.status-warning {
  color: var(--github-color-warning);
}

.error-status-icon svg {
  width: 16px;
  height: 16px;
}

/* GitHub 风格标签 */
.github-label {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;
  border: 1px solid transparent;
}

.github-label.label-danger {
  background-color: #ffebe9;
  color: var(--github-color-danger);
  border-color: #ffcccb;
}

.github-label.label-warning {
  background-color: #fff8dc;
  color: var(--github-color-warning);
  border-color: #ffd700;
}

.error-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.error-timestamp {
  font-size: 12px;
  color: var(--github-color-fg-muted);
}

.error-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s cubic-bezier(0.3, 0, 0.5, 1);
}

.github-error-item:hover .error-actions {
  opacity: 1;
}

.error-content {
  margin-left: 48px;
}

.error-message {
  color: var(--github-color-fg-default);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
  word-break: break-word;
}

.error-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--github-color-fg-muted);
}

.error-file,
.error-component {
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
}

/* 堆栈信息部分 */
.error-stack-section {
  margin-left: 48px;
  margin-top: 12px;
}

.stack-toggle {
  margin-bottom: 8px;
}

.github-code-block {
  background: var(--github-color-bg-subtle);
  border: 1px solid var(--github-color-border-default);
  border-radius: 6px;
  overflow: hidden;
}

.github-code-block pre {
  margin: 0;
  padding: 16px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 12px;
  line-height: 1.45;
  color: var(--github-color-fg-default);
  background: transparent;
}

.github-code-block code {
  background: transparent;
  padding: 0;
  font-size: inherit;
  color: inherit;
}

/* GitHub 风格空状态 */
.github-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 24px;
  text-align: center;
}

.empty-state-icon {
  margin-bottom: 16px;
  color: var(--github-color-success);
}

.empty-state-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--github-color-fg-default);
}

.empty-state-description {
  margin: 0;
  font-size: 14px;
  color: var(--github-color-fg-muted);
}

/* GitHub 风格的微交互效果 */
.github-error-indicator,
.github-btn,
.github-btn-icon,
.filter-tab,
.github-error-item {
  transition: all 0.2s cubic-bezier(0.3, 0, 0.5, 1);
}

/* 焦点状态 */
.github-btn:focus,
.github-btn-icon:focus,
.filter-tab:focus {
  outline: 2px solid var(--github-color-primary);
  outline-offset: 2px;
}

/* 活跃状态 */
.github-btn:active {
  transform: scale(0.98);
}

.github-error-indicator:active {
  transform: translateY(-1px) scale(0.98);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .github-error-indicator {
    bottom: 16px;
    right: 16px;
    width: 44px;
    height: 44px;
  }

  .github-error-drawer {
    --el-drawer-size: 100% !important;
  }

  .github-panel-header {
    padding: 12px 16px;
  }

  .panel-title {
    font-size: 18px;
  }

  .github-filters {
    padding: 0 16px;
  }

  .filter-tab {
    padding: 10px 12px;
    font-size: 13px;
  }

  .error-item-main {
    padding: 12px 16px;
  }

  .error-content {
    margin-left: 32px;
  }

  .error-stack-section {
    margin-left: 32px;
  }

  .github-bulk-actions {
    padding: 8px 16px;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --github-color-bg-default: #0d1117;
    --github-color-bg-subtle: #161b22;
    --github-color-border-default: #30363d;
    --github-color-border-muted: #21262d;
    --github-color-fg-default: #e6edf3;
    --github-color-fg-muted: #7d8590;
    --github-color-fg-subtle: #6e7681;
  }

  .github-label.label-danger {
    background-color: rgba(248, 81, 73, 0.1);
    border-color: rgba(248, 81, 73, 0.4);
  }

  .github-label.label-warning {
    background-color: rgba(187, 128, 9, 0.1);
    border-color: rgba(187, 128, 9, 0.4);
  }
}

/* 滚动条样式 */
.github-error-list::-webkit-scrollbar {
  width: 8px;
}

.github-error-list::-webkit-scrollbar-track {
  background: var(--github-color-bg-default);
}

.github-error-list::-webkit-scrollbar-thumb {
  background: var(--github-color-border-muted);
  border-radius: 4px;
}

.github-error-list::-webkit-scrollbar-thumb:hover {
  background: var(--github-color-border-default);
}

/* 选择状态的视觉反馈 */
.github-error-item.selected {
  background-color: rgba(9, 105, 218, 0.05);
  border-left: 3px solid var(--github-color-primary);
}

/* 加载动画 */
@keyframes github-loading {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

.loading {
  animation: github-loading 1.5s ease-in-out infinite;
}
</style>
