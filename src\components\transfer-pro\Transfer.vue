<template>
  <div class="v-transfer">
    <div class="transfer-panel source-panel">
      <div class="panel-header">
        <label>
          <input type="checkbox" 
                 :checked="sourceCheckedAll" 
                 @change="handleSourceCheckAll">
          {{ sourceTitle }}
        </label>
        <span class="count">{{ sourceCheckedCount }}/{{ filteredSourceData.length }}</span>
      </div>
      <div class="panel-body">
        <div class="search-input" v-if="filterable">
          <input type="text" v-model="sourceSearchQuery" placeholder="请输入搜索内容">
        </div>
        <DynamicScroller
          class="transfer-list"
          :items="filteredSourceData"
          :min-item-size="36"
          key-field="id">
          <template v-slot="{ item, index, active }">
            <DynamicScrollerItem
              :item="item"
              :active="active"
              :size-dependencies="[
                item[labelProp],
                sourceChecked.includes(item[keyProp]),
                renderContent
              ]"
              :data-index="index">
              <div class="transfer-item" @click="handleItemClick(item[keyProp], 'source')">
                <label>
                  <input type="checkbox" 
                         :checked="sourceChecked.includes(item[keyProp])"
                         @change="(e) => handleItemCheck(e, item[keyProp], 'source')">
                  <div class="content-wrapper">
                    <slot v-if="$scopedSlots.default" 
                          :item="item"
                          :checked="sourceChecked.includes(item[keyProp])">
                      {{ item[labelProp] }}
                    </slot>
                    <span v-else>{{ item[labelProp] }}</span>
                  </div>
                </label>
              </div>
            </DynamicScrollerItem>
          </template>
        </DynamicScroller>
      </div>
    </div>

    <div class="transfer-buttons">
      <button @click="removeFromTarget" 
              :disabled="!targetChecked.length || showProgress">
        <
      </button>
      <button @click="addToTarget" 
              :disabled="!sourceChecked.length || showProgress">
        >
      </button>
    </div>

    <div class="transfer-panel target-panel">
      <div class="panel-header">
        <label>
          <input type="checkbox" 
                 :checked="targetCheckedAll" 
                 @change="handleTargetCheckAll">
          {{ targetTitle }}
        </label>
        <span class="count">{{ targetCheckedCount }}/{{ filteredTargetData.length }}</span>
      </div>
      <div class="panel-body">
        <div class="search-input" v-if="filterable">
          <input type="text" v-model="targetSearchQuery" placeholder="请输入搜索内容">
        </div>
        <DynamicScroller
          class="transfer-list"
          :items="filteredTargetData"
          :min-item-size="36"
          key-field="id">
          <template v-slot="{ item, index, active }">
            <DynamicScrollerItem
              :item="item"
              :active="active"
              :size-dependencies="[
                item[labelProp],
                targetChecked.includes(item[keyProp]),
                renderContent
              ]"
              :data-index="index">
              <div class="transfer-item" @click="handleItemClick(item[keyProp], 'target')">
                <label>
                  <input type="checkbox" 
                         :checked="targetChecked.includes(item[keyProp])"
                         @change="(e) => handleItemCheck(e, item[keyProp], 'target')">
                  <div class="content-wrapper">
                    <slot v-if="$scopedSlots.default" 
                          :item="item"
                          :checked="targetChecked.includes(item[keyProp])">
                      {{ item[labelProp] }}
                    </slot>
                    <span v-else>{{ item[labelProp] }}</span>
                  </div>
                </label>
              </div>
            </DynamicScrollerItem>
          </template>
        </DynamicScroller>
      </div>
    </div>

    <div class="progress-wrapper" v-if="showProgress">
      <div class="progress-content">
        <div class="progress-bar-container">
          <div class="progress-bar" :style="{ width: progress + '%' }"></div>
        </div>
        <span class="progress-text">{{ progressText }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

export default {
  name: 'VTransfer',
  components: {
    DynamicScroller,
    DynamicScrollerItem
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    value: {
      type: Array,
      default: () => []
    },
    filterable: {
      type: Boolean,
      default: false
    },
    keyProp: {
      type: String,
      default: 'key'
    },
    labelProp: {
      type: String,
      default: 'label'
    },
    sourceTitle: {
      type: String,
      default: '源列表'
    },
    targetTitle: {
      type: String,
      default: '目标列表'
    },
    renderContent: Function
  },
  data() {
    return {
      sourceChecked: [],
      targetChecked: [],
      sourceSearchQuery: '',
      targetSearchQuery: '',
      showProgress: false,
      progress: 0,
      progressType: ''
    }
  },
  computed: {
    sourceData() {
      const valueSet = new Set(this.value);
      return this.data.filter(item => !valueSet.has(item[this.keyProp]));
    },
    targetData() {
      const valueSet = new Set(this.value);
      return this.data.filter(item => valueSet.has(item[this.keyProp]));
    },
    filteredSourceData() {
      if (!this.sourceSearchQuery) return this.sourceData
      return this.sourceData.filter(item => 
        item[this.labelProp].toLowerCase().includes(this.sourceSearchQuery.toLowerCase())
      )
    },
    filteredTargetData() {
      if (!this.targetSearchQuery) return this.targetData
      return this.targetData.filter(item => 
        item[this.labelProp].toLowerCase().includes(this.targetSearchQuery.toLowerCase())
      )
    },
    sourceCheckedCount() {
      return this.sourceChecked.length
    },
    targetCheckedCount() {
      return this.targetChecked.length
    },
    sourceCheckedAll() {
      return this.filteredSourceData.length > 0 && 
             this.sourceChecked.length === this.filteredSourceData.length
    },
    targetCheckedAll() {
      return this.filteredTargetData.length > 0 && 
             this.targetChecked.length === this.filteredTargetData.length
    },
    progressText() {
      if (!this.showProgress) return '';
      return '请稍后...';
    }
  },
  methods: {
    handleSourceCheckAll(e) {
      if (this.filteredSourceData.length > 1000) {
        // 大数据量时，使用 setTimeout 分批处理
        const batchSize = 1000;
        const keys = this.filteredSourceData.map(item => item[this.keyProp]);
        
        if (e.target.checked) {
          const processBatch = (startIndex) => {
            const endIndex = Math.min(startIndex + batchSize, keys.length);
            const batch = keys.slice(startIndex, endIndex);
            this.sourceChecked.push(...batch);
            
            if (endIndex < keys.length) {
              setTimeout(() => processBatch(endIndex), 0);
            }
          };
          
          this.sourceChecked = [];
          processBatch(0);
        } else {
          this.sourceChecked = [];
        }
      } else {
        this.sourceChecked = e.target.checked 
          ? this.filteredSourceData.map(item => item[this.keyProp])
          : [];
      }
    },
    handleTargetCheckAll(e) {
      if (this.filteredTargetData.length > 1000) {
        // 大数据量时，使用 setTimeout 分批处理
        const batchSize = 1000;
        const keys = this.filteredTargetData.map(item => item[this.keyProp]);
        
        if (e.target.checked) {
          const processBatch = (startIndex) => {
            const endIndex = Math.min(startIndex + batchSize, keys.length);
            const batch = keys.slice(startIndex, endIndex);
            this.targetChecked.push(...batch);
            
            if (endIndex < keys.length) {
              setTimeout(() => processBatch(endIndex), 0);
            }
          };
          
          this.targetChecked = [];
          processBatch(0);
        } else {
          this.targetChecked = [];
        }
      } else {
        this.targetChecked = e.target.checked
          ? this.filteredTargetData.map(item => item[this.keyProp])
          : [];
      }
    },
    handleItemCheck(e, key, type) {
      const checkedArray = type === 'source' ? this.sourceChecked : this.targetChecked;
      if (e.target.checked) {
        if (!checkedArray.includes(key)) {
          checkedArray.push(key);
        }
      } else {
        const index = checkedArray.indexOf(key);
        if (index > -1) {
          checkedArray.splice(index, 1);
        }
      }
    },
    addToTarget() {
      if (this.sourceChecked.length > 1000) {
        const batchSize = 100;
        const newValue = new Set(this.value);
        const total = this.sourceChecked.length;
        let processed = 0;
        
        const processBatch = () => {
          const start = processed;
          const end = Math.min(start + batchSize, total);
          
          for (let i = start; i < end; i++) {
            newValue.add(this.sourceChecked[i]);
          }
          
          processed = end;
          
          this.$emit('progress', {
            type: 'add',
            current: processed,
            total: total
          });
          
          if (processed < total) {
            requestAnimationFrame(processBatch);
          } else {
            this.$emit('input', Array.from(newValue));
            this.sourceChecked = [];
            this.$emit('progress', { type: 'add', current: total, total });
          }
        };
        
        this.$emit('progress', { type: 'add', current: 0, total });
        requestAnimationFrame(processBatch);
      } else {
        const newValue = new Set(this.value);
        this.sourceChecked.forEach(key => newValue.add(key));
        this.$emit('input', Array.from(newValue));
        this.sourceChecked = [];
      }
    },
    removeFromTarget() {
      if (this.targetChecked.length > 1000) {
        const batchSize = 100;
        const newValue = new Set(this.value);
        const targetCheckedSet = new Set(this.targetChecked);
        const total = this.targetChecked.length;
        let processed = 0;
        
        const processBatch = () => {
          const start = processed;
          const end = Math.min(start + batchSize, total);
          
          for (let i = start; i < end; i++) {
            newValue.delete(this.targetChecked[i]);
          }
          
          processed = end;
          
          this.$emit('progress', {
            type: 'remove',
            current: processed,
            total: total
          });
          
          if (processed < total) {
            requestAnimationFrame(processBatch);
          } else {
            this.$emit('input', Array.from(newValue));
            this.targetChecked = [];
            this.$emit('progress', { type: 'remove', current: total, total });
          }
        };
        
        this.$emit('progress', { type: 'remove', current: 0, total });
        requestAnimationFrame(processBatch);
      } else {
        const targetCheckedSet = new Set(this.targetChecked);
        const newValue = this.value.filter(key => !targetCheckedSet.has(key));
        this.$emit('input', newValue);
        this.targetChecked = [];
      }
    },
    handleItemClick(key, type) {
      const checkedArray = type === 'source' ? this.sourceChecked : this.targetChecked;
      const isChecked = checkedArray.includes(key);
      
      if (isChecked) {
        const index = checkedArray.indexOf(key);
        if (index > -1) {
          checkedArray.splice(index, 1);
        }
      } else {
        if (!checkedArray.includes(key)) {
          checkedArray.push(key);
        }
      }
    },
    handleProgress({ type, current, total }) {
      this.progressType = type;
      this.showProgress = current < total;
      this.progress = (current / total) * 100;
    }
  },
  created() {
    this.$on('progress', this.handleProgress);
  },
  beforeDestroy() {
    this.$off('progress', this.handleProgress);
  }
}
</script>

<style scoped>
.v-transfer {
  position: relative; /* Added for overlay positioning context */
  display: inline-flex; /* Retained for auto-width */
  align-items: center;
  justify-content: center;
  gap: 24px;
  padding: 8px;
}

.transfer-panel {
  /* width: 300px; */ /* Removed for auto-width */
  min-width: 220px;   /* Added minimum width */
  max-width: 400px;   /* Added maximum width */
  height: 400px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.transfer-panel:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
}

.panel-header {
  padding: 12px 16px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.panel-header label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.panel-header input[type="checkbox"],
.transfer-item input[type="checkbox"] {
  position: relative;
  width: 16px;
  height: 16px;
  margin: 0;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  background-color: #fff;
  transition: all 0.2s ease;
}

.panel-header input[type="checkbox"]:checked,
.transfer-item input[type="checkbox"]:checked {
  background-color: #409eff;
  border-color: #409eff;
}

.panel-header input[type="checkbox"]:checked::after,
.transfer-item input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  top: 42%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: translate(-50%, -50%) rotate(45deg);
}

.panel-header input[type="checkbox"]:hover,
.transfer-item input[type="checkbox"]:hover {
  border-color: #409eff;
}

.panel-header input[type="checkbox"]:focus,
.transfer-item input[type="checkbox"]:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.panel-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-input {
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fff;
  box-sizing: border-box;
}

.search-input input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  outline: none;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.search-input input:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.search-input input::placeholder {
  color: #c0c4cc;
}

.transfer-list {
  flex: 1;
  margin: 0;
  padding: 0;
  list-style: none;
  overflow-y: auto;
  height: 100%;
}

.transfer-list::-webkit-scrollbar {
  width: 6px;
}

.transfer-list::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 3px;
}

.transfer-list::-webkit-scrollbar-track {
  background: #f5f7fa;
}

.transfer-item {
  padding: 8px 16px;
  box-sizing: border-box;
  min-height: 36px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.transfer-item:hover {
  background-color: #f5f7fa;
}

.transfer-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
  width: 100%;
  pointer-events: none;
}

.transfer-item input[type="checkbox"] {
  pointer-events: auto;
  flex: 0 0 16px;
  width: 16px;
  height: 16px;
}

.transfer-item .content-wrapper {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.transfer-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
}

.transfer-buttons button {
  width: 32px;
  height: 32px;
  padding: 0;
  border: 1px solid #409eff; /* Element UI Primary Color */
  background-color: #409eff; /* Element UI Primary Color */
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #ffffff; /* White text */
  transition: all 0.1s ease; /* Faster transition */
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.transfer-buttons button:hover:not(:disabled) {
  color: #ffffff; /* White text */
  border-color: #66b1ff; /* Lighter blue on hover */
  background-color: #66b1ff; /* Lighter blue on hover */
  box-shadow: 0 4px 12px 0 rgba(64, 158, 255, 0.1);
}

.transfer-buttons button:active:not(:disabled) { /* Added active state */
  background-color: #3a8ee6; /* Darker blue on active */
  border-color: #3a8ee6; /* Darker blue on active */
  color: #ffffff;
  box-shadow: none; /* Remove shadow on active */
}

.transfer-buttons button:disabled {
  cursor: not-allowed;
  background-color: #a0cfff; /* Lighter blue for disabled */
  border-color: #a0cfff; /* Lighter blue for disabled */
  color: #ffffff; /* White text for disabled */
  box-shadow: none;
  /* opacity: 0.6; */ /* Removed opacity, using specific disabled colors */
}

.count {
  color: #909399;
  font-size: 12px;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 10px;
}

.progress-wrapper {
  position: absolute; /* Changed to absolute for overlay */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.85); /* Semi-transparent overlay */
  border-radius: 8px; /* Match parent component's border-radius */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  /* Removed old width, height, margin */
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-bar-container {
  width: 200px; /* Width of the progress bar track */
  height: 8px;  /* Height of the progress bar track */
  background: #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px; /* Space between bar and text */
}

.progress-bar {
  /* position: absolute; */ /* Removed absolute positioning */
  /* left: 0; */
  /* top: 0; */
  height: 100%;
  background: #409eff;
  transition: width 0.2s ease;
}

.progress-text {
  /* position: absolute; */ /* Removed absolute positioning */
  /* left: 50%; */
  /* top: 8px; */
  /* transform: translateX(-50%); */ /* Removed transform */
  font-size: 14px; /* Slightly larger for better visibility */
  color: #303133;   /* Darker text color */
  white-space: nowrap;
}
</style> 