import request from '@/utils/request'

export function getProductLinkTestList(params) {
  return request({
    url: '/loan/xm/platformProductTestLink/list',
    method: 'get',
    params
  })
}

// 新增测试配置
export function addProductLinkTest(data) {
  return request({
    url: '/loan/xm/platformProductTestLink/add',
    method: 'post',
    data
  })
}

// 更新测试配置
export function updateProductLinkTest(data) {
  return request({
    url: '/loan/xm/platformProductTestLink/update',
    method: 'post',
    data
  })
}

// 获取产品
export function getProductList(params) {
  return request({
    url: '/loan/xm/halfPlatformGroupConfig/product/list',
    method: 'get',
    params
  })
}

// 批量创建企微链接
export function batchCreateQwLink(params) {
  return request({
    url: '/loan/xm/platformProductTestLink/batch/create/qw/link',
    method: 'get',
    params
  })
}

// 获取测试手机号验证码
export function getTestPhoneCode(params) {
  return request({
    url: `/loan/xm/platformProductTestLink/get/test/phoneCode/${params.phone}`,
    method: 'get'
  })
}
