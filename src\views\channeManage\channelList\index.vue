<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="状态"
          clearable
          size="small"
        >
          <el-option value="" label="全部"></el-option>
          <el-option value="1" label="正常"></el-option>
          <el-option value="2" label="禁用"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商务" prop="commerce">
        <el-select
          size="small"
          v-model="queryParams.commerce"
          filterable
          clearable
          placeholder="请选择商务"
        >
          <el-option
            v-for="item in affairsList"
            :key="item.userId"
            :label="item.nickName"
            :value="item.userId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="渠道名称" prop="channelName">
        <el-input
          clearable
          placeholder="请输入渠道名称"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item label="乙方名称" prop="partyUsername">
        <el-input
          clearable
          placeholder="请输入乙方名称"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.partyUsername"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道ID" prop="channelId">
        <el-input
          clearable
          placeholder="请输入渠道ID"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model.number="queryParams.channelId"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-refresh"
          type="primary"
          size="mini"
          @click="addChannel"
          >新增渠道</el-button
        >
        <el-button
          icon="el-icon-download"
          type="primary"
          size="mini"
          @click="handleExport"
          v-hasPermi="['loan:backstageChannel:exportChannelFail']"
          >渠道失败原因导出</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 渠道列表 -->

    <el-table v-loading="loading" :data="channelList">
      <el-table-column label="渠道ID" prop="id" align="center" />
      <el-table-column label="渠道名称" prop="channelName" align="center" />
      <el-table-column label="项目归属" prop="projectName" align="center" />
      <el-table-column label="归属乙方" prop="partyUsername" align="center" />
      <el-table-column label="商务" prop="userName" align="center" />
      <!-- <el-table-column
        label="是否开启三要素"
        prop="isThreeElements"
        align="center"
      >
        <template  slot-scope="{row}">
          <div>
            {{ row.isThreeElements ? "是" : "否" }}
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="状态" prop="status" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.status"

              :active-text="row.status == 1 ? '合作中' : ''"
              :inactive-text="row.status == 2 ? '已停用' : ''"
              :active-value="1"
              :inactive-value="2"
              @change="changeStatus($event, row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="撞库状态" prop="checkStatus" align="center">
        <template slot-scope="{ row }">
          <div  v-if="row.type==1">
            <el-switch
              v-model="row.checkStatus"
              :active-text="row.checkStatus == 1 ? '开启' : ''"
              :inactive-text="row.checkStatus == 2 ? '关闭' : ''"
              :active-value="1"
              :inactive-value="2"
              @change="changeCheckStatus($event, row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="500px">
        <template slot-scope="{ row }">
          <div>
            <el-button
              type="text"
              size="small"
              @click="editChannel(row)"
              icon="el-icon-edit"
              >修改渠道</el-button
            >
            <el-button
              :disabled="row.status == 2"
              type="text"
              size="small"
              @click="addUrl(row)"
              icon="el-icon-circle-plus-outline"
              >添加链接</el-button
            >
            <el-button
              type="text"
              size="small"
              icon="el-icon-document-copy"
              @click="copyChannle(row)"
              >复制链接</el-button
            >
            <el-button
              type="text"
              size="small"
              icon="el-icon-document"
              @click="
                $router.push(
                  `/channeManage/cost?id=${row.id}&name=${row.channelName}`
                )
              "
              >成本记录</el-button
            >
            <el-button
              type="text"
              size="small"
              icon="el-icon-setting"
              @click="
                $router.push(
                  `/channeManage/settings?id=${row.id}&name=${row.channelName}`
                )
              "
              >渠道配置</el-button
            >
            <el-button
              :disabled="row.status == 2"
              type="text"
              size="small"
              icon="el-icon-s-check"
              @click="handleAutoCreateUrl(row)"
              >一键生成</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 新增或者修改 -->
    <el-dialog
      :title="isAdd ? '新增渠道' : '修改渠道'"
      :visible.sync="channleAvisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="渠道名称" prop="channelName">
          <el-input
            v-model="formData.channelName"
            placeholder="请输入渠道名称"
          />
        </el-form-item>
        <el-form-item label="商务" prop="commerce">
          <el-select
            size="small"
            v-model="formData.commerce"
            filterable
            clearable
            placeholder="请选择商务"
          >
            <el-option
              v-for="item in affairsList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合作类型" prop="type">
          <el-select
            size="small"
            v-model="formData.type"
            filterable
            clearable
            placeholder="请选择合作类型"
          >
            <el-option :value="1" label="联登"> </el-option>
            <el-option :value="2" label="半流程"> </el-option>
            <el-option :value="3" label="api"> </el-option>
            <el-option :value="4" label="全流程UV"> </el-option>
            <el-option :value="5" label="信息流"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合作方式" prop="cooperation">
          <el-select
            size="small"
            v-model="formData.cooperation"
            filterable
            clearable
            placeholder="请选择商务"
          >
            <el-option :value="1" label="CPA"> </el-option>
            <el-option :value="2" label="CPS"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合作价格" prop="cooperativePrice">
          <el-input
            size="small"
            :min="0"
            oninput="if(value*1<0)value=0"
            clearable
            v-model="formData.cooperativePrice"
            placeholder="请输入合作价格"
            type="number"
          ></el-input>
        </el-form-item>
        <el-form-item label="渠道归属的乙方">
          <el-select
            size="small"
            v-model="formData.partybId"
            filterable
            clearable
            placeholder="请选择渠道归属的乙方"
          >
            <el-option
              v-for="item in partyBIdList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="归属项目" prop="projectId">
          <el-select
            clearable
            v-model="formData.projectId"
            placeholder="请选择归属项目"
          >
            <el-option
              v-for="item in projectList"
              :key="item.projectId"
              :label="item.name"
              :value="item.projectId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="请选择渠道状态" prop="status">
          <el-radio v-model="formData.status" :label="1">启用</el-radio>
          <el-radio v-model="formData.status" :label="2">禁用</el-radio>
        </el-form-item>
        <!-- <el-form-item label="是否检查三要素" prop="status">
          <el-radio v-model="formData.isThreeElements" :label="1"
            >启用</el-radio
          >
          <el-radio v-model="formData.isThreeElements" :label="0"
            >禁用</el-radio
          >
        </el-form-item> -->
        <el-form-item label="创建数量" prop="number" v-if="isAdd">
          <el-input-number
            v-model="formData.number"
            :min="1"
            :max="10"
            label="描述文字"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加Url -->
    <el-dialog
      title="添加链接"
      :visible.sync="urlAvisible"
      @close="urlCancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formUrlData"
        :model="formUrlData"
        :rules="UrlRules"
        label-width="120px"
      >
        <el-form-item label="主体" prop="subject">
          <el-input v-model="formUrlData.subject" placeholder="请输入主体" />
        </el-form-item>
        <el-form-item label="链接" prop="url">
          <el-input v-model="formUrlData.url" placeholder="请输入链接" />
        </el-form-item>
        <el-form-item label="QQ" prop="qq">
          <el-radio v-model="formUrlData.qq" :label="1">可用</el-radio>
          <el-radio v-model="formUrlData.qq" :label="2">禁用</el-radio>
        </el-form-item>
        <el-form-item label="微信" prop="">
          <el-radio v-model="formUrlData.wechat" :label="1">可用</el-radio>
          <el-radio v-model="formUrlData.wechat" :label="2">禁用</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUrlForm">确 定</el-button>
        <el-button @click="urlCancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 复杂弹窗 -->
    <el-dialog
      title="复制链接"
      :visible.sync="copyAvisible"
      @close="copyCancel"
      width="900px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-table v-loading="loading" border :data="copyChannleList">
        <el-table-column label="主体" prop="subject" align="center" />
        <el-table-column label="链接" prop="url" align="center" />
        <el-table-column label="QQ" prop="qq" align="center">
          <template slot-scope="{ row }">
            <div>
              <el-switch
                @change="changeUrlStatus($event, 'qq', row)"
                v-model="row.qq"
                active-text="启用"
                inactive-text="禁用"
                :active-value="1"
                :inactive-value="2"
              >
              </el-switch>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="微信" prop="wechat" align="center">
          <template slot-scope="{ row }">
            <div>
              <el-switch
                @change="changeUrlStatus($event, 'wechat', row)"
                v-model="row.wechat"
                active-text="启用"
                inactive-text="禁用"
                :active-value="1"
                :inactive-value="2"
              >
              </el-switch>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center">
          <template slot-scope="{ row }">
            <div>
              <el-button type="text" @click="copyUrl(row)">复制</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 导出渠道列表 -->
    <el-dialog
      title="导出渠道失败原因"
      :visible.sync="exportVisible"
      center
      width="800px"
      append-to-body
      @close="cancleExport"
      :close-on-click-modal="false"
    >
      <el-form
        label-position="right"
        ref="exportFormData"
        :model="exportFormData"
        :rules="exportRules"
        label-width="180px"
      >
        <el-form-item label="请输入渠道ID" prop="channelId">
          <el-input v-model="exportFormData.channelId"></el-input>
        </el-form-item>
        <el-form-item label="请选择导出时间段" prop="stopTime">
          <el-date-picker
            v-model="value1"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            @change="getExportTime"
          >
          </el-date-picker>
        </el-form-item>
        <el-button
          type="primary"
          style="margin-left: 200px"
          @click="exportSubmit"
          >确认导出</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { getAffairsList } from "@/api/partyB";
import {
  queryAllPartyB,
  addChannleOne,
  getChannelList,
  addedUrlOne,
  copyChannleOne,
  editUrlStatus,
  autoCreateUrl,
  editChannleStatus,
  getDetailedChannel,
  editDetailedChannel,
  exportChannelFail,
  editChannleCheckStatus
} from "@/api/channeManage/channelList";
import { getProjectType } from "@/api/channeManage/domain";
export default {
  name: "domain",
  data() {
    return {
      isAdd: true,
      channleAvisible: false,
      urlAvisible: false,
      copyAvisible: false,
      exportVisible: false,
      loading: false,
      affairsList: [],
      partyBIdList: [],
      channelList: [],
      copyChannleList: [],
      projectList: [],
      value1: [],
      total: 0,
      exportFormData: {
        channelId: "",
        startTime: "",
        stopTime: "",
      },
      exportRules: {
        channelId: [
          {
            required: true,
            message: "请输入渠道ID",
            trigger: "blur",
          },
        ],
        stopTime: [
          {
            required: true,
            message: "请输入时间",
            trigger: "change",
          },
        ],
      },
      rules: {
        channelName: [
          {
            required: true,
            message: "请输入渠道名称",
            trigger: "blur",
          },
        ],
        commerce: [
          {
            required: true,
            message: "请选择商务",
            trigger: "blur",
          },
        ],
        partybId: [
          {
            required: true,
            message: "请选择渠道归属的乙方",
            trigger: "blur",
          },
        ],
        projectId: [
          {
            required: true,
            message: "请选择项目归属",
            trigger: "blur",
          },
        ],
        status: [
          {
            required: true,
            message: "请选择状态",
            trigger: "blur",
          },
        ],
        number: [
          {
            required: true,
            message: "请输入创建数量",
            trigger: "blur",
          },
        ],
        cooperativePrice: [
          {
            required: true,
            message: "请输入合作价格",
            trigger: "blur",
          },
        ],
        isThreeElements: [
          {
            required: true,
            message: "请选择是否开启三要素",
            trigger: "blur",
          },
        ],
        cooperation: [
          {
            required: true,
            message: "请输入合作方式",
            trigger: "blur",
          },
        ],
        type: [
          {
            required: true,
            message: "请选择合作类型",
            trigger: "blur",
          },
        ],
      },
      UrlRules: {
        subject: [
          {
            required: true,
            message: "请输入主体",
            trigger: "blur",
          },
        ],
        url: [
          {
            required: true,
            message: "请输入链接",
            trigger: "blur",
          },
        ],
      },
      formData: {
        channelName: "",
        commerce: "",
        partybId: "",
        status: 1,
        cooperation: "",
        cooperativePrice: "",
        number: 1,
        projectId: "",
        isThreeElements: 0,
        type: "",
      },
      formUrlData: {
        channelId: "",
        subject: "",
        url: "",
        qq: 1,
        wechat: 1,
      },
      queryParams: {
        status: "",
        commerce: "",
        channelName: "",
        partyUsername: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    async initPage() {
      const res = await getAffairsList();
      this.affairsList = res.data;

      getProjectType().then((res) => {
        this.projectList = res.data;
      });
    },
    getList() {
      this.loading = true;

      if (this.$store.getters.userInfo.userName === "dingcan") {
        this.queryParams.commerce = this.affairsList.find(
          (item) => item.nickName === "dingcan"
        ).userId;
      }

      getChannelList(this.queryParams).then((res) => {
        this.channelList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    //查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    //新增渠道
    addChannel() {
      this.isAdd = true;
      this.channleAvisible = true;
      queryAllPartyB().then((res) => {
        this.partyBIdList = res.data;
      });
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    editChannel(e) {
      queryAllPartyB().then((res) => {
        this.partyBIdList = res.data;
      });
      getDetailedChannel({ id: e.id }).then((res) => {
        this.isAdd = false;
        this.channleAvisible = true;
        this.formData.channelName = res.data.channelName;
        this.formData.commerce = res.data.commerce;
        this.formData.cooperation = res.data.cooperation;
        this.formData.partybId = res.data.partybId;
        this.formData.status = res.data.status;
        this.formData.cooperativePrice = res.data.cooperativePrice;
        this.formData.projectId = res.data.projectId;
        this.formData.isThreeElements = res.data.isThreeElements;
        this.formData.type = res.data.type;
        this.formData.id = e.id;
      });
    },
    //新增链接
    addUrl(row) {
      this.urlAvisible = true;
      this.formUrlData.channelId = row.id;
    },
    urlCancel() {
      this.formUrlData = {
        channelId: "",
        subject: "",
        url: "",
        qq: 1,
        wechat: 1,
      };
      this.urlAvisible = false;
      this.$refs.formUrlData.resetFields();
    },
    cancel() {
      this.isAdd = true;
      this.channleAvisible = false;
      this.formData = {
        channelName: "",
        commerce: "",
        partybId: "",
        status: 1,
        cooperation: "",
        cooperativePrice: "",
        number: 1,
        isThreeElements: 0,
        projectId: "",
        type: "",
      };

      this.$refs.formData.resetFields();
    },
    submitUrlForm() {
      this.$refs.formUrlData.validate((valid) => {
        if (valid) {
          addedUrlOne(this.formUrlData).then((res) => {
            if (res.code == 200) {
              this.$message.success("添加成功");
              this.urlCancel();
              this.getList();
            }
          });
        }
      });
    },
    //提交表单
    submitForm() {
      this.formData.cooperativePrice = this.formData.cooperativePrice * 1;
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addChannleOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.cancel();
                this.getList();
              }
            });
          } else {
            editDetailedChannel(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.cancel();
                this.getList();
              }
            });
          }
        }
      });
    },
    //复制链接弹窗
    copyChannle(row) {
      copyChannleOne({ channelId: row.id }).then((res) => {
        this.copyChannleList = res.data;
        this.copyAvisible = true;
      });
    },
    changeStatus(e, row) {
      this.$confirm(`确定${row.status == 2 ? "禁用" : "启用"}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          editChannleStatus({ id: row.id, status: e })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "禁用成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch((err) => {
          this.getList();
        });
    },
    copyCancel() {
      this.copyAvisible = false;
      this.copyChannleList = [];
    },
    copyUrl(row) {
      let url = row.url;
      let oInput = document.createElement("input");
      oInput.value = url;
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象;

      document.execCommand("Copy"); // 执行浏览器复制命令
      this.$message({
        message: "复制成功",
        type: "success",
      });
      oInput.remove();
    },
    changeUrlStatus(e, type, row) {
      var data = {
        id: row.id,
        qq: null,
        wechat: null,
      };
      if (type == "qq") {
        data.qq = e;
        data.wechat = row.wechat;
      } else {
        data.qq = row.qq;
        data.wechat = e;
      }
      editUrlStatus(data).then((res) => {
        if (res.code == 200) {
          this.$message.success("状态修改成功");
        }
      });
    },
    //一键生成
    handleAutoCreateUrl(row) {
      autoCreateUrl({ channelId: row.id }).then((res) => {
        if (res.code == 200) {
          this.$message.success("一键生成成功");
          this.getList();
        }
      });
    },
    getExportTime() {
      if (this.value1 !== null) {
        this.exportFormData.startTime = this.value1[0];
        this.exportFormData.stopTime = this.value1[1];
      } else {
        this.exportFormData.startTime = "";
        this.exportFormData.stopTime = "";
      }
    },
    exportSubmit() {
      this.$refs.exportFormData.validate((valid) => {
        if (valid) {
          exportChannelFail(this.exportFormData).then((res) => {
            let a = document.createElement("a");
            let blob = new Blob([res], { type: "application/vnd.ms-excel" });
            let objectUrl = URL.createObjectURL(blob);
            a.setAttribute("href", objectUrl);
            a.setAttribute("download", "渠道失败原因导出.xlsx");
            a.click();
            this.$message.success("导出成功");
          });
        }
      });
    },
    cancleExport() {
      this.value1 = [];
      this.exportVisible = false;
      this.exportFormData.startTime = "";
      this.exportFormData.stopTime = "";
      this.exportFormData.channelId = "";
      this.$refs.exportFormData.resetFields();
    },
    handleExport() {
      this.exportVisible = true;
    },
    changeCheckStatus(e,row) {

      this.$confirm(`确定${row.checkStatus == 2 ? "关闭" : "启用"}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          editChannleCheckStatus({ id: row.id, status: e })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch((err) => {
          this.getList();
        });
    },
  },
  async mounted() {
    this.queryParams.channelName = this.$route.query.channelName || "";

    await this.initPage();
    this.getList();
  },
};
</script>

<style>
.el-switch__label.el-switch__label--left.is-active {
  color: #333;
}
</style>
