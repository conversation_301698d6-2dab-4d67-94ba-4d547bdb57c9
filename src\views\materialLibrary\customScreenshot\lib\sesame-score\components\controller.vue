<template>
  <div class="controller-container">
    <!-- 使用截图按钮组件 -->
    <capture-button 
      :preview-url="previewUrl"
      :device-os="form.device.os"
      @capture-screenshot="$emit('capture-screenshot')"
    />
    
    <el-form :model="form" label-width="90px" size="small">
      <!-- 使用平台类型设置组件 -->
      <platform-type-setting 
        :deviceConfig="form.device" 
        @update:platform="updatePlatform" 
      />
      
      <!-- 使用统一的状态栏设置组件 -->
      <status-bar-setting 
        :statusBarConfig="form.statusBar" 
        @update:statusBar="updateStatusBar" 
      />

      <!-- 用户信息 -->
      <el-divider>用户信息</el-divider>
      <el-form-item label="用户名">
        <el-input v-model="form.userName" @change="handleConfigChange" clearable></el-input>
      </el-form-item>

      <div class="form-row">
        <el-form-item label="芝麻分" class="form-item-half">
          <el-input-number
            v-model="form.score"
            :min="350"
            :max="950"
            @change="handleConfigChange"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
        
        <el-form-item label="新消息数" class="form-item-half">
          <el-input-number
            v-model="form.messageCount"
            :min="0"
            @change="handleConfigChange"
            controls-position="right"
          ></el-input-number>
        </el-form-item>
      </div>

      <el-form-item label="进度条控制">
        <el-switch
          v-model="form.arcSettings.linkedToScore"
          @change="handleArcLinkChange"
          active-text="自动计算"
          inactive-text="手动控制"
        ></el-switch>
        <div class="tip-text">注意：自动计算的圆弧进度可能不准确，建议手动控制进度值</div>
      </el-form-item>

      <el-form-item label="圆弧进度" v-if="!form.arcSettings.linkedToScore">
        <el-slider
          v-model="form.arcSettings.percentage"
          :min="0.25"
          :max="0.9"
          :step="0.01"
          :format-tooltip="formatTooltip"
          @input="handleConfigChange"
        ></el-slider>
      </el-form-item>

      <!-- 服务配置 -->
      <el-divider>服务配置</el-divider>
      <div class="service-config-container">
        <!-- 芝麻粒配置 -->
        <div class="service-config-item">
          <div class="service-title">芝麻粒</div>
          <el-input
            v-model="form.services.sesameGrain.title"
            placeholder="标题"
            @change="handleConfigChange"
            size="small"
            clearable
          ></el-input>
          <el-input
            v-model="form.services.sesameGrain.subtitle"
            placeholder="副标题"
            @change="handleConfigChange"
            size="small"
            clearable
          ></el-input>
          <el-checkbox
            v-model="form.services.sesameGrain.showDot"
            @change="handleConfigChange"
          >显示红点</el-checkbox>
        </div>

        <!-- 额度配置 -->
        <div class="service-config-item">
          <div class="service-title">我的额度</div>
          <el-input
            v-model="form.services.quota.title"
            placeholder="标题"
            @change="handleConfigChange"
            size="small"
            clearable
          ></el-input>
          <el-input
            v-model="form.services.quota.subtitle"
            placeholder="副标题"
            @change="handleConfigChange"
            size="small"
            clearable
          ></el-input>
          <el-checkbox
            v-model="form.services.quota.showDot"
            @change="handleConfigChange"
          >显示红点</el-checkbox>
        </div>

        <!-- 芝麻名片配置 -->
        <div class="service-config-item">
          <div class="service-title">芝麻名片</div>
          <el-input
            v-model="form.services.sesameCard.title"
            placeholder="标题"
            @change="handleConfigChange"
            size="small"
            clearable
          ></el-input>
          <el-input
            v-model="form.services.sesameCard.subtitle"
            placeholder="副标题"
            @change="handleConfigChange"
            size="small"
            clearable
          ></el-input>
          <el-checkbox
            v-model="form.services.sesameCard.showDot"
            @change="handleConfigChange"
          >显示红点</el-checkbox>
        </div>
      </div>

      <!-- 广告图配置 -->
      <el-divider>广告图配置</el-divider>
      <el-form-item label="广告图">
        <el-radio-group v-model="form.adImage.url" @change="handleConfigChange">
          <div class="ad-options">
            <div class="ad-option">
              <el-radio label="https://jst.oss-utos.hmctec.cn/common/path/c226a93f56d545afa90124387838ed85.png">
                <img 
                  src="https://jst.oss-utos.hmctec.cn/common/path/c226a93f56d545afa90124387838ed85.png" 
                  alt="广告图1"
                  class="ad-thumbnail"
                />
              </el-radio>
            </div>
            <div class="ad-option">
              <el-radio label="https://jst.oss-utos.hmctec.cn/common/path/4545b0034f24431383db5cc0620b4846.png">
                <img 
                  src="https://jst.oss-utos.hmctec.cn/common/path/4545b0034f24431383db5cc0620b4846.png" 
                  alt="广告图2"
                  class="ad-thumbnail"
                />
              </el-radio>
            </div>
            <div class="ad-option">
              <el-radio label="https://jst.oss-utos.hmctec.cn/common/path/0a97ad4638354546826f06b61bb888e4.png">
                <img 
                  src="https://jst.oss-utos.hmctec.cn/common/path/0a97ad4638354546826f06b61bb888e4.png" 
                  alt="广告图3"
                  class="ad-thumbnail"
                />
              </el-radio>
            </div>
          </div>
        </el-radio-group>
      </el-form-item>

      <!-- 消息列表 -->
      <el-divider>消息列表</el-divider>
      <el-form-item>
        <div v-for="(message, index) in form.messages" :key="index" class="message-item">
          <el-input
            v-model="message.title"
            placeholder="消息标题"
            @change="handleConfigChange"
            clearable
          >
          </el-input>
          <el-input
            v-model="message.date"
            placeholder="日期"
            @change="handleConfigChange"
            clearable
          >
          </el-input>
          <el-button
            type="danger"
            icon="el-icon-delete"
            circle
            @click="removeMessage(index)"
          ></el-button>
        </div>
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="addMessage"
          size="small"
        >添加消息</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import StatusBarSetting from '../../components/StatusBarSetting.vue'
import PlatformTypeSetting from '../../components/PlatformTypeSetting.vue'
import CaptureButton from '../../components/CaptureButton.vue'

export default {
  name: 'SesameScoreController',
  components: {
    StatusBarSetting,
    PlatformTypeSetting,
    CaptureButton
  },
  props: {
    previewUrl: {
      type: String,
      default: null
    },
    configData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      form: this.deepClone(this.configData)
    }
  },
  watch: {
    configData: {
      handler(newVal) {
        this.form = this.deepClone(newVal);
      },
      deep: true
    }
  },
  methods: {
    updatePlatform(deviceConfig) {
      this.form.device = deviceConfig;
      this.handleConfigChange();
    },
    updateStatusBar(statusBarConfig) {
      this.form.statusBar = statusBarConfig;
      this.handleConfigChange();
    },
    handleConfigChange() {
      this.$emit('update:config', this.deepClone(this.form));
    },
    addMessage() {
      this.form.messages.push({
        title: '',
        date: ''
      });
      this.handleConfigChange();
    },
    removeMessage(index) {
      this.form.messages.splice(index, 1);
      this.handleConfigChange();
    },
    handleArcLinkChange() {
      this.handleConfigChange();
    },
    formatTooltip(value) {
      return `${Math.round(value * 100)}%`;
    },
    // 深拷贝函数
    deepClone(obj) {
      if (obj === null || typeof obj !== 'object') {
        return obj;
      }
      
      // 处理Date
      if (obj instanceof Date) {
        return new Date(obj.getTime());
      }
      
      // 处理Array
      if (Array.isArray(obj)) {
        return obj.map(item => this.deepClone(item));
      }
      
      // 处理Object
      const clonedObj = {};
      Object.keys(obj).forEach(key => {
        clonedObj[key] = this.deepClone(obj[key]);
      });
      
      return clonedObj;
    }
  }
}
</script>

<style scoped lang="scss">
.controller-container {
  .message-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;

    .el-input {
      margin-right: 10px;
    }
  }

  .el-form-item {
    margin-bottom: 22px;

    .el-input + .el-input {
      margin-top: 10px;
    }

    .el-checkbox {
      margin-top: 10px;
      display: block;
    }
  }

  .status-bar-icons {
    display: flex;
    align-items: center;
    
    .el-select {
      width: 100%;
    }
  }

  .unit-label {
    margin-left: 5px;
    color: #606266;
  }

  .action-buttons {
    margin: 20px 0;
    display: flex;
    gap: 10px;
  }

  .ad-options {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .ad-option {
    text-align: center;
  }

  .ad-thumbnail {
    width: 120px;
    height: 90px;
    object-fit: cover;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    margin-top: 5px;
  }

  .form-row {
    display: flex;
    gap: 20px;
  }

  .form-item-half {
    width: calc(50% - 10px);
  }

  .service-config-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .service-config-item {
    width: calc(33.33% - 15px);
    margin-bottom: 15px;
  }

  .service-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #409EFF;
  }

  .tip-text {
    margin-top: 5px;
    font-size: 12px;
    color: #E6A23C;
    line-height: 1.4;
  }
}
</style>