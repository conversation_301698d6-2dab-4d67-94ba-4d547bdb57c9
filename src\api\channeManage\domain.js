import request from '@/utils/request'

//添加域名
export const addDomainOne = (data) => {
    return request({
        url: '/loan/PartyB/orgAdd',
        method: "post",
        data
    })
}
//获取列表
export const getDomainList = (data) => {
    return request({
        url: '/loan/PartyB/orgList',
        method: "get",
        params: data
    })
}
//修改
export const editDomainOne = (data) => {
    return request({
        url: '/loan/PartyB/orgEdit',
        method: "post",
        data
    })
}
//删除
export const delDomainOne = (data) => {
    return request({
        url: '/loan/PartyB/orgRemove',
        method: "get",
        params: data
    })
}

//查询渠道配置
export const getConfigDetailed = (data) => {
    return request({
        url: "/loan/PartyB/configDetailed",
        method: 'get',
        params: data
    })
}
//查询渠道配置
export const addConfigDetailed = (data) => {
    return request({
        url: "/loan/backstageChannel/channelConfig",
        method: 'post',
        data
    })
}
//查询渠道配置标识
export const getChannelType=()=>{
    return request({
        url:'/loan/backstageChannel/queryStyle',
        method:'get'
    })
}
//查询项目
export const getProjectType=()=>{
    return request({
        url:'/loan/backstageChannel/listProject',
        method:'get'
    })
}