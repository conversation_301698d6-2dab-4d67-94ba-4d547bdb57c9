<template>
  <div class="message-assistant">
    <div class="message-header">
      <span class="message-title">消息助手</span>
      <div class="message-info">
        <span class="new-message-count">{{ messageCount }}条新消息</span>
        <span class="dot"></span>
        <img src="https://jst.oss-utos.hmctec.cn/common/path/1f914532710b45218af613d8597fca40.png" class="arrow">
      </div>
    </div>

    <div class="message-list">
      <div v-for="(message, index) in messages" :key="index" class="message-item">
        <div class="message-title">
          <img class="message-icon" :src="getIconUrl(message.iconType)" alt="">
          <div class="message-title-text">{{ message.title }}</div>
        </div>
        <div class="message-date">{{ message.date }}</div>
      </div>
    </div>
  </div>
</template>

<script>
// 导入图标图片
import barbaraFarmIcon from '@/assets/images/icon/barbaraFarm.png'
import taobaoIcon from '@/assets/images/icon/taobao.png'

export default {
  name: 'MessageAssistant',
  props: {
    messageCount: {
      type: Number,
      default: 0
    },
    messages: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    getIconUrl(iconType) {
      const iconMap = {
        'bus': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iIzQwOUVGRiIvPgo8cGF0aCBkPSJNOCA4SDI0VjI0SDhWOFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0xMCAxNkgxNFYyMEgxMFYxNloiIGZpbGw9IiM0MDlFRkYiLz4KPHA+YXRoIGQ9Ik0xOCAxNkgyMlYyMEgxOFYxNloiIGZpbGw9IiM0MDlFRkYiLz4KPC9zdmc+',
        'taobao': taobaoIcon,
        'shopping': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iIzY3QzIzQSIvPgo8cGF0aCBkPSJNOCA4SDI0VjI0SDhWOFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0xMiAxMkgxNlYxNkgxMlYxMloiIGZpbGw9IiM2N0MyM0EiLz4KPC9zdmc+',
        'monthly': 'https://jst.oss-utos.hmctec.cn/common/path/81b56869b302404dbb988305c8a08ce4.png',
        'finance': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iI0Y1NjU2NSIvPgo8cGF0aCBkPSJNOCA4SDI0VjI0SDhWOFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0xNiA4VjI0IiBzdHJva2U9IiNGNTY1NjUiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4=',
        'barbaraFarm': barbaraFarmIcon,
        'default': 'https://jst.oss-utos.hmctec.cn/common/path/81b56869b302404dbb988305c8a08ce4.png'
      };
      return iconMap[iconType] || iconMap['default'];
    }
  }
}
</script>

<style scoped lang="scss">
.message-assistant {
  width: 695px;
  position: relative;
  z-index: 1;
  margin: 0 28px;
  background-color: #FFFFFF;
  border-radius: 20px;
  padding-bottom: 25px;

  .message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 23px 20px 27px;

    .message-title {
      font-size: 28px;
      line-height: 41px;
      color: #333333;
      font-family: 'SourceHanSansSC-Medium';
    }

    .message-info {
      color: #999999;
      font-size: 24px;
      line-height: 35px;
      display: flex;
      align-items: center;
      font-family: 'SourceHanSansSC-Regular';

      .dot {
        width: 12px;
        height: 12px;
        background-color: #FF411C;
        border-radius: 50%;
        margin: 0 17px 0 4px;
        margin-top: 5px;
      }

      .arrow {
        width: 12px;
        height: 20px;
      }
    }
  }

  .message-list {
    display: flex;
    flex-direction: column;
    gap: 27px;

    .message-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;

      .message-title {
        display: flex;
        align-items: center;
        gap: 12px;

        .message-icon {
          transform: translateY(2px);
          width: 32px;
          height: 32px;
        }

        .message-title-text {
          font-size: 24px;
          line-height: 35px;
          color: #333333;
          font-family: 'SourceHanSansSC-Regular';
        }
      }

      .message-date {
        font-size: 24px;
        line-height: 35px;
        color: #999999;
      }
    }
  }
}
</style> 