<script>
import { updateUser } from '@/api/tencentXJ/userManagement'
import { getCityList } from '@/api/statisticalManage'
import {
  HOUSE_TYPES,
  VEHICLE_TYPES,
  PROVIDENT_FUND_TYPES,
  SESAME_CREDIT_TYPES,
  SOCIAL_SECURITY_TYPES,
  CREDIT_STATUS_TYPES,
  VOCATION_TYPES,
  INSURANCE_TYPES,
  SEX_TYPES
} from '@/const/userQualification'
import test from '@/utils/test'

export default {
  name: 'EditUser',
  data() {
    return {
      visible: false,
      form: {},
      cityList: [],
      qualificationOptions: {
        houseId: HOUSE_TYPES,
        vehicleId: VEHICLE_TYPES,
        providentId: PROVIDENT_FUND_TYPES,
        sesameId: SESAME_CREDIT_TYPES,
        socialId: SOCIAL_SECURITY_TYPES,
        overdueId: CREDIT_STATUS_TYPES,
        vocationId: VOCATION_TYPES,
        insureId: INSURANCE_TYPES,
        sex: SEX_TYPES
      },
      rules: {
        name: [
          { required: true, message: '请输入用户姓名', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            if (!test.chinese(value)) {
              callback(new Error('姓名必须为中文'));
            } else {
              callback();
            }
          }, trigger: 'blur' }
        ],
        sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
        age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
        demandAmount: [{ required: true, message: '请输入需求金额', trigger: 'blur' }],
        city: [{ required: true, message: '请选择城市', trigger: 'change' }],
        vocationId: [{ required: true, message: '请选择职业身份', trigger: 'change' }],
        providentId: [{ required: true, message: '请选择公积金情况', trigger: 'change' }],
        sesameId: [{ required: true, message: '请选择芝麻分', trigger: 'change' }],
        houseId: [{ required: true, message: '请选择房产情况', trigger: 'change' }],
        vehicleId: [{ required: true, message: '请选择车产情况', trigger: 'change' }],
        socialId: [{ required: true, message: '请选择社保情况', trigger: 'change' }],
        insureId: [{ required: true, message: '请选择保险情况', trigger: 'change' }],
        overdueId: [{ required: true, message: '请选择信用情况', trigger: 'change' }],
        talkId: [
          // { required: true, message: '请输入对话 id', trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    async open(user) {
      this.form = { ...user };

      Object.keys(this.form).forEach(key => {
        if (this.form[key] && typeof this.form[key] === 'number') {
          this.form[key] = this.form[key].toString();
        }
      });

      await this.fetchCityData();

      // 处理城市回显
      if (this.form.city && !Array.isArray(this.form.city)) {
        const cityName = this.form.city;
        const matchedCity = this.findCity(cityName);
        if (matchedCity) {
          this.form.city = matchedCity;
        } else {
          this.form.city = [cityName]; // 如果找不到匹配的省份,只保留市名
        }
      }

      this.visible = true;
    },

    findCity(cityName) {
      for (const province of this.cityList) {
        if (province.children) {
          for (const city of province.children) {
            if (city.value === cityName) {
              return [province.value, city.value];
            }
          }
        } else if (province.value === cityName) {
          return [province.value];
        }
      }
      return null;
    },

    async fetchCityData() {
      try {
        const res = await getCityList();
        this.cityList = res.data.map((item) => {
          if (item.citys) {
            return {
              value: item.name,
              label: item.name,
              children: item.citys.map((citem) => ({
                value: citem.name,
                label: citem.name
              }))
            };
          } else {
            return {
              value: item.name,
              label: item.name
            };
          }
        });
      } catch (error) {
        console.error('获取城市列表失败:', error);
        this.$message.error('获取城市列表失败');
      }
    },

    getParams() {
      const params = { ...this.form };
      delete params.phone;
      // 确保城市是字符串格式
      params.city = Array.isArray(params.city) ? params.city[params.city.length - 1] : params.city;
      delete params.applyResult;
      return params;
    },

    async handleSubmit() {
      try {
        await this.$refs.form.validate();
        await updateUser(this.getParams())
        this.$message.success('用户信息更新成功')
        this.$emit('success', this.getParams())
        this.closeDialog();
      } catch (error) {
        if (error === false) {
          this.$message.error('请填写所有必填项')
        }
      }
    },

    closeDialog() {
      this.visible = false;
      this.$refs.form.resetFields();
    }
  }
}
</script>

<template>
  <el-dialog
    title="编辑用户信息"
    :visible.sync="visible"
    width="1000px"
    @close="closeDialog"
    :append-to-body="true"
  >
    <el-form :model="form" :rules="rules" ref="form" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="用户姓名" prop="name">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="性别" prop="sex">
            <el-select v-model="form.sex">
              <el-option v-for="(value, key) in qualificationOptions.sex" :key="key" :label="value"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="年龄" prop="age">
            <el-input-number v-model="form.age" :min="0" :step="1" :precision="0"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="需求金额" prop="demandAmount">
            <el-input-number v-model="form.demandAmount" :min="0" :step="1" :precision="0"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="城市" prop="city">
            <el-cascader
              :show-all-levels="false"
              v-model="form.city"
              :options="cityList"
              clearable
              filterable
              placeholder="请选择城市"
            ></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="职业身份" prop="vocationId">
            <el-select v-model="form.vocationId">
              <el-option v-for="(value, key) in qualificationOptions.vocationId" :key="key" :label="value"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="公积金" prop="providentId">
            <el-select v-model="form.providentId">
              <el-option v-for="(value, key) in qualificationOptions.providentId" :key="key" :label="value"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="芝麻分" prop="sesameId">
            <el-select v-model="form.sesameId">
              <el-option v-for="(value, key) in qualificationOptions.sesameId" :key="key" :label="value"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="房产" prop="houseId">
            <el-select v-model="form.houseId">
              <el-option v-for="(value, key) in qualificationOptions.houseId" :key="key" :label="value"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="车产" prop="vehicleId">
            <el-select v-model="form.vehicleId">
              <el-option v-for="(value, key) in qualificationOptions.vehicleId" :key="key" :label="value"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="社保" prop="socialId">
            <el-select v-model="form.socialId">
              <el-option v-for="(value, key) in qualificationOptions.socialId" :key="key" :label="value"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保险" prop="insureId">
            <el-select v-model="form.insureId">
              <el-option v-for="(value, key) in qualificationOptions.insureId" :key="key" :label="value"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="信用情况" prop="overdueId">
            <el-select v-model="form.overdueId">
              <el-option v-for="(value, key) in qualificationOptions.overdueId" :key="key" :label="value"
                :value="key"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="对话 id" prop="talkId">
            <el-input v-model="form.talkId"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
