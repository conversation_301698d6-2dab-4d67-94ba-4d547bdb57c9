<template>
  <div class="app-container">
    <div class="title">产品链路测试</div>
    <!-- 筛选表单 -->
    <el-form :inline="true" :model="queryParams" class="demo-form-inline">
      <el-form-item label="链路ID">
        <el-input
          v-model="queryParams.linkId"
          placeholder="请输入链路ID"
        ></el-input>
      </el-form-item>
      <el-form-item label="链路名称">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入链路名称"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button
          type="primary"
          @click="openDialog('add')"
          style="margin-bottom: 20px"
          >新增链路</el-button
        >
        <el-button type="primary" @click="createWechatTestLink">企微测试批量生成</el-button>
        <el-button type="primary" @click="openPhoneCodeDialog">查询验证码</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="linkId" label="链路ID" width="400px">
        <template slot-scope="scope">
          <span>{{ scope.row.linkId }}</span>
          <el-button
            type="text"
            @click="clickCopy(scope.row.linkId)"
            style="margin-left: 10px"
            >复制</el-button
          >
        </template>
      </el-table-column>
      <el-table-column prop="name" label="链路名称"></el-table-column>
      <el-table-column label="半流程产品">
        <template slot-scope="scope">
          <div v-if="scope.row.halfProduct" v-html="getProductInfo(scope.row.halfProduct)"></div>
        </template>
      </el-table-column>
      <!-- 新增：企微产品列 -->
      <el-table-column label="企微产品">
        <template slot-scope="scope">
          <div v-if="scope.row.wechatProduct" v-html="getProductInfo(scope.row.wechatProduct)"></div>
        </template>
      </el-table-column>
      <!-- 新增：UV产品列 -->
      <el-table-column label="UV产品">
        <template slot-scope="scope">
          <div v-if="scope.row.uvProduct" v-html="getProductInfo(scope.row.uvProduct)"></div>
        </template>
      </el-table-column>
      <!-- 新增：联登产品列 -->
      <el-table-column label="联登产品">
        <template slot-scope="scope">
          <div v-if="scope.row.ldProduct" v-html="getProductInfo(scope.row.ldProduct)"></div>
        </template>
      </el-table-column>
      <!-- 新增：出量产品列 -->
      <el-table-column label="出量产品">
        <template slot-scope="scope">
          <div v-if="scope.row.outputProduct" v-html="getProductInfo(scope.row.outputProduct)"></div>
        </template>
      </el-table-column>
      <!-- 新增：失效时间  -->
      <el-table-column label="失效时间">
        <template slot-scope="scope">
          <div v-if="scope.row.expirationFlag!=1" v-html="scope.row.expirationDate" style="color: green;"></div>
          <div v-else style="color: red;">已过期</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250px">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" @click="copyLink(scope.row.linkId)"
            >复制链接</el-button
          >
          <el-button type="text" @click="showQrcode(scope.row)"
            >页面二维码</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogType === 'add' ? '新增链路' : '编辑链路'"
      :visible.sync="dialogVisible"
      width="600px"
      @closed="resetForm"
    >
      <el-form :model="form" label-width="120px">
        <el-form-item label="">
          <el-checkbox
            v-model="isOnline"
            @change="handleOnlineStatusChange">
            仅显示在线产品
          </el-checkbox>
        </el-form-item>
        <el-form-item label="链路名称" prop="name">
          <el-input
            style="width: 100%"
            v-model.trim="form.name"
            placeholder="请输入链路名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="半流程产品" prop="halfProduct">
          <el-select
            v-model="form.halfProduct"
            placeholder="请选择半流程产品"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="item in halfProduct"
              :key="`${item.platformType}-${item.productId}`"
              :label="`${item.platform} - ${item.productName} - ${item.productId}`"
              :value="`${item.platformType}-${item.productId}`"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企微产品" prop="wechatProduct">
          <el-select
            v-model="form.wechatProduct"
            placeholder="请选择企微产品"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="item in wechatProduct"
              :key="`${item.platformType}-${item.productId}`"
              :label="`${item.platform} - ${item.productName} - ${item.productId}`"
              :value="`${item.platformType}-${item.productId}`"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="UV产品" prop="uvProduct">
          <el-select
            v-model="form.uvProduct"
            placeholder="请选择UV产品"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="item in uvProduct"
              :key="`${item.platformType}-${item.productId}`"
              :label="`${item.platform} - ${item.productName} - ${item.productId}`"
              :value="`${item.platformType}-${item.productId}`"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联登产品" prop="ldProduct">
          <el-select
            v-model="form.ldProduct"
            placeholder="请选择联登产品"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="item in ldProduct"
              :key="`${item.platformType}-${item.productId}`"
              :label="`${item.platform} - ${item.productName} - ${item.productId}`"
              :value="`${item.platformType}-${item.productId}`"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出量产品" prop="outputProduct">
          <el-select
            v-model="form.outputProduct"
            placeholder="请选择出量产品"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="item in outputProduct"
              :key="`${item.platformType}-${item.productId}`"
              :label="`${item.platform} - ${item.productName} - ${item.productId}`"
              :value="`${item.platformType}-${item.productId}`"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 添加智优选 -->
        <el-form-item label="" prop="islinkTypes">
          <el-checkbox
            v-model="form.islinkTypes">
            智优选
          </el-checkbox>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 新增：二维码弹窗 -->
    <el-dialog
      title="页面二维码"
      :visible.sync="qrcodeDialogVisible"
      width="1000px"
    >
      <div class="qrcode-container">
        <qrcode-vue :value="qrcodeLink" :size="200" level="H"></qrcode-vue>
        <div style="margin-top: 10px; text-align: center">
          手机扫码，快速测试
        </div>
        <el-descriptions :column="1" border style="width: 100%;margin-top: 10px;">
          <el-descriptions-item label="链路名称">{{ currentLinkName }}</el-descriptions-item>
          <el-descriptions-item label="链路ID">
            {{ currentLinkId }}
            <el-button type="text" style="padding: 0;" icon="el-icon-copy-document" @click="clickCopy(currentLinkId)">复制</el-button>
          </el-descriptions-item>
          <el-descriptions-item label="半流程产品" v-if="getProductInfo(currentHalfProduct)">
            <div v-html="getProductInfo(currentHalfProduct)"></div>
          </el-descriptions-item>
          <el-descriptions-item label="企微产品" v-if="getProductInfo(currentWechatProduct)">
            <div v-html="getProductInfo(currentWechatProduct)"></div>
          </el-descriptions-item>
          <el-descriptions-item label="UV产品" v-if="getProductInfo(currentUvProduct)">
            <div v-html="getProductInfo(currentUvProduct)"></div>
          </el-descriptions-item>
          <el-descriptions-item label="联登产品" v-if="getProductInfo(currentLdProduct)">
            <div v-html="getProductInfo(currentLdProduct)"></div>
          </el-descriptions-item>
          <el-descriptions-item label="出量产品" v-if="getProductInfo(currentOutputProduct)">
            <div v-html="getProductInfo(currentOutputProduct)"></div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 在 template 中添加新的弹窗 -->
    <el-dialog
      title="企微测试链路生成"
      :visible.sync="wechatTestDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="wechat-test-dialog">
        <div v-if="wechatTestLoading" class="loading-content">
          <el-icon class="el-icon-loading"></el-icon>
          <p>正在生成企微测试链路，请稍候...</p>
        </div>
        <div v-else class="success-content">
          <i class="el-icon-success" style="font-size: 50px; color: #67C23A;"></i>
          <p>企微测试链路生成成功！</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: center;">
        <el-button @click="closeWechatTestDialog">关闭弹窗</el-button>
        <el-button
          type="primary"
          @click="goToWechatTestLink"
          :disabled="wechatTestLoading">
          查看链路
        </el-button>
      </div>
    </el-dialog>

    <!-- 引入组件 -->
    <phone-code-dialog
      :visible.sync="phoneCodeDialogVisible"
    />
  </div>
</template>

<script>
import {
  getProductLinkTestList,
  addProductLinkTest,
  updateProductLinkTest,
  getProductList,
  batchCreateQwLink,
} from "@/api/distributeStatistics/productLinkTest";
import clipboard from "@/utils/clipboard";
import { getBaseH5Url } from "@/utils/env";
import QrcodeVue from "qrcode.vue"; // 导入 QrcodeVue 组件
import PhoneCodeDialog from './components/PhoneCodeDialog.vue' // 导入组件

export default {
  name: "ProductLinkTest",
  components: {
    QrcodeVue, // 注册 QrcodeVue 组件
    PhoneCodeDialog // 注册组件
  },
  data() {
    return {
      queryParams: {
        linkId: "",
        name: "",
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      total: 0,
      halfProduct: [],
      dialogVisible: false,
      form: {
        id: null,
        name: "",
        halfProduct: "",
        wechatProduct: "",
        uvProduct: "",
        ldProduct: "",
        outputProduct: "",
        islinkTypes:false
      },
      dialogType: "add",
      qrcodeDialogVisible: false,
      qrcodeLink: "",
      wechatProduct: [],
      uvProduct: [],
      ldProduct: [],
      outputProduct: [],
      currentLinkId: "",
      currentLinkName: "",
      currentHalfProduct: "",
      currentWechatProduct: "",
      currentUvProduct: "",
      currentLdProduct: "",
      currentOutputProduct: "",
      isOnline: false,
      // 所有产品列表
      allProduct: {
        halfProduct: [],
        wechatProduct: [],
        uvProduct: [],
        ldProduct: [],
        outputProduct: [],
      },
      submitLoading: false,
      wechatTestLoading: false,
      wechatTestDialogVisible: false,
      phoneCodeDialogVisible: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async createWechatTestLink() {
      this.wechatTestDialogVisible = true;
      this.wechatTestLoading = true;

      try {
        await batchCreateQwLink({
          linkTemplateSig: "v59"
        });
        this.wechatTestLoading = false;
      } catch (error) {
        this.$message.error("生成失败");
        this.wechatTestDialogVisible = false;
      }
    },

    async getAllProductList() {
      const requestList = [
        getProductList({type: 1}),
        getProductList({type: 2}),
        getProductList({type: 3}),
        getProductList({type: 4}),
        getProductList({type: 5}),
      ];

      const [wechatProduct, halfProduct, ldProduct, uvProduct, outputProduct] = await Promise.all(requestList);

      this.allProduct = {
        wechatProduct: wechatProduct.data || [],
        halfProduct: halfProduct.data || [],
        ldProduct: ldProduct.data || [],
        uvProduct: uvProduct.data || [],
        outputProduct: outputProduct.data || [],
      }
    },

    clickCopy(text) {
      clipboard
        .copyText(text)
        .then(() => {
          this.$message.success("复制成功");
        })
        .catch(() => {
          this.$message.error("复制失败");
        });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        linkId: "",
        name: "",
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    async getList() {
      const response = await getProductLinkTestList(this.queryParams);
      if (response.code === 200) {
        this.tableData = response.rows;
        this.total = response.total;
      }
    },

    async getShowProductList() {
      const requestList = [
        this.getProductList(1, "wechatProduct"),
        this.getProductList(2, "halfProduct"),
        this.getProductList(3, "ldProduct"),
        this.getProductList(4, "uvProduct"),
        this.getProductList(5, "outputProduct"),
        this.getProductList(6, "linkTypes")
      ];
      await Promise.all(requestList);
    },

    async openDialog(type = "add", row = null) {
      this.dialogType = type;
      this.dialogVisible = true;
      this.isOnline = false;
      // this.form.islinkTypes = false;
      this.submitLoading = false;

      await this.getShowProductList();

      if (type === "edit" && row) {
        const halfProductObj = row.halfProduct ? JSON.parse(row.halfProduct) : {};
        const wechatProductObj = row.wechatProduct ? JSON.parse(row.wechatProduct) : {};
        const uvProductObj = row.uvProduct ? JSON.parse(row.uvProduct) : {};
        const ldProductObj = row.ldProduct ? JSON.parse(row.ldProduct) : {};
        const outputProductObj = row.outputProduct ? JSON.parse(row.outputProduct) : {};


        this.form = {
          id: row.id,
          name: row.name,
          halfProduct: halfProductObj.platformType
            ? `${halfProductObj.platformType}-${halfProductObj.productId}`
            : "",
          wechatProduct: wechatProductObj.platformType
            ? `${wechatProductObj.platformType}-${wechatProductObj.productId}`
            : "",
          uvProduct: uvProductObj.platformType
            ? `${uvProductObj.platformType}-${uvProductObj.productId}`
            : "",
          ldProduct: ldProductObj.platformType
            ? `${ldProductObj.platformType}-${ldProductObj.productId}`
            : "",
          outputProduct: outputProductObj.platformType
            ? `${outputProductObj.platformType}-${outputProductObj.productId}`
            : "",
          linkId: row.linkId,
          islinkTypes: row.linkTypes==3?true:false
        };
      }

    },
    handleEdit(row) {
      this.openDialog("edit", row);
    },

    resetForm() {
      this.form = {
        id: null,
        name: "",
        halfProduct: "",
        wechatProduct: "",
        uvProduct: "",
        ldProduct: "",
        outputProduct: "",
        linkTypes: 0,
        islinkTypes: false
      };
    },

    async submitForm() {
      const { name, halfProduct, wechatProduct, uvProduct, ldProduct, outputProduct } = this.form;

      if (!name) {
        this.$message.error("请输入链路名称");
        return;
      }

      // 检查至少选择一种产品
      if (!halfProduct && !wechatProduct && !uvProduct && !ldProduct && !outputProduct) {
        this.$message.error("请至少选择一种产品");
        return;
      }

      this.submitLoading = true;
      await this.getAllProductList();

      const formData = {
        ...this.form,
        halfProduct: this.getSelectedProduct(this.form.halfProduct, this.allProduct.halfProduct),
        wechatProduct: this.getSelectedProduct(this.form.wechatProduct, this.allProduct.wechatProduct),
        uvProduct: this.getSelectedProduct(this.form.uvProduct, this.allProduct.uvProduct),
        ldProduct: this.getSelectedProduct(this.form.ldProduct, this.allProduct.ldProduct),
        outputProduct: this.getSelectedProduct(this.form.outputProduct, this.allProduct.outputProduct),
        linkTypes: this.form.islinkTypes?3:0,
      };

      // 删除未选择的产品类型字段
      if (!formData.halfProduct) {
        delete formData.halfProduct
      }
      if (!formData.wechatProduct) {
        delete formData.wechatProduct
      }
      if (!formData.uvProduct) {
        delete formData.uvProduct
      }
      if (!formData.ldProduct) {
        delete formData.ldProduct
      }
      if (!formData.outputProduct) {
        delete formData.outputProduct
      }

      if (this.dialogType === "add") {
        delete formData.id;
      }

      let response;

      if (this.dialogType === "add") {
        response = await addProductLinkTest(formData);
      } else {
        response = await updateProductLinkTest(formData);
      }

      this.submitLoading = false;

      if (response.code === 200) {
        this.$message.success(this.dialogType === "add" ? "添加成功" : "更新成功");
        this.dialogVisible = false;
        this.getList();
      } else {
        this.$message.error(this.dialogType === "add" ? "添加失败" : "更新失败");
      }
    },
    copyLink(linkId) {
      const baseUrl = getBaseH5Url();
      const fullLink = `${baseUrl}?linkId=${linkId}`;

      clipboard
        .copyText(fullLink)
        .then(() => {
          this.$message.success("链接复制成功");
        })
        .catch(() => {
          this.$message.error("链接复制失败");
        });
    },

    // 新增：显示二维码弹窗
    showQrcode(row) {
      const baseUrl = getBaseH5Url();
      this.qrcodeLink = `${baseUrl}?linkId=${row.linkId}`;
      this.currentLinkId = row.linkId;
      this.currentLinkName = row.name;
      this.currentHalfProduct = row.halfProduct;
      this.currentWechatProduct = row.wechatProduct;
      this.currentUvProduct = row.uvProduct;
      this.currentLdProduct = row.ldProduct;
      this.currentOutputProduct = row.outputProduct;
      this.qrcodeDialogVisible = true;
    },
    async getProductList(type, targetArray) {
      const params = {
        type
      }

      if (this.isOnline) {
        params.isOnline = true
      }
      if (this.islinkTypes) {
        params.linkTypes = 3
      }

      const response = await getProductList(params);
      if (response.code === 200) {
        this[targetArray] = response.data;
      }
    },
    getSelectedProduct(selectedValue, productArray) {
      if (!selectedValue) {
        return "";
      }
      const [platformType, productId] = selectedValue.split("-");
      const product = productArray.find(
        (item) =>
          item.platformType == platformType && item.productId == productId
      )
      return product ? JSON.stringify(product) : "";
    },
    getProductInfo(product) {
      if (!product) return "";

      const {productName, platform, status, businessName} = JSON.parse(product);

      if (!productName || !platform) {
        return "";
      }

      // 商务名称
      const businessNameText = businessName ? `-${businessName}` : "";

      return `【${platform}】${productName}(${status === 1 ? '<span style="color: green;">在线</span>' : '<span style="color: red;">下线</span>'})${businessNameText}`;
    },
    handleOnlineStatusChange() {
      this.getShowProductList();
    },
    closeWechatTestDialog() {
      this.wechatTestDialogVisible = false;
    },
    goToWechatTestLink() {
      this.$router.push('/wechatTestLink');
      this.wechatTestDialogVisible = false;
    },
    openPhoneCodeDialog() {
      this.phoneCodeDialogVisible = true
    }
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .el-form {
    margin-bottom: 20px;
  }

  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }
}

.qrcode-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}

.wechat-test-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;

  .loading-content,
  .success-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;

    p {
      margin: 0;
      font-size: 16px;
      color: #606266;
    }
  }

  .el-icon-loading {
    font-size: 50px;
    color: #409EFF;
  }
}
</style>

