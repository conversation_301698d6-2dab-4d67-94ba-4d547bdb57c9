<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="乙方名称" prop="name">
        <el-input v-model="queryParams.partybName" size="small" placeholder="请输入乙方名称"></el-input>
      </el-form-item>
      <el-form-item label="申请类型" prop="type">
        <el-select v-model="queryParams.cashOutType" placeholder="请选择类型" clearable size="small">
          <el-option value="" label="全部"></el-option>
          <el-option value="0" label="请款"></el-option>
          <el-option value="1" label="退款"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-select v-model="queryParams.examineStatus" placeholder="请选择审核状态" clearable size="small">
          <el-option value="" label="全部"></el-option>
          <el-option value="0" label="待审核"></el-option>
          <el-option value="1" label="已驳回"></el-option>
          <el-option value="2" label="已通过"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableList" :row-key="(row) => row.examineId">
      <el-table-column label="提交时间" prop="submitTime" align="center" />
      <el-table-column label="乙方名称" prop="partybName" align="center" />
      <el-table-column label="申请类型" prop="cashOutType" align="center">
        <template slot-scope="{ row }">
          <div>
            <strong v-show="row.cashOutType == 1">退款</strong>
            <strong v-show="row.cashOutType != 1">请款</strong>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="申请金额" prop="money" align="center" />
      <el-table-column label="审核状态" prop="examineStatus" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-tag type="danger" size="small" v-if="row.examineStatus == 1" effect="plain">已驳回</el-tag>
            <el-tag type="warning" size="small" v-else-if="row.examineStatus == 0" effect="plain">待审核</el-tag>
            <el-tag type="success" size="small" v-else effect="plain">已通过</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="申请人" prop="userName" align="center" />
      <el-table-column label="我方主体" prop="subjectName" align="center" />
      <el-table-column label="操作" prop="phone" align="center">
        <template slot-scope="{row}">
          <div>
            <span class="f_c005 c-p" @click.stop="handleAudit(row)" v-hasPermi="['loan:partybeexamine:cashout']"
              v-if="row.seeoredit"> 审核
            </span>
            <span class="f_c005 c-p" @click.stop="handleAudit(row)" v-if="!row.seeoredit"
              v-hasPermi="['loan:partybeexamine:info']"> 查看
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <drawer :drawer.sync="showDrawer" :processStep="processStep" :formData="formData" @applyBtn="applyBtn" :statusCode="statusCode"
      :remark="remark"></drawer>
    <refundDrawer :drawer.sync="showRefoundDrawer" :processStep="processStep" :formData="formData" @refundBtn="refundBtn"
      :statusCode="statusCode" :imageUrl="imageUrl" :srcList="srcList" :remark="remark"></refundDrawer>
  </div>
</template>

<script>

import { partybeexamineList, partybeexamineCashout, partybeexamineRefund, financeDetail, partybeexamineRefundinfo } from "@/api/partyB";

import drawer from "./components/drawer.vue"
import refundDrawer from "./components/refundDrawer.vue"
export default {
  data() {
    return {


      tableList: [],
      total: 0,
      loading: false,
      queryParams: {
        partybName: "",
        cashOutType: "",
        examineStatus: "",
        pageNum: 1,
        pageSize: 10,
      },
      formData: {},
      refundStep: [
      ],
      processStep: [],
      statusJson: {
        0: "发起",
        1: "通过",
        2: "驳回",
        3: "结束",
      },
      //原因
      remark: "",
      //默认
      milepostActive: 2,
      // 动态添加类名
      stepActive: 'stepActive',
      //上传凭证
      imageUrl: "",
      //放大图片
      srcList: [],
      //点击状态
      statusCode: true,
      showDrawer: false,
      showRefoundDrawer: false


    }

  },
  components: {
    drawer,
    refundDrawer
  },
  mounted() {
    this.loading = false;
    this.getList();
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handlePartyB() {
      let url = this.$router.resolve('/partyB/partyblist?name=' + this.formData.partyName)
      window.open(url.href, '_blank')
    },
    getList() {
      this.loading = true;
      partybeexamineList(this.queryParams).then(res => {
        this.tableList = res.rows;
        this.total = res.total;
        this.loading = false;
      })
    },


    //操作审核
    handleAudit(data) {
      this.statusCode = data.seeoredit;
      if (data.cashOutType == 0) {
        let obj = {
          cashOutId: data.cashOutId
        }
        this.cashoutHanle(obj);
      } else {
        let obj = {
          refundId: data.refundId
        }
        this.refundHanle(obj);
      }
    },
    //请款审核详情
    cashoutHanle(obj) {
      financeDetail(obj).then(res => {
        this.showDrawer = true
        this.formData = JSON.parse(JSON.stringify(res.data));
        this.processStep = this.formData.list;
      })

    },
    //乙方请款同意/或拒绝
    applyBtn(_type, ev) {

      let obj = {
        cashoutRemark: ev.remark || '',
        cashoutType: _type || 0,
        id: this.formData.id || '',
        file: ev.file || ''
      }
      let data = new FormData();
      for (let i in obj) {
        data.append(i, obj[i]);
      }
      if (_type == 1 && !ev.remark) {
        this.$message.error("请输入审批意见");
        return;
      }
      if (_type == 0 && this.formData.iscashier && !ev.file) {
        this.$message.error("请上传凭证");
        return;
      }
      partybeexamineCashout(data).then(res => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.showDrawer = false;
          this.formData = {}
          this.getList();
        }
      })
    },

    //退款审核详情
    refundHanle(obj) {
      partybeexamineRefundinfo(obj).then(res => {
        this.showRefoundDrawer = true
        this.formData = JSON.parse(JSON.stringify(res.data));
        this.processStep = this.formData.list;
        this.imageUrl = this.formData.fileName;
        this.srcList = [this.imageUrl];
      })
    },
    //退款确认/拒绝 操作
    refundBtn(_type, ev) {
      let obj = {
        cashoutRemark: ev.remark || '',
        cashoutType: _type || 0,
        id: this.formData.id
      }
      if (_type == 1 && !ev.remark) {
        this.$message.error("请输入审批意见");
        return;
      }
      partybeexamineRefund(obj).then(res => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.showRefoundDrawer = false;
          this.formData = {};
          this.getList();
        }
      })
    },

  }
}
</script>

<style lang="scss" scoped>
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.flex {
  display: flex;
}

.check-info1 {
  margin-top: 10px;
  overflow: auto;
  max-height: 560px;
}
</style>
