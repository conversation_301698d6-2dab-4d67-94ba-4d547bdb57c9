<template>
  <el-drawer title="款项管理" :visible.sync="drawer_" size="665px" :direction="direction" @close="hanldeColse">
    <div class="drawer-conatiner">
      <div class="drawer-title">退款信息</div>
      <div class="drawer-wrap">
        <div class="flex">
          <span class="drawer-label">乙方名称</span>
          <span class="drawer-value">{{ formData.partyName }} <span class="f_c005 c-p"
              @click="handlePartyB">前往查看乙方详情>></span></span>
        </div>
        <div class="flex">
          <span class="drawer-label">乙方类型</span>
          <span class="drawer-value">{{ formData.partyTypes }}</span>
        </div>
        <div class="flex">
          <span class="drawer-label">当前在线渠道</span>
          <span class="drawer-value">{{ formData.partybId }}</span>
        </div>
        <div class="flex">
          <span class="drawer-label">我方收款主体</span>
          <span class="drawer-value">{{ formData.subject }}</span>
        </div>
        <div class="flex">
          <span class="drawer-label">收款开户行</span>
          <span class="drawer-value">{{ formData.openingBank }}</span>
        </div>
        <div class="flex">
          <span class="drawer-label">收款银行卡号</span>
          <span class="drawer-value">{{ formData.proceedsNumber }}</span>
        </div>
        <div class="flex">
          <span class="drawer-label">收款银行卡号</span>
          <span class="drawer-value">{{ formData.proceedsNumber }}</span>
        </div>
        <div class="flex">
          <span class="drawer-label">退款金额</span>
          <span class="drawer-value">{{ formData.refundMoney }}</span>
        </div>
        <div class="flex">
          <span class="drawer-label">退款原因</span>
          <span class="drawer-value">{{ formData.refundCause }}</span>
        </div>
        <div class="flex">
          <span class="drawer-label">退款凭证</span>
          <span class="drawer-value"> <el-image style="width: 100px; height: 100px" :src="imageUrl"
              :preview-src-list="srcList">
            </el-image></span>
        </div>
        <div class="flex" v-show="statusCode">
          <span class="drawer-label">审批意见</span>
          <span class="drawer-value"><el-input type="textarea" style="width:300px" :rows="2" v-model="remark"
              maxlength="20" show-word-limit placeholder="请输入审批意见,驳回必填" /></span>

        </div>
        <div class="flex" v-show="statusCode">
          <span class="drawer-label"></span>
          <span class="drawer-value flex">
            <span class="submit-btn flex justify-content-c align-items-c c-p " style="border: 1px solid #DCDCDC;"
              @click.stop="refundBtn(1)">驳 回</span>
            <span class="submit-btn flex justify-content-c align-items-c c-p b_primay" style="color:#fff"
              @click.stop="refundBtn(0)">通 过</span>
          </span>

        </div>
      </div>
      <div class="drawer-title">审核进度</div>

      <div class="drawer-process">
        <div v-for="(item, index) in processStep" :key="index">
          <div class="flex align-items-c">
            <span
              :class="['iconfont f-suceess drawer-process-icon', iconList[item.status], colorList[item.status]]"></span>
            <span class="drawer-process-user">{{ item.isCreateBy ? "" : item.codeName + '-' }} {{ item.userName }}
              ({{
                item.isCreateBy ?
                '发起人'
                : '审核人'
              }}) </span>
            <span :class="['drawer-process-status', tagList[item.status]]" v-if="item.status != 0"> {{
              statusJson[item.status] || "" }}</span>
          </div>
          <div :class="['drawer-process-line', item.status == 0 ? 'boder-none' : '']">
            <div class="drawer-process-time">{{ item.checkTime || "-" }}</div>
            <div :class="['drawer-process-remark', item.status == 2 ? 'fail' : '']"
              v-if="item.checkRemark && item.status != 3">{{
                item.checkRemark || "-" }}</div>
          </div>
        </div>
      </div>
    </div>

  </el-drawer>
</template>

<script>

export default {

  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    statusCode: {
      type: Boolean,
      default: true
    },
    imageUrl: {
      type: String,
      default: ''
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    srcList: {
      type: Array,
      default: () => []
    },
    processStep: {
      type: Array,
      default: () => []
    },
    formData: {
      type: Object,
      default: () => { }
    },


  },
  data() {
    return {
      remark: "",
      statusJson: {
        0: "发起",
        1: "已通过",
        2: "已驳回",
        3: "结束",
        4: '待审核'
      },
      iconList: {
        0: 'icon-a-chaji6',
        1: 'icon-a-paichu3',
        2: 'icon-a-paichu2',
        3: 'icon-a-paichu3',
        4: 'icon-a-paichu1',
      },

      colorList: {
        0: 'f-suceess',
        1: 'f-suceess',
        2: 'f-danger',
        3: 'f-suceess',
        4: 'f-info',
      },
      tagList: {
        0: 'success',
        1: 'success',
        2: 'danger',
        3: 'success',
        4: 'info',
      },
      invoiceType: {
        1: '对公无发票',
        2: '对公有发票',
        3: '对私有发票',
        4: '对私无发票',
      },

    }
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer
      },
      set(v) {
        this.$emit("update:drawer", false)
      }
    }
  },
  methods: {

    hanldeColse() {
      this.remark = ""
    },
    refundBtn(type) {
      this.$emit('refundBtn', type, { remark: this.remark })
    },
    handlePartyB() {
      let url = this.$router.resolve('/partyB/partyblist?name=' + this.formData.partyName)
      window.open(url.href, '_blank')
    },

  }
}

</script>
<style  lang="scss" scoped>
.drawer-conatiner {
  padding: 20px;
}

.drawer-title {
  font-size: 18px;
  font-weight: 400;
  color: #3D3D3D;
  position: relative;
  padding-left: 20px;

  &::after {
    content: "";
    display: block;
    position: absolute;
    width: 4px;
    height: 20px;
    background: #e37318;
    top: 4px;
    left: 0;
  }
}

.drawer-wrap {
  box-sizing: border-box;
  padding-top: 20px;

  .drawer-label {
    width: 150px;
    font-size: 16px;
    text-align: right;
    margin-right: 30px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 10px;
  }

  .drawer-value {
    font-size: 16px;
    color: #000000;
    margin-bottom: 10px;
  }
}

.drawer-process {
  padding: 20px;

  &-icon {
    margin-right: 10px;
  }

  &-user {
    font-size: 16px;
    color: #181716;
    width: 250px;
  }

  &-status {
    margin-left: 100px;
    font-size: 12px;
    padding: 2px 4px;
    background: #E5F9E9;
    border-radius: 2px;

    &.success {
      background: #E5F9E9;
      color: #3FA372;
      border: 1px solid #3FA372;
    }

    &.danger {
      color: #FF0000;
      background: #FFECEC;
      border: 1px solid #f00;
    }

    &.info {
      color: #FF8F1F;
      background: #FFE8D1;
      border: 1px solid #FF8F1F;
    }
  }

  &-line {
    margin: 3px 0px 5px 8px;
    border-left: 1px dashed #D8D8D8;
    padding-left: 20px;
    padding-bottom: 10px;

  }

  &-time {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 10px;
  }

  &-remark {
    width: 100%;
    background: #FAFAFA;
    border-radius: 4px 4px 4px 4px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    padding: 20px;

    &.fail {
      background: #FFECEC;
      color: #f00;
      border: 1px solid #f00;
    }
  }
}

.submit-btn {
  width: 90px;
  height: 32px;
  font-size: 12px;
  margin-right: 20px;
  border-radius: 5px;
}

.boder-none {
  border: none;
}</style>
