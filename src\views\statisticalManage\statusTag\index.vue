<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="今日:" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="昨日:" prop="time">
        <el-date-picker
          v-model="value2"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="筛选渠道">
        <el-select
          ref="select"
          size="small"
          v-model="checkValue"
          filterable
          collapse-tags
          clearable
          placeholder="请选择"
          @change="handleChange"
        >
          <el-option
            v-for="item in optionsList"
            :key="item.id"
            :label="`id:${item.id}--${item.channelName}`"
            :value="`id:${item.id}--${item.channelName}`"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-form-item label="惠逸花1">
          <el-checkbox v-model="showHyh" :disabled="showZyx"></el-checkbox>
        </el-form-item>
        <el-form-item label="惠逸花2">
          <el-checkbox v-model="showZyx" :disabled="showHyh"></el-checkbox>
        </el-form-item>
      </el-form-item>
    </el-form>
    <div class="content-box">
      <!-- <div class="chart">
        <div class="chart-box" ref="echartUv"></div>
      </div> -->
      <el-table border :data="filterList" @sort-change="handleSortChange">
        <el-table-column label="渠道ID" prop="channelId" align="center" />
        <el-table-column label="渠道名称" prop="channelName" align="center" />
        <el-table-column label="落地页UV" prop="uvNum" align="center" />
        <el-table-column label="昨日落地页UV" prop="uvNum1" align="center" />
        <!-- <el-table-column
          label="落地页注册数"
          prop="registerNum"
          align="center"
        />
        <el-table-column
          label="昨日落地页注册数"
          prop="registerNum1"
          align="center"
        /> -->
        <el-table-column
          label="咨询订单提交数量"
          prop="fromNum"
          align="center"
        />
        <el-table-column
          label="昨日咨询订单提交数量"
          prop="fromNum1"
          align="center"
        />
        <el-table-column
          label="收益"
          prop="profit"
          align="center"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template slot-scope="{ row }">
            <div
              :style="{
                color: `${row.profit > row.profit1 ? 'red' : 'blue'}`,

                'font-size': '16px',
              }"
            >
              {{ row.profit }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="昨日收益" prop="profit1" align="center" />
      </el-table>
    </div>
  </div>
</template>

<script>
import { getNewChannelStatisticsList } from "@/api/statisticalManage";
import { getChannelList } from "@/api/productManage/product";
export default {
  data() {
    return {
      checkValue: "",
      showHyh: false,
      showZyx: false,
      optionsList: [],
      isPhone: false,
      value1: [this.getTodayTime() + " 00:00:00", this.getTodayTime(true)],
      value2: [this.getYesTime() + " 00:00:00", this.getYesTime(true)],
      total: 0,
      dataList: [],
      queryParams: {
        channelName: "",
        startTime: `${this.getTodayTime()} 00:00:00`,
        endTime: this.getTodayTime(true),
      },
      queryParams1: {
        channelName: "",
        startTime: `${this.getYesTime()} 00:00:00`,
        endTime: `${this.getYesTime(true)}`,
      },
    };
  },
  methods: {
    handleQuery() {
      if (!(this.value1 && this.value2))
        return this.$message.error("请选择时间");
      if (this.value1 !== null) {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      if (this.value2 !== null) {
        this.queryParams1.pageNum = 1;
        this.queryParams1.startTime = this.value2[0];
        this.queryParams1.endTime = this.value2[1];
      } else {
        this.queryParams1.startTime = "";
        this.queryParams1.endTime = "";
      }
      this.getList();
    },

    getTodayTime(flag) {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      let hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
      let minute =
        date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      let second =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      if (!flag) {
        return YY + "-" + MM + "-" + DD;
      } else {
        return `${YY}-${MM}-${DD} ${hour}:${minute}:${second}`;
      }
    },

    getYesTime(flag) {
      var date = new Date(new Date().valueOf() - 86400 * 1000);

      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      let hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
      let minute =
        date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      let second =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      if (!flag) {
        return YY + "-" + MM + "-" + DD;
      } else {
        return `${YY}-${MM}-${DD} ${hour}:${minute}:${second}`;
      }
    },
    //前端排序
    handleSortChange(column) {
      this.proptype = column.prop;
      let sortingType = column.order;

      if (sortingType == "ascending") {
        //正序
        this.dataList = this.dataList.sort(
          (a, b) => b[this.proptype] - a[this.proptype]
        );
      }
      if (sortingType == "descending") {
        // 倒序
        this.dataList = this.dataList.sort(
          (a, b) => a[this.proptype] - b[this.proptype]
        );
      }
    },
    getList() {
      this.dataList = [];
      Promise.all([
        getNewChannelStatisticsList(this.queryParams),
        getNewChannelStatisticsList(this.queryParams1),
      ]).then(([res1, res2]) => {
        res1.rows.map((item1) => {
          res2.rows.map((item2) => {
            if (item1.channelId == item2.channelId) {
              this.dataList.push({
                uvNum: item1.uvNum,
                registerNum: item1.registerNum,
                fromNum: item1.fromNum,
                onlineApplyNum: item1.onlineApplyNum,
                profit: item1.profit,
                channelName: item1.channelName,
                uvNum1: item2.uvNum,
                registerNum1: item2.registerNum,
                fromNum1: item2.fromNum,
                onlineApplyNum1: item2.onlineApplyNum,
                profit1: item2.profit,
                channelId:item1.channelId
              });
            }
          });
        });
      });
    },
    handleChange(row) {
      // if (!this.checkValue) return this.$message.error("请选择渠道");
      // this.queryParams.channelName = row.split("--")[1];
      // this.queryParams1.channelName = row.split("--")[1];
      // this.getList();
    },
  },
  computed: {
    filterList() {
      let data = this.checkValue.length
        ? this.dataList.filter((item) => {
            let id =
              this.checkValue &&
              this.checkValue.split("--")[0].split(":")[1] * 1;

            if (id) {

              return item.channelId == id;
            }
          })
        : this.dataList;

      if (this.showZyx && data.length) {
        data = data.filter((item, index) => {
          return item.channelName.includes("智优选");
        });
      }
      if (this.showHyh && data.length) {
        data = this.checkValue.length
          ? data.filter((item, index) => {
              return !item.channelName.includes("智优选");
            })
          : data.filter((item, index) => {
              return index && !item.channelName.includes("智优选");
            });
      }

      return data;
    },
  },

  mounted() {
    this.getList();
    getChannelList().then((res) => {
      this.optionsList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
.content-box {
  .chart {
    display: flex;
    flex-direction: column;
    padding: 0 30px 10px;
    box-sizing: border-box;
    .chart-title {
      font-size: 30px;
      color: #333;
      font-weight: bold;
    }
    .chart-box {
      margin-top: 40px;
      flex: 1;
    }
  }
}

@media only screen and (max-width: 1400px) {
  .content-box {
    display: flex;
    flex-direction: column;

    .chart {
      width: 100%;
      margin-bottom: 20px;

      .chart-box {
        width: 100%;
      }
    }
  }
}

@media only screen and (max-width: 750px) {
  .content-box {
    display: flex;

    .chart {
      padding: 0;

      .chart-title {
        font-size: 18px;
        color: #333;
        font-weight: bold;
      }
    }
  }
}
</style>
