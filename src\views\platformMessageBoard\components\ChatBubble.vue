<template>
  <div :class="['chat-bubble', chatItem.type === 1 ? 'admin-bubble' : 'crm-bubble']">
    <div class="bubble-header">
      <el-avatar :size="avatarSize" :src="getAvatarUrl()">
        {{ getAvatarText(chatItem.type) }}
      </el-avatar>
      <span class="bubble-user">{{ chatItem.createBy }}</span>
      <span class="bubble-time">{{ formatTime(chatItem.created) }}</span>
    </div>
    <div class="bubble-content">
      {{ chatItem.content }}
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'

export default {
  name: 'ChatBubble',
  props: {
    chatItem: {
      type: Object,
      required: true,
      default: () => ({})
    },
    avatarSize: {
      type: Number,
      default: 24
    }
  },
  methods: {
    /** 获取头像URL */
    getAvatarUrl() {
      // 这里可以根据类型返回不同的头像URL，暂时返回空字符串使用默认头像
      return ''
    },
    /** 获取头像文本 */
    getAvatarText(type) {
      return type === 1 ? 'A' : 'C'
    },
    /** 格式化时间为简短格式 */
    formatTime(date) {
      if (!date) return ''
      const now = dayjs()
      const target = dayjs(date)
      const diffDays = now.diff(target, 'day')

      if (diffDays === 0) {
        // 今天，显示时间
        return target.format('HH:mm')
      } else if (diffDays === 1) {
        // 昨天
        return '昨天 ' + target.format('HH:mm')
      } else if (diffDays < 7) {
        // 一周内，显示星期
        const weekdays = ['日', '一', '二', '三', '四', '五', '六']
        return '周' + weekdays[target.day()] + ' ' + target.format('HH:mm')
      } else {
        // 超过一周，显示日期
        return target.format('MM-DD HH:mm')
      }
    }
  }
}
</script>

<style scoped>
.chat-bubble {
  max-width: 80%;
  position: relative;
}

.admin-bubble {
  align-self: flex-end;
  margin-left: auto;
}

.crm-bubble {
  align-self: flex-start;
  margin-right: auto;
}

.bubble-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  font-size: 12px;
  gap: 8px;
}

.admin-bubble .bubble-header {
  flex-direction: row-reverse;
}

.bubble-user {
  font-weight: 500;
  color: #303133;
}

.bubble-time {
  color: #909399;
  font-size: 11px;
}

.bubble-content {
  background: #f5f7fa;
  padding: 8px 12px;
  border-radius: 12px;
  color: #606266;
  line-height: 1.4;
  word-break: break-word;
  font-size: 14px;
  position: relative;
}

.admin-bubble .bubble-content {
  background: #95EC69;
  color: #333333;
}

.crm-bubble .bubble-content {
  background: #FFFFFF;
  color: #333333;
}

/* 聊天气泡尾巴效果 */
.admin-bubble .bubble-content::after {
  content: '';
  position: absolute;
  top: 6px;
  right: -6px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-left-color: #95EC69;
}

.crm-bubble .bubble-content::after {
  content: '';
  position: absolute;
  top: 6px;
  left: -6px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-right-color: #FFFFFF;
}
</style>