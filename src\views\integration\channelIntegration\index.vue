<template>
  <div class="app-container">
    <!-- 筛选表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item label="渠道" prop="channelId">
        <el-select
          v-model="queryParams.channelId"
          placeholder="请选择渠道"
          clearable
          filterable
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.id"
            :label="`${item.id}-${item.channelName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="整合渠道名称" prop="integrationChannelName">
        <el-input
          v-model="queryParams.integrationChannelName"
          placeholder="请输入整合渠道名称"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          @click="handleAddConfig"
          >新增关联关系</el-button
        >
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table
      :data="accountList"
      :row-key="getRowKey"
      ref="accountTable"
    >
      <el-table-column
        label="整合渠道名称"
        align="center"
        width="200"
        prop="integrationName"
      />
      <el-table-column label="关联渠道" align="center" min-width="280">
        <template slot-scope="scope">
          <div style="display: flex; flex-direction: column; gap: 4px;align-items: center;">
            <el-tag
              v-for="channelId in (scope.row.vo && scope.row.vo.channelIds) || []"
              :key="channelId"
              type="info"
              size="mini"
              style="width: fit-content;"
            >
              {{ getChannelName(channelId) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="关联乙方" align="center" min-width="280">
        <template slot-scope="scope">
          <div style="display: flex; flex-direction: column; gap: 4px;align-items: center;">
            <el-tag
              v-for="partyId in (scope.row.vo && scope.row.vo.partyIds) || []"
              :key="partyId"
              type="success"
              size="mini"
              style="width: fit-content;"
            >
              {{ getPartyName(partyId) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="余额" align="center" width="120">
        <template slot-scope="scope">
          {{ scope.row.balanceAmount || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="成本" align="center" width="120">
        <template slot-scope="scope">
          {{ scope.row.costAmount || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="预付" align="center" width="120">
        <template slot-scope="scope">
          {{ scope.row.prepayAmount || 0 }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="300"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleShowDetail(scope.row)"
            >详情</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleUpdateConfig(scope.row)"
            >修改关联</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleUpdateBalance(scope.row)"
            >编辑余额</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleAddInvoice(scope.row)"
            >新增发票</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleDeleteConfig(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 渠道关联关系配置弹窗 -->
    <ConfigDialog
      ref="configDialog"
      @success="refreshData"
    />

    <!-- 发票管理弹窗 -->
    <InvoiceDialog
      ref="invoiceDialog"
      @success="refreshData"
    />

    <!-- 余额编辑弹窗 -->
    <BalanceDialog
      ref="balanceDialog"
      @success="refreshData"
    />

    <!-- PDF预览弹窗 -->
    <PdfPreviewDialog
      ref="pdfPreviewDialog"
    />

    <!-- 详情抽屉 -->
    <el-drawer
      title="详情信息"
      :visible.sync="detailDrawerVisible"
      direction="rtl"
      size="60%"
      :before-close="handleDrawerClose"
    >
      <div style="padding: 20px" v-if="currentDetailRow">
        <!-- 财务信息 -->
        <el-descriptions :column="4" border style="margin-bottom: 20px">
          <el-descriptions-item label="成本金额">
            {{ (currentDetailRow.vo && currentDetailRow.vo.costAmount) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="预付金额">
            {{ (currentDetailRow.vo && currentDetailRow.vo.prepayAmount) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="已开票金额">
            {{ (currentDetailRow.vo && currentDetailRow.vo.hasInvoiceAmount) || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="未开票金额">
            {{ (currentDetailRow.vo && currentDetailRow.vo.notInvoiceAmount) || 0 }}
          </el-descriptions-item>
        </el-descriptions>

        <el-tabs v-model="detailActiveTab" type="card">
          <el-tab-pane label="成本统计" name="statistics">
            <el-table
              :data="
                (currentDetailRow.vo && currentDetailRow.vo.operatingStatisticsList) || []
              "
              border
              size="small"
            >
              <el-table-column prop="channelName" label="渠道名称" />
              <el-table-column prop="channelCost" label="渠道成本" />
              <el-table-column prop="enteringDate" label="录入日期" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="预付记录" name="prepay">
            <el-table
              :data="(currentDetailRow.vo && currentDetailRow.vo.partybExamineList) || []"
              border
              size="small"
            >
              <el-table-column prop="partybId" label="乙方ID" width="120">
                <template slot-scope="scope">
                  {{ getPartyName(scope.row.partybId) }}
                </template>
              </el-table-column>
              <el-table-column prop="money" label="金额" />
              <el-table-column prop="submitTime" label="提交时间" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="发票记录" name="invoice">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  size="small"
                  @click="handleAddInvoice(currentDetailRow)"
                  >新增发票</el-button
                >
              </el-col>
            </el-row>
            <el-table
              :data="
                (currentDetailRow.vo &&
                  currentDetailRow.vo.integrationInvoiceRecordList) ||
                []
              "
              border
              size="small"
            >
              <el-table-column prop="partyId" label="乙方" width="120">
                <template slot-scope="scope">
                  {{ getPartyName(scope.row.partyId) }}
                </template>
              </el-table-column>
              <el-table-column prop="fileData" label="发票日期" />
              <el-table-column prop="amount" label="发票金额" />
              <el-table-column prop="remark" label="备注" />
              <el-table-column prop="file" label="发票文件" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.row.file" class="file-preview-table">
                    <template v-if="isImageFile(scope.row.file)">
                      <el-image
                        :src="scope.row.file"
                        :preview-src-list="[scope.row.file]"
                        fit="cover"
                        style="width: 30px; height: 30px; cursor: pointer"
                      />
                      <span style="margin-left: 8px; color: #409EFF;">图片</span>
                    </template>
                    <template v-else>
                      <i
                        :class="getFileIcon(scope.row.file)"
                        :style="{ color: getFileColor(scope.row.file) }"
                      ></i>
                      <el-button
                        size="mini"
                        type="text"
                        @click="handlePreviewFile(scope.row.file)"
                        class="file-link"
                        >查看</el-button
                      >
                    </template>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleUpdateInvoice(scope.row, currentDetailRow)"
                    >修改</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleDeleteInvoice(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  deleteIntegrationConfig,
  deleteIntegrationInvoice,
  getIntegrationAccountList,
} from "@/api/integration";
import { getAllChannelList } from "@/api/channeManage/channelList";
import { queryAllPartyB } from "@/api/channeManage/channelList";
import dayjs from "dayjs";
import ConfigDialog from "./components/ConfigDialog.vue";
import InvoiceDialog from "./components/InvoiceDialog.vue";
import BalanceDialog from "./components/BalanceDialog.vue";
import PdfPreviewDialog from "./components/PdfPreviewDialog.vue";

export default {
  name: "ChannelIntegration",
  components: {
    ConfigDialog,
    InvoiceDialog,
    BalanceDialog,
    PdfPreviewDialog,
  },
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 账本列表
      accountList: [],
      // 渠道选项
      channelOptions: [],
      // 乙方选项
      partyOptions: [],

      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        startDate: null,
        endDate: null,
        channelId: null,
        integrationChannelName: null,
      },
      // 详情抽屉相关
      detailDrawerVisible: false,
      currentDetailRow: null,
      detailActiveTab: "statistics",

    };
  },
  created() {
    this.setDefaultDate();
    this.getList();
    this.getChannelOptions();
    this.getPartyOptions();
  },
  methods: {
    /** 查询账本列表 */
    getList() {
      // 处理日期范围
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startDate = this.dateRange[0];
        this.queryParams.endDate = this.dateRange[1];
      } else {
        this.queryParams.startDate = null;
        this.queryParams.endDate = null;
      }

      return getIntegrationAccountList(this.queryParams).then((response) => {
        this.accountList = response.data || [];
        return response;
      });
    },
    /** 刷新数据并更新抽屉中的当前行 */
    refreshData() {
      const currentDetailRowId = this.currentDetailRow ? this.currentDetailRow.id : null;

      this.getList().then(() => {
        // 如果抽屉是打开状态，更新当前详情行数据
        if (this.detailDrawerVisible && currentDetailRowId) {
          const updatedRow = this.accountList.find(row => row.id === currentDetailRowId);
          if (updatedRow) {
            this.currentDetailRow = updatedRow;
          }
        }
      });
    },
    /** 获取渠道选项 */
    getChannelOptions() {
      getAllChannelList().then((response) => {
        this.channelOptions = response.data || [];
      });
    },
    /** 获取乙方选项 */
    getPartyOptions() {
      queryAllPartyB().then((response) => {
        this.partyOptions = response.data || [];
      });
    },
    /** 设置默认日期 */
    setDefaultDate() {
      const todayStr = dayjs().format('YYYY-MM-DD');
      this.dateRange = [todayStr, todayStr];
    },
    /** 根据渠道ID获取渠道名称 */
    getChannelName(channelId) {
      const channel = this.channelOptions.find((item) => item.id == channelId);
      return channel ? `${channel.id}-${channel.channelName}` : channelId;
    },
    /** 根据乙方ID获取乙方名称 */
    getPartyName(partyId) {
      const party = this.partyOptions.find((item) => item.id == partyId);
      return party ? `${party.id}-${party.name}` : partyId;
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.dateRange = [];
      this.setDefaultDate();
      this.handleQuery();
    },

    /** 新增关联关系按钮操作 */
    handleAddConfig() {
      this.$refs.configDialog.openAdd(this.channelOptions, this.partyOptions);
    },
    /** 修改关联关系按钮操作 */
    handleUpdateConfig(row) {
      const configData = {
        id: row.integrationId,
        integrationChannelName: row.integrationName,
        integrationStatus: row.integrationStatus || 1,
        channelIds: (row.vo && row.vo.channelIds) || [],
        partyIds: (row.vo && row.vo.partyIds) || [],
      };
      this.$refs.configDialog.openEdit(configData, this.channelOptions, this.partyOptions);
    },
    /** 删除关联关系按钮操作 */
    handleDeleteConfig(row) {
      const integrationId = row.integrationId;
      this.$modal
        .confirm("是否确认删除该渠道关联关系？")
        .then(() => {
          return deleteIntegrationConfig({ id: integrationId });
        })
        .then(() => {
          this.refreshData();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 新增发票按钮操作 */
    handleAddInvoice(row) {
      this.$refs.invoiceDialog.openAdd(row, this.partyOptions);
    },
    /** 修改发票按钮操作 */
    handleUpdateInvoice(row, integratedRow) {
      this.$refs.invoiceDialog.openEdit(row, integratedRow, this.partyOptions);
    },
    /** 删除发票按钮操作 */
    handleDeleteInvoice(row) {
      const invoiceId = row.id;
      this.$modal
        .confirm("是否确认删除该发票记录？")
        .then(() => {
          return deleteIntegrationInvoice({ id: invoiceId });
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          this.refreshData();
        })
        .catch(() => {});
    },
    /** 预览文件 */
    handlePreviewFile(fileUrl) {
      if (!fileUrl) return;

      const extension = this.getFileExtension(fileUrl);
      if (extension === 'pdf') {
        // PDF文件使用弹窗预览
        this.$refs.pdfPreviewDialog.open(fileUrl);
      } else {
        // 其他文件直接在新窗口打开
        window.open(fileUrl, "_blank");
      }
    },
    /** 根据文件URL获取文件图标 */
    getFileIcon(fileUrl) {
      if (!fileUrl) return "fas fa-file";

      const extension = this.getFileExtension(fileUrl);
      const iconMap = {
        pdf: "fas fa-file-pdf",
        jpg: "fas fa-file-image",
        jpeg: "fas fa-file-image",
        png: "fas fa-file-image",
      };

      return iconMap[extension] || "fas fa-file";
    },
    /** 根据文件URL获取文件颜色 */
    getFileColor(fileUrl) {
      if (!fileUrl) return "#606266";

      const extension = this.getFileExtension(fileUrl);
      const colorMap = {
        pdf: "#e74c3c",
        jpg: "#f39c12",
        jpeg: "#f39c12",
        png: "#f39c12",
      };

      return colorMap[extension] || "#606266";
    },
    /** 获取文件扩展名 */
    getFileExtension(fileUrl) {
      if (!fileUrl) return "";

      const url = fileUrl.split("?")[0]; // 移除查询参数
      const parts = url.split(".");
      return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : "";
    },
    /** 判断是否为图片文件 */
    isImageFile(fileUrl) {
      if (!fileUrl) return false;
      
      const extension = this.getFileExtension(fileUrl);
      const imageExtensions = ['jpg', 'jpeg', 'png'];
      return imageExtensions.includes(extension);
    },
    /** 编辑余额按钮操作 */
    handleUpdateBalance(row) {
      this.$refs.balanceDialog.open(row);
    },
    /** 显示详情抽屉 */
    handleShowDetail(row) {
      this.currentDetailRow = row;
      this.detailActiveTab = "statistics";
      this.detailDrawerVisible = true;
    },
    /** 关闭详情抽屉 */
    handleDrawerClose(done) {
      this.currentDetailRow = null;
      this.detailActiveTab = "statistics";
      done();
    },
    /** 获取行键 */
    getRowKey(row) {
      return row.id;
    },
  },
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.file-preview-table {
  display: flex;
  align-items: center;
  gap: 6px;
}

.file-preview-table i {
  font-size: 16px;
  flex-shrink: 0;
}
</style>
