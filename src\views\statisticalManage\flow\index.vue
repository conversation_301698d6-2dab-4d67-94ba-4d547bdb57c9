<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="城市">
        <BaseCascader
          :options="cityList"
          :is_deep="true"
          :iscollapse="true"
          :has_all_select="true"
          @getOptions="getCityCodes"
        />
      </el-form-item>
      <el-form-item label="推广类型" prop="mid">
        <el-select
          @change="handleQuery"
          clearable
          v-model="queryParams.mid"
          placeholder="请选择推广类型"
          size="small"
          style="width: 100%"
        >
          <el-option label="助贷-银行机构" :value="1"> </el-option>
          <el-option label="线上-贷超" :value="2"> </el-option>
          <el-option label="线上-大额" :value="3"> </el-option>
          <el-option label="助贷-全国1" :value="4"> </el-option>
          <el-option label="助贷-全国2" :value="5"> </el-option>
          <el-option label="助贷-全国3" :value="6"> </el-option>
          <el-option label="助贷-全国4" :value="7"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border @sort-change="handleSortChange" :data="dataList">
      <el-table-column label="城市" prop="cityName" align="center" />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="UV"
        prop="uvCount"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="订单数"
        prop="bd"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="咨询数"
        prop="successCount"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="咨询失败数"
        prop="failCount"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="当前城市在线推广数量"
        prop="currentProductCount"
        align="center"
      />

      <el-table-column
        label="城市最高合作价"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="maxPrice"
        align="center"
      />
      <el-table-column
        label="城市最低合作价"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="minPrice"
        align="center"
      />
    </el-table>
  </div>
</template>
<script>
import { getUrbanTrafficDistributionStatistics } from "@/api/statisticalManage";
import { getCityAll } from "@/api/productManage/product";
import BaseCascader from "@/components/cascader";
export default {
  data() {
    return {
      value1: [this.getWeekTime(), this.getTime()],
      dataList: [],
      cityList: [],
      codeList: [],
      queryParams: {
        cityCodes: [],
        mid: "",
        startTime: this.getWeekTime(),
        endTime: this.getTime(),
      },
    };
  },
  methods: {
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    getWeekTime() {
      let wekDay = Date.now() - 86400 * 7 * 1000;
      var date = new Date(parseInt(wekDay));
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    //搜索
    handleQuery() {
      if (this.value1 !== null) {
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }

      this.queryParams.cityCodes = this.codeList.map((item) => {
        if (item.length > 1) {
          return item[1];
        } else {
          return item[0];
        }
      });

      this.getList();
    },
    //排序
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    getCityCodes(e) {
      this.codeList = e;
    },
    getList() {
      getUrbanTrafficDistributionStatistics(this.queryParams).then((res) => {
        this.dataList = res.data;
      });
    },
  },
  components: {
    BaseCascader,
  },
  mounted() {
    this.getList();

    //获取城市格式化
    getCityAll().then((res) => {
      let data = null;
      data = res.data.map((item) => {
        if (item.citys) {
          {
            return {
              value: item.code,
              label: item.name,
              disabled: false,
              children: [
                ...item.citys.map((citem) => {
                  return {
                    value: citem.code,
                    label: citem.name,
                    disabled: false,
                  };
                }),
              ],
            };
          }
        } else {
          return {
            value: item.code,
            label: item.name,
            disabled: false,
          };
        }
      });
      this.cityList = JSON.parse(JSON.stringify(data));
    });
  },
};
</script>

<style lang="scss" scoped>
</style>
