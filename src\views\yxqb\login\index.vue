<template>
  <div class="login-container">
    <el-form 
      ref="loginForm" 
      :model="loginForm" 
      :rules="loginRules"
      class="login-form" 
      label-position="top"
      @keyup.enter.native="handleLogin"
    >
      <div class="title-container">
        <h3 class="title">系统登录</h3>
      </div>
      <el-form-item label="用户名" prop="loginName">
        <el-input 
          v-model.trim="loginForm.loginName"
          placeholder="请输入用户名"
          prefix-icon="el-icon-user"
        ></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input 
          v-model.trim="loginForm.password"
          type="password" 
          placeholder="请输入密码"
          prefix-icon="el-icon-lock"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button 
          type="primary" 
          class="submit-btn" 
          @click="handleLogin"
        >登录</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        loginName: '',
        password: ''
      },
      loginRules: {
        loginName: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.$store.dispatch('yxqb/Login', this.loginForm).then(res => {
            this.$router.replace({ path: '/yxqbPushList' })
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.login-form {
  width: 400px;
  padding: 35px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;

  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
  }

  .el-form-item {
    margin-bottom: 25px;
  }

  .el-input {
    height: 40px;
    
    ::v-deep .el-input__inner {
      height: 40px;
      line-height: 40px;
      border-radius: 4px;
      padding-left: 45px;
      border: 1px solid #dcdfe6;
      
      &:focus {
        border-color: #409EFF;
      }
    }
    
    ::v-deep .el-input__prefix {
      left: 10px;
      font-size: 16px;
      color: #909399;
    }
  }
}

.title-container {
  margin-bottom: 30px;
  
  .title {
    font-size: 26px;
    color: #303133;
    text-align: center;
    font-weight: bold;
  }
}

.submit-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 4px;
  margin-top: 10px;
  
  &:hover {
    opacity: 0.9;
  }
}
</style>
