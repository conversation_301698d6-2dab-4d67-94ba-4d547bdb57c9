import request from '@/utils/request'

// 查询子平台的返点申请
export function getRebateList(data) {
  return request({
    url: `/loan/xm/partyfirst/rebate/gitlist?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    method: 'post',
    data
  })
}

// 审核前查看明细
export function getRebateDetail(params) {
  return request({
    url: '/loan/xm/partyfirst/rebate/before/apply',
    method: 'get',
    params
  })
}

// 审核
export function auditRebate(data, platformType) {
  return request({
    url: `/loan/xm/partyfirst/rebate/apply?platformType=${platformType}`,
    method: 'post',
    data
  })
}

// 文件审核
export function auditRebateFile(data, platformType) {
  return request({
    url: `/loan/xm/partyfirst/rebate/applyByAudit?platformType=${platformType}`,
    method: 'post',
    data
  })
}

