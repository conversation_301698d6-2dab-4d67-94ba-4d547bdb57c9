<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="接口名称" prop="name">
        <el-input size="small" clearable placeholder="请输入接口名称" @keyup.enter.native="handleQuery"
          v-model="queryParams.name"></el-input>
      </el-form-item>
      <el-form-item label="唯一标识" prop="name">
        <el-input size="small" clearable placeholder="请输入唯一标识" @keyup.enter.native="handleQuery"
          v-model="queryParams.apiKey"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" v-hasPermi="['loan:productApi:query']"
          @click="handleQuery">筛选</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" size="mini" v-hasPermi="['loan:productApi:add']"
          @click="handleadd">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" border :data="apiList">
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="名称" prop="name" align="center" />
      <el-table-column label="ApiKey-与代码配置一致 " prop="apiKey" align="center" />
      <el-table-column label="创建人" prop="createBy" align="center" />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="修改时间" prop="updateTime" align="center" />
      <el-table-column label="修改人" prop="updateBy" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button size="mini" type="text" icon="el-icon-edit" v-hasPermi="['loan:productApi:edit']"
              @click="handleEdit(row)">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" v-hasPermi="['loan:productApi:remove']"
              @click="handleDelete(row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="isadd ? '新增商户对接类型' : '修改商户对接类型'" :visible.sync="apiAvisible" @close="cancle" width="600px"
      append-to-body center :close-on-click-modal="false">
      <el-form ref="formData" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input size="small" v-model="formData.name"></el-input>
        </el-form-item>
        <el-form-item label="唯一标识" prop="apiKey">
          <el-input size="small" v-model="formData.apiKey" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="其它参数" prop="apiJson" v-if="!isadd">
          <div style="display:flex">
            <el-input type="textarea" disabled :rows="8" v-model="formData.apiJson" placeholder="请输入其它参数" />
            <el-button size="mini" style="height:30px" @click="handleConfigKey">配置</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancle">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="其他参数" :visible.sync="otherAvisible" width="800px" append-to-body center
      @close="otherAvisible = false" :close-on-click-modal="false">
      <el-form label-width="200px">
        <el-form-item :label="item[0]" v-for="(item, index) in paramData" :key="index">
          <div style="display: flex;">
            <el-input v-model="paramJson[item[0]]" placeholder="请输入" />
            <el-button type="info" size="small" @click="hanldeDatail(index, item[0])">删除</el-button>
            <el-button @click="hanldeAddKey" type="primary" v-if="(paramData.length - 1) == index" size="small">添加key
            </el-button>
          </div>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="hanldeAddKey" type="primary" v-if="!paramData.length" size="small">添加key
        </el-button>
        <el-button type="primary" @click="submitJson">确 定</el-button>
        <el-button @click="cancelKey">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="添加key" width="900px" :visible.sync="keyvisible" append-to-body center :close-on-click-modal="false"
      @close="keyName = ''">
      <el-form label-width="200px">
        <el-form-item label="key名称">
          <el-input v-model="keyName" placeholder="请输入key名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitJsonKey">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getProducApiList,
  addProducApiOne,
  editProducApiOne,
  delProducApiOne,

} from "@/api/productManage/api";
export default {
  name: "ApiType",
  data() {
    return {
      loading: false,
      apiAvisible: false,
      apiList: [],
      isadd: true,
      total: 0,
      keyName: "",
      paramData: [],
      paramJson: {},
      otherAvisible: false,
      keyvisible: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: "",
        apiKey: "",
      },
      formData: {
        name: "",
        apiKey: "",
      },
      rules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        apiKey: [
          { required: true, message: "请输入唯一标识", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleadd() {
      this.isadd = true;
      this.apiAvisible = true;
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isadd) {
            addProducApiOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.getList();
                this.cancle();
              }
            });
          } else {
            editProducApiOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.getList();
                this.cancle();
              }
            });
          }
        }
      });
    },
    cancle() {
      this.formData = {
        name: "",
        apiKey: "",
      };
      this.apiAvisible = false;
      this.$refs.formData.resetFields();
    },
    handleEdit(row) {
      this.isadd = false;
      this.apiAvisible = true;
      this.formData.apiKey = row.apiKey;
      this.formData.apiJson = row.apiJson;
      this.formData.name = row.name;
      this.formData.id = row.id;
    },
    handleDelete(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delProducApiOne(row.id).then((res) => {
            if (res.code == 200) {
              this.$message.success("删除成功");
              this.getList();
            }
          });
          console.log(row.id);
        })
        .catch((err) => { });
    },
    handleConfigKey() {
      this.otherAvisible = true
      this.paramData = Object.entries(JSON.parse(this.formData.apiJson || "{}"))
      this.paramJson = JSON.parse(this.formData.apiJson || '{}')
    },
    hanldeAddKey() {
      this.keyvisible = true
    },

    cancelKey() {
      this.paramData = []
      this.paramJson = {}
      this.otherAvisible = false
    },
    hanldeAddKey() {
      this.keyvisible = true
    },
    submitJson() {
      this.formData.apiJson = Object.keys(this.paramJson).length ? JSON.stringify(this.paramJson) : ''
      this.otherAvisible = false
    },
    hanldeDatail(index, key) {
      this.paramData.splice(index, 1)
      delete this.paramJson[key]
    },
    submitJsonKey() {
      if (!this.keyName) return this.$message.error('key不能为空')
      this.paramData.push([this.keyName, ""])
      this.keyvisible = false
    },
    getList() {
      getProducApiList(this.queryParams).then((res) => {
        this.apiList = res.rows;
        this.total = res.total;
        this.loading = false;
        if (this.apiList.length == 0 && this.queryParams.pageNum > 1) {
          this.queryParams.pageNum = this.queryParams.pageNum - 1;
          this.loading = false;
          this.getList();
          return;
        }
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped></style>
