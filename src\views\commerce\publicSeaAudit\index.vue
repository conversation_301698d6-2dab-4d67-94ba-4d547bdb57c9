<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form :inline="true" :model="queryParams" ref="queryForm" size="small">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dateRange"
          clearable
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="商户名称" prop="partyName">
        <el-input
          v-model="queryParams.partyName"
          clearable
          placeholder="请输入商户名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="平台" prop="platformId">
        <el-select v-model="queryParams.platformId" placeholder="请选择平台" clearable>
          <el-option v-for="item in platformOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="tableData" border>
      <el-table-column label="平台" prop="platformName" align="center" />
      <el-table-column label="商户名称" prop="name" align="center" />
      <el-table-column label="电话号码" prop="tel" align="center" />
      <el-table-column label="商户类型" prop="type" align="center" :formatter="(row) => typeJson[row.type]" />
      <el-table-column label="原商务" prop="userName" align="center" />
      <el-table-column label="首充时间" prop="firstChargeTime" align="center" />
      <el-table-column label="移入时间" prop="migrationTime" align="center" />
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleAudit(scope.row)">审核</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 审核对话框 -->
    <el-dialog title="公海商户审核" :visible.sync="dialogVisible" width="500px" append-to-body>
      <div class="audit-info">
        <div class="info-item"><span class="label">公司名称:</span> <span class="value">{{currentRow && currentRow.name}}</span></div>
        <div class="info-item"><span class="label">电话号码:</span> <span class="value">{{currentRow && currentRow.tel}}</span></div>
        <div class="info-item"><span class="label">商户类型:</span> <span class="value">{{currentRow && typeJson[currentRow.type]}}</span></div>
        <div class="info-item"><span class="label">原商务:</span> <span class="value">{{currentRow && currentRow.userName}}</span></div>
        <div class="info-item"><span class="label">可用余额:</span> <span class="value red">{{ currentRow && currentRow.availableAmount }}</span></div>
        <div class="info-item"><span class="label">保证金:</span> <span class="value red">{{ currentRow && currentRow.depositAmount }}</span></div>
      </div>
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px" class="mt-20">
        <el-form-item label="审核意见" prop="reason">
          <el-input type="textarea" v-model="auditForm.reason" placeholder="请输入审核意见，拒绝必须填写" :rows="3"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit(2)">通 过</el-button>
        <el-button type="danger" @click="submitAudit(3)">拒 绝</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPublicSeaAuditList, auditPublicSea } from '@/api/commerce/publicSeaAudit'
import { getPlatformList } from '@/api/xmxrChannelManage/channelList'
import Pagination from '@/components/Pagination'

export default {
  name: 'PublicSeaAudit',
  components: {
    Pagination
  },
  data() {
    return {
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 平台选项
      platformOptions: [],
      // 日期范围
      dateRange: [],
      // 商户类型映射
      typeJson: {
        0: '门店商户',
        1: '个人商户',
        2: '线上商户'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        platformId: '',
        partyName: '',
        startTime: '',
        endTime: ''
      },
      // 审核对话框
      dialogVisible: false,
      // 当前审核的商户
      currentRow: null,
      // 审核表单
      auditForm: {
        partyFirstId: '',
        platformId: '',
        status: 2,
        reason: ''
      },
      // 审核表单校验规则
      auditRules: {
        status: [
          { required: true, message: '请选择审核结果', trigger: 'change' }
        ]
      },
      // 不通过原因校验规则
      reasonRules: [
        { required: true, message: '请输入不通过原因', trigger: 'blur' }
      ]
    }
  },
  created() {
    this.getPlatformOptions()
    this.getList()
  },
  methods: {
    // 获取平台列表
    getPlatformOptions() {
      getPlatformList().then(res => {
        this.platformOptions = res.data || []
      })
    },
    // 获取列表数据
    getList() {
      // 处理日期范围
      if (this.dateRange && this.dateRange.length) {
        this.queryParams.startTime = this.dateRange[0]
        this.queryParams.endTime = this.dateRange[1]
      } else {
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
      }
      
      getPublicSeaAuditList(this.queryParams).then(res => {
        this.tableData = res.rows || []
        this.total = res.total
      })
    },
    // 查询按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.dateRange = []
      this.queryParams.startTime = ''
      this.queryParams.endTime = ''
      this.handleQuery()
    },
    // 审核操作
    handleAudit(row) {
      this.currentRow = row
      this.auditForm = {
        partyFirstId: row.partyFirstId,
        platformId: row.platformId,
        status: 2,
        reason: ''
      }
      this.dialogVisible = true
    },
    // 提交审核
    submitAudit(status) {
      this.auditForm.status = status;
      
      // 如果是拒绝且没有填写原因，则验证不通过
      if (status === 3 && !this.auditForm.reason) {
        this.$message.warning('拒绝时必须填写审核意见');
        return;
      }
      
      auditPublicSea(this.auditForm).then(res => {
        this.$message.success('审核操作成功');
        this.dialogVisible = false;
        this.getList();
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.el-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

.audit-info {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  
  .info-item {
    margin-bottom: 10px;
    font-size: 14px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      display: inline-block;
      width: 80px;
      color: #606266;
    }
    
    .value {
      font-weight: 500;
      color: #303133;
      
      &.red {
        color: #f56c6c;
      }
    }
  }
}
</style>