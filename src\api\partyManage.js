import request from '@/utils/request'
//新增甲方用户
export const AddUsermanageOne = (data) => {
  return request({
    url: '/loan/partyaAdmin/usermanage/add',
    method: "post",
    data
  })
}
//编辑甲方用户
export const editUsermanageOne = (data) => {
  return request({
    url: '/loan/partyaAdmin/usermanage/edit',
    method: "post",
    data
  })
}
//编辑甲方用户状态
export const editUsermanageStatus = (data) => {
  return request({
    url: '/loan/partyaAdmin/usermanage/editStatus',
    method: "post",
    data
  })
}
//编辑甲方用户进量
export const editUsermanagePushStatus = (data) => {
  return request({
    url: '/loan/partyaAdmin/usermanage/editPushStatus',
    method: "post",
    data
  })
}
//获取甲方用户列表
export const getUsermanageList = (data) => {
  return request({
    url: '/loan/partyaAdmin/usermanage/list',
    method: "get",
    params: data
  })
}

//获取账户是否账号主账号
export const getUserAccountType = () => {
  return request({
    url: '/loan/partyaAdmin/user/getAccountType',
    method: "get"
  })
}
//获取账户是否账号主账号
export const editUserPushStatus = (data) => {
  return request({
    url: '/loan/partyaAdmin/user/editPushStatus',
    method: "post",
    data
  })
}
//获取用户列表
export const getUserList = (data) => {
  return request({
    url: "/loan/partyaAdmin/user/userList",
    method: "get",
    params: data
  })
}
//获取产品列表
export const getProductList = (data) => {
  return request({
    url: '/loan/partyaAdmin/product/list',
    method: "get",
    params: data
  })
}
//产品修改状态
export const productUpdateStatus = (data) => {
  return request({
    url: "/loan/partyaAdmin/product/updateStatus",
    method: "post",
    data
  })
}
//查询甲方用户权限
export const getPartyAdminRole = (data) => {
  return request({
    url: "/loan/partyaAdmin/usermanage/queryRole",
    method: 'get',
    params: data
  })
}
//修改甲方用户权限
export const editPartyAdminRole = (data) => {
  return request({
    url: "/loan/partyaAdmin/usermanage/role",
    method: 'post',
    data
  })
}

//获取甲方用户新颜报告
export const getReportDetail = (id) => {
  return request({
    url: `/loan/partyaAdmin/user/query/${id}`,
    method: 'get'

  })
}

//获取用户手机号
export const getUserPhone = (id) => {
  return request({
    url: `/loan/partyaAdmin/user/getPhone/${id}`,
    method: "get"
  })
}
//修改用户手机号
export const editUserPhoneStatus = (data) => {
  return request({
    url: `/loan/partyaAdmin/user/updateStatus`,
    method: "post",
    data
  })
}
//获取用户统计
export const getUserTotal = () => {
  return request({
    url: "/loan/partyaAdmin/user/getTotal",
    method: "get"
  })
}
//获取线索分配统计
export const getClewStatistics = (data) => {
  return request({
    url: "/loan/partyaAdmin/user/clewStatistics",
    method: "get",
    params: data
  })
}
export const getPartyaAdminProductlist = () => {
  return request({
    url: "/loan/partyaAdmin/user/productlist",
    method: "get",

  })
}

//获取甲方消耗列表
export const getPartAconsumeList = (data) => {
  return request({
    url: "/loan/partyaAdmin/productProfit/list",
    method: "get",
    params: data
  })
}
//获取产品列表
export const getPartyFirstInfoList = () => {
  return request({
    url: '/loan/partyaAdmin/productProfit/partyFirstInfo',
    method: "get"
  })
}
//甲方充值
export const partyaAdminreCharge = (data) => {
  return request({
    url: '/loan/partyaAdmin/order/recharge',
    method: 'post',
    data
  })
}
//获取甲方充值记录
export const getChargeOrderList = (data) => {

  return request({
    url: "/loan/partyaAdmin/order/list",
    method: 'get',
    params: data
  })
}
//获取甲方支付状态
export const getPartypayStatus = (data) => {
  return request({
    url: "/loan/partyaAdmin/order/query/payStatus",
    method: 'get',
    params: data
  })
}
//获取甲方退款记录
export const getpartyRefound = (data) => {
  return request({
    url: "/loan/partyaAdmin/order/refundList",
    method: 'get',
    params: data
  })
}
//甲方退款
export const partyRefund = (data) => {
  return request({
    url: '/loan/partyaAdmin/order/refund',
    method: "post",
    data
  })
}
//判断甲方是否有待支付的订单
export const orderPayProgress = () => {
  return request({
    url: "/loan/partyaAdmin/order/orderPayProgress",
    method: "get"
  })
}
//查询退款进度
export const getQueryRefundProgess = (data) => {
  return request({
    url: "/loan/partyaAdmin/order/queryRefund",
    method: "get",
    params: data
  })
}


//获取甲方合同城市
export const getFindAllArea = () => {
  return request({
    url: "/loan/partya/findAllArea",
    method: "get"
  })
}

//上传甲方合同图片
export const contractUploadFile = (data, id) => {
  return request({
    url: '/loan/partya/contract/uploadFile/' + id,
    method: "post",
    data
  })
}
//甲方上传线下资料
export const AddPartyaAdminContract = (data) => {
  return request({
    // url: "/loan/partya/contract/addContract",
    url: "/loan/partya/contract/uploadData",
    method: "post",
    data
  })
}
//甲方上传线下合同
export const addPartyContractOther = (data) => {
  return request({
    url: "/loan/partya/contract/uploadContract",
    method: "post",
    data
  })
}
//甲方修改线下合同
export const updatePartyContractOther = (data) => {
  return request({
    url: "/loan/partya/contract/updateContract",
    method: "post",
    data
  })
}

//上传甲方线上资料
export const addOnLineContract = (data) => {
  return request({
    // url: "/loan/partya/contract/addOnLineContract",
    url: "/loan/partya/contract/addOnLineContract",
    method: "post",
    data
  })
}

// 修改线上甲方资料
export const updateOnLineContract = (data) => {
  return request({
    url: "/loan/partya/contract/updateOnLineContract",
    method: "post",
    data
  })
}
//获取甲方合同信息
export const getContractInfo = () => {
  return request({
    url: "/loan/partyaAdmin/contract/getContractInfo",
    method: "get"
  })
}
//修改甲方合同资料
export const updataContractInfo = (data) => {
  return request({
    // url: "/loan/partya/contract/updateContract",
    url: "/loan/partya/contract/updateData",
    method: "post",
    data
  })
}
//甲方迁移用户列表
export const getTransferUserList = (data) => {
  return request({
    url: '/loan/partyaAdmin/moreAdvancedRecord/getTransferUserList',
    method: 'get',
    params: data

  })
}
//甲方迁移确认移交
export const addMoreAdvancedRecord = (data) => {
  return request({
    url: '/loan/partyaAdmin/moreAdvancedRecord/transfer',
    method: 'post',
    params: data

  })
}

//获取甲方用户跟进记录
export const getMoreAdvancedRecordList = (data) => {
  return request({
    url: "/loan/partyaAdmin/moreAdvancedRecord/getMoreAdvancedRecordList",
    method: "get",
    params: data
  })
}
