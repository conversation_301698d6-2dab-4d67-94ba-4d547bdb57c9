<template>
  <div class="app-container">
    <div class="title">企微测试链接</div>
    <!-- 筛选表单 -->
    <el-form :inline="true" :model="queryParams" class="demo-form-inline">
      <el-form-item label="链路ID">
        <el-input
          v-model="queryParams.linkId"
          placeholder="请输入链路ID"
        ></el-input>
      </el-form-item>
      <el-form-item label="链路名称">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入链路名称"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="primary" @click="openPhoneCodeDialog">查询验证码</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="tableData" style="width: 100%">
      <!-- PC端显示完整列 -->
      <template v-if="!isMobileDevice">
        <el-table-column prop="linkId" label="链路ID" width="400px">
          <template slot-scope="scope">
            <span>{{ scope.row.linkId }}</span>
            <el-button
              type="text"
              @click="clickCopy(scope.row.linkId)"
              style="margin-left: 10px"
              >复制</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="name" label="链路名称"></el-table-column>
        <el-table-column label="企微产品">
          <template slot-scope="scope">
            <div v-if="scope.row.wechatProduct" v-html="getProductInfo(scope.row.wechatProduct)"></div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300px">
          <template slot-scope="scope">
            <el-button type="text" @click="copyLink(scope.row.linkId)"
              >复制链接</el-button
            >
            <el-button type="text" @click="showQrcode(scope.row)"
              >页面二维码</el-button
            >
            <el-button type="text" @click="openPage(scope.row.linkId)"
              >打开页面</el-button
            >
          </template>
        </el-table-column>
      </template>

      <!-- 移动端只显示部分列 -->
      <template v-else>
        <el-table-column prop="name" label="链路名称"></el-table-column>
        <el-table-column label="企微产品">
          <template slot-scope="scope">
            <div v-if="scope.row.wechatProduct" v-html="getProductInfo(scope.row.wechatProduct)"></div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100px">
          <template slot-scope="scope">
            <el-button type="text" @click="openPage(scope.row.linkId)"
              >打开页面</el-button
            >
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页器 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>

    <!-- 二维码弹窗 -->
    <el-dialog
      title="页面二维码"
      :visible.sync="qrcodeDialogVisible"
      width="1000px"
    >
      <div class="qrcode-container">
        <qrcode-vue :value="qrcodeLink" :size="200" level="H"></qrcode-vue>
        <div style="margin-top: 10px; text-align: center">
          手机扫码，快速测试
        </div>
        <el-descriptions :column="1" border style="width: 100%;margin-top: 10px;">
          <el-descriptions-item label="链路名称">{{ currentLinkName }}</el-descriptions-item>
          <el-descriptions-item label="链路ID">
            {{ currentLinkId }}
            <el-button type="text" style="padding: 0;" icon="el-icon-copy-document" @click="clickCopy(currentLinkId)">复制</el-button>
          </el-descriptions-item>
          <el-descriptions-item label="半流程产品" v-if="getProductInfo(currentHalfProduct)">
            <div v-html="getProductInfo(currentHalfProduct)"></div>
          </el-descriptions-item>
          <el-descriptions-item label="企微产品" v-if="getProductInfo(currentWechatProduct)">
            <div v-html="getProductInfo(currentWechatProduct)"></div>
          </el-descriptions-item>
          <el-descriptions-item label="UV产品" v-if="getProductInfo(currentUvProduct)">
            <div v-html="getProductInfo(currentUvProduct)"></div>
          </el-descriptions-item>
          <el-descriptions-item label="联登产品" v-if="getProductInfo(currentLdProduct)">
            <div v-html="getProductInfo(currentLdProduct)"></div>
          </el-descriptions-item>
          <el-descriptions-item label="出量产品" v-if="getProductInfo(currentOutputProduct)">
            <div v-html="getProductInfo(currentOutputProduct)"></div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <phone-code-dialog 
      :visible.sync="phoneCodeDialogVisible"
    ></phone-code-dialog>
  </div>
</template>

<script>
import {
  getProductLinkTestList
} from "@/api/distributeStatistics/productLinkTest";
import clipboard from "@/utils/clipboard";
import { getBaseH5Url } from "@/utils/env";
import QrcodeVue from "qrcode.vue";
import PhoneCodeDialog from './components/PhoneCodeDialog.vue';
import { isMobile } from '@/utils/device';

export default {
  name: "ProductLinkTest",
  components: {
    QrcodeVue,
    PhoneCodeDialog,
  },
  data() {
    return {
      queryParams: {
        linkId: "",
        name: "",
        pageNum: 1,
        pageSize: 10,
        linkTypes: 1,
      },
      tableData: [],
      total: 0,
      qrcodeDialogVisible: false,
      qrcodeLink: "",
      currentLinkId: "",
      currentLinkName: "",
      currentHalfProduct: "",
      currentWechatProduct: "",
      currentUvProduct: "",
      currentLdProduct: "",
      currentOutputProduct: "",
      phoneCodeDialogVisible: false,
      isMobileDevice: isMobile(),
    };
  },
  created() {
    this.getList();
  },
  methods: {
    clickCopy(text) {
      clipboard
        .copyText(text)
        .then(() => {
          this.$message.success("复制成功");
        })
        .catch(() => {
          this.$message.error("复制失败");
        });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        linkId: "",
        name: "",
        pageNum: 1,
        pageSize: 10,
        linkTypes: 1,
      };
      this.getList();
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    async getList() {
      const response = await getProductLinkTestList(this.queryParams);
      if (response.code === 200) {
        this.tableData = response.rows;
        this.total = response.total;
      }
    },

    generateH5Link(linkId) {
      const baseUrl = getBaseH5Url();
      return `${baseUrl}?linkId=${linkId}`;
    },

    copyLink(linkId) {
      const fullLink = this.generateH5Link(linkId);

      clipboard
        .copyText(fullLink)
        .then(() => {
          this.$message.success("链接复制成功");
        })
        .catch(() => {
          this.$message.error("链接复制失败");
        });
    },
    showQrcode(row) {
      this.qrcodeLink = this.generateH5Link(row.linkId);
      this.currentLinkId = row.linkId;
      this.currentLinkName = row.name;
      this.currentHalfProduct = row.halfProduct;
      this.currentWechatProduct = row.wechatProduct;
      this.currentUvProduct = row.uvProduct;
      this.currentLdProduct = row.ldProduct;
      this.currentOutputProduct = row.outputProduct;
      this.qrcodeDialogVisible = true;
    },
    getProductInfo(product) {
      if (!product) return "";

      const {productName, platform, status, businessName} = JSON.parse(product);

      if (!productName || !platform) {
        return "";
      }

      // 商务名称
      const businessNameText = businessName ? `-${businessName}` : "";

      return `【${platform}】${productName}(${status === 1 ? '<span style="color: green;">在线</span>' : '<span style="color: red;">下线</span>'})${businessNameText}`;
    },
    openPhoneCodeDialog() {
      this.phoneCodeDialogVisible = true;
    },
    openPage(linkId) {
      const fullLink = this.generateH5Link(linkId);
      window.open(fullLink, '_blank');
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .el-form {
    margin-bottom: 20px;
  }

  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }

  // 移动端适配
  @media screen and (max-width: 768px) {
    padding: 10px;

    .title {
      font-size: 16px;
      margin-bottom: 15px;
    }

    .el-form {
      :deep(.el-form-item) {
        margin-bottom: 10px;
      }
    }

    .el-pagination {
      margin-top: 15px;
    }
  }
}

.qrcode-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}
</style>

