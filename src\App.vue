<template>
  <div id="app">
    <router-view />
    <!-- API切换器 - 全局显示 -->
    <api-switcher />
    <!-- 错误监控器 - 全局显示 -->
    <error-monitor />
  </div>
</template>

<script>
import ApiSwitcher from '@/components/ApiSwitcher'
import ErrorMonitor from '@/components/ErrorMonitor'

export default {
  name: "App",
  components: {
    ApiSwitcher,
    ErrorMonitor
  },
  metaInfo() {
    return {
      title:
        this.$store.state.settings.dynamicTitle &&
        this.$store.state.settings.title,
      titleTemplate: (title) => {
        return title
          ? `${title} - ${process.env.VUE_APP_TITLE}`
          : process.env.VUE_APP_TITLE;
      },
    };
  },
};
</script>
<style>
@media (max-width: 768px) {
  .el-picker-panel {
    left: 0 !important;
    max-width: 100vw !important;
    overflow-x: scroll !important;
  }

  .el-picker-panel__body {
    display: flex !important;
    width: 100vw !important;
    overflow: scroll !important;
  }
}

@font-face {
  font-family: "DIN";
  src: url("https://jst.oss-utos.hmctec.cn/common/path/444ddb6a33b0452c9c1ea88b01368b58.ttf");
}

@font-face {
  font-family: 'SourceHanSansSC-Medium';
  src: url('https://jst.oss-utos.hmctec.cn/common/path/9587223648fd45ceb3dda5a10792ec13.otf') format('opentype');
}

@font-face {
  font-family: 'SourceHanSansSC-Regular';
  src: url('https://jst.oss-utos.hmctec.cn/common/path/bc7192fd21e74b03878040de6e58c02c.otf') format('opentype');
}

@font-face {
  font-family: 'DIN-Bold';
  src: url('https://jst.oss-utos.hmctec.cn/common/path/81d6b93882f948a1ad7a9c6c9b78ba04.ttf') format('truetype');
}

@font-face {
  font-family: 'SanFranciscoText-Medium';
  src: url('https://jst.oss-utos.hmctec.cn/common/path/b60d43c842c646ecba15252d7bdd0e9a.otf') format('opentype');
}

/* src\assets\fonts\D-DINExp.ttf */
@font-face {
  font-family: 'D-DINExp';
  src: url('../src//assets/fonts/D-DINExp.ttf') format('truetype');
}
/* @font-face {
  font-family: 'SanFranciscoText-Bold';
  src: url('https://jst.oss-utos.hmctec.cn/common/path/9a11fb5bc31a4fb79fc6f25c314b3683.otf') format('opentype');
}

@font-face {
  font-family: 'SanFranciscoText-Heavy';
  src: url('https://jst.oss-utos.hmctec.cn/common/path/0d373b8e7e094c869b59f28b2ea71629.otf') format('opentype');
}

@font-face {
  font-family: 'MiLanProVF';
  src: url('https://jst.oss-utos.hmctec.cn/common/path/c1a0aaf105da450b82d8bbc3017c218e.ttf') format('truetype');
} */
</style>
