<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">


      <el-form-item label="唯一订单号" prop="orderNo">
        <el-input size="small" clearable v-model="queryParams.orderNo" placeholder="请输入商家订单号"></el-input>
      </el-form-item>
      <el-form-item label="分账订单号" prop="stbBatchNo">
        <el-input size="small" clearable v-model="queryParams.stbBatchNo" placeholder="请输入智付订单号"></el-input>
      </el-form-item>
      <el-form-item label="分账回退订单号" prop="stbRefundNo">
        <el-input size="small" clearable v-model="queryParams.stbRefundNo" placeholder="请输入分账回退订单号"></el-input>
      </el-form-item>
      <el-form-item label="智付订单号" prop="tradeNo">
        <el-input size="small" clearable v-model="queryParams.tradeNo" placeholder="请输入分账回退订单号"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery" size="small">筛选
        </el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleBackOff" size="small"
          v-hasPermi="['loan:din_pay:refund_order:add']">申请分账订单回退
        </el-button>

      </el-form-item>
    </el-form>

    <el-table border :data="tableList">
      <!-- <el-table-column label="创建人" prop="createBy" align="center" /> -->
      <el-table-column label="创建时间" prop="createTime" align="center" width="160" />
      <el-table-column label="回退分账商户号" prop="accountId" align="center" width="160" />
      <el-table-column label="唯一订单号" prop="orderNo" align="center" width="200" />
      <el-table-column label="分账订单号" prop="stbBatchNo" align="center" width="200" />
      <el-table-column label="分账回退订单号" prop="stbRefundNo" align="center" width="200" />
      <el-table-column label="回退金额" prop="stbRefundAmout" align="center" width="200" />
      <el-table-column label="分账回退状态" prop="stbStatus" align="center" width="120">
        <template  slot-scope="{row}">
          <div>
            {{ statusJson[row.stbStatus] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="智付订单号" prop="tradeNo" align="center" width="200" />
      <el-table-column label="回退分账商户号" prop="accountId" align="center" width="200" />
      <el-table-column label="回退流水号" prop="stbRefundId" align="center" width="200" />
      <el-table-column label="回退描述" prop="stbRemark" align="center" width="200" />
      <el-table-column label="创建人" prop="createBy" align="center" width="100" />
      <el-table-column label="创建时间" prop="createTime" align="center" width="160" />
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div v-if="row.stbStatus == -1">
            <el-button type="text" @click="handleQueryStatus(row)" v-hasPermi="['loan:din_pay:refund_order:query']">查询
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog title="申请分账订单回退" :visible.sync="showAdd" width="700px" append-to-body center
      :close-on-click-modal="false" @close="cancel">
      <el-form ref="formData" label-width="140px" :model="formData" :rules="rules">
        <el-form-item label="商家订单号" prop="orderNo">
          <el-input v-model="formData.orderNo" maxlength="30" oninput="value=value.replace(/[^0-9]/g,'')"
            @blur="formData.orderNo = $event.target.value" placeholder="请输入商家订单号"></el-input>
        </el-form-item>
        <el-form-item label="回退金额" prop="stbRefundAmout">
          <el-input v-model="formData.stbRefundAmout" oninput="value=value.replace(/[^0-9.]/g,'')"
            @blur="formData.stbRefundAmout = $event.target.value" placeholder="请输入回退金额"></el-input>
        </el-form-item>
        <el-form-item label="回退的分账商户号" prop="accountId">
          <el-input v-model="formData.accountId" placeholder="请输入分账商户号"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getRefundOrderList, addStbRefundOrderOne, getStbRefundOrderQuery } from "@/api/financial"
export default {
  data() {
    const validateNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error("金额不能为空"));
      } else if (!/^(([1-9]{1}\d{0,5})|(0{1}))(\.\d{1,2})?$/.test(value)) {
        callback(new Error("输入不合法"));
      } else {
        callback();
      }
    };

    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      statusJson: {
        "-1": "发起回退",
        0: "分账未回退或者回退失败",
        1: "分账回退成功",
      },
      stbRefundNo: "",
      formData: {
        orderNo: "",
        stbRefundAmout: "",
        accountId: ""
      },
      rules: {
        orderNo: [
          { required: true, message: "请输入订单号", trigger: "blur" },
        ],
        stbRefundAmout: [
          { required: true, validator: validateNumber, trigger: "blur" },
        ],
        accountId: [
          { required: true, message: "请输入分账商户号", trigger: "blur" },
        ],
      },
      total: 0,
      showAdd: false,

      tableList: []
    }
  },
  methods: {
    getList() {
      getRefundOrderList(this.queryParams).then(res => {
        this.tableList = res.rows
        this.total = res.total
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleBackOff() {
      this.showAdd = true
    },
    //确认退款
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          addStbRefundOrderOne(this.formData).then(res => {
            this.cancel()
            this.getList()
            this.$message.success("操作成功")
          })
        }
      })
    },
    //取消退款
    cancel() {
      this.formData = {
        orderNo: "",
        stbRefundAmout: "",
        accountId: ""
      }
      this.showAdd = false
      this.$refs.formData.resetFields()
    },
    //查询退款状态
    handleQueryStatus(row) {

      getStbRefundOrderQuery({
        stbRefundNo: row.stbRefundNo
      }).then(res => {
        this.$message.success(res.msg)
        this.getList()
      })
    },

  },
  mounted() {
    this.getList()
  }
}
</script>

<style lang="scss" scoped>
</style>
