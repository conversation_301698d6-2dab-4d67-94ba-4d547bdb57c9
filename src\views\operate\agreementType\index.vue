<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="协议名称" prop="orderNo">
        <el-input
          size="small"
          clearable
          v-model="queryParams.name"
          placeholder="请输入协议名称"
        ></el-input>
      </el-form-item>

      <el-form-item label="协议KEY" prop="protocolSig">
        <el-input
          size="small"
          clearable
          v-model="queryParams.protocolSig"
          placeholder="请输入协议KEY"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="addAgreementType" size="small"
          >新增协议归属</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="list" border>
      <el-table-column label="ID" align="center" prop="protocolSigId" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="标识" align="center" prop="protocolSig" />
      <el-table-column label="添加时间" prop="createTime" align="center" />
      <el-table-column label="备注" prop="remark" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button type="text" @click="handleEdit(row)" icon="el-icon-edit"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="isadd ? '新增协议归属' : '修改协议归属'"
      :visible.sync="agreementTypeAvisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="归属名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入归属名称" />
        </el-form-item>
        <el-form-item label="归属标识" prop="protocolSig">
          <el-input
            v-model="formData.protocolSig"
            :disabled="!isadd"
            placeholder="请输入归属标识"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="formData.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addProtocolTypeOne,
  getProtocolTypeList,
  editProtocolTypeOne,
  delProtocolTypeOne,
} from "@/api/operate/agreement";
export default {
  data() {
    return {
      agreementTypeAvisible: false,
      isadd: true,
      total: 0,
      list: [],
      formData: {
        name: "",
        remark: "",
        protocolSig: "",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        protocolSig: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入协议类型名称", trigger: "blur" },
        ],
        protocolSig: [
          { required: true, message: "请输入归属标识", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    addAgreementType() {
      this.agreementTypeAvisible = true;
      this.isadd = true;
      if (this.formData.protocolSigId) {
        delete this.formData.protocolSigId;
      }
    },
    getList() {
      getProtocolTypeList(this.queryParams).then((res) => {
        this.list = res.rows;
        this.total = res.total;
        if (this.list.length == 0 && this.queryParams.pageNum > 1) {
          this.queryParams.pageNum = this.queryParams.pageNum - 1;
          this.getList();
          return;
        }
      });
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isadd) {
            addProtocolTypeOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.cancel();
                this.getList();
              }
            });
          } else {
            editProtocolTypeOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.cancel();
                this.getList();
              }
            });
          }
        }
      });
    },
    cancel() {
      this.agreementTypeAvisible = false;
      this.formData = {
        name: "",
        remark: "",
      };
      this.$refs.formData.resetFields();
    },
    handleEdit(row) {
      this.agreementTypeAvisible = true;
      this.isadd = false;
      this.formData.name = row.name;
      this.formData.protocolSigId = row.protocolSigId;
      this.formData.remark = row.remark;
      this.formData.protocolSig = row.protocolSig;
    },
    handleDelete(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delProtocolTypeOne(row.protocolSigId).then((res) => {
            if (res.code == 200) {
              this.$message.success("删除成功");
              this.getList();
            }
          });
        })
        .catch((err) => {});
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped></style>
