<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="租户ID" prop="tenantId">
        <el-input
          v-model.number="queryParams.tenantId"
          placeholder="请输入租户ID"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="tableList">
      <el-table-column
        label="租户流量平台ID"
        width="130"
        align="center"
        prop="tenantPlatformId"
      />
      <el-table-column
        label="租户ID"
        width="100"
        align="center"
        prop="tenantId"
      />
      <el-table-column label="平台名称" align="center" prop="platformName" />
      <el-table-column label="租户主体名称" align="center" prop="subjectName" />
      <el-table-column label="申请开通时间" align="center" prop="applyTime" />
      <el-table-column label="审核备注" align="center" prop="checkRemark" />
      <el-table-column label="操作">
        <template  slot-scope="{row}">
          <div>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleSuccess(row)"
              >通过</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="handleFail(row)"
              >不通过</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="审核不通过"
      :visible.sync="open"
      width="600px"
      append-to-body
      @close="cancel"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="不通过原因" prop="checkRemark">
          <el-input
            v-model="form.checkRemark"
            type="textarea"
            placeholder="请输入不通过原因"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPlatformCheckList,
  editUpdateCheck,
  editUpdateCheckFail,
} from "@/api/saas/tenant";
export default {
  name: "SaasTenant",
  data() {
    return {
      tableList: [],
      open: false,
      total: 0,
      form: {
        checkRemark: "",
        tenantPlatformId: "",
      },
      rules: {
        checkRemark: [
          { required: true, message: "不通过原因不能为空", trigger: "blur" },
        ],
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: "",
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    handleSuccess(row) {
      this.$confirm("确定通过吗？", { type: "warning" })
        .then((res) => {
          editUpdateCheck(row.tenantPlatformId).then((res) => {
            this.getList();
            this.$message.success("操作成功");
          });
        })
        .catch((err) => {});
    },
    handleFail(row) {
      this.open = true;
      this.form.tenantPlatformId = row.tenantPlatformId;
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          editUpdateCheckFail(this.form).then((res) => {
            this.getList();
            this.cancel();
            this.$message.success("操作成功");
          });
        }
      });
    },
    cancel() {
      this.open = false;
      this.form = {
        checkRemark: "",
        tenantPlatformId: "",
      };
      this.resetForm("form");
    },
    getList() {
      getPlatformCheckList(this.queryParams).then((res) => {
        this.tableList = res.rows;
        this.total = res.total;
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
