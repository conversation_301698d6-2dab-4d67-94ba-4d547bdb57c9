import request from '@/utils/request'

/**
 * 获取企微/贷超自动测试启用禁用状态
 * @returns {Promise}
 */
export function getAutoTestStatus() {
  return request({
    url: '/loan/xm/autoTest/autoTestEnable',
    method: 'get'
  })
}

/**
 * 启用/禁用自动测试
 * @param {Object} data
 * @param {Boolean} data.enabled - true: 启用 false: 禁用
 * @param {String} data.type - 类型 企微 贷超
 * @returns {Promise}
 */
export function updateAutoTestStatus(data) {
  return request({
    url: '/loan/xm/autoTest/autoTestEnable',
    method: 'post',
    params: data
  })
} 