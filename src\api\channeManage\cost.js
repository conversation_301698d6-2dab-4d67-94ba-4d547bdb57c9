import request from '@/utils/request'
//获取成本列表
export const getCostLsit = (data) => {
    return request({
        url: "/loan/PartyB/costLsit",
        method: "get",
        params: data
    })
}

//新增成本数据
export const addCostOne=(data)=>{
    return request({
        url:"/loan/backstageChannel/makeChannelCost",
        method:"post",
        data
    })
}
//新增成本数据
export const editCostOne=(data)=>{
    return request({
        url:"/loan/backstageChannel/channelcostModify",
        method:"post",
        data
    })
}

//批量导入渠道成本
export const expotData=(data)=>{
    return request({
        url:'/loan/backstageChannel/makeChannelCosts',
        method:"post",
        data
    })
}