import BigNumber from 'bignumber.js'

/**
 * 计算多个数的总和。可以直接传入多个数，或者传入一个包含数的数组。
 *
 * @param {...(number|number[])} args - 多个数值或包含数值的数组。如果只传入一个参数，并且这个参数是数组，则使用这个数组作为参数列表。
 * @returns {number} - 所有数值的总和。
 *
 * @example
 * // 直接传入多个数
 * sum(1, 2, 3, 4); // 10
 *
 * // 传入一个数组
 * sum([1, 2, 3, 4]); // 10
 *
 * // 包含null或undefined的情况
 * sum(1, 2, null, 4); // 7
 */
export function sum(...args) {
  // 检查是否传入了一个数组，如果是，就使用这个数组作为args
  if (args.length === 1 && Array.isArray(args[0])) {
    args = args[0];
  }

  if (args.length === 0) {
    return 0;
  }

  args = args.map(item => item || 0);

  return BigNumber.sum.apply(null, args).toNumber();
}

/**
 * 减法运算，返回第一个数减去后续所有数的结果
 * @param {number} minuend - 被减数
 * @param {...number} subtrahends - 减数列表
 * @returns {number} - 减法结果
 */
export function subtract(minuend, ...subtrahends) {
  let result = new BigNumber(minuend || 0);
  subtrahends.forEach(num => {
    result = result.minus(num || 0);
  });
  return result.toNumber();
}

/**
 * 除法运算，返回第一个数除以第二个数的结果
 * @param {number} dividend - 被除数
 * @param {number} divisor - 除数
 * @param {number} [decimalPlaces=2] - 保留小数位数
 * @returns {number} - 除法结果
 */
export function divide(dividend, divisor, decimalPlaces = 2) {
  if (!divisor || divisor === 0) return 0;
  return new BigNumber(dividend || 0)
    .dividedBy(divisor)
    .decimalPlaces(decimalPlaces)
    .toNumber();
}

export default {
  sum,
  subtract,
  divide
}
