<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams">
      <el-form-item
        label="手机号"
        prop="phone"
        :rules="[{ required: true, message: '请输入手机号', trigger: 'blur' }]"
      >
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table 
      v-loading="loading" 
      :data="recordList" 
      style="width: 100%" 
      border
      @row-click="handleRowClick"
      ref="recordTable"
    >
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-row style="width: 100%; margin: 0; padding: 20px">
            <el-col :span="24" style="margin-bottom: 20px">
              <div style="margin: 10px 0">
                <el-tag
                  :style="{
                    backgroundColor: getPlatformColor(props.row.platformType),
                    color: '#fff',
                    border: 'none',
                  }"
                  size="mini"
                >
                  {{ getPlatformName(props.row.platformType) }} - 匹配记录
                </el-tag>
              </div>
              <el-table
                :data="props.row.checkInfoList"
                border
                size="mini"
                style="width: 100%"
                max-height="500"
              >
                <el-table-column
                  prop="loanName"
                  label="名称"
                  align="center"
                  min-width="300"
                />
                <el-table-column
                  prop="loanType"
                  label="类型"
                  align="center"
                  min-width="200"
                />
                <el-table-column
                  prop="checkStatus"
                  label="状态"
                  align="center"
                  min-width="150"
                >
                  <template slot-scope="checkScope">
                    <el-tag
                      :type="
                        checkScope.row.checkStatus === '成功'
                          ? 'success'
                          : 'warning'
                      "
                      size="mini"
                    >
                      {{ checkScope.row.checkStatus }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="checkDate"
                  label="匹配时间"
                  align="center"
                  min-width="200"
                />
              </el-table>
            </el-col>
            <el-col :span="24">
              <div style="margin: 10px 0">
                <el-tag
                  :style="{
                    backgroundColor: getPlatformColor(props.row.platformType),
                    color: '#fff',
                    border: 'none',
                  }"
                  size="mini"
                >
                  {{ getPlatformName(props.row.platformType) }} - 申请记录
                </el-tag>
              </div>
              <el-table
                :data="props.row.applyInfoList"
                border
                size="mini"
                style="width: 100%"
                max-height="500"
              >
                <el-table-column
                  prop="applyName"
                  label="申请名称"
                  align="center"
                  min-width="500"
                />
                <el-table-column
                  prop="price"
                  label="金额"
                  align="center"
                  min-width="300"
                >
                  <template slot-scope="applyScope">
                    <span>{{ applyScope.row.price }}元</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="applyDate"
                  label="申请时间"
                  align="center"
                  min-width="200"
                />
              </el-table>
            </el-col>
          </el-row>
        </template>
      </el-table-column>

      <el-table-column label="平台" align="center" min-width="180">
        <template slot-scope="scope">
          <el-tag
            :style="{
              backgroundColor: getPlatformColor(scope.row.platformType),
              color: '#fff',
              border: 'none',
            }"
            size="mini"
          >
            {{ getPlatformName(scope.row.platformType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="匹配记录数" align="center" min-width="150">
        <template slot-scope="scope">
          {{ scope.row.checkInfoList.length }}
        </template>
      </el-table-column>

      <el-table-column label="申请记录数" align="center" min-width="150">
        <template slot-scope="scope">
          {{ scope.row.applyInfoList.length }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { queryCheckAndApplyByPhone } from "@/api/platformProductManagement/platformRecords";
import { getPlatformList } from '@/api/xmxrChannelManage/channelList'

export default {
  name: "PlatformRecords",
  data() {
    return {
      loading: false,
      recordList: [],
      queryParams: {
        phone: undefined,
      },
      platformList: [],
      // 预定义的基础颜色数组 - 适中的配色
      baseColors: [
        '#52C41A',  // 清新绿
        '#4B7BE5',  // 优雅蓝
        '#FA8C16',  // 温暖橙
        '#F15A5A',  // 玫瑰红
        '#13A8A8',  // 青蓝色
        '#597EF7',  // 靛蓝色
        '#2F9688',  // 翠绿色
        '#E8943C',  // 琥珀色
        '#3B7EC9',  // 宝蓝色
        '#D48806'   // 赭石色
      ]
    };
  },
  methods: {
    getList() {
      this.loading = true;
      queryCheckAndApplyByPhone(this.queryParams)
        .then((response) => {
          this.recordList = response.data || [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      if (!this.queryParams.phone) {
        this.$message.warning("请输入手机号");
        return;
      }
      this.getList();
    },
    handleRowClick(row) {
      this.$refs.recordTable.toggleRowExpansion(row);
    },
    handleView(row) {
      this.$message.info("查看记录：" + row.phone);
    },
    // 获取平台名称
    getPlatformName(platformType) {
      const platform = this.platformList.find(item => item.id == platformType);
      return platform ? platform.name : '';
    },
    // 根据平台类型获取颜色
    getPlatformColor(platformType) {
      // 使用平台ID作为索引来选择颜色
      const colorIndex = (platformType - 1) % this.baseColors.length;
      return this.baseColors[colorIndex];
    }
  },
  mounted() {
    getPlatformList().then(res => {
      this.platformList = res.data || [];
    });
  }
};
</script>

<style scoped></style>
