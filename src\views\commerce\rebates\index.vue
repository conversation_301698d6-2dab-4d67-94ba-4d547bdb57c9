<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="时间">
        <el-date-picker
          size="small"
          v-model="dateRange"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="推广名称">
        <el-input
          size="small"
          v-model="queryParams.productName"
          placeholder="请输入推广名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="申请人">
        <el-input
          size="small"
          v-model="queryParams.applyBy"
          placeholder="请输入申请人姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          v-model="queryParams.examineStatus"
          filterable
          clearable
          @change="handleQuery"
          size="small"
        >
          <el-option :value="0" label="全部"></el-option>
          <el-option :value="1" label="通过"></el-option>
          <el-option :value="2" label="驳回"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery"
          >筛选
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tables" show-summary :summary-method="handleGetSummary">
      <el-table-column label="申请时间" prop="createTime" align="center" />
      <el-table-column label="返点金额" prop="price" align="center" />
      <el-table-column label="返点日期" prop="rebateDate" align="center" />
      <el-table-column label="推广产品名称" prop="productName" align="center" />
      <el-table-column
        label="推广合作价格"
        prop="cooperationCost"
        align="center"
      />
      <el-table-column label="申请原因" prop="reason" align="center" />
      <el-table-column label="申请人" prop="createBy" align="center" />
      <el-table-column label="审核状态" prop="createBy" align="center">
        <template slot-scope="{ row }">
          {{ row.examineStatus | filterType }}
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="entryTime" align="center">
        <template slot-scope="{ row }">
          <span
            class="c-p f_c005"
            v-if="row.examineStatus == 0"
            @click="handleAuditApply(row, 0)"
            v-hasPermi="['loan:partyfirst:rebate:apply']"
            style="margin-right: 10px"
            >通过</span
          >
          <span
            class="c-p f_c005"
            v-if="row.examineStatus == 0"
            @click="handleAuditApply(row, 1)"
            v-hasPermi="['loan:partyfirst:rebate:apply']"
            style="margin-right: 10px"
            >驳回</span
          >
          <span type="text" v-if="row.examineStatus == 1" class="f-suceess"
            >已通过</span
          >
          <span class="f-fail" v-if="row.examineStatus == 2">已驳回</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="init"
    />

    <el-dialog
      title="返点详情"
      :visible.sync="rebatesVisible"
      width="30%"
      center
    >
      <el-table :data="dataList">
        <el-table-column
          label="返点时间"
          prop="date"
          align="center"
        ></el-table-column>
        <el-table-column
          label="渠道ID"
          prop="channelId"
          align="center"
        ></el-table-column>
        <el-table-column
          label="返点金额"
          prop="rebateAmount"
          align="center"
        ></el-table-column>
        <el-table-column
          label="返点原因"
          prop="reason"
          align="center"
        ></el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button @click="rebatesVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getRebates, rebateAudit, getRebate } from "@/api/commerce/rebates";
export default {
  name: "rebates",
  data() {
    return {
      dateRange: [],
      dataList: [],
      rebatesVisible: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applyBy: "",
        applyTime: "",
        examineStatus: "",
        productName: "",
        rebateDate: "",
      },
      tables: [],
      total: 0,
      extra: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    /**
     *  @param { Function } init 初始化 - 获取返点列表数据
     */
    init() {
      let param = {
        ...this.queryParams,
      };
      if (this.dateRange && this.dateRange.length) {
        param.startTime = this.dateRange[0];
        param.endTime = this.dateRange[1];
      }
      getRebates(param).then((res) => {
        this.tables = res.rows;
        this.total = res.total;
        this.extra = res.extra;
      });
    },

    /**
     *  @param { Function } handleQuery 搜索内容
     */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.init();
    },

    /**
     *  @param { Function } handleAuditApply 状态审核
     */
    handleAuditApply({ createBy = "", id = "" }, type = 0) {
      if (type == 0) {
        getRebate({ id }).then((res) => {
          console.log(res);
          this.id = id;
          this.rebatesVisible = true;
          this.dataList = [];
          if (res.data.detailList && res.data.detailList.length) {
            this.dataList = res.data.detailList.map((item) => {
              return {
                ...item,
                date: res.data.rebateDate,
              };
            });
          }
        });


      } else {
        this.$prompt("请输入驳回理由", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        })
          .then(({ value }) => {
            if (!value) return this.$message.error("请填写驳回理由");
            rebateAudit({ id, type, remarks: value }).then((res) => {
              this.$message({
                type: "success",
                message: `已驳回`,
              });
              this.init();
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "取消操作",
            });
          });
      }
    },
    handleSubmit() {
      rebateAudit({ id: this.id, type: 0 }).then((res) => {
        this.$message({
          type: "success",
          message: `操作成功`,
        });
        this.rebatesVisible = false;
        this.init();
      });
    },
    /**
     *  @param { Function } handleGetSummary 获取统计参数
     */
    handleGetSummary(rows) {
      const { columns, data } = rows;
      let arr = [];
      columns.forEach((item, index) => {
        if (index == 0) {
          arr[index] = "合计";
          return;
        }
        if (index == 1) {
          arr[index] = Object.values(this.extra)[0];
          return;
        }
        arr[index] = "";
      });

      return arr;
    },
  },
  filters: {
    filterType(type) {
      if (type == 0) return "待审核";
      if (type == 1) return "通过";
      if (type == 2) return "驳回";
      return "未知";
    },
  },
};
</script>
