<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :rules="rules" size="small" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :clearable="false"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 折叠表格展示 -->
    <el-table :data="outboundList" border row-key="integrationName" :default-expand-all="true">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div class="expand-content">
            <el-table :data="props.row.list" border size="small">
              <el-table-column label="序号" type="index" min-width="50" align="center" />
              <el-table-column label="渠道ID" prop="channelId" min-width="70" align="center" />
              <el-table-column label="渠道名称" prop="channelName" min-width="120" align="center" show-overflow-tooltip />
              <el-table-column label="UV" prop="uvNum" min-width="60" align="center" />
              <el-table-column label="注册数" prop="registerNum" min-width="70" align="center" />
              <el-table-column label="表单数" prop="formNum" min-width="70" align="center" />
              <el-table-column label="线下申请" prop="offlineNum" min-width="80" align="center" />
              <el-table-column label="线下收益" prop="offlineProfit" min-width="80" align="center">
                <template slot-scope="scope">
                  {{ formatMoney(scope.row.offlineProfit) }}
                </template>
              </el-table-column>
              <el-table-column label="出量申请" prop="outputNum" min-width="80" align="center" />
              <el-table-column label="出量收益" prop="outputProfit" min-width="80" align="center">
                <template slot-scope="scope">
                  {{ formatMoney(scope.row.outputProfit) }}
                </template>
              </el-table-column>
              <el-table-column label="半流程申请" prop="halfNum" min-width="90" align="center" />
              <el-table-column label="半流程收益" prop="halfProfit" min-width="90" align="center">
                <template slot-scope="scope">
                  {{ formatMoney(scope.row.halfProfit) }}
                </template>
              </el-table-column>
              <el-table-column label="企微申请" prop="qwNum" min-width="80" align="center" />
              <el-table-column label="企微收益" prop="qwProfit" min-width="80" align="center">
                <template slot-scope="scope">
                  {{ formatMoney(scope.row.qwProfit) }}
                </template>
              </el-table-column>
              <el-table-column label="贷超申请" prop="dcNum" min-width="80" align="center" />
              <el-table-column label="贷超收益" prop="dcProfit" min-width="80" align="center">
                <template slot-scope="scope">
                  {{ formatMoney(scope.row.dcProfit) }}
                </template>
              </el-table-column>
              <el-table-column label="总收益" prop="profit" min-width="80" align="center">
                <template slot-scope="scope">
                  <span class="profit-total">{{ formatMoney(scope.row.profit) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="产值" prop="outputValue" min-width="70" align="center">
                <template slot-scope="scope">
                  {{ calculateOutputValue(scope.row) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="整合名称" prop="integrationName" min-width="150" align="center" show-overflow-tooltip />
      <el-table-column label="UV" min-width="60" align="center">
        <template slot-scope="scope">
          {{ getGroupSummaryField(scope.row, 'uvNum') }}
        </template>
      </el-table-column>
      <el-table-column label="注册数" min-width="70" align="center">
        <template slot-scope="scope">
          {{ getGroupSummaryField(scope.row, 'registerNum') }}
        </template>
      </el-table-column>
      <el-table-column label="表单数" min-width="70" align="center">
        <template slot-scope="scope">
          {{ getGroupSummaryField(scope.row, 'formNum') }}
        </template>
      </el-table-column>
      <el-table-column label="线下申请" min-width="80" align="center">
        <template slot-scope="scope">
          {{ getGroupSummaryField(scope.row, 'offlineNum') }}
        </template>
      </el-table-column>
      <el-table-column label="线下收益" min-width="80" align="center">
        <template slot-scope="scope">
          {{ formatMoney(getGroupSummaryField(scope.row, 'offlineProfit')) }}
        </template>
      </el-table-column>
      <el-table-column label="出量申请" min-width="80" align="center">
        <template slot-scope="scope">
          {{ getGroupSummaryField(scope.row, 'outputNum') }}
        </template>
      </el-table-column>
      <el-table-column label="出量收益" min-width="80" align="center">
        <template slot-scope="scope">
          {{ formatMoney(getGroupSummaryField(scope.row, 'outputProfit')) }}
        </template>
      </el-table-column>
      <el-table-column label="半流程申请" min-width="90" align="center">
        <template slot-scope="scope">
          {{ getGroupSummaryField(scope.row, 'halfNum') }}
        </template>
      </el-table-column>
      <el-table-column label="半流程收益" min-width="90" align="center">
        <template slot-scope="scope">
          {{ formatMoney(getGroupSummaryField(scope.row, 'halfProfit')) }}
        </template>
      </el-table-column>
      <el-table-column label="企微申请" min-width="80" align="center">
        <template slot-scope="scope">
          {{ getGroupSummaryField(scope.row, 'qwNum') }}
        </template>
      </el-table-column>
      <el-table-column label="企微收益" min-width="80" align="center">
        <template slot-scope="scope">
          {{ formatMoney(getGroupSummaryField(scope.row, 'qwProfit')) }}
        </template>
      </el-table-column>
      <el-table-column label="贷超申请" min-width="80" align="center">
        <template slot-scope="scope">
          {{ getGroupSummaryField(scope.row, 'dcNum') }}
        </template>
      </el-table-column>
      <el-table-column label="贷超收益" min-width="80" align="center">
        <template slot-scope="scope">
          {{ formatMoney(getGroupSummaryField(scope.row, 'dcProfit')) }}
        </template>
      </el-table-column>
      <el-table-column label="总收益" min-width="80" align="center">
        <template slot-scope="scope">
          <span class="profit-total">{{ formatMoney(getGroupSummaryField(scope.row, 'profit')) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="产值" min-width="70" align="center">
        <template slot-scope="scope">
          {{ calculateGroupOutputValue(scope.row) }}
        </template>
      </el-table-column>
    </el-table>


  </div>
</template>

<script>
import { getOutboundChannelDetail } from "@/api/distributionStatistics/outboundChannelIntegration";
import { divide } from "@/utils/calculate";
import dayjs from "dayjs";

export default {
  name: "OutboundChannelIntegration",
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
      // 话务渠道整合表格数据
      outboundList: [],
      // 查询参数
      queryParams: {
        dateRange: [],
        startTime: null,
        endTime: null
      },
      // 表单验证规则
      rules: {
        dateRange: [
          { required: true, message: '请选择时间范围', trigger: 'change' }
        ]
      }
    };
  },
  created() {
    this.setDefaultDate();
    this.getList();
  },
  methods: {
    /** 设置默认日期为当天 */
    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format("YYYY-MM-DD 00:00:00");
      const endTime = today.format("YYYY-MM-DD 23:59:59");
      this.queryParams.dateRange = [startTime, endTime];
    },
    /** 查询话务渠道整合列表 */
    async getList() {
      this.buildQueryParams();

      try {
        const response = await getOutboundChannelDetail(this.queryParams);
        // 处理返回的数据结构，只展示list数组的第一层数据，忽略platformStats
        this.outboundList = response.data || [];
        this.processData();
      } catch (error) {
        console.error('获取数据失败:', error);
        this.$message.error('获取数据失败');
        this.outboundList = [];
      }
    },
    /** 处理数据，移除platformStats */
    processData() {
      this.outboundList.forEach(group => {
        if (group.list && Array.isArray(group.list)) {
          group.list.forEach(item => {
            // 移除platformStats数据，只保留第一层数据
            delete item.platformStats;
          });
        }
      });
    },
    /** 格式化金额 */
    formatMoney(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }
      return Number(value).toFixed(2);
    },
    /** 计算组汇总数据 */
    getGroupSummary(group) {
      if (!group.list || !Array.isArray(group.list) || group.list.length === 0) {
        return '暂无数据';
      }

      const summary = group.list.reduce((acc, item) => {
        acc.uvNum += Number(item.uvNum || 0);
        acc.registerNum += Number(item.registerNum || 0);
        acc.formNum += Number(item.formNum || 0);
        acc.offlineNum += Number(item.offlineNum || 0);
        acc.offlineProfit += Number(item.offlineProfit || 0);
        acc.outputNum += Number(item.outputNum || 0);
        acc.outputProfit += Number(item.outputProfit || 0);
        acc.halfNum += Number(item.halfNum || 0);
        acc.halfProfit += Number(item.halfProfit || 0);
        acc.qwNum += Number(item.qwNum || 0);
        acc.qwProfit += Number(item.qwProfit || 0);
        acc.dcNum += Number(item.dcNum || 0);
        acc.dcProfit += Number(item.dcProfit || 0);
        acc.profit += Number(item.profit || 0);
        return acc;
      }, {
        uvNum: 0,
        registerNum: 0,
        formNum: 0,
        offlineNum: 0,
        offlineProfit: 0,
        outputNum: 0,
        outputProfit: 0,
        halfNum: 0,
        halfProfit: 0,
        qwNum: 0,
        qwProfit: 0,
        dcNum: 0,
        dcProfit: 0,
        profit: 0
      });

      return `UV:${summary.uvNum} | 注册:${summary.registerNum} | 表单:${summary.formNum} | 线下申请:${summary.offlineNum} | 线下收益:${this.formatMoney(summary.offlineProfit)} | 出量申请:${summary.outputNum} | 出量收益:${this.formatMoney(summary.outputProfit)} | 半流程申请:${summary.halfNum} | 半流程收益:${this.formatMoney(summary.halfProfit)} | 企微申请:${summary.qwNum} | 企微收益:${this.formatMoney(summary.qwProfit)} | 贷超申请:${summary.dcNum} | 贷超收益:${this.formatMoney(summary.dcProfit)} | 总收益:${this.formatMoney(summary.profit)}`;
    },
    /** 获取组汇总字段值 */
    getGroupSummaryField(group, field) {
      if (!group.list || !Array.isArray(group.list) || group.list.length === 0) {
        return 0;
      }

      return group.list.reduce((acc, item) => {
        return acc + Number(item[field] || 0);
      }, 0);
    },
    /** 计算单个渠道的产值 */
    calculateOutputValue(row) {
      const profit = Number(row.profit || 0);
      const uvNum = Number(row.uvNum || 0);
      const registerNum = Number(row.registerNum || 0);

      if (profit === 0) return '-';

      const result = divide(profit, (uvNum || registerNum), 1);
      return result || '-';
    },
    /** 计算组汇总的产值 */
    calculateGroupOutputValue(group) {
      const totalProfit = this.getGroupSummaryField(group, 'profit');
      const totalUvNum = this.getGroupSummaryField(group, 'uvNum');
      const totalRegisterNum = this.getGroupSummaryField(group, 'registerNum');

      if (totalProfit === 0) return '-';

      const result = divide(totalProfit, (totalUvNum || totalRegisterNum), 1);
      return result || '-';
    },
    /** 构建查询参数 */
    buildQueryParams() {
      if (this.queryParams.dateRange && this.queryParams.dateRange.length === 2) {
        this.queryParams.startTime = this.queryParams.dateRange[0];
        this.queryParams.endTime = this.queryParams.dateRange[1];
      } else {
        this.queryParams.startTime = null;
        this.queryParams.endTime = null;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.$refs["queryForm"].validate(valid => {
        if (valid) {
          this.getList();
        } else {
          this.$message.error('请完善查询条件');
          return false;
        }
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.setDefaultDate();
      this.resetForm("queryForm");
      this.handleQuery();
    }
  }
};
</script>

<style scoped>
.expand-content {
  padding: 10px;
  background-color: #fafafa;
}

.profit-total {
  font-weight: bold;
  color: #67c23a;
}

/* 表格样式优化 */
.el-table {
  font-size: 12px;
}

.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.el-table td {
  padding: 8px 0;
}

/* 展开行内的表格样式 */
.expand-content .el-table {
  font-size: 11px;
  border: 1px solid #dcdfe6;
}

.expand-content .el-table th {
  background-color: #f8f9fa;
  font-size: 11px;
}

.expand-content .el-table td {
  padding: 6px 0;
}

/* 主表格行样式 */
.el-table__row {
  cursor: pointer;
}

.el-table__row:hover {
  background-color: #f5f7fa;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  .el-table {
    font-size: 11px;
  }

  .el-table-column {
    min-width: 60px;
  }

  .expand-content .el-table {
    font-size: 10px;
  }
}
</style>
