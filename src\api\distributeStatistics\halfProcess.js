import request from '@/utils/request'

// 查询平台半流程分组配置列表
export const getHalfPlatformGroupConfig = (params) => {
  return request({
    url: '/loan/xm/halfPlatformGroupConfig/list',
    method: 'get',
    params
  })
}

// 查询平台半流程产品列表
export const getHalfPlatformProduct = (params) => {
  return request({
    url: '/loan/xm/halfPlatformGroupConfig/listHalfProduct',
    method: 'get',
    params
  })
}

// 新增平台半流程分组配置
export const addHalfPlatformProduct = (data) => {
  return request({
    url: '/loan/xm/halfPlatformGroupConfig/add',
    method: 'post',
    data
  })
}

// 修改平台半流程分组配置
export const editHalfPlatformProduct = (data) => {
  return request({
    url: '/loan/xm/halfPlatformGroupConfig/update',
    method: 'post',
    data
  })
}

// 查询全流程出量产品列表
export const getFullProcessProductList = (params) => {
  return request({
    url: 'loan/xm/halfPlatformGroupConfig/listOutputProduct',
    method: 'get',
    params
  })
}
