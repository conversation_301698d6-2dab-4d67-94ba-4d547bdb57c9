<template>
  <div v-if="content" class="config-description-panel">
    <el-collapse v-model="activeDescriptionPanels" class="compact-collapse">
      <el-collapse-item name="description">
        <template slot="title">
          <span class="collapse-title">
            <i class="fas fa-info-circle" style="font-size: 14px; color: #2196f3;"></i>
            配置说明
          </span>
        </template>
        <div v-html="content" class="description-content"></div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
export default {
  name: 'ConfigDescription',
  props: {
    content: {
      type: String,
      default: ''
    }
  },
  
  data() {
    return {
      activeDescriptionPanels: []
    }
  }
}
</script>

<style lang="scss" scoped>
.config-description-panel {
  margin: 8px;
  flex-shrink: 0;

  .compact-collapse {
    border: 1px solid #e8eaed;
    border-radius: 4px;

    ::v-deep .el-collapse-item__header {
      background: #f1f3f4;
      color: #202124;
      border-bottom: 1px solid #e8eaed;
      padding: 8px 12px;
      font-weight: 500;
      font-size: 13px;
      min-height: 36px;

      .collapse-title {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }

    ::v-deep .el-collapse-item__content {
      padding: 12px;
      background: #ffffff;
      
      .description-content {
        color: #5f6368;
        line-height: 1.5;
        font-size: 13px;

        ::v-deep p {
          margin: 6px 0;
        }

        ::v-deep ul, ::v-deep ol {
          margin: 6px 0;
          padding-left: 18px;
        }

        ::v-deep li {
          margin: 3px 0;
        }
      }
    }
  }
}
</style>