import request from '@/utils/request'

// 查询子平台的返点申请
export function getQwlinkUpdateLogList(params) {
  return request({
    // url: `/xm/qw/qwlinkUpdateLog/list?pageNum=${data.pageNum}&pageSize=${data.pageSize}&`,
    url: `/xm/qw/qwlinkUpdateLog/list`,
    method: 'get',
    params
  })
}

// 查询子平台的返点申请
export function getQwlinkUpdateLogProductNameList(params) {
  return request({
    url: `/xm/qw/getQwProductName`,
    method: 'get',
    params
  })
}
