<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="用户手机号" prop="phone">
        <el-input v-model="queryParams.phone" oninput="value=value.replace(/[^\d]/g,'')" maxlength="11" placeholder="请输入用户手机号" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户ID" prop="consumerId">
        <el-input v-model="queryParams.consumerId" type="number" placeholder="请输入用户ID" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" type="primary">搜索</el-button>
      </el-form-item>

    </el-form>
    <el-table :data="list" border>
      <el-table-column label="协议ID" align="center" prop="protocolId" />
      <el-table-column label="协议名称" align="center" prop="protocolName" />
      <el-table-column label="协议版本号" align="center" prop="versionNo" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{row}">
          <div>
            <el-button type="text" @click="hanldeShowDeatil(row)">查看详情</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog title="协议详情" :visible.sync="showDetail" width="800px" :close-on-click-modal="false">
      <div v-html="content" id="pdfDom" class="content">
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { getUserProtocol, getUserProtocolDeatil } from "@/api/operate/agreement";
export default {
  data() {
    return {
      list: [],
      total: 0,
      content: "",

      showDetail: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        consumerId: "",
        phone: ""
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    getList() {
      getUserProtocol(this.queryParams).then(res => {
        this.total = res.total
        this.list = res.rows
      })
    },
    hanldeShowDeatil(row) {
      getUserProtocolDeatil(row.consumerId, row.protocolId, row.versionNo).then(res => {
        this.content = res.data.content
        this.showDetail = true
      })
    }
  },
  mounted() {

  }
}
</script>

<style lang="scss" scoped>
.content {
  max-height: 600px;
  overflow-y: scroll;
}
</style>
