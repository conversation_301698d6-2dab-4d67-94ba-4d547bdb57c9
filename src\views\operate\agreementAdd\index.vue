<template>
  <div class="app-container">
    <el-form :model="formData" ref="formData" :rules="rules" label-position="left" label-width="120px">
      <el-form-item label="协议名称" prop="name">
        <el-input clearable placeholder="请输入协议名称" v-model="formData.name"></el-input>
      </el-form-item>
      <!-- <el-form-item label="协议类型" prop="protocolTypeId">
        <el-select
          clearable
          filterable
          v-model.number="formData.protocolTypeId"
          placeholder="请选择协议类型"
        >
          <el-option
            v-for="item in options"
            :key="item.protocolTypeId"
            :label="item.name"
            :value="item.protocolTypeId"
          >
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="项目归属">
        <el-select clearable filterable multiple v-model="formData.projectIds" placeholder="请选择项目归属">
          <el-option v-for="item in projectOptions" :key="item.projectId" :label="item.name" :value="item.projectId">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="协议类型" prop="protocolSigIds">
        <el-select clearable filterable multiple v-model="formData.protocolSigIds" placeholder="请选择协议类型">
          <el-option v-for="item in signOptions" :key="item.protocolSigId" :label="item.name"
            :value="item.protocolSigId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否内容为链接" prop="isLink">
        <el-radio v-model="formData.isLink" :label="true">是</el-radio>
        <el-radio v-model="formData.isLink" :label="false">否</el-radio>
      </el-form-item>
      <el-form-item label="是否跟新版本" prop="updateVersion" v-if="$route.query.id">
        <el-radio v-model="formData.updateVersion" :label="true">是</el-radio>
        <el-radio v-model="formData.updateVersion" :label="false">否</el-radio>
      </el-form-item>
      <el-form-item label="协议内容" prop="content">
        <div id="editor" />
      </el-form-item>
      <el-form-item>
        <el-button @click="submitForm" type="primary">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  addProtocolOne,
  editProtocolOne,
  getProtocolOne,
  getListSig,
  getlistProject,
} from "@/api/operate/agreement";
import editor from "wangeditor";

export default {
  data() {
    return {
      options: [],
      signOptions: [],
      projectOptions: [],
      formData: {
        content: "",
        name: "",
        isLink: false,
        updateVersion: false,
        projectIds: [],
        protocolSigIds: [],
      },
      rules: {
        name: [{ required: true, message: "请输入协议名称", trigger: "blur" }],
        content: [
          { required: true, message: "请输入协议类容", trigger: "blur" },
        ],
        projectIds: [
          { required: true, message: "请选择项目归属", trigger: "blur" },
        ],
        protocolSigIds: [
          { required: true, message: "请选择协议类型", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    submitForm() {
      this.formData.content = this.editor.txt.html();
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (!this.$route.query.id) {
            addProtocolOne(this.formData).then((res) => {
              if ((res.code = 200)) {
                this.$message.success("新增成功");
                this.$router.push("/operate/agreement");
              }
            });
          } else {
            this.formData.protocolId = this.$route.query.id;
            editProtocolOne(this.formData).then((res) => {
              if ((res.code = 200)) {
                this.$message.success("修改成功");
                this.$router.push("/operate/agreement");
              }
            });
          }
        }
      });
    },
  },
  mounted() {
    this.editor = new editor("#editor");
    this.editor.config.height = 500;
    this.editor.config.zIndex = 10;
    this.editor.config.fontNames = [
      "黑体",
      "仿宋",
      "楷体",
      "标楷体",
      "华文仿宋",
      "华文楷体",
      "宋体",
      "微软雅黑",
      "Arial",
      "Tahoma",
      "Verdana",
      "Times New Roman",
      "Courier New",
    ];



    this.editor.create();
    getListSig().then((res) => {
      this.signOptions = res.data;
    });
    getlistProject().then((res) => {
      this.projectOptions = res.data;
    });
    if (this.$route.query.id) {
      getProtocolOne(this.$route.query.id).then((res) => {
        this.formData.name = res.data.name;
        this.formData.projectIds = res.data.projectIds;
        this.formData.protocolSigIds = res.data.sigIds;
        this.formData.isLink = res.data.isLink;
        this.formData.updateVersion = false

        this.editor.txt.html(res.data.content);
      });
    }
  },
};
</script>

<style lang="scss" scoped>

</style>
