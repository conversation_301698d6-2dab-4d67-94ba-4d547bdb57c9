<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="产品名称" prop="searchProductName">
        <el-input
          size="small"
          clearable
          placeholder="请输入产品名称"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.searchProductName"
        ></el-input>
      </el-form-item>
      <el-form-item label="产品筛选规则" prop="channelStatus">
        <el-select
          v-model="queryParams.channelStatus"
          size="small"
          clearable

        >
          <el-option value="1" label="仅展示配置渠道"> </el-option>
          <el-option value="2" label="不展示配置渠道"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="渠道"

      >
        <el-select
          clearable
          size="small"
          v-model="channelIds"
          placeholder="请选择渠道筛选方式"
          filterable
          multiple
          collapse-tags
        >
          <el-option
            :value="item.id"
            :label="item.id + '--' + item.channelName"
            v-for="item in channelList"
            :key="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="dataList" :row-key="(row) => row.id">
      <el-table-column label="产品ID" prop="productId" align="center" />
      <el-table-column label="产品名称" prop="productName" align="center" />
      <el-table-column label="产品筛选规则" prop="channelStatus" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ row.channelStatus == 1 ? "仅展示配置渠道" : "不展示配置渠道" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="限制渠道"
        prop="channelStr"
        align="center"

      />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getProductLimitList,
  getChannelList,
} from "@/api/productManage/product";

export default {
  name: "",
  data() {
    return {
      total: 0,
      dataList: [],
      channelList: [],
      channelIds:[],

      queryParams: {
        pageNum: 1,
        pageSize: 10,
        channelStatus: "",
        searchProductName: "",
        channelStr:"",
      },
    };
  },

  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      this.queryParams.channelStr=this.channelIds.join(",")
      getProductLimitList(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total;
      });
    },
  },
  mounted() {
    this.getList();
    getChannelList().then((res) => {
      this.channelList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped></style>
