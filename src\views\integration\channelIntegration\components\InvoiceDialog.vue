<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="600px"
    append-to-body
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="invoiceForm"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="乙方" prop="partyId">
        <el-select
          v-model="form.partyId"
          placeholder="请选择乙方"
          filterable
        >
          <el-option
            v-for="item in availableParties"
            :key="item.id"
            :label="`${item.id}-${item.name}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发票日期" prop="fileData">
        <el-date-picker
          v-model="form.fileData"
          type="date"
          placeholder="选择发票日期"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="发票文件" prop="file">
        <el-upload
          v-if="!form.file"
          ref="fileUpload"
          action=""
          :http-request="handleFileUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="handleBeforeUpload"
          :show-file-list="false"
          :limit="1"
          accept=".pdf,.jpg,.jpeg,.png"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
        </el-upload>
        <div v-if="form.file" style="margin-top: 8px">
          <div class="file-preview">
            <template v-if="isImageFile(form.file)">
              <el-image
                :src="form.file"
                :preview-src-list="[form.file]"
                fit="cover"
                style="width: 40px; height: 40px; cursor: pointer"
              />
              <span class="file-name">{{ form.fileName || "已上传图片" }}</span>
            </template>
            <template v-else>
              <i
                :class="getFileIcon(form.file)"
                :style="{ color: getFileColor(form.file) }"
              ></i>
              <span class="file-name">{{
                form.fileName || "已上传文件"
              }}</span>
              <template v-if="isPdfFile(form.file)">
                <el-button
                  size="mini"
                  type="text"
                  @click="showPdfPreview"
                >预览</el-button>
              </template>
              <template v-else>
                <el-button
                  size="mini"
                  type="text"
                  @click="openFileInNewTab"
                >预览</el-button>
              </template>
            </template>
            <el-button
              size="mini"
              type="text"
              @click="handleRemoveFile"
              class="file-remove"
              >删除</el-button
            >
          </div>
        </div>
      </el-form-item>
      <el-form-item label="发票金额" prop="amount">
        <el-input-number
          v-model="form.amount"
          :min="0"
          :precision="2"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </div>

    <!-- PDF预览弹窗 -->
    <PdfPreviewDialog ref="pdfPreviewDialog" />
  </el-dialog>
</template>

<script>
import {
  addIntegrationInvoice,
  updateIntegrationInvoice,
} from "@/api/integration";
import { uploadFileToOSS } from "@/api/file";
import PdfPreviewDialog from "./PdfPreviewDialog.vue";

export default {
  name: "InvoiceDialog",
  components: {
    PdfPreviewDialog
  },
  data() {
    return {
      // 内部状态管理
      dialogVisible: false,
      title: "",
      operationType: "add",
      availableParties: [],
      // 表单数据
      form: {
        id: null,
        partyId: null,
        integratedId: null,
        fileData: null,
        file: null,
        amount: null,
        remark: "",
        fileName: null,
      },
      // 表单验证规则
      rules: {
        partyId: [{ required: true, message: "请选择乙方", trigger: "change" }],
        fileData: [
          { required: true, message: "请选择发票日期", trigger: "change" },
        ],
        file: [
          { required: true, message: "请上传发票文件", trigger: "change" },
        ],
        amount: [
          { required: true, message: "请输入发票金额", trigger: "blur" },
        ],
        remark: [{ required: true, message: "请输入备注", trigger: "blur" }],
      },
    };
  },
  methods: {
    /** 打开新增发票弹窗 */
    openAdd(rowData, partyOptions) {
      this.title = "新增发票";
      this.operationType = "add";
      this.availableParties = this.getAvailableParties(rowData, partyOptions);
      
      // 重置表单数据并设置新增所需的字段
      this.form = {
        id: null,
        partyId: null,
        integratedId: rowData.integrationId,
        fileData: null,
        file: null,
        amount: null,
        remark: "",
        fileName: null,
      };
      
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.invoiceForm) {
          this.$refs.invoiceForm.clearValidate();
        }
      });
      
      this.dialogVisible = true;
    },
    
    /** 打开编辑发票弹窗 */
    openEdit(invoiceData, rowData, partyOptions) {
      this.title = "修改发票";
      this.operationType = "edit";
      this.availableParties = this.getAvailableParties(rowData, partyOptions);
      
      // 回填表单数据
      this.form = {
        id: invoiceData.id,
        partyId: invoiceData.partyId,
        integratedId: rowData.integrationId,
        fileData: invoiceData.fileData,
        file: invoiceData.file,
        amount: invoiceData.amount,
        remark: invoiceData.remark,
        fileName: invoiceData.fileName || "已上传文件",
      };
      
      this.dialogVisible = true;
    },

    /** 获取可用的乙方列表 */
    getAvailableParties(rowData, partyOptions) {
      if (!rowData || !rowData.vo || !rowData.vo.partyIds) {
        return [];
      }
      const partyIds = rowData.vo.partyIds;
      return partyOptions.filter((party) => partyIds.includes(party.id));
    },

    /** 提交表单 */
    handleSubmit() {
      this.$refs["invoiceForm"].validate((valid) => {
        if (valid) {
          // 构建符合接口文档要求的参数
          const formData = {
            partyId: this.form.partyId,
            integratedId: this.form.integratedId,
            fileData: this.form.fileData,
            file: this.form.file,
            amount: this.form.amount,
            remark: this.form.remark,
          };

          // 根据操作类型调用对应接口
          if (this.operationType === "edit") {
            // 修改发票需要传id
            formData.id = this.form.id;
            updateIntegrationInvoice(formData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.handleClose();
              this.$emit("success");
            });
          } else {
            // 新增发票不需要传id
            addIntegrationInvoice(formData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.handleClose();
              this.$emit("success");
            });
          }
        }
      });
    },
    
    /** 取消 */
    handleCancel() {
      this.handleClose();
    },
    
    /** 关闭弹窗 */
    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
    },
    
    /** 重置表单 */
    resetForm() {
      this.form = {
        id: null,
        partyId: null,
        integratedId: null,
        fileData: null,
        file: null,
        amount: null,
        remark: "",
        fileName: null,
      };
      this.$nextTick(() => {
        if (this.$refs.invoiceForm) {
          this.$refs.invoiceForm.resetFields();
        }
      });
    },

    /** 自定义文件上传 */
    async handleFileUpload({ file }) {
      try {
        const formData = new FormData();
        formData.append("file", file);
        const response = await uploadFileToOSS(formData);

        // 根据接口返回格式处理数据
        if (response.code === 200 && response.url) {
          this.form.file = response.url;
          this.form.fileName = file.name;
          this.$message.success("文件上传成功");
        } else {
          this.$message.error("文件上传失败");
        }
      } catch (error) {
        console.error("文件上传错误:", error);
        this.$message.error("文件上传失败");
      }
    },

    /** 文件上传成功回调 */
    handleUploadSuccess(response, file) {
      // 已在handleFileUpload中处理
    },

    /** 文件上传失败回调 */
    handleUploadError(error) {
      this.$message.error("文件上传失败");
    },

    /** 删除文件 */
    handleRemoveFile() {
      this.form.file = null;
      this.form.fileName = null;
      // 只有当上传组件存在时才清除文件列表
      this.$nextTick(() => {
        if (this.$refs.fileUpload) {
          this.$refs.fileUpload.clearFiles();
        }
      });
    },

    /** 上传前校验 */
    handleBeforeUpload(file) {
      // 文件类型校验：只允许PDF和图片格式
      const allowedTypes = ["pdf", "jpg", "jpeg", "png"];
      const fileName = file.name.toLowerCase();
      const fileExtension = fileName.split(".").pop();

      if (!allowedTypes.includes(fileExtension)) {
        this.$message.error("只能上传PDF或图片格式的文件（pdf/jpg/jpeg/png）");
        return false;
      }

      // 文件大小校验（10MB = 10 * 1024 * 1024 bytes）
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$message.error("文件大小不能超过10MB");
        return false;
      }

      return true;
    },

    /** 根据文件URL获取文件图标 */
    getFileIcon(fileUrl) {
      if (!fileUrl) return "fas fa-file";

      const extension = this.getFileExtension(fileUrl);
      const iconMap = {
        pdf: "fas fa-file-pdf",
        jpg: "fas fa-file-image",
        jpeg: "fas fa-file-image",
        png: "fas fa-file-image",
      };

      return iconMap[extension] || "fas fa-file";
    },

    /** 根据文件URL获取文件颜色 */
    getFileColor(fileUrl) {
      if (!fileUrl) return "#606266";

      const extension = this.getFileExtension(fileUrl);
      const colorMap = {
        pdf: "#e74c3c",
        jpg: "#f39c12",
        jpeg: "#f39c12",
        png: "#f39c12",
      };

      return colorMap[extension] || "#606266";
    },

    /** 获取文件扩展名 */
    getFileExtension(fileUrl) {
      if (!fileUrl) return "";

      const url = fileUrl.split("?")[0]; // 移除查询参数
      const parts = url.split(".");
      return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : "";
    },

    /** 判断是否为图片文件 */
    isImageFile(fileUrl) {
      if (!fileUrl) return false;
      
      const extension = this.getFileExtension(fileUrl);
      const imageExtensions = ['jpg', 'jpeg', 'png'];
      return imageExtensions.includes(extension);
    },

    /** 判断是否为PDF文件 */
    isPdfFile(fileUrl) {
      if (!fileUrl) return false;
      
      const extension = this.getFileExtension(fileUrl);
      return extension === 'pdf';
    },

    /** 显示PDF预览 */
    showPdfPreview() {
      if (this.form.file && this.isPdfFile(this.form.file)) {
        this.$refs.pdfPreviewDialog.open(this.form.file);
      }
    },

    /** 在新标签页打开文件 */
    openFileInNewTab() {
      if (this.form.file) {
        window.open(this.form.file, '_blank');
      }
    },
  },
};
</script>

<style scoped>
.file-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-preview i {
  font-size: 20px;
  flex-shrink: 0;
}

.file-name {
  flex: 1;
  color: #606266;
  font-size: 14px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.file-remove {
  flex-shrink: 0;
  color: #f56c6c;
}
</style> 