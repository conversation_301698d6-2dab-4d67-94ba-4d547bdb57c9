import request from '@/utils/request'
//获取助贷列表
export const getloanModeList = (data) => {
  return request({
    url: "/loan/loanMode/list",
    method: "get",
    params: data
  })
}
//新增助贷
export const addloanModeOne = (data) => {
  return request({
    url: "/loan/loanMode/add",
    method: "post",
    data
  })
}
//编辑助贷
export const editloanModeOne = (data) => {
  return request({
    url: "/loan/loanMode/edit",
    method: "post",
    data
  })
}
// //删除助贷
// export const delloanModeOne = (id) => {
//   return request({
//     url: `/loan/loanMode/del/${id}`,
//     method: "post",
//   })
// }
//修改api产品配置信息
export const updateParam = (data) => {
  return request({
    url: '/loan/loanMode/updateParam',
    method: "post",
    data
  })
}

//软删除api
export const delLoanModeOne = (data) => {
  return request({
    url: '/loan/loanMode/delete',
    method: "post",
    data
  })
}
