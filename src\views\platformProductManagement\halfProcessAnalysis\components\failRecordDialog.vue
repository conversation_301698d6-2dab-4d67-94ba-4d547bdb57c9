<template>
  <el-dialog
    :title="dialogTitle"
    :visible="visible"
    width="80%"
    :before-close="handleClose"
    append-to-body
    @open="handleOpen"
  >
    <div class="fail-record-dialog">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="失败记录" name="fail">
          <div class="operation-bar">
            <el-button type="primary" size="small" @click="handleSaveMD5" :disabled="!selection.length">
              保存选中MD5为txt
            </el-button>
            <el-button type="primary" size="small" @click="handleCopyMD5" :disabled="!selection.length">
              复制选中MD5
            </el-button>
          </div>
          <el-table
            ref="failTable"
            :data="tableData"
            border
            style="width: 100%"
            max-height="500"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
            />
            <el-table-column
              prop="channelId"
              label="渠道ID"
              width="100"
            />
            <el-table-column
              prop="channelName"
              label="渠道名称"
              min-width="120"
            />
            <el-table-column
              prop="md5Phone"
              label="MD5手机号"
              min-width="180"
            />
            <el-table-column
              prop="feedbackContent"
              label="反馈内容"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column
              prop="feedbackTime"
              label="回调时间"
              width="200"
            />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="未回调记录" name="notCallback">
          <div class="operation-bar">
            <el-button type="primary" size="small" @click="handleSaveMD5" :disabled="!selection.length">
              保存选中MD5为txt
            </el-button>
            <el-button type="primary" size="small" @click="handleCopyMD5" :disabled="!selection.length">
              复制选中MD5
            </el-button>
          </div>
          <el-table
            ref="notCallbackTable"
            :data="tableData"
            border
            style="width: 100%"
            max-height="500"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
            />
            <el-table-column
              prop="channelId"
              label="渠道ID"
              width="100"
            />
            <el-table-column
              prop="channelName"
              label="渠道名称"
              min-width="120"
            />
            <el-table-column
              prop="md5Phone"
              label="MD5手机号"
              min-width="180"
            />
            <el-table-column
              prop="feedbackContent"
              label="反馈内容"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column
              prop="feedbackTime"
              label="回调时间"
              width="200"
            />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script>
import { getHalfProcessFailList, getHalfProcessNotCallbackList } from '@/api/platformProductManagement/halfProcessAnalysis'
import { saveAs } from 'file-saver'
import clipboard from '@/utils/clipboard'

export default {
  name: 'FailRecordDialog',

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => ({})
    },
    defaultActiveTab: {
      type: String,
      default: 'fail',
      validator: function(value) {
        return ['fail', 'notCallback'].includes(value)
      }
    }
  },

  data() {
    return {
      tableData: [],
      selection: [],
      activeTab: this.defaultActiveTab
    }
  },

  computed: {
    dialogTitle() {
      return this.activeTab == 'fail' ? '失败记录' : '未回调记录'
    },

    currentTable() {
      return this.$refs[this.activeTab == 'fail' ? 'failTable' : 'notCallbackTable']
    }
  },

  methods: {
    async fetchData() {
      const api = this.activeTab == 'fail' ? getHalfProcessFailList : getHalfProcessNotCallbackList
      const res = await api(this.params)
      this.tableData = res.data || []
    },

    handleOpen() {
      this.activeTab = this.defaultActiveTab
      this.fetchData()
    },

    handleClose() {
      this.currentTable.clearSelection()
      this.selection = []
      this.$emit('update:visible', false)
    },

    handleTabClick() {
      this.currentTable.clearSelection()
      this.selection = []
      this.fetchData()
    },

    handleSelectionChange(selection) {
      this.selection = selection
    },

    handleSaveMD5() {
      const md5Phones = this.selection.map(item => item.md5Phone).join('\n')
      const blob = new Blob([md5Phones], { type: 'text/plain;charset=utf-8' })
      saveAs(blob, 'md5_phones.txt')
      this.$message.success('文件保存成功')
    },

    handleCopyMD5() {
      const md5Phones = this.selection.map(item => item.md5Phone).join('\n')
      console.log(md5Phones.length);
      
      if (md5Phones.length > 7000) {
        this.$message.warning('选中的MD5内容过长，请使用保存txt方式')
        return
      }
      
      clipboard.copyText(md5Phones).then(() => {
        this.$message.success('复制成功')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.fail-record-dialog {
  // padding: 20px;
  
  .operation-bar {
    margin-bottom: 16px;
  }
}
</style> 