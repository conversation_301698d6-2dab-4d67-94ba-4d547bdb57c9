<template>
  <div class="app-container">
    <div class="status" v-if="show">
      合同状态: <span :style="color[status]">{{ statusType[status] }}</span>
      <span v-if="status == 3" style="margin-left: 10px">失败理由：{{ contractCheckRemark }}</span>
    </div>
    <el-form label-position="top" :model="formData" ref="formData" :rules="rules">
      <div>
        <div class="account">账户认证</div>
        <div class="info">
          <el-row type="flex" justify="space-between">
            <el-col :span="6">
              <el-form-item label="合同编号" prop="">
                <el-input maxlength="50" :disabled="status == 1 " size="mini"
                  v-model="formData.contractNo" placeholder="请输入合同编号" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="认证类型" prop="authType">
                <el-select :disabled="status == 1 " v-model="formData.authType" clearable size="mini"
                  style="width: 100%" placeholder="请选择认证类型">
                  <el-option label="公司" :value="1"></el-option>
                  <el-option label="个人" :value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公司全称：">
                <el-input maxlength="40" :disabled="status == 1 " size="mini"
                  v-model="formData.companyName" placeholder="请输入公司全称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" justify="space-between">
            <el-col :span="6">
              <el-form-item label="公司统一社会信用码:">
                <el-input maxlength="30" :disabled="status == 1 " size="mini"
                  v-model="formData.companyCreditCode" placeholder="请输入公司统一社会信用码" />
              </el-form-item>
            </el-col>

          </el-row>
          <el-row type="flex" justify="space-between">
            <el-col :span="6">
              <el-form-item label="联系人：" prop="contactsName">
                <el-input :disabled="status == 1 " size="mini" maxlength="6" placeholder="请输入联系人"
                  v-model="formData.contactsName" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="联系人电话:" prop="contactsPhone">
                <el-input :disabled="status == 1 " size="mini" maxlength="11"
                  v-model.trim="formData.contactsPhone" placeholder="请输入联系人电话 ：" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="联系人邮箱:" prop="contactsEmail">
                <el-input :disabled="status == 1 " size="mini" maxlength="50"
                  v-model.trim="formData.contactsEmail" placeholder="请输入联系人邮箱1" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="联系人地址:" prop="contactsAddress">
                <el-input maxlength="50" :disabled="status == 1 " size="mini"
                  v-model="formData.contactsAddress" placeholder="请输入联系人地址" />
              </el-form-item>
            </el-col>
            <el-col :span="6" :offset="3">
              <el-form-item label="备注" prop="remark">
                <el-input :disabled="status == 1 " size="mini" v-model="formData.remark"
                  placeholder="请输入备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
      <div>
        <el-row type="flex" justify="space-between">
          <el-col :span="6">
            <el-form-item label="合同文件" prop="contractFilename">
              <el-upload class="upload-demo" action="#" :auto-upload="false" :show-file-list="false"
                :on-change="(e) => changeUpfile(e, { type: 3, name: 'contractFilename', path: 'contractFilepath' })">
                <el-button size="mini" type="primary">点击上传</el-button>
              </el-upload>
            </el-form-item>
            <el-tag type="info" size="small" v-if="formData.contractFilename"
              @close="handleRemoveFile({ name: 'contractFilename', path: 'contractFilepath' })" closable>
              <span class="tag">{{ formData.contractFilename }}</span>
            </el-tag>
          </el-col>
          <el-col :span="6">

            <el-form-item label="营业执照" prop="businessLicenseFilename">
              <el-upload class="avatar-uploader" ref="businessLicenseFilename" :show-file-list="false"
                :on-change="(e) => changeUpfile(e, { type: 1, name: 'businessLicenseFilename', path: 'businessLicenseFilepath' })"
                action="#" :auto-upload="false">

                <img v-if="imageUrl" :src="imageUrl" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="其他文件">
              <el-upload class="upload-demo" action="#" :auto-upload="false" :limit="10" :fileList="fileList"
                :on-change="(e, fileList) => handleOtherFile(e, fileList)"
                :on-remove="(e, fileList) => handleOtherRemove(e, fileList)">
                <el-button size="mini" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">单个文件不能超过2MB；软著、放款资质、ICP备案或其他资料请上传至补充资料</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 流程 -->
        <div v-if="processList.length">
          <div class="check-title">审核信息</div>
          <div class="check-info">
            <el-timeline>
              <el-timeline-item v-for="(activity, index) in processList" :key="index"
                :color="processColor(activity)">
                <div :class="[activity.status == 2 ? 'c_red' : '']">
                  <div> {{ activity.name }}</div>
                  <div>时间:{{ activity.checkTime }}</div>
                  <div v-if="activity.status != -2">状态：{{ statusJson[activity.status] }}</div>
                  <div v-if="activity.status != -2"> 备注：{{ activity.checkRemark || "-" }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
    </el-form>
    <div class="center">
      <el-button v-if="status == 0" type="primary" class="submit" @click="submitform">提交</el-button>
      <el-button type="primary" v-if="status == 2||status == 3" class="submit" v-hasPermi="['partyaAdmin:contract:update']" @click="submitupDataform">修改</el-button>
    </div>
  </div>
</template>

<script>
import {
  contractUploadFile,
  addOnLineContract,
  updateOnLineContract,
} from "@/api/partyManage";
import { getContractInfo } from "@/api/partyA";
export default {
  props: ["id"],
  data() {
    var validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("手机号不能为空"));
      } else if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
        callback(new Error("手机号格式错误"));
      } else {
        callback();
      }
    };
    var validateEamil = (rule, value, callback) => {
      if (!value) {
        callback(new Error("邮箱不能为空"));
      } else if (
        !/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(
          value
        )
      ) {
        callback(new Error("邮箱格式错误"));
      } else {
        callback();
      }
    };
    var validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("名字不能为空"));
      } else if (!/^[\u4e00-\u9fa5]{2,6}$/.test(value)) {
        callback(new Error("名字格式错误"));
      } else {
        callback();
      }
    };


    var validateID = (rule, value, callback) => {
      if (!value) {
        callback(new Error("授权代表身份证号不能为空"));
      } else if (
        !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/.test(
          value
        ) ||
        !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/.test(
          value
        )
      ) {
        callback(new Error("格式错误"));
      } else {
        callback();
      }
    };
    return {
      imageUrl: "",
      cityList: [],
      provinceList: [],
      areaList: [],
      countrysList: [],
      fileList: [],
      processList: [],
      contractCheckRemark: "",
      show: false,
      color: {
        0: "color:red",
        1: "color:#DAA520",
        2: "color:#32CD32",
        3: "color:red",
      },
      statusType: {
        // @ApiModelProperty(value = "合同审核状态(0未上传/1待审核/2审核成功/3审核失败)")
        0: "未上传",
        1: "待审核",
        2: "审核成功",
        3: "审核失败",
      },
      statusJson: {
        0: "发起",
        1: "通过",
        2: "驳回",
        3: "结束",
      },
      status: 0,
      formData: {
        authType: null,
        partyFirstId: null,
        companyName: null,
        companyCreditCode: null,
        contactsName: null,
        contactsPhone: null,
        contactsEmail: null,
        contactsAddress: null,
        businessLicenseFilename: null,
        businessLicenseFilepath: null,
        address: null,
        contractNo: null,
        remark: null,
        contractFilename: null,
        contractFilepath: null,
        otherInfoFiles: []
      },
      rules: {
        authType: [
          { required: true, message: "请选择认证类型", trigger: "change" },
        ],
        contractFilename: [
          { required: true, message: "请选择", trigger: "change" },
        ],
        businessLicenseFilename: [
          { required: true, message: "请选择", trigger: "change" },
        ],
        companyName: [
          { required: true, message: "请输入公司全称", trigger: "blur" },
        ],
        companyCreditCode: [
          {
            required: true,
            message: "请输入公司统一社会信用码",
            trigger: "blur",
          },
        ],
        contactsName: [
          { required: true, validator: validateName, trigger: "blur" },
        ],
        contactsPhone: [
          { required: true, validator: validatePhone, trigger: "blur" },
        ],
        contactsEmail: [
          { required: true, validator: validateEamil, trigger: "blur" },
        ],
        contactsAddress: [
          { required: true, message: "请输入联系人地址", trigger: "blur" },
        ],

        address: [{ required: true, message: "请输入地址", trigger: "blur" }],
      },
    };
  },
  methods: {

    // @ApiModelProperty(value = "合同审核状态(0未上传/1待审核/2审核成功/3审核失败)")
    // private Integer contractCheckStatus;
    //上传文件
    changeUpfile(e, dataInfo) {
      if (dataInfo.type == 1) {
        const isJPG =
          e.raw.type === "image/jpg" ||
          e.raw.type === "image/jpeg" ||
          e.raw.type === "image/png";
        const isLt2M = e.size / 1024 / 1024 < 10;
        if (!isJPG) {

          this.$message.error("上传图片只能是 JPG/PNG/jpeg 格式!");
          return;
        }
        if (!isLt2M) {

          this.$message.error("上传图片大小不能超过 10MB!");
          return;
        }
      }
      if (dataInfo.type == 3) {
        const isLt2M = e.size / 1024 / 1024 < 10;
        if (!isLt2M) {
          this.$message.error("上传文件大小不能超过 10MB!");
          return
        }
      }
      if (dataInfo.type == 2) {
        if (!e.raw.type.includes("video")) {

          this.$message.error("请上传视频");
          return;
        }
        const isLt2M = e.size / 1024 / 1024 < 20;
        if (!isLt2M) {

          this.$message.error("上传文件大小不能超过 20MB!");
          return;
        }
      }

      this.$refs.formData.clearValidate(dataInfo.name)
      let data = new FormData();
      data.append("fileNum", dataInfo.type);
      data.append("file", e.raw);
      // if (dataInfo.name == "businessLicenseFilename") {
      //   this.imageUrl = URL.createObjectURL(e.raw)
      // }
      contractUploadFile(data, this.id).then((res) => {
        this.formData[dataInfo.name] = res.filename
        this.formData[dataInfo.path] = res.filepath
        if (dataInfo.name == "businessLicenseFilename") {
          this.imageUrl =res.url|| URL.createObjectURL(e.raw)

        }
        if (dataInfo.name == "frontIdCardFilename") {
          this.imageUrl1 = URL.createObjectURL(e.raw)
        }
        if (dataInfo.name == "reverseIdCardFilename") {
          this.imageUrl2 = URL.createObjectURL(e.raw)
        }
      });
    },
    //删除文件
    handleRemoveFile(e) {
      this.formData[e.name] = ""
      this.formData[e.path] = ""
    },
    //上传其他文件
    handleOtherFile(e, fileList) {
      const isLt2M = e.size / 1024 / 1024 < 10;
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 10MB!");
        return
      }
      let data = new FormData();
      data.append("fileNum", 3);
      data.append("file", e.raw);
      contractUploadFile(data, this.id).then(res => {
        this.fileList.push({ name: res.filename, url: res.filepath })
        this.formData.otherInfoFiles = this.fileList.map(item => {
          return {
            filename: item.name,
            filepath: item.url
          }
        })
      })
    },
    //删除其他文件
    handleOtherRemove(e, fileList) {
      this.formData.otherInfoFiles = fileList.map(item => {
        return {
          filename: item.name,
          filepath: item.url
        }
      })
    },

    submitform() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.partyFirstId = this.id;
          addOnLineContract(this.formData).then((res) => {
            if (res.code == 200) {
              this.getInfo();
              this.$parent.$parent.contactLineAvisible = false;
              this.$parent.$parent.getList();
              this.$message.success("上传成功");
            }
          });
        }
      });
    },


    submitupDataform() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.partyFirstId = this.id;
          updateOnLineContract(this.formData).then((res) => {
            this.$message.success("修改成功");
            this.$parent.$parent.contactLineAvisible = false;
            this.$parent.$parent.getList();
            this.getInfo();
          });
        }
      });
    },
    getInfo() {
      getContractInfo(this.id).then((res) => {
        const data = res.data;
        this.show = true;
        this.status = res.data.contractCheckStatus;
        this.contractCheckRemark = res.data.contractCheckRemark;
        this.imageUrl = res.data.businessLicenseFilenameUrl
        this.processList = res.data.processList
        let list = res.data.fileOrderList || []
        this.fileList = list.map(item => {
          return {
            name: item.filename,
            url: item.filepath
          }
        })
        this.formData.otherInfoFiles = list.map(item => {
          return {
            filename: item.filename,
            filepath: item.filepath
          }
        })
        // 还原数据
        for (let key in data) {
          // 接口请求出来的
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = data[key];
          }
        }
      });
    },
  },
  computed: {
    processColor: () => {
      return (item) => {
        if (item.status == -1) {
          return ''
        } else if (item.status == 2) {
          return "#ff0000"
        } else {
          return "#00a607"
        }
      };
    }
  },
  mounted() {
    this.getInfo();
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  // padding: 20px 40px;
  padding: 0 0;
}
.avatar {
  max-height: 150px;
}
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 150px;
  height: 150px;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 150px;
  text-align: center;
}

.account {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

::v-deep .info .el-form-item__label {
  line-height: 0px !important;
  font-size: 12px;
}

.iamge-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 270px;
  border: 1px solid #9999;

  &:hover {
    border: 1px solid #1890ff;
  }

  &-title {
    margin-top: 20px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #666;
  }
}



::v-deep .el-upload-list__item-name {
  width: 120px;
  overflow: hidden;
}

.submit {
  margin-top: 20px;
  width: 150px;
}

.center {
  display: flex;
  justify-content: center;
}

.status {
  margin-bottom: 20px;
  font-size: 16px;
  color: #333;
}

.images {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  &-item {
    margin-bottom: 20px;
    width: 300px;
  }
}

.remove {
  margin-top: -16px;
  width: 100%;
  display: flex;
  justify-content: center;

  button {
    padding: 0;
    width: 40px;
    height: 20px;
    line-height: 0;
  }
}

.file-remove {
  display: flex;
  width: 100%;
  height: 30px;
  justify-content: center;
  margin-top: 2px;

  button {
    padding: 0;
    width: 40px;
    height: 20px;
    line-height: 0;
  }
}

.f_c {
  height: 80px;
  line-height: 80px;
}

.tag {
  width: 100px;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::v-deep .el-tag.el-tag--info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 150px;
}

::v-deep .el-dialog__body {
  max-height: calc(100vh - 120px);
  overflow: auto;
}

.check-info {
  max-height: 300px;
  overflow: auto;
  width: 100%;
  margin-top: 10px;
}

.check-title {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  position: sticky;
  top: 0;
}

.c_red {
  color: #f00;
}
</style>
