<template>
  <main class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="申请时间">
        <el-date-picker size="small" v-model="dateRange" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="dateOpt" @change="handleQuery">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="申请类型" prop="status">
        <el-select v-model="queryParams.type" placeholder="请选择申请类型" clearable size="small">
          <el-option value="0" label="全部"></el-option>
          <el-option value="1" label="费用报销"></el-option>
          <el-option value="2" label="外部返点"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择审核状态" clearable size="small">
          <el-option value="-1" label="全部"></el-option>
          <el-option value="0" label="待审核"></el-option>
          <el-option value="1" label="已通过"></el-option>
          <el-option value="2" label="已拒绝"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
        <el-button type="success" size="mini" icon="el-icon-plus" v-hasPermi="['sys:personal:add_reimbursement']"
          @click="handleOpenExpense">报销申请</el-button>
        <el-button type="warning" size="mini" icon="el-icon-plus" v-hasPermi="['sys:personal:add_rebates']"
          @click="isRebates = true, rebatesData.id = ''">返点申请</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tables">
      <el-table-column label="申请时间" prop="date" align="center" />
      <el-table-column label="请款单号" prop="billno" align="center" width="320" />
      <el-table-column label="申请人" prop="name" align="center" />
      <el-table-column label="申请类型" prop="typeStr" align="center" />
      <el-table-column label="金额" prop="price" align="center" />
      <el-table-column label="审核状态" prop="statusStr" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <el-button type="text" icon="el-icon-document-copy" @click="handleOpenDetail(row)"
            v-hasPermi="['sys:personal:list']">详情</el-button>
          <template v-if="row.status == 2">
            <el-button type="text" icon="el-icon-edit" v-hasPermi="['sys:personal:restart']"
              @click="handleResetApply(row)">重新提交</el-button>
            <el-button type="text" icon="el-icon-edit" v-if="row.type == 2" v-hasPermi="['sys:personal:restart']"
              @click="handleUpdateApply(row)">修改</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="init" />

    <el-dialog title="新增请款报销" :visible.sync="isExpense" width="800px" append-to-body center :close-on-click-modal="false"
      @close="handleCloseExpense">
      <el-form label-width="120px" :model="expenseData" :rules="expenseRules" ref="refExpense">
        <el-form-item label="报销金额" prop="price">
          <div class="flex">
            <el-input v-model="expenseData.price" type="number" placeholder="请输入报销金额" />
          </div>
        </el-form-item>
        <el-form-item label="报销类型" prop="type">
          <el-select v-model="expenseData.type" placeholder="请选择报销类型" clearable size="small">
            <el-option value="0" label="办公用品"></el-option>
            <el-option value="1" label="差旅费"></el-option>
            <el-option value="2" label="交通费"></el-option>
            <el-option value="3" label="招待费"></el-option>
            <el-option value="4" label="招聘费"></el-option>
            <el-option value="5" label="日常福利"></el-option>
            <el-option value="6" label="其他"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark" :rules="[{ required: true, message: '请输入备注', trigger: 'blur' }]"
          v-if="expenseData.type == 6">
          <el-input v-model="expenseData.remark" type="texteare" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="报销凭证" prop="files">
          <el-upload class="upload-demo" action="/" :auto-upload="false" multiple :limit="5" :on-change="handleChooseFile"
            :show-file-list="false">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传图片/PDF/word文档文件，且不超过2M</div>
          </el-upload>
          <div class="file-list" v-for="(item, i) in expenseData.files" :key="i">
            <span>{{ item.name }}</span>
            <i class="el-icon-close" @click="handleDelFile(i)"></i>
          </div>
        </el-form-item>
        <el-form-item label="收款人姓名" prop="name">
          <el-input v-model="expenseData.name" disabled />
        </el-form-item>
        <el-form-item label="收款银行卡号" prop="bankCardNo">
          <el-input v-model="expenseData.bankCardNo" @input="(v) => (expenseData.bankCardNo = v.replace(/[^\d]/g, ''))"
            @blur="e => expenseData.bankCardNo = e.target.value" placeholder="请输入收款银行卡号" />
        </el-form-item>
        <el-form-item label="收款卡开户行" prop="bankCardName">
          <el-input v-model="expenseData.bankCardName" placeholder="请输入收款卡开户行" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleExpenseApply">立即申请</el-button>
        <!-- <el-button>取 消</el-button> -->
      </div>
    </el-dialog>

    <el-dialog :title="`${rebatesData.id ? '修改' : '新增'}外部返点申请`" :visible.sync="isRebates" width="900px" append-to-body
      center :close-on-click-modal="false">
      <el-form label-width="120px" :model="rebatesData" :rules="rebatesRules" ref="refRebates">
        <el-form-item label="返点产品明细">
          <div class="flex justify-end">
            <el-button type="text" @click="isProduct = true, options = [], productName = ''">选择产品</el-button>
          </div>
          <div class="pro-wrap">
            <el-table :data="rebatesData.productRebates"
              v-if="rebatesData.productRebates && rebatesData.productRebates.length">

              <el-table-column label="推广名称" prop="productName" align="center" width="240" />
              <el-table-column label="返点时间" prop="date" align="center" width="120" />
              <el-table-column label="有效曝光量" prop="matchingNum" align="center" width="90" />
              <el-table-column label="返点金额" prop="price" align="center">
                <template slot-scope="{ row }">
                  <el-input v-model="row.price" type="number" @blur="handleChangePrice" />
                </template>
              </el-table-column>
              <el-table-column label="" align="center" width="100">
                <template slot-scope="{ row }">
                  <el-button type="text" class="pro-search" size="mini" @click="handleDelRebatesPro(row)">删 除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <el-form-item label="外部返点金额" prop="price">
          <el-input v-model="rebatesData.price" disabled />
        </el-form-item>
        <el-form-item label="收款人姓名" prop="name">
          <el-input v-model="rebatesData.name" placeholder="请输入收款人姓名" />
        </el-form-item>
        <el-form-item label="收款银行卡号" prop="bankCardNo">
          <el-input v-model="rebatesData.bankCardNo" @input="(v) => (rebatesData.bankCardNo = v.replace(/[^\d]/g, ''))"
            @blur="e => rebatesData.bankCardNo = e.target.value" placeholder="请输入收款银行卡号" />
        </el-form-item>
        <el-form-item label="收款卡开户行" prop="bankCardName">
          <el-input v-model="rebatesData.bankCardName" placeholder="请输入收款卡开户行" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleRebateApply">提 交</el-button>
      </div>
    </el-dialog>

    <el-dialog title="选择产品" :visible.sync="isProduct" center :close-on-click-modal="false">
      <div style="color:red;margin-bottom: 10px;" v-if="rebatesData.id">修改将清除之前产品信息，请确认后修改</div>
      <div class="pro-flex" style="padding: 0 0 12px;">
        <div class="name">推广名称</div>
        <el-select v-model="productName" style="width:100%" filterable remote reserve-keyword clearable
          placeholder="请输入推广ID或推广名称" :remote-method="remoteMethod" @change="hanldeProductChange">
          <el-option v-for="item in options" :key="item.productId" :label="`${item.productName}-id(${item.productId})`"
            :value="item.productId">
          </el-option>
        </el-select>

      </div>
      <el-table :data="products">
        <el-table-column label="推广ID" prop="productId" align="center" width="90" />
        <el-table-column label="推广名称" prop="productName" align="center" width="240" />
        <el-table-column label="返点周期" prop="date" align="center">
          <template slot-scope="{ row }">
            <div class="pro-flex">

              <el-date-picker v-model="row.date" type="datetimerange" align="right" value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']"
                :picker-options="dateOpt">
              </el-date-picker>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="" align="center" width="100">
          <template slot-scope="{ row }">
            <el-button type="text" class="pro-search" size="mini" @click="handleDelProduct(row)">删 除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleChoosePro">确 认</el-button>
      </div>
    </el-dialog>

    <el-dialog title="请款详情" :visible.sync="showApply" width="900px" append-to-body center>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form label-position="left" label-width="100px">
            <el-form-item label="报销金额">
              <el-input v-model="applyDetail.price" disabled></el-input>
            </el-form-item>
            <el-form-item label="报销类型">
              <el-input v-model="applyDetail.typeStr" disabled></el-input>
            </el-form-item>
            <el-form-item label="报销人">
              <el-input v-model="applyDetail.username" disabled></el-input>
            </el-form-item>
            <el-form-item label="报销凭证">
              <!-- <el-input v-model="applyDetail.proceedsAccount" disabled></el-input> -->
              <div v-for="(item, index) in applyDetail.files " :key="index" class="files">
                <span> {{ item }}</span>
                <el-button size="mini" type="primary" @click="handleDownloadFile(item)">下载</el-button>
              </div>
            </el-form-item>
            <el-form-item label="收款人姓名">
              <el-input v-model="applyDetail.createUser" disabled></el-input>
            </el-form-item>
            <el-form-item label="收款银行卡号">
              <el-input v-model="applyDetail.bankCardNo" disabled></el-input>
            </el-form-item>
            <el-form-item label="收款卡开户行">
              <el-input v-model="applyDetail.bankCardName" disabled></el-input>
            </el-form-item>
            <el-form-item label="凭证" v-if="applyDetail.certificate">
              <el-image style="width: 100px; height: 100px" :src="applyDetail.certificate"
                :preview-src-list="[applyDetail.certificate]">
              </el-image>
            </el-form-item>
            <el-form-item label="审批意见">
              <el-input v-model="applyDetail.remark" type="textarea" maxlength="20" show-word-limit disabled></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <div>
            <div class="check-title">审核流程</div>
            <div class="check-info1" v-if="applyDetail.processes && applyDetail.processes.length">
              <el-timeline>
                <el-timeline-item v-for="(item, index) in applyDetail.processes" :key="index" :color="processColor(item)">
                  <div :class="[item.status == 2 ? 'c_red' : '']">
                    <div> {{ item.userRemark }}</div>
                    <div>时间:{{ item.checkTime || '-' }}</div>
                    <div v-if="item.status != 3">状态：{{ item.checkResult || '-' }}</div>
                    <div v-if="item.status != 3"> 备注：{{ item.checkRemark || "-" }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
            <div class="check-center" v-else>暂无数据</div>
          </div>
        </el-col>
      </el-row>
    </el-dialog>

    <el-dialog title="外部返点详情" :visible.sync="showBack" width="900px" append-to-body center :close-on-click-modal="false">
      <el-row :gutter="20">
        <el-col :span="15">
          <el-form label-position="left" label-width="120px">
            <el-form-item label="外部返点金额">
              <el-input v-model="backDetail.price" disabled></el-input>
            </el-form-item>
            <el-form-item label="返点产品明细">
              <el-table :data="backDetail.productRebates" border max-height="250px" style="width: 100%">
                <el-table-column prop="productName" align="center" label="推广名称">
                </el-table-column>
                <el-table-column prop="date" align="center" label="返点日期">
                </el-table-column>
                <el-table-column prop="matchingNum" align="center" label="有效曝光量">
                </el-table-column>
                <el-table-column prop="price" align="center" label="返点金额">
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-form-item label="收款人姓名">
              <el-input v-model="backDetail.username" disabled></el-input>
            </el-form-item>
            <el-form-item label="收款人银行卡号">
              <el-input v-model="backDetail.bankCardNo" disabled></el-input>
            </el-form-item>
            <el-form-item label="收款卡开户行">
              <el-input v-model="backDetail.bankCardName" disabled></el-input>
            </el-form-item>
            <el-form-item label="凭证" v-if="backDetail.certificate">
              <el-image style="width: 100px; height: 100px" :src="backDetail.certificate"
                :preview-src-list="[backDetail.certificate]">
              </el-image>
            </el-form-item>
            <el-form-item label="审批意见">
              <el-input v-model="backDetail.remark" type="textarea" maxlength="20" show-word-limit disabled></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="9">
          <div>
            <div class="check-title">审核流程</div>
            <div class="check-info1" v-if="backDetail.processes && backDetail.processes.length">
              <el-timeline>
                <el-timeline-item v-for="(item, index) in backDetail.processes" :key="index" :color="processColor(item)">
                  <div :class="[item.status == 2 ? 'c_red' : '']">
                    <div> {{ item.userRemark }}</div>
                    <div>时间:{{ item.checkTime || '-' }}</div>
                    <div v-if="item.status != 3">状态：{{ item.checkResult || '-' }}</div>
                    <div v-if="item.status != 3"> 备注：{{ item.checkRemark || "-" }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
            <div class="check-center" v-else>暂无数据</div>
          </div>
        </el-col>
      </el-row>
    </el-dialog>
  </main>
</template>

<script>
import {
  getReimbursementDetail,
  getRebatesDeatil,
} from "@/api/auditFinance/request"
import { getFinanceProducts, getRebates, rebateApply, getFinances, expenseApply, resetApply, editApply } from '@/api/financialApply'

const fileType = ["doc", "docx", "pdf", 'jpg', 'jpeg', 'png']
export default {
  name: 'apply',
  data() {
    return ({
      dateRange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        status: ''
      },
      options: [],
      tables: [{ refundDate: 1 }],
      total: 0,
      isExpense: false,   // 是否打开费用报销弹窗
      expenseData: {
        files: [],
        name: [],
        type: '',
        price: '',
        bankCardNo: '',
        bankCardName: '',
        remark: ''
      },    // 当前费用申请参数
      expenseRules: {
        price: [{ required: true, message: '请输入报销金额', trigger: 'blur' }],
        type: [{ required: true, message: '请选择申请类型', trigger: 'blur' }],
        files: [{ required: true, message: '请上传报销凭证', trigger: 'blur' }],
        name: [
          { required: true, message: '请输入收款人姓名', trigger: 'blur' },
          { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
        ],
        bankCardNo: [
          { required: true, message: '请输入收款银行卡号', trigger: 'blur' },
          { min: 15, max: 30, message: '长度在 15 到 30 个数字', trigger: 'blur' }
        ],
        bankCardName: [
          { required: true, message: '请输入开户行', trigger: 'blur' }
        ]
      }, // 校验规则
      isRebates: false,   // 是否打开外部返点申请弹窗
      rebatesData: {
        price: '',
        productRebates: [],
        name: '',
        bankCardNo: '',
        bankCardName: ''
      },                  // 外部返点申请参数
      rebatesRules: {
        name: [
          { required: true, message: '请输入收款人姓名', trigger: 'blur' },
          { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
        ],
        bankCardNo: [
          { required: true, message: '请输入收款银行卡号', trigger: 'blur' },
          { min: 15, max: 30, message: '长度在 15 到 30 个数字', trigger: 'blur' }
        ],
        bankCardName: [
          { required: true, message: '请输入开户行', trigger: 'blur' }
        ]
      },  // 校验规则
      isDetail: false,    // 是否打开详情弹窗
      detail: {},         // 返点 || 报销详情
      isProduct: false,   // 是否打开筛选产品弹窗
      productName: "",    // 推广名称,
      products: [],       // 产品列表
      showApply: false,   // 请款详情弹窗
      applyDetail: {},    // 请款详情
      showBack: false,    // 返点详情弹窗
      backDetail: {},     // 返点详情
      dateOpt: {
        disabledDate: time => {
          return time.getTime() > Date.now();
        }
      }
    })
  },
  computed: {
    processColor: () => {
      return (item) => {
        if (item.status == -1) {
          return ''
        } else if (item.status == 2) {
          return "#ff0000"
        } else {
          return "#00a607"
        }
      };
    }
  },
  created() {
    this.init()
  },
  methods: {
    /**
     *  @param { Function } init 初始化 -- 获取请款列表
     */
    init() {
      let param = this.queryParams

      if (this.dateRange && this.dateRange.length) {
        param = {
          ...param,
          startTime: this.dateRange[0],
          stopTime: this.dateRange[1]
        }
      } else {
        param = {
          ...param,
          startTime: "",
          stopTime: ""
        }
      }
      getFinances(param)
        .then(res => {
          this.tables = res.rows
          this.total = res.total
        })
    },

    /**
     *  @param { Function } handleQuery 搜索
     */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.init()
    },

    /**
     *  @param { Function } handleOpenDetail 打开详情弹窗
     */
    handleOpenDetail(row = {}) {
      if (row.type == 1) {
        getReimbursementDetail({ cashOutId: row.cashOutId })
          .then(res => {
            this.showApply = true
            this.applyDetail = res.data
          })
      } else {
        getRebatesDeatil({ cashOutId: row.cashOutId }).then(res => {
          this.showBack = true
          this.backDetail = res.data
          this.processList = res.data.processes || []
          this.productList = res.data.productRebates || []
        })
      }
    },
    handleUpdateApply(row) {
      if (row.type == 2) {
        getRebatesDeatil({ cashOutId: row.cashOutId }).then(res => {

          this.rebatesData.price = res.data.price
          this.rebatesData.bankCardNo = res.data.bankCardNo
          this.rebatesData.bankCardName = res.data.bankCardName
          this.rebatesData.name = res.data.username
          this.rebatesData.productRebates = res.data.productRebates.map(item => {
            return {
              ...item,
              date: item.date.slice(0, 11)
            }
          }) || []
          this.rebatesData.id = row.cashOutId
          this.isRebates = true
        })
      }


    },


    /**
     *  @param { Function } handleResetApply 重新申请
     */
    handleResetApply(row = {}) {
      this.$confirm(`您确认是否重新提交${row.type == 1 ? '报销' : '返点'}申请`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resetApply({ id: row.cashOutId })
          .then(res => {
            this.$message.success('提交成功')
            this.init()
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消操作'
        });
      });
    },

    remoteMethod(query) {
      if (query !== '') {
        getFinanceProducts({ productName: query }).then(res => {
          console.log(res);
          this.options = res.data
        })
      } else {
        this.options = [];
      }
    },

    hanldeProductChange(e) {
      if (this.products.some(item => item.productId == e)) return
      if (!this.options.filter(item => item.productId == e)[0]) return
      console.log(e, 'e')
      let data = this.options.filter(item => item.productId == e)[0] || []
      data.date = data.startTime ? [data.startTime, data.stopTime] : []
      let arr = [data, ...this.products]
      let obj = {}
      arr = arr.reduce((newArr, next) => {
        obj[next.productId] ? "" : (obj[next.productId] = true && newArr.push(next))
        return newArr
      }, [])
      this.products = JSON.parse(JSON.stringify(arr))
      this.productName = ""
      this.options = []
    },
    /**
     *  @param { Function } handleDelProduct 删除选中产品
     */
    handleDelProduct({ productId = null ,date=null}) {
      this.products = this.products.filter(item => productId != item.productId)
    },

    // 删除已选择产品
    handleDelRebatesPro({ date = null, productId = null }) {


      this.rebatesData.productRebates = this.rebatesData.productRebates.filter(item => !(productId == item.productId&& date == item.date))
      this.products = this.products.filter(item =>!(productId == item.productId&& date == item.date))
      let price = 0
      this.rebatesData.productRebates.forEach(item => {
        price += item.price || 0
      })
      this.rebatesData.price = price.toFixed(2)
    },

    /**
     *  @param { Function } handleChoosePro 确认选中产品
     */
    handleChoosePro() {
      if (!this.products.every(item => item.date.length)) {
        return this.$message.error('请选择返点范围')
      }

      let products = []

      this.products.forEach(item => {
        products = [...products, { productId: item.productId, productName: item.productName, startTime: item.date[0].toLocaleString().replace(/\//g, '-'), stopTime: item.date[1].toLocaleString().replace(/\//g, '-') }]
      })


      getRebates({ products })
        .then(res => {
          this.isProduct = false
          this.rebatesData.productRebates = res.data
          let price = 0
          res.data.forEach(item => {
            price += item.price || 0
          })
          this.rebatesData.price = price.toFixed(2)
          this.productName = ''
        })
    },

    /**
     *  @param { Function } handleRebateApply 外部返点申请
     */
    handleRebateApply() {
      if (!this.rebatesData.productRebates.length) {
        return this.$message.error("请选择产品")
      }
      this.$refs.refRebates.validate(volid => {
        if (volid) {
          if (this.rebatesData.id) {
            editApply(this.rebatesData).then(res => {
              this.isRebates = false
              this.$refs.refRebates.resetFields()
              Object.assign(this.$data, this.$options.data.call(this).rebatesData)
              this.rebatesData.productRebates = []
              this.init()
              console.log(33333333);
              this.$message.success('修改成功')
            })
          } else {
            delete this.rebatesData.id
            rebateApply(this.rebatesData)
              .then(res => {
                this.isRebates = false
                this.$refs.refRebates.resetFields()
                Object.assign(this.$data, this.$options.data.call(this).rebatesData)
                this.rebatesData.productRebates = []
                this.init()
                this.$message.success('操作成功')
              })
          }

        }
      })
    },

    /**
     *  @param { Function } handleOpenExpense 打开报销弹窗
     */
    handleOpenExpense() {
      this.expenseData.name = this.$store.state.user.userInfo.nickName
      this.isExpense = true
    },

    /**
     *  @param { Function } handleCloseExpense 关闭报销弹窗
     */
    handleCloseExpense() {
      this.$refs.refExpense.resetFields()
      Object.assign(this.$data, this.$options.data.call(this).expenseData)
    },

    /**
     *  @param { Function } 个人新增报销申请
     */
    handleExpenseApply() {
      this.$refs.refExpense.validate(voild => {
        if (voild) {
          let formData = new FormData();
          for (let i in this.expenseData) {
            if (i == 'files') {
              this.expenseData.files.forEach(item => {
                formData.append(i, item.raw);
              })
            } else {
              formData.append(i, this.expenseData[i]);
            }
          }

          expenseApply(formData)
            .then(res => {
              this.isExpense = false
              this.init()
            })
        }
      })
    },

    /**
     *  @param { Function } handleChooseFile 选择文件
     */
    handleChooseFile(file, files) {
      let fileExtension = "";
      if (file.name.lastIndexOf(".") > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
      }
      const isTypeOk = fileType.some((type) => file.name.includes(type));
      if (!isTypeOk) {
        this.$message.error(`凭证文件只能是 图片/PDF/word文档 类型!`);
        return false;
      }

      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isLt2M) {
        this.$message.error("上传凭证大小不能超过 2MB!");
        return;
      }
      this.expenseData.files = [file, ...this.expenseData.files]
    },

    /**
     *  @parma { Function } handleDelFile 删除文件
     */
    handleDelFile(i) {
      this.expenseData.files = this.expenseData.files.filter((item, index) => i != index)
    },

    /**
     *  @param { Function } handleDownloadFile 下载文件
     */
    handleDownloadFile(url) {
      window.location.href = url
    },

    /**
     *  @param { Function } handleChangePrice 计算返点金额
     */
    handleChangePrice() {
      let price = 0
      this.rebatesData.productRebates.forEach(item => {
        price += item.price / 1 || 0
      })
      this.rebatesData.price = price.toFixed(2)
    },

    /**
     *  @parma { Function } handleDisabledDate 禁用可选时间
     */
    handleDisabledDate(e) {
      return e.getTime() > Date.now()
    }
  },
  filters: {
    filterStatus(status = 0) {
      if (status == 0) return '待审核'
      if (status == 1) return '通过'
      if (status == 2) return '拒绝'
    },
    filterTypeApply(type) {
      if (type == 0) return '费用报销'
      if (type == 1) return '返点申请'
      return '/'
    }
  }
}
</script>

<style scoped lang="scss">
.pro-flex {
  display: flex;
  align-items: center;

  .name {
    padding-right: 16px;
    min-width: 90px;
  }

  .pro-search {
    margin-left: 16px;
  }
}

.file-list {
  padding: 0 10px 0 2px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  &:hover {
    background-color: rgba(200, 200, 200, 0.1);
  }

  span {
    flex: 1;
    overflow: hidden;
  }
}

.files {
  display: flex;
  margin-top: 5px;

  span {
    display: block;
    width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.check-info1 {
  margin-top: 10px;
  max-height: 500px;
  overflow: auto;
}
</style>

