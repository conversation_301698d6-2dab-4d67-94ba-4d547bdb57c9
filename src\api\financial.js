import request from '@/utils/request'
//查询退款列表
export const getRefundList = (data) => {
  return request({
    url: '/loan/partya/refund/list',
    method: 'get',
    params: data
  })
}
//确认退款
export const confireRefound = (data) => {
  return request({
    url: '/loan/partya/refund/pass',
    method: 'post',
    data
  })
}
//拒绝退款
export const rejectRefound = (data) => {
  return request({
    url: "/loan/partya/refund/doReject",
    method: "post",
    data
  })
}


//获取入账列表
export const getEntryList = (data) => {
  return request({
    url: '/loan/partya/entry/list',
    method: "get",
    params: data
  })
}

//确认或者拒绝入账
export const entryUpdateOne = (data) => {
  return request({
    url: '/loan/partya/entry/updateOne',
    method: "post",
    data
  })
}

//获取请款列表
export const getPartBCashOutList = (data) => {
  return request({
    url: "/loan/finance/cashOutList",
    method: "get",
    params: data
  })
}

//拒绝请款
export const rejectPayment = (data) => {
  return request({
    url: "/loan/finance/doReject",
    method: "post",
    data
  })
}
//同意请款
export const agreePayment = (data) => {
  return request({
    url: "/loan/finance/pass",
    method: "post",
    data
  })
}

//请款详情
export const getFinanceDetail = (data) => {
  return request({
    url: '/loan/finance/detail',
    method: 'get',
    params: data
  })
}

//查询收款主体
export const getSubjectAll = () => {
  return request({
    url: "/loan/partya/entry/subjectAll",
    method: "get"
  })
}


/**
 * 回退订单开始
 *
*/
// 查询回退订单列表
export const getRefundOrderList = (data) => {
  return request({
    url: "/loan/dinPay/refund/order/getRefundOrderList",
    method: "get",
    params: data
  })
}


//申请分账订单回退
export const addStbRefundOrderOne = (data) => {
  return request({
    url: '/loan/dinPay/refund/order/stbRefundOrder',
    method: "post",
    data
  })
}


//退款查询
export const getStbRefundOrderQuery = (data) => {
  return request({
    url: '/loan/dinPay/refund/order/stbRefundOrderQuery',
    method: 'get',
    params: data
  })
}
/**
 * 结束
*/


//查询退款列表
export const getRefundMerchantOrderList = (data) => {
  return request({
    url: '/loan/dinPay/refund/getRefundList',
    method: 'get',
    params: data
  })
}


//申请分账订单退款
export const applyStbRefund = (data) => {
  return request({
    url: "/loan/dinPay/refund/stbRefund",
    method: "post",
    data
  })
}

