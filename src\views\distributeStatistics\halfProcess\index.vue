<template>
  <div class="app-container">
    <el-form :model="form" inline size="mini">
      <el-form-item label="分组">
        <el-radio-group v-model="form.id" @change="changeGroup">
          <el-radio v-for="item in groupList" :key="item.id" :label="item.id">{{
            item.groupName
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="clickAdd">新增</el-button>
        <el-button type="primary" @click="clickEdit" v-if="groupList.length"
          >修改</el-button
        >
      </el-form-item>
    </el-form>

    <div v-if="groupList.length">
      状态:
      <span
        :style="`color: ${infoData.status == 1 ? 'green' : 'red'}`"
        class="staus-text"
        >{{ infoData.status == 1 ? "开启" : "关闭" }}</span
      >
    </div>
    <div class="channel-child">
      <div class="channel-title">平台半流程分组</div>
      <!-- <el-tag
        v-for="item in infoData.productList"
        :key="item.id"
        style="margin-right: 10px; margin-bottom: 10px"
      >

        {{ item.platform + "--" + item.productName + "--" + item.productId }}

      </el-tag> -->
      <div class="tag-list">
        <div
          class="tag-item"
          v-for="item in infoData.productList"
          :key="item.id"
        >
          <div>平台:{{ item.platform }}</div>
          <div>产品ID:{{ item.productId }}</div>
          <div>产品名字:{{ item.productName }}</div>
        </div>
      </div>
    </div>

    <el-dialog
      :title="formData.id ? '修改' : '新增'"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="1000px"
      @close="handleclose"
    >
      <el-form :model="formData" :rules="rules" ref="formData">
        <el-form-item label="分组名称" prop="groupName">
          <el-input
            v-model="formData.groupName"
            placeholder="请输入分组名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="半流程产品" prop="jsonData">
          <el-select
            v-model="formData.jsonData"
            multiple
            clearable
            placeholder="请选择半流程产品"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="item in productList"
              :key="`${item.platformType}-${item.productId}`"
              :label="
                item.platform +
                '-' +
                item.productName +
                (item.groupName ? `(${item.groupName})` : '')
              "
              :value="`${item.platformType}-${item.productId}`"
              :disabled="
                (!!item.groupName &&
                  !backData.includes(
                    `${item.platformType}-${item.productId}`
                  )) ||
                formData.jsonData.length == 6
              "
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio v-model="formData.status" :label="1">开启</el-radio>
          <el-radio v-model="formData.status" :label="2">关闭</el-radio>
        </el-form-item>
        <el-form-item label="排序">
          <el-input v-model="formData.sort" placeholder="请输入排序"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleclose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getHalfPlatformGroupConfig,
  getHalfPlatformProduct,
  addHalfPlatformProduct,
  editHalfPlatformProduct,
} from "@/api/distributeStatistics/halfProcess";

export default {
  data() {
    return {
      rules: {
        groupName: [
          { required: true, message: "请输入分组名称", trigger: "blur" },
        ],
        jsonData: [{ required: true, message: "选择产品", trigger: "blur" }],
      },
      form: {
        id: "",
      },
      groupList: [],
      productList: [],
      infoData: {
        productList: [],
        id: "",
        status: 1,
      },
      backData: [],
      formData: {
        groupName: "",
        id: "",
        status: 1,
        sort: 0,
        id: "",
        jsonData: [],
        groupConfigJson: [],
      },
      row: {},
      dialogVisible: false,
    };
  },
  methods: {
    getGroupList() {
      getHalfPlatformGroupConfig({ type: 1 }).then((res) => {
        this.groupList = res.data;
        if (res.data.length > 0) {
          this.form.id = res.data[0].id;
          this.row = res.data[0];
          this.infoData.productList = JSON.parse(this.row.groupConfigJson);
          this.infoData.status = this.row.status;
        }
      });
    },
    changeGroup() {
      this.row = this.groupList.find((item) => item.id == this.form.id) || {};
      this.infoData.productList = JSON.parse(this.row.groupConfigJson);
      this.infoData.status = this.row.status;
    },
    clickAdd() {
      getHalfPlatformProduct({ type: 1 }).then((res) => {
        this.productList = res.data;
        this.dialogVisible = true;
      });
    },
    clickEdit() {
      getHalfPlatformProduct({ type: 1 }).then((res) => {
        this.productList = res.data;
        this.dialogVisible = true;
        let data = JSON.parse(this.row.groupConfigJson);
        let jsonData = [];
        data.forEach((item) => {
          jsonData.push(item.platformType + "-" + item.productId);
          this.backData.push(item.platformType + "-" + item.productId);
        });
        this.formData = {
          groupName: this.row.groupName,
          id: this.row.id,
          status: this.row.status,
          sort: this.row.sort,
          jsonData,
        };
      });
    },
    handleclose() {
      this.dialogVisible = false;
      this.formData = {
        groupName: "",
        id: "",
        status: 1,
        sort: 0,
        jsonData: [],
        groupConfigJson: [],
      };
      this.backData = [];
    },
    handleSubmit() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          let groupConfigJson = [];
          this.productList.forEach((item) => {
            this.formData.jsonData.forEach((item2) => {
              if (item.platformType + "-" + item.productId == item2) {
                groupConfigJson.push(item);
              }
            });
          });

          let data = {
            groupConfigJson: JSON.stringify(groupConfigJson),
            groupName: this.formData.groupName,
            status: this.formData.status,
            sort: this.formData.sort,
            type: 1
          };
          if (this.formData.id) {
            data.id = this.formData.id;
            editHalfPlatformProduct(data).then((res) => {
              this.$message({
                message: "修改成功",
                type: "success",
              });
              this.handleEditGroup();
              this.handleclose();
            });
          } else {
            addHalfPlatformProduct(data).then((res) => {
              this.$message({
                message: "新增成功",
                type: "success",
              });
              this.handleAddGroup();
              this.handleclose();
            });
          }
        }
      });
    },
    handleAddGroup() {
      getHalfPlatformGroupConfig({ type: 1 }).then((res) => {
        this.groupList = res.data;
        this.form.id = res.data[this.groupList.length - 1].id || 0;
        this.row = res.data[this.groupList.length - 1] || {};
        this.infoData.status = this.row.status;
        this.infoData.productList = JSON.parse(this.row.groupConfigJson);
      });
    },
    handleEditGroup() {
      getHalfPlatformGroupConfig({ type: 1 }).then((res) => {
        this.groupList = res.data;
        this.row = this.groupList.find((item) => item.id == this.form.id) || {};
        this.infoData.productList = JSON.parse(this.row.groupConfigJson);
        this.infoData.status = this.row.status;
      });
    },
  },
  mounted() {
    this.getGroupList();
  },
};
</script>

<style lang="scss" scoped>
$box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
.channel-child {
  width: 900px;
  min-height: 300px;
  box-shadow: $box-shadow;
  padding: 5px;
  margin-top: 30px;
}
.channel-title {
  border-bottom: 1px solid #ececec;
  margin-bottom: 10px;
  line-height: 40px;
  color: #666;
  font-size: 16px;
  font-weight: bold;
}
.staus-text {
  font-weight: bold;
}
.tag-item {
  display: flex;
  flex-direction: column;
  border: 1px solid #ccdbee;
  padding: 5px 10px;
  margin-bottom: 10px;
  margin-right: 10px;
  border-radius: 5px;
  color: #004DAB;
  background: #e6edf7;
}
.tag-list {
  display: flex;
  flex-wrap: wrap;
}
</style>
<style>
input[aria-hidden="true"] {
  display: none !important;
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none;
}
</style>
