import request from '@/utils/request'
//获取短信列表
export const getNoteList = (data) => {
    return request({
        url: "/loan/dreamSend/dreamSendlist",
        method: "get",
        params: data
    })
}
//新增短息模板
export const addNoteOne = (data) => {
    return request({
        url: "/loan/dreamSend/dreamSendAddNew",
        method: "post",
        data
    })
}
//新增短息模板
export const editNoteOne = (data) => {
    return request({
        url: "/loan/dreamSend/dreamSendEditNew",
        method: "post",
        data
    })
}
//新增短息模板
export const delNoteOne = (data) => {
    return request({
        url: "/loan/dreamSend/dreamSendRemove",
        method: "get",
        params: data
    })
}


//获取短信key列表
export const getSmsTemplateList = (data) => {
    return request({
        url: '/loan/smsTemplate/list',
        method: "get",
        params: data
    })
}

//新增短信列表
export const addSmsTemplateOne = (data) => {
    return request({
        url: '/loan/smsTemplate/addone',
        method: "post",
        data
    })
}
//修改短信列表
export const editSmsTemplateOne = (data) => {
    return request({
        url: '/loan/smsTemplate/updateone',
        method: "post",
        data
    })
}

//获取短信配置模块列表
export const getSmsConfigurationTemplate = (data) => {
    return request({
        url: '/loan/smsConfigurationTemplate/list',
        method: 'get',
        params: data
    })
}
//获取短信配置模块列表
export const addSmsConfigurationOne = (data) => {
    return request({
        url: '/loan/smsConfigurationTemplate/addone',
        method: 'post',
        data
    })
}
//获取短信配置模块列表
export const editSmsConfigurationOne = (data) => {
    return request({
        url: '/loan/smsConfigurationTemplate/updateone',
        method: 'post',
        data
    })
}

//获取短信配置模块
export const getSmsConfigurate = () => {
    return request({
        url: "/loan/smsConfigurationTemplate/findListOnline",
        method: "get"
    })
}
//获取短信配置模块
export const getsmsTemplateKey = (data) => {
    return request({
        url: "/loan/smsTemplate/otherlist",
        method: "get",
        params: data
    })
}