<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams">
      <el-form-item label="IP地址">
        <el-input v-model="queryParams.ip" placeholder="请输入IP地址" clearable style="width: 240px"
          @keyup.enter.native="handleAdd" @input="validateInput" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleAdd">添加</el-button>
        <el-button type="primary" @click="handleGetCurrentIp">你的公网IP</el-button>
      </el-form-item>
    </el-form>

    <!-- 显示当前公网IP -->
    <div v-if="currentPublicIp" class="public-ip-box">
      <span class="public-ip-title">当前公网IP:</span>
      <el-tag type="primary">{{ currentPublicIp }}</el-tag>
    </div>

    <el-table :data="ipList" border style="width: 100%">
      <el-table-column type="index" label="序号" width="80" align="center">
      </el-table-column>
      <el-table-column prop="ip" label="IP地址" align="center">
      </el-table-column>
      <el-table-column label="操作" width="160" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="openEditDialog(scope.row, scope.$index)"
          >编辑</el-button>
          <el-button
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑弹窗 -->
    <el-dialog :title="editIndex === -1 ? '添加IP白名单' : '编辑IP白名单'" :visible.sync="dialogVisible" width="500px" :close-on-click-modal="false">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="editForm.ip" placeholder="请输入IP地址" @input="validateEditInput"></el-input>
        </el-form-item>
        <div class="dialog-tip">支持IP通配符*，如：192.168.1.*</div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getIpWhiteList, updateIpWhiteList, getCurrentPublicIp } from '@/api/autoTest/ipWhiteList'
import validator from 'validator'

export default {
  name: 'IpWhiteList',
  data() {
    return {
      // 查询参数
      queryParams: {
        ip: ''
      },
      // IP白名单列表
      ipList: [],
      // 当前公网IP
      currentPublicIp: '',
      // 当前编辑的IP索引，-1表示新增
      editIndex: -1,
      // 弹窗可见性
      dialogVisible: false,
      // 编辑表单
      editForm: {
        ip: ''
      }
    }
  },
  created() {
    this.getList()
    this.getPublicIp()
  },
  methods: {
    /** 获取IP白名单列表 */
    async getList() {
      const response = await getIpWhiteList()
      if (response.data && Array.isArray(response.data)) {
        this.ipList = response.data.map(ip => ({ ip }))
      } else {
        this.ipList = []
      }
    },
    /** 获取当前公网IP */
    async getPublicIp() {
      const response = await getCurrentPublicIp()
      if (response.data) {
        // 接口只返回单个IP
        this.currentPublicIp = response.data
      }
    },
    /** 验证输入，只允许数字、点和通配符* */
    validateInput(value) {
      // 使用正则过滤非法字符，只保留数字、点和星号
      const filteredValue = value.replace(/[^0-9.*]/g, '')
      if (filteredValue !== value) {
        this.queryParams.ip = filteredValue
      }
    },
    /** 验证编辑框输入 */
    validateEditInput(value) {
      // 使用正则过滤非法字符，只保留数字、点和星号
      const filteredValue = value.replace(/[^0-9.*]/g, '')
      if (filteredValue !== value) {
        this.editForm.ip = filteredValue
      }
    },
    /** 校验IP格式是否正确 */
    validateIpFormat(ip) {
      // 先使用validator校验普通IP
      if (validator.isIP(ip)) {
        return true
      }

      // 全通配符
      if (ip === '*') {
        return true
      }

      // 支持带通配符的IP地址，如：127.0.0.*、127.*.0.1、*.0.0.1等
      if (/^(\d{1,3}|\*)\.(\d{1,3}|\*)\.(\d{1,3}|\*)\.(\d{1,3}|\*)$/.test(ip)) {
        // 确保每个数字段落在合法范围内（0-255）
        const parts = ip.split('.');
        for (let i = 0; i < parts.length; i++) {
          const part = parts[i];
          if (part !== '*' && (parseInt(part) < 0 || parseInt(part) > 255)) {
            return false;
          }
        }
        return true;
      }

      return false;
    },
    /** 打开编辑弹窗 */
    openEditDialog(row, index) {
      this.editIndex = index
      this.editForm.ip = row.ip
      this.dialogVisible = true
    },
    /** 添加IP */
    async handleAdd() {
      if (!this.queryParams.ip) {
        this.$message.warning('请输入IP地址')
        return
      }

      // 使用自定义IP校验，支持通配符
      if (!this.validateIpFormat(this.queryParams.ip)) {
        this.$message.warning('请输入正确的IP地址格式，允许使用通配符*')
        return
      }

      // 检查是否已存在相同IP
      const existingIp = this.ipList.find(item => item.ip == this.queryParams.ip)
      if (existingIp) {
        this.$message.warning('该IP地址已存在于白名单中')
        return
      }

      // 添加到本地列表
      this.ipList.push({ ip: this.queryParams.ip })
      
      // 更新到服务器
      await this.updateWhiteList()
      // 刷新列表
      await this.getList()
      // 清空输入框
      this.queryParams.ip = ''
    },
    /** 提交表单 */
    async submitForm() {
      if (!this.editForm.ip) {
        this.$message.warning('请输入IP地址')
        return
      }

      // 使用自定义IP校验，支持通配符
      if (!this.validateIpFormat(this.editForm.ip)) {
        this.$message.warning('请输入正确的IP地址格式，允许使用通配符*')
        return
      }

      if (this.editIndex !== -1) {
        // 编辑模式
        // 检查是否修改了IP，如果修改后的IP已存在，则提示错误
        const existingIndex = this.ipList.findIndex(
          (item, idx) => idx !== this.editIndex && item.ip == this.editForm.ip
        )
        if (existingIndex !== -1) {
          this.$message.warning('该IP地址已存在于白名单中')
          return
        }

        // 更新IP
        this.ipList[this.editIndex].ip = this.editForm.ip
        this.$message.success('IP地址已更新')
      }

      // 更新到服务器
      await this.updateWhiteList()
      // 关闭弹窗并重置状态
      this.dialogVisible = false
      this.editIndex = -1
      this.editForm.ip = ''
      // 刷新列表
      await this.getList()
    },
    /** 删除IP白名单项 */
    async handleDelete(row) {
      await this.$confirm('确认从IP白名单中删除该IP？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      const index = this.ipList.findIndex(item => item.ip == row.ip)
      if (index !== -1) {
        this.ipList.splice(index, 1)
        await this.updateWhiteList()
        // 刷新列表
        await this.getList()
        this.$message.success('IP已删除')
      }
    },
    /** 刷新列表 */
    async refreshList() {
      await this.getList()
    },
    /** 更新白名单到服务器 */
    async updateWhiteList() {
      // 提取所有IP地址
      const ipArray = this.ipList.map(item => item.ip)
      
      await updateIpWhiteList(ipArray)
    },
    /** 处理获取当前公网IP按钮点击 */
    async handleGetCurrentIp() {
      const response = await getCurrentPublicIp()
      if (response.data) {
        // 接口只返回单个IP
        this.currentPublicIp = response.data
        // 回填到输入框
        this.queryParams.ip = response.data
        this.$message.success('获取公网IP成功')
      }
    }
  }
}
</script>

<style scoped>
.public-ip-box {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.public-ip-title {
  font-weight: bold;
  margin-right: 10px;
}
.dialog-tip {
  color: #909399;
  font-size: 12px;
  margin-top: -15px;
  margin-left: 80px;
}
</style>