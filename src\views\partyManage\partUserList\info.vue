<template>
  <div class="contanier" @click="closeInfo" @scroll.stop>
    <div class="moveRight">
      <div class="tab" @click.stop>
        <div
          :class="['flex', tabIndex == 0 ? 'active' : '']"
          @click.stop="toggleIndex(0)"
        >
          用户信息
        </div>
        <div
          v-if="isXy"
          :class="['flex', tabIndex == 1 ? 'active' : '']"
          @click.stop="toggleIndex(1)"
        >
          全景雷达
        </div>
      </div>
      <div class="info" @click.stop>
        <div class="info-content">
          <div class="info-content-header">
            <div class="info-content-avaer">
              <img :src="require(`@/assets/images/user-icon.png`)" alt="" />
              <span>{{ pushUser.name }}</span>
            </div>
            <div class="info-content-submit" @click="handleTransfer">
              移交线索
            </div>
          </div>
          <div class="info-content-desc">
            <div>
              <span>需求资金</span>
              <span>{{ pushUser.demandAmount || 0 }}</span>
            </div>
            <div
              style="
                background: linear-gradient(50deg, #fdd771 0%, #fbb342 100%);
              "
            >
              <span>意向产品</span>
              <span class="info-content-product">{{
                pushUser.productName || "-"
              }}</span>
            </div>
            <div
              style="
                background: linear-gradient(50deg, #b8c3f9 0%, #8a97f3 100%);
              "
            >
              <span>客户需求</span>
              <span>-</span>
            </div>
            <div
              style="
                background: linear-gradient(50deg, #ddbde0 0%, #d87fe0 100%);
              "
            >
              <span>客户等级</span>
              <span>-</span>
            </div>
            <div
              style="
                background: linear-gradient(50deg, #ffc4a2 0%, #ff9f68 100%);
              "
            >
              <span>客户来源</span>
              <span>-</span>
            </div>
          </div>
        </div>
        <div class="info-content-clue" v-if="tabIndex == 0">
          <div class="left">
            <div class="title">线索信息</div>
            <div class="base">
              <div class="base-list">
                <div class="base-item">
                  <span>姓名</span>
                  <div class="flex">{{ pushUser.name }}</div>
                </div>
                <div class="base-item" @click="handleUserPhone">
                  <span>电话</span>
                  <div class="flex" style="cursor: pointer">
                    {{ isEve ? phone : pushUser.phone }}
                    <img
                      v-if="!isEve"
                      style="margin-left: 0.12rem"
                      :src="require(`@/assets/images/yc.png`)"
                      alt=""
                    />
                    <img
                      v-if="isEve"
                      style="margin-left: 0.12rem"
                      :src="require(`@/assets/images/xs.png`)"
                      alt=""
                    />
                  </div>
                </div>
                <div class="base-item">
                  <span>年龄</span>
                  <div class="flex">{{ pushUser.age || "-" }}</div>
                </div>
                <div class="base-item">
                  <span>性别</span>
                  <div class="flex">{{ pushUser.sex || "-" }}</div>
                </div>
                <div class="base-item">
                  <span>职业</span>
                  <div class="flex">{{ pushUser.vocationMessage || "-" }}</div>
                </div>
              </div>
              <div class="more">
                <div class="more-list">
                  <div class="more-item">
                    <div>
                      <img
                        :src="require(`@/assets/images/address-icon.png`)"
                        alt=""
                      />
                      <span>地区</span>
                    </div>
                    <div>{{ pushUser.city || "-" }}</div>
                  </div>
                  <div class="more-item" style="background: #e9fcf0">
                    <div>
                      <img
                        :src="require(`@/assets/images/school-icon.png`)"
                        alt=""
                      />

                      <span>教育程度</span>
                    </div>
                    <div>{{ pushUser.educationMessage || "-" }}</div>
                  </div>
                </div>
                <div class="more-list">
                  <div class="more-item" style="background: #fddede">
                    <div class="">
                      <img
                        :src="require(`@/assets/images/time-iocn.png`)"
                        alt=""
                      />
                      <span>贷款期限</span>
                    </div>
                    <div>-</div>
                  </div>
                  <div class="more-item" style="background: #fbf2e4">
                    <div>
                      <img
                        :src="require(`@/assets/images/use-icon.png`)"
                        alt=""
                      />
                      <span>贷款目的</span>
                    </div>
                    <div>-</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="qualifications">
              <div class="qualifications-title">资产信息</div>

              <div class="qualifications-content">
                <div class="qualifications-list">
                  <div class="qualifications-item flex">
                    <span>社保缴纳</span>
                    <div class="flex">{{ pushUser.socialMessage || "-" }}</div>
                  </div>
                  <div class="qualifications-item flex">
                    <span>商业保险</span>
                    <div class="flex">{{ pushUser.insureMessage || "-" }}</div>
                  </div>
                  <div class="qualifications-item flex">
                    <span>房产信息</span>
                    <div class="flex">{{ pushUser.houseMessage || "-" }}</div>
                  </div>
                  <div class="qualifications-item flex">
                    <span>车辆价值</span>
                    <div class="flex">-</div>
                  </div>
                  <div class="qualifications-item flex">
                    <span>车辆是否本人名下</span>
                    <div class="flex">-</div>
                  </div>
                  <div class="qualifications-item flex">
                    <span>微粒贷额度</span>
                    <div class="flex">
                      {{ pushUser.particleMessage || "-" }}
                    </div>
                  </div>
                  <div class="qualifications-item flex">
                    <span>花呗额度</span>
                    <div class="flex">{{ pushUser.tokioMessage || "-" }}</div>
                  </div>
                </div>
                <div class="qualifications-list">
                  <div class="qualifications-item">
                    <span>公积金缴纳</span>
                    <div class="flex">
                      {{ pushUser.providentMessage || "-" }}
                    </div>
                  </div>
                  <div class="qualifications-item">
                    <span>逾期记录</span>
                    <div class="flex">{{ pushUser.overdueMessage || "-" }}</div>
                  </div>
                  <div class="qualifications-item">
                    <span>车产信息</span>
                    <div class="flex">{{ pushUser.vehicleMessage || "-" }}</div>
                  </div>
                  <div class="qualifications-item">
                    <span>车辆年限</span>
                    <div class="flex">-</div>
                  </div>
                  <div class="qualifications-item">
                    <span>信用卡额度</span>
                    <div class="flex">{{ pushUser.creditMessage || "-" }}</div>
                  </div>
                  <div class="qualifications-item">
                    <span>京东白条额度</span>
                    <div class="flex">{{ pushUser.jdwhiteMessage || "-" }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="right">
            <div class="right-add">
              <div class="title2">跟进记录</div>
              <div
                class="submit flex"
                v-hasPermi="['partyaAdmin:moreAdvancedRecord:list']"
                @click="handleAddFollow"
              >
                添加跟进记录
              </div>
            </div>

            <div class="right-move" v-if="moreAdvancedList.length">
              <div
                class="right-list"
                v-for="(item, index) in moreAdvancedList"
                :key="index"
              >
                <div class="right-time">{{ item.createTime }}</div>
                <div class="right-desc">
                  {{ item.content }}
                </div>
                <div class="right-remark" v-if="item.remark">
                  备注:{{ item.remark }}
                </div>
              </div>
            </div>

            <div style="text-align: center; margin-top: 20px" v-else>
              暂无跟进记录~~~
            </div>
          </div>
        </div>

        <div class="info-content-clue" v-if="tabIndex == 1">
          <div style="width: 100%">
            <div class="behavior">
              <div class="behavior-left">
                <!-- <div class="tilte">贷款行为分</div>
              <div class="score flex">{{ info.b22170001 || "-" }}</div> -->
                <div class="flex1">
                  <img :src="require(`@/assets/images/icon-7.png`)" alt="" />
                  <span>贷款行为分</span>
                  <span>{{ info.b22170001 || "-" }}</span>
                </div>
                <div class="flex1">
                  <img :src="require(`@/assets/images/icon-1.png`)" alt="" />
                  <span>最后一次贷款放贷时间</span>
                  <span>{{ info.b22170054 || "-" }}</span>
                </div>
                <div class="flex1">
                  <img :src="require(`@/assets/images/icon-2.png`)" alt="" />
                  <span>最后一次履约距今天数</span>
                  <span> {{ info.b22170050 || "-" }}</span>
                </div>
                <div class="flex1">
                  <img :src="require(`@/assets/images/icon-3.png`)" alt="" />
                  <span>正常还款订单比例</span>
                  <span> {{ info.b22170034 || "-" }}</span>
                </div>
                <div class="flex1">
                  <img :src="require(`@/assets/images/icon-4.png`)" alt="" />
                  <span>信用贷款时长</span>
                  <span> {{ info.b22170053 || "-" }}</span>
                </div>
                <div class="flex1">
                  <img :src="require(`@/assets/images/icon-5.png`)" alt="" />
                  <span>贷款已结清订单数</span>
                  <span> {{ info.b22170052 || "-" }}</span>
                </div>
              </div>
              <div class="behavior-right">
                <div class="behavior-right-left">
                  <div class="title1">近十二个月的贷款笔数</div>
                  <div class="echarts" ref="echarts"></div>
                </div>
                <div class="behavior-right-list">
                  <div class="content">
                    <div class="content-title">总结</div>
                    <div>
                      <span>1k及以下</span>
                      <span>{{ info.b22170012 || 0 }}次</span>
                    </div>
                    <div>
                      <span>1k-3k</span>
                      <span>{{ info.b22170013 || 0 }}次</span>
                    </div>
                    <div>
                      <span>3k-10k</span>
                      <span>{{ info.b22170014 || 0 }}次</span>
                    </div>
                    <div>
                      <span>1w以上</span>
                      <span>{{ info.b22170015 || 0 }}次</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="moreInfo">
              <div class="moreInfo-title">
                <div>行为时间</div>
                <div>贷款笔数</div>
                <div>贷款机构</div>
                <div>贷款金额</div>
                <div>失败扣款笔数</div>
                <div>履约贷款总金额</div>
                <div>履约贷款次数</div>
              </div>
              <div class="moreInfo-item">
                <div>近1个月</div>
                <div>{{ info.b22170002 || 0 }}</div>
                <div>{{ info.b22170007 || 0 }}</div>
                <div>{{ info.b22170016 || 0 }}</div>
                <div>{{ info.b22170035 || 0 }}</div>
                <div>{{ info.b22170040 || 0 }}</div>
                <div>{{ info.b22170045 || 0 }}</div>
              </div>
              <div class="moreInfo-item">
                <div>近3个月</div>
                <div>{{ info.b22170003 || 0 }}</div>
                <div>{{ info.b22170008 || 0 }}</div>
                <div>{{ info.b22170017 || 0 }}</div>
                <div>{{ info.b22170036 || 0 }}</div>
                <div>{{ info.b22170041 || 0 }}</div>
                <div>{{ info.b22170046 || 0 }}</div>
              </div>
              <div class="moreInfo-item">
                <div>近6个月</div>
                <div>{{ info.b22170004 || 0 }}</div>
                <div>{{ info.b22170009 || 0 }}</div>
                <div>{{ info.b22170018 || 0 }}</div>
                <div>{{ info.b22170037 || 0 }}</div>
                <div>{{ info.b22170042 || 0 }}</div>
                <div>{{ info.b22170047 || 0 }}</div>
              </div>
              <div class="moreInfo-item">
                <div>近12个月</div>
                <div>{{ info.b22170005 || 0 }}</div>
                <div>{{ info.b22170010 || 0 }}</div>
                <div>{{ info.b22170019 || 0 }}</div>
                <div>{{ info.b22170038 || 0 }}</div>
                <div>{{ info.b22170043 || 0 }}</div>
                <div>{{ info.b22170048 || 0 }}</div>
              </div>
              <div class="moreInfo-item">
                <div>近24个月</div>
                <div>{{ info.b22170006 || 0 }}</div>
                <div>{{ info.b22170011 || 0 }}</div>
                <div>{{ info.b22170020 || 0 }}</div>
                <div>{{ info.b22170040 || 0 }}</div>
                <div>{{ info.b22170044 || 0 }}</div>
                <div>{{ info.b22170049 || 0 }}</div>
              </div>
            </div>
            <div class="descInfo">
              <div class="item">
                <span>近6个月M0+逾期贷款笔数</span>
                <span class="flex"> {{ info.b22170025 || 0 }}</span>
              </div>
              <div class="item">
                <span>近6个月M1+逾期贷款笔数</span>
                <span class="flex"> {{ info.b22170028 || 0 }}</span>
              </div>
              <div class="item">
                <span>近6个月累计逾期金额</span>
                <span class="flex"> {{ info.b22170031 || 0 }}</span>
              </div>
              <div class="item">
                <span>近12个月M0+逾期贷款笔数</span>
                <span class="flex">{{ info.b22170026 || 0 }}</span>
              </div>
              <div class="item">
                <span>近12个月M1+逾期贷款笔数</span>
                <span class="flex"> {{ info.b22170029 || 0 }}</span>
              </div>
              <div class="item">
                <span>近12个月累计逾期金额</span>
                <span class="flex"> {{ info.b22170032 || 0 }}</span>
              </div>
              <div class="item">
                <span>近24个月M0+逾期贷款笔数</span>
                <span class="flex"> {{ info.b22170027 || 0 }}</span>
              </div>
              <div class="item">
                <span>近24个月M1+逾期贷款笔数</span>
                <span class="flex"> {{ info.b22170029 || 0 }}</span>
              </div>
              <div class="item">
                <span>近24个月累计逾期金额</span>
                <span class="flex"> {{ info.b22170033 || 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      title="添加跟进记录"
      :visible.sync="userAvisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="150px"
      >
        <!-- <el-form-item label="手机号">
          <el-input v-model="phone" placeholder="手机号" />
        </el-form-item> -->
        <el-form-item label="用户状态" prop="status">
          <el-select
            v-model="formData.status"
            placeholder="状态"
            clearable
            size="small"
          >
            <el-option value="0" label="未联系"></el-option>
            <el-option value="1" label="感兴趣"></el-option>
            <el-option value="2" label="不感兴趣"></el-option>
            <el-option value="3" label="未接通"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="formData.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="移交线索"
      :visible.sync="transferAvisible"
      @close="transferCancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="transferData"
        :model="transferData"
        :rules="transferRules"
        label-width="150px"
      >
        <el-form-item label="移交至" prop="userId">
          <el-select
            v-model="transferData.userId"
            placeholder="请选择移交至"
            clearable
            filterable
            size="small"
          >
            <el-option
              v-for="i in userList"
              :value="i.userId"
              :key="i.userId"
              :label="i.userName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="transferData.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="transferSubmit">确 定</el-button>
        <el-button @click="transferCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTransferUserList,
  getUserPhone,
  editUserPhoneStatus,
  addMoreAdvancedRecord,
  getMoreAdvancedRecordList,
} from "@/api/partyManage";
import * as echarts from "echarts";
export default {
  props: ["infoShow", "pushUser", "info", "id", "isXy"],
  data() {
    return {
      callAvisiable: false,
      userAvisible: false,
      transferAvisible: false,
      isEve: false,
      tabIndex: 0,
      phone: "",
      moreAdvancedList: [],
      userList: [],
      formData: {
        status: "",
        remark: "",
        id: "",
      },
      transferData: {
        userId: "",
        partyFirstPushId: "",
        remark: "",
      },
      transferRules: {
        userId: [
          {
            required: true,
            message: "请选择移交至",
            trigger: "blur",
          },
        ],
      },
      rules: {
        status: [
          {
            required: true,
            message: "请选择状态",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    closeInfo() {
      this.infoShow.isShow = false;
    },
    toggleIndex(e) {
      console.log(this.info);
      this.tabIndex = e;
      let that = this;
      if (e == 1) {
        setTimeout(function () {
          var myChart = echarts.init(that.$refs.echarts);
          // 指定配置和数据
          var option = {
            color: ["#4B85FF"],
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
              },
            },

            grid: {
              left: "3%",
              top: "7%",
              right: "0%",
              bottom: "4%",
              containLabel: true,
            },
            xAxis: [
              {
                type: "category",
                data: ["1k及以下", "1k-3k", "3k-10K", "1W以上"],
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  textStyle: {
                    color: "#333333",
                    fontSize: "12",
                  },
                  align: "center",
                  //  margin:20,
                  // // showMaxLabel:true,
                  // rotate:15
                },
                axisLine: {
                  show: false,
                },
              },
            ],
            yAxis: [
              {
                type: "value",
                // minInterval:1,
                axisLabel: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  lineStyle: {
                    color: ["#cccc"],
                    type: "dashed",
                  },
                },
              },
            ],

            series: [
              {
                name: "贷款笔数",
                type: "bar",
                barWidth: "35%",

                itemStyle: {
                  barBorderRadius: 5,
                },
              },
            ],
          };

          option.series[0].data = [
            that.info.b22170012 || 0,
            that.info.b22170013 || 0,
            that.info.b22170014 || 0,
            that.info.b22170015 || 0,
          ];
          // 把配置给实例对象
          myChart.setOption(option);
          window.addEventListener("resize", function () {
            myChart.resize();
          });
        }, 0);


      }
    },
    handleTransfer() {
      getTransferUserList({ partyFirstPushId: this.id }).then((res) => {
        this.userList = res.data;
        this.transferAvisible = true;
      });
    },
    //确认移交
    transferSubmit() {
      this.$refs.transferData.validate((valid) => {
        if (valid) {
          this.transferData.partyFirstPushId = this.id;
          addMoreAdvancedRecord(this.transferData).then((res) => {
            if (res.code == 200) {
              this.$message.success("转接成功");
              this.transferCancel();
              this.infoShow.isShow = false;
              this.$parent.getList();
            }
          });
        }
      });
    },
    //添加跟进记录
    handleAddFollow() {
      getUserPhone(this.pushUser.id).then((res) => {
        this.phone = res.data.phone;
        this.formData.id = res.data.id;
        this.userAvisible = true;
      });
    },
    handleUserPhone() {
      if (!this.isEve && !this.phone) {
        getUserPhone(this.pushUser.id).then((res) => {
          this.phone = res.data.phone;
          this.isEve = true;
        });
      } else {
        this.isEve = !this.isEve;
      }
    },
    getList() {
      getMoreAdvancedRecordList({ partyFirstPushId: this.id }).then((res) => {
        this.moreAdvancedList = res.data;
      });
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          editUserPhoneStatus(this.formData).then((res) => {
            if (res.code == 200) {
              this.$message.success("状态更新成功");
              this.$parent.getInfo();
              this.$parent.getList();
              this.getList();
              this.cancel();
            }
          });
        }
      });
    },
    cancel() {
      this.formData = {
        status: "",
        remark: "",
        id: "",
      };
      this.userAvisible = false;
      this.$refs.formData.resetFields();
    },
    transferCancel() {
      this.transferData = {
        userId: "",
        partyFirstPushId: "",
        remark: "",
      };
      this.transferAvisible = false;
      this.$refs.transferData.resetFields();
    },
  },
  created() {
    (function flexible(window, document) {
      var docEl = document.documentElement;
      var dpr = window.devicePixelRatio || 1;

      // adjust body font size
      function setBodyFontSize() {
        if (document.body) {
          document.body.style.fontSize = 12 * dpr + "px";
        } else {
          document.addEventListener("DOMContentLoaded", setBodyFontSize);
        }
      }
      setBodyFontSize();

      // set 1rem = viewWidth / 10
      function setRemUnit() {
        var rem = docEl.clientWidth / 24;
        docEl.style.fontSize = rem + "px";
      }

      setRemUnit();

      // reset rem unit on page resize
      window.addEventListener("resize", setRemUnit);
      window.addEventListener("pageshow", function (e) {
        if (e.persisted) {
          setRemUnit();
        }
      });

      // detect 0.5px supports
      if (dpr >= 2) {
        var fakeBody = document.createElement("body");
        var testElement = document.createElement("div");
        testElement.style.border = ".5px solid transparent";
        fakeBody.appendChild(testElement);
        docEl.appendChild(fakeBody);
        if (testElement.offsetHeight === 1) {
          docEl.classList.add("hairlines");
        }
        docEl.removeChild(fakeBody);
      }
    })(window, document);
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.contanier {
  width: 100vw;
  min-height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  font-family: Microsoft YaHei;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
  display: flex;
  justify-content: flex-end;
  padding-top: 83px;
  overflow-y: auto;
  .moveRight {
    display: flex;
    right: -100%;
    animation: moveTop 0.2s ease;
    animation-fill-mode: forwards;
    position: absolute;
  }
  .tab {
    display: flex;
    flex-direction: column;
    margin-top: 2.3rem;
    div {
      width: 1.64rem;
      height: 0.6rem;
      background: #ffffff;
      margin-bottom: 0.2rem;
      color: #707070;
      font-size: 0.24rem;
      font-weight: 500;
      cursor: pointer;
      border-radius: 8px 0px 0px 8px;
    }
    .active {
      background-color: #4b85ff;
      color: #fff;
    }
  }
  .info {
    // position: absolute;
    width: 11.8rem;
    &-content {
      padding: 0.3rem;
      background-color: #fff;
      z-index: 100;
      &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      &-submit {
        width: 1.3rem;
        height: 0.4rem;
        background: #4b85ff;
        text-align: center;
        line-height: 0.4rem;
        color: #fff;
        border-radius: 0.04rem;
        font-size: 0.12rem;
        cursor: pointer;
      }
      &-avaer {
        img {
          width: 0.42rem;
          height: 0.42rem;
          vertical-align: middle;
        }
        span {
          margin-left: 10px;
          font-size: 0.18rem;
          color: #666;
        }
      }
      &-product {
        font-size: 0.18rem;
        padding: 0 0.1rem;
      }
      &-desc {
        display: flex;
        margin-top: 0.15rem;
        margin-bottom: 0.2rem;
        div {
          margin-right: 0.4rem;
          width: 1.7rem;
          height: 0.9rem;
          background: linear-gradient(50deg, #6ddfe2 0%, #46c6ca 100%);
          opacity: 1;
          border-radius: 4px;
          display: flex;
          justify-content: space-around;
          flex-direction: column;
          align-items: center;
          font-size: 0.2rem;
          color: #fff;
        }
      }
      &-clue {
        background: #fff;
        box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
        padding: 0.3rem;
        z-index: 100;
        display: flex;

        // width: 11.8rem;
        .left {
          flex: 0.6;
          border-right: 1px solid #ececec;
        }
        .right {
          flex: 0.4;
          padding: 0 0.2rem;
          font-size: 0.14rem;
          line-height: 1.5;
          cursor: pointer;
          .submit {
            background: #4b85ff;
            border-radius: 4px;
            color: #fff;
            font-size: 0.14rem;
            width: 1.5rem;
            height: 0.4rem;
          }
          &-add {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          &-move {
            overflow: auto;
            height: 7rem;
            &::-webkit-scrollbar-thumb {
              /*滚动条里面小方块*/
              width: 20px;
              height: 10px;
              border-radius: 5px;
              box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
              background: #eff2f9;
            }
            &::-webkit-scrollbar-track {
              /*滚动条里面轨道*/
              box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
              border-radius: 0;
              background-color: #f3f3f3;
            }
            &::-webkit-scrollbar {
              /*滚动条整体样式*/
              width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
              background-color: #f3f3f3;
            }
          }
          &-time {
            color: #999;
            margin-top: 0.2rem;
          }
          &-desc {
            color: #333333;
            word-break: break-all;
            font-size: 0.16rem;
          }
          &-remark {
            color: #4b85ff;
            word-break: break-all;
          }
          .title2 {
            color: #333;
            font-size: 0.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;

            &::before {
              content: "";
              display: block;
              width: 0.04rem;
              height: 0.2rem;
              background: #4b85ff;
              margin-right: 0.1rem;
            }
          }
        }
        .base {
          display: flex;
          margin-top: 0.2rem;
          &-list {
            flex: 3.5;
          }
          &-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-right: 10px;
            &:not(:nth-of-type(5)) {
              margin-bottom: 0.2rem;
            }
            span {
              color: #333;
              font-size: 0.2rem;
            }
            div {
              width: 1.6rem;
              height: 0.36rem;
              font-size: 0.16rem;
              background: #eff2f9;
              border-radius: 0.05rem;
            }
          }

          .more {
            flex: 6.5;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            &-list {
              display: flex;
              justify-content: space-between;
              padding-right: 0.3rem;
            }
            &-item {
              width: 1.88rem;
              height: 1.1rem;
              border-radius: 4px;
              background: #d4e9fe;
              display: flex;
              justify-content: space-around;
              flex-direction: column;
              font-size: 0.22rem;
              & div:nth-of-type(2) {
                padding-left: 0.16rem;
                font-size: 0.2rem;

                color: #333333;
              }
              img {
                width: 0.5rem;
                height: 0.5rem;
                vertical-align: middle;
                margin: 0 0.16rem;
              }
            }
          }
        }
        .title {
          color: #333;
          font-size: 0.2rem;
          font-weight: 600;
          display: flex;
          align-items: center;
          &::before {
            content: "";
            display: block;
            width: 0.04rem;
            height: 0.2rem;
            background: #4b85ff;
            margin-right: 0.1rem;
          }
        }
        .behavior {
          width: 100%;
          display: flex;
          justify-content: space-between;
          &-left {
            width: 3.6rem;
            height: 3.1rem;
            background: #ffffff;
            margin-right: 0.3rem;
            box-shadow: 0px 3px 6px rgba(153, 153, 153, 0.16);
            opacity: 1;
            border-radius: 0.08rem;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            // .tilte {
            //   color: #333333;
            //   font-size: 0.2rem;
            // }
            // .score {
            //   margin-top: 0.2rem;
            //   width: 3.1rem;
            //   height: 2.1rem;
            //   font-weight: 600;
            //   color: #4b85ff;
            //   font-size: 0.26rem;
            //   background: url("../../../assets/images/user-info.png") no-repeat;
            //   background-size: 100%;
            // }
            img {
              width: 0.36rem;
              height: 0.36rem;
            }

            .flex1 {
              display: flex;
              justify-content: space-between;
              padding: 0 0.24rem;
              & :nth-child(3) {
                font-weight: 600;
                color: #4b85ff;
              }
            }
          }
          &-right {
            width: 6.7rem;
            height: 3.1rem;
            background: #ffffff;
            box-shadow: 0px 3px 6px rgba(153, 153, 153, 0.16);
            opacity: 1;
            border-radius: 0.08rem;
            display: flex;
            justify-content: space-between;
            &-left {
              width: 4rem;
              height: 3rem;
              display: flex;
              flex-direction: column;
            }
            .title1 {
              text-align: center;
              margin: 0.1rem 0;
              font-size: 0.16rem;
            }
            .echarts {
              flex: 1;
            }
            &-list {
              height: 100%;
              width: 2.3rem;
              padding: 0.2rem 0;
              margin-right: 0.2rem;

              .content {
                height: 100%;
                background: #f8f8fc;
                border-radius: 0.08rem;
                display: flex;
                flex-direction: column;
                justify-content: space-around;

                div {
                  font-size: 0.18rem;
                  color: #666666;
                  padding: 0 0.2rem;
                  display: flex;
                  justify-content: space-between;
                  & :nth-child(2) {
                    font-weight: 600;
                    color: #666;
                  }
                }
              }
            }
          }
        }
        .moreInfo {
          width: 100%;
          margin-top: 0.2rem;
          height: 3rem;
          background: #ffffff;
          box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
          opacity: 1;
          border-radius: 0.08rem;
          padding-bottom: 0.2rem;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          &-title {
            display: flex;
            justify-content: space-around;
            div {
              width: 1.3rem;
              text-align: center;
              font-size: 0.18rem;
              color: #666666;
              height: 0.36rem;
              line-height: 0.36rem;
            }
          }
          &-item {
            display: flex;
            justify-content: space-around;
            div {
              width: 1.3rem;
              text-align: center;
              font-size: 0.18rem;
              height: 0.36rem;
              background: #f8f9fb;
              opacity: 1;
              border-radius: 4px;
              color: #4b85ff;
              line-height: 0.36rem;
            }
          }
        }
        .descInfo {
          margin-top: 0.2rem;
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          .item {
            width: 33.33333333333%;
            display: flex;
            align-items: center;
            margin-bottom: 0.1rem;
            & :nth-child(1) {
              font-size: 0.16rem;
              line-height: 0px;
              color: #666666;
            }
            & :nth-child(2) {
              font-size: 0.16rem;
              flex: 1;
              line-height: 0px;
              color: #4b85ff;
              height: 0.38rem;
              background: #eff2f9;
              margin: 0 0.14rem;
              border-radius: 4px;
            }
          }
        }
      }
    }
    .qualifications {
      margin: 0.2rem 0;
      &-title {
        color: #333;
        font-size: 0.2rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        margin-bottom: 0.2rem;
        &::before {
          content: "";
          display: block;
          width: 0.04rem;
          height: 0.2rem;
          background: #4b85ff;
          margin-right: 0.1rem;
        }
      }
      &-content {
        display: flex;
        justify-content: space-between;
      }
      &-list {
        flex: 1;
      }
      &-item {
        display: flex;
        align-items: center;
        font-size: 0.2rem;
        margin-bottom: 0.16rem;

        span {
          color: #666666;
        }
        div {
          flex: 1;
          margin-right: 0.24rem;
          margin-left: 0.1rem;
          height: 0.38rem;
          color: #333333;
          background: #eff2f9;
          border-radius: 0.04rem;
        }
      }
    }
  }
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
@keyframes moveTop {
  0% {
    right: -100%;
  }

  100% {
    // opacity: 1;
    right: 0;
  }
}
</style>
