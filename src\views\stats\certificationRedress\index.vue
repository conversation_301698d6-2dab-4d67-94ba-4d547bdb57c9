<template>
  <div class="app-container">
    <filter-form @query="handleQuery" @reset="handleReset" ref="filterForm" />

    <!-- 统计信息展示区 -->
    <stats-summary :stats="currentStats" />

    <!-- 统计Tab -->
    <stats-tabs 
      ref="statsTabs"
      @tab-change="handleTabChange"
      @type-change="handleTypeChange"
    />

    <!-- 图表容器 -->
    <stats-chart ref="statsChart" />

  </div>
</template>

<script>
import { getCertificationRedressStats } from '@/api/stats/certificationRedress';
import _ from 'lodash';
import FilterForm from './components/FilterForm.vue';
import StatsSummary from './components/StatsSummary.vue';
import StatsTabs from './components/StatsTabs.vue';
import StatsChart from './components/StatsChart.vue';

export default {
  name: 'CertificationRedressStats',
  components: {
    FilterForm,
    StatsSummary,
    StatsTabs,
    StatsChart
  },
  data() {
    return {
      currentStats: null // 当前显示的统计数据
    };
  },
  mounted() {
    this.fetchAndRenderCurrentTab();
  },
  methods: {
    fetchAndRenderCurrentTab() {
      const type = this.$refs.statsTabs.getCurrentType();
      this.fetchAndRenderChart(type);
    },
    fetchAndRenderChart: _.debounce(function(type) {
      const queryParams = this.$refs.filterForm.getQueryParams();
      if (!queryParams.channelId) {
        this.$message.warning('请选择渠道');
        return;
      }

      const params = { ...queryParams, type };
      getCertificationRedressStats(params).then(response => {
        const data = response.data || [];
        
        // 只保存当前统计数据
        if (response.extend) {
          this.currentStats = response.extend;
        }
        
        this.$refs.statsChart.renderChart(data);
      }).catch(() => {
        this.$refs.statsChart.clearChart();
      });
    }, 200),
    handleTabChange() {
      this.$nextTick(() => {
        this.fetchAndRenderCurrentTab();
      });
    },
    handleTypeChange(type) {
      this.fetchAndRenderChart(type);
    },
    handleQuery() {
      this.fetchAndRenderCurrentTab();
    },
    handleReset() {
      this.$refs.statsTabs.reset();
      this.fetchAndRenderCurrentTab();
    }
  }
};
</script>

<style scoped>

</style>