<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="queryParams" ref="queryForm" class="demo-form-inline">
      <el-form-item label="短信平台账号" prop="smsAccountId">
        <el-select
          v-model="queryParams.smsAccountId"
          filterable
          remote
          :remote-method="remoteSearch"
          :loading="selectLoading"
          clearable
          placeholder="请选择短信平台账号"
          style="width: 300px"
          @change="handleQuery"
        >
          <el-option
            v-for="item in accountOptions"
            :key="item.accountId"
            :label="item.accountName"
            :value="item.accountId">
            <span>{{ item.accountName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">ID: {{ item.accountId }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发送状态" prop="sendStatus">
        <el-select v-model="queryParams.sendStatus" placeholder="请选择发送状态" clearable @change="handleQuery">
          <el-option label="未完成" :value="0"></el-option>
          <el-option label="发送成功" :value="1"></el-option>
          <el-option label="发送失败" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发送时间" prop="searchDate">
        <el-date-picker
          @change="handleQuery"
          v-model="queryParams.searchDate"
          type="date"
          placeholder="选择发送时间"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column
        prop="sendNo"
        label="短信发送批次号"
        align="center">
      </el-table-column>
      <el-table-column
        prop="transactionId"
        label="事务id"
        align="center">
      </el-table-column>
      <el-table-column
        prop="accountName"
        label="短信平台账号名称"
        align="center">
      </el-table-column>
      <el-table-column
        prop="sendStatus"
        label="发送状态"
        align="center">
        <template slot-scope="scope">
          <el-tag size="mini" :type="scope.row.sendStatus == 0 ? 'info' : scope.row.sendStatus == 1 ? 'success' : 'danger'">
            {{ scope.row.sendStatus == 0 ? '未完成' : scope.row.sendStatus == 1 ? '发送成功' : '发送失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="successTotal"
        label="发送成功数量"
        align="center">
        <template slot-scope="scope">
          <span class="clickable-cell success-cell" @click="handleRowClick(scope.row)">{{ scope.row.successTotal }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="failTotal"
        label="发送失败数量"
        align="center">
        <template slot-scope="scope">
          <span class="clickable-cell fail-cell" @click="handleRowClick(scope.row)">{{ scope.row.failTotal }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="sendTotal"
        label="发送总数量"
        align="center">
        <template slot-scope="scope">
          <span class="clickable-cell total-cell" @click="handleRowClick(scope.row)">{{ scope.row.sendTotal }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination">
    </el-pagination>
  </div>
</template>

<script>
import { getSmsReports, getSmsAccountList } from '@/api/openSmsManage'
import dayjs from 'dayjs'

export default {
  name: 'SmsReport',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 下拉框加载状态
      selectLoading: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 账号选项
      accountOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        smsAccountId: undefined,
        sendStatus: undefined,
        searchDate: dayjs().format('YYYY-MM-DD') // 使用 dayjs 设置默认日期
      }
    }
  },
  created() {
    // 等待下一个 tick，确保表单已经挂载
    this.$nextTick(() => {
      this.handleQuery()
    })
  },
  methods: {
    /** 远程搜索 */
    remoteSearch(query) {
      if (query !== '') {
        this.selectLoading = true
        getSmsAccountList({
          accountName: query
        }).then(res => {
          this.accountOptions = res.data || []
          this.selectLoading = false
        })
      } else {
        this.accountOptions = []
      }
    },
    /** 查询列表 */
    getList() {
      if (!this.queryParams.searchDate) {
        this.$message.warning('请选择发送时间')
        return
      }
      const params = {
        ...this.queryParams
      }
      getSmsReports(params).then(res => {
        this.tableData = res.rows || []
        this.total = res.total
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (!this.queryParams.searchDate) {
        this.$message.warning('请选择发送时间')
        return
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryForm.resetFields()
      // 重置时保持默认日期
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        smsAccountId: undefined,
        sendStatus: undefined,
        searchDate: dayjs().format('YYYY-MM-DD') // 使用 dayjs 设置默认日期
      }
      this.handleQuery()
    },
    /** 分页大小改变 */
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    /** 分页页码改变 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    /** 表格行点击事件 */
    handleRowClick(row) {
      // 处理日期，将单个日期转换为开始和结束时间
      const date = this.queryParams.searchDate
      const startDateTime = dayjs(date).startOf('day').format('YYYY-MM-DD 00:00:00')
      const endDateTime = dayjs(date).endOf('day').format('YYYY-MM-DD 23:59:59')

      window.sessionStorage.setItem('pageData_smsReportDetail', JSON.stringify({
        sendNo: row.sendNo,
        searchStartDateTime: startDateTime,
        searchEndDateTime: endDateTime
      }))

      this.$router.push('/openSmsManage/smsReportDetail')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
  .clickable-cell {
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
  .success-cell {
    color: #67C23A;  // element-ui 的成功色
  }
  .fail-cell {
    color: #F56C6C;  // element-ui 的危险色
  }
  .total-cell {
    color: #409EFF;  // element-ui 的主题色
  }
}
</style> 