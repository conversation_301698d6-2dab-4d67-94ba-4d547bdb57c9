<template>
    <div class="app-container">
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="success" icon="el-icon-refresh" size="mini" @click="getList()">刷新数据</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="el-icon-notebook-2" size="mini"
                    @click="handleViewRecordList">发放记录</el-button>
            </el-col>
        </el-row>
        <el-table v-loading="loading" :data="flowPacketList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="ID" align="center" prop="id" />
            <el-table-column label="流量包名称" align="center" prop="name" />
            <el-table-column label="类型" align="center" prop="type">
                <template slot-scope="scope">
                    <span>{{ typeFormat(scope.row) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="加速权重值" align="center" prop="weight" />
            <el-table-column label="加速UV数" align="center" prop="uvNum" />
            <el-table-column label="购买价格" align="center" prop="price">
                <template slot-scope="scope">
                    <span>{{ scope.row.price ? scope.row.price.toFixed(2) : '0.00' }}</span>
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="status">
                <template slot-scope="scope">
                    <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="2"
                        @change="handleStatusChange(scope.row)"></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit"
                        @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-check"
                        @click="handleGenerate(scope.row)">生成流量包</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 流量包发放记录弹窗 -->
        <usage-record-dialog :visible.sync="recordDialogVisible" :flow-packet-id="currentFlowPacketId"
            :flow-packet-name="currentFlowPacketName" />

        <!-- 添加或修改 流量 包对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="form-container-flow">
                <el-form-item label="流量包名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入流量包名称" style="width: 100%" />
                </el-form-item>
                <el-form-item label="类型" prop="type">
                    <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
                        <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label"
                            :value="parseInt(dict.value)" />
                    </el-select>
                </el-form-item>
                <el-form-item label="加速权重值" prop="weight">
                    <el-input-number v-model="form.weight" :precision="2" :step="0.1" :min="0" controls-position="right"
                        placeholder="请输入加速权重值" style="width: 100%" />
                </el-form-item>
                <el-form-item label="加速UV数" prop="uvNum">
                    <el-input-number v-model="form.uvNum" :min="0" controls-position="right" placeholder="请输入加速UV数"
                        style="width: 100%" />
                </el-form-item>
                <el-form-item label="购买价格" prop="price">
                    <el-input-number v-model="form.price" :precision="2" :step="0.01" :min="0.01" controls-position="right"
                        placeholder="请输入购买价格" style="width: 100%" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio :label="1">有效</el-radio>
                        <el-radio :label="2">无效</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    listFlowPacket,
    addFlowPacket,
    updateFlowPacket,
    updateFlowPacketStatus,
    generateFlowPacket
} from "@/api/platformProductManagement/flowPacketConfig";
import UsageRecordDialog from './components/UsageRecordDialog';

export default {
    name: "FlowPacketConfig",
    components: {
        UsageRecordDialog
    },
    data() {
        return {
            // 流量包发放记录弹窗是否显示
            recordDialogVisible: false,
            // 当前选中的流量包ID
            currentFlowPacketId: null,
            // 当前选中的流量包名称
            currentFlowPacketName: "",
            // 当前正在生成流量包的ID
            currentGeneratingId: null,
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 总条数
            total: 0,
            // 流量包表格数据
            flowPacketList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 状态数据字典
            statusOptions: [
                {
                    value: "1",
                    label: "有效"
                },
                {
                    value: "2",
                    label: "无效"
                }
            ],
            // 类型数据字典
            typeOptions: [
                {
                    value: "1",
                    label: "加速包"
                }
            ],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                name: undefined,
                status: undefined
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                name: [
                    { required: true, message: "流量包名称不能为空", trigger: "blur" }
                ],
                type: [
                    { required: true, message: "类型不能为空", trigger: "change" }
                ],
                weight: [
                    { required: true, message: "加速权重值不能为空", trigger: "blur" }
                ],
                uvNum: [
                    { required: true, message: "加速UV数不能为空", trigger: "blur" }
                ],
                price: [
                    { required: true, message: "购买价格不能为空", trigger: "blur" },
                    { type: 'number', min: 0.01, message: "价格必须大于0", trigger: "blur" }
                ],
                status: [
                    { required: true, message: "状态不能为空", trigger: "change" }
                ]
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        // 类型格式化
        typeFormat(row) {
            return row.type === 1 ? '加速包' : '';
        },
        // 获取流量包列表
        getList() {
            this.flowPacketList = [];
            this.loading = true;
            listFlowPacket(this.queryParams).then(response => {
                console.log('返回的数据', response);
                this.flowPacketList = response.data;
                this.total = response.data.length;
                this.loading = false;
            }).catch((error) => {
                this.flowPacketList = [];
                this.total = 0;
                this.loading = false;
                // 显示错误信息
                this.$message.error('刷新失败：' + (error.message || '未知错误'));
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: undefined,
                name: undefined,
                type: 1,
                weight: undefined,
                uvNum: undefined,
                price: undefined,
                status: 1
            };
            this.resetForm("form");
        },

        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        // 新增按钮操作
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加流量包";
        },
        // 修改按钮操作
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids;
            this.form = JSON.parse(JSON.stringify(row));
            this.open = true;
            this.title = "修改流量包";
        },
        // 生成流量包
        handleGenerate(row) {
            // 记录当前生成的流量包ID
            this.currentGeneratingId = row.id;
            
            // 检查流量包状态是否为有效状态
            if (row.status !== 1) {
                // 状态为无效，弹出一次确认框
                this.$confirm('流量包当前为无效状态，需要先启用才能生成，是否继续?', "提示", {
                    confirmButtonText: "继续",
                    cancelButtonText: "取消",
                    type: "warning",
                    customClass: "top-message-box"
                }).then(async () => {
                    try {
                        // 1. 先更新状态为有效
                        const updateResponse = await updateFlowPacketStatus({
                            id: row.id,
                            status: 1
                        });

                        if (updateResponse.code !== 200) {
                            this.$message.error(updateResponse.msg || "流量包状态更新失败");
                            return Promise.reject();
                        }

                        // 更新本地状态
                        row.status = 1;

                        // 2. 直接生成流量包，不再弹出确认框
                        const response = await generateFlowPacket(row.id);

                        if (response.code == 200) {
                            // 处理成功响应
                            this.handleGenerateSuccess(response);
                        } else {
                            this.$message.error(response.msg || "流量包生成失败");
                        }
                    } catch (error) {
                        if (error && error.message) {
                            this.$message.error("操作失败：" + error.message);
                        }
                    }
                }).catch(() => {
                    // 用户取消操作
                });
            } else {
                // 状态已经是有效，正常确认生成流程
                this.$confirm('确认生成该流量包?', "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                    customClass: "top-message-box"
                }).then(() => {
                    return generateFlowPacket(row.id);
                }).then(response => {
                    if (response.code == 200) {
                        this.handleGenerateSuccess(response);
                    } else {
                        this.$message.error(response.msg || "流量包生成失败");
                    }
                }).catch(error => {
                    if (error && error.message) {
                        this.$message.error("流量包生成失败：" + error.message);
                    }
                });
            }
        },

        // 处理生成流量包成功的响应
        handleGenerateSuccess(response) {
            // 只提取API返回的msg字段
            let successMsg = response.msg || "流量包生成成功";
            
            // 不再提取验证码
            const code = "";

            // 直接复制文本到剪贴板
            const copyToClipboard = (text) => {
                // 创建临时textarea元素
                const textarea = document.createElement('textarea');
                textarea.value = text;
                textarea.style.position = 'fixed'; // 避免滚动到底部
                document.body.appendChild(textarea);
                textarea.select();

                try {
                    // 执行复制命令
                    document.execCommand('copy');
                } catch (err) {
                    console.error('复制失败:', err);
                }

                // 移除临时元素
                document.body.removeChild(textarea);
            };

            // 准备对话框内容
            let dialogContent = `
                <div style="text-align: center; padding: 20px 0;">
                    <div style="margin-bottom: 15px;">
                        <i class="el-icon-success" style="color: #67C23A; font-size: 40px;"></i>
                    </div>
                    <div style="font-size: 16px; margin-bottom: 15px;">流量包生成成功！</div>
                    <div style="background-color: #f0f9eb; color: #67C23A; padding: 8px; 
                        border-radius: 4px; display: inline-block; font-size: 16px; margin-bottom: 15px;">
                        ${successMsg}
                    </div>
                </div>
            `;
            
            // 复制内容到剪贴板
            copyToClipboard(successMsg);
            
            // 添加复制按钮
            dialogContent += `
                <div style="text-align: center;">
                    <a href="javascript:;" class="copy-code-btn" 
                       style="color: #409EFF; text-decoration: none; font-size: 14px; display: inline-block;">
                        <i class="el-icon-document-copy"></i> 复制结果
                    </a>
                </div>
                </div>
            `;

            // 显示自定义对话框
            this.$msgbox({
                title: '流量包生成成功',
                message: dialogContent,
                showCancelButton: true,
                confirmButtonText: '再次生成流量包',
                cancelButtonText: '关闭',
                dangerouslyUseHTMLString: true,
                customClass: 'verify-code-dialog',
                
                beforeClose: (action, instance, done) => {
                    if (action === 'confirm') {
                        // 用户点击"再次生成流量包"
                        const currentRow = this.flowPacketList.find(item => item.id === this.currentGeneratingId);
                        if (currentRow) {
                            setTimeout(() => {
                                this.handleGenerate(currentRow);
                            }, 100);
                        }
                    }
                    done();
                }
            }).then(action => {
                // 无需处理，在beforeClose已经处理
            }).catch(() => {
                // 用户点击取消，不做任何操作
            });
            
            // 添加点击事件，实现复制功能
            setTimeout(() => {
                const copyBtn = document.querySelector('.copy-code-btn');
                if (copyBtn) {
                    copyBtn.addEventListener('click', () => {
                        copyToClipboard(successMsg);
                        this.$message.success('复制成功');
                    });
                }
            }, 100);
            
            // 操作成功后刷新列表
            this.getList();
        },

        // 查看特定流量包的发放记录
        handleViewRecord(row) {
            this.currentFlowPacketId = row.id;
            this.currentFlowPacketName = row.name;
            this.recordDialogVisible = true;
        },

        // 查看所有流量包的发放记录
        handleViewRecordList() {
            this.currentFlowPacketId = "";
            this.currentFlowPacketName = "";
            this.recordDialogVisible = true;
        },
        // 状态修改
        handleStatusChange(row) {
            let text = row.status === 1 ? "有效" : "无效";
            this.$confirm('确认要将"' + row.name + '"流量包状态设为"' + text + '"吗?', "警告", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                customClass: "top-message-box"
            }).then(() => {
                return updateFlowPacketStatus({
                    id: row.id,
                    status: row.status
                });
            }).then(response => {
                if (response.code == 200) {
                    this.$message.success(text + "成功");
                    // 重新加载表格数据
                    this.getList();
                } else {
                    this.$message.error(response.msg || text + "失败");
                    // 状态回滚
                    row.status = row.status === 1 ? 2 : 1;
                }
            }).catch(() => {
                // 状态回滚
                row.status = row.status === 1 ? 2 : 1;
            });
        },
        // 提交按钮
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.id != undefined) {
                        updateFlowPacket(this.form).then(response => {
                            if (response.code == 200) {
                                this.$message.success("修改成功");
                                this.open = false;
                                this.getList();
                            } else {
                                this.$message.error(response.msg || "修改失败");
                            }
                        }).catch(error => {
                            this.$message.error("修改失败：" + (error.message || "未知错误"));
                        });
                    } else {
                        addFlowPacket(this.form).then(response => {
                            if (response.code == 200) {
                                this.$message.success("新增成功");
                                this.open = false;
                                this.getList();
                            } else {
                                this.$message.error(response.msg || "新增失败");
                            }
                        }).catch(error => {
                            this.$message.error("新增失败：" + (error.message || "未知错误"));
                        });
                    }
                }
            });
        }
    }
};
</script>

<style>
.form-container-flow >>> .el-input-number .el-input__inner {
    text-align: left;
}

/* 验证码对话框样式 */
.verify-code-dialog .el-message-box__header {
    padding: 15px !important;
    border-bottom: 1px solid #EBEEF5 !important;
}
.verify-code-dialog .el-message-box__content {
    padding: 10px 20px !important;
}
.verify-code-dialog .el-message-box__btns {
    padding: 10px 20px 15px !important;
}
.verify-code-dialog .el-button--primary {
    background-color: #409EFF !important;
    border-color: #409EFF !important;
}
.verify-code-dialog {
    margin-top: -20% !important;
}

/* 自定义顶部对话框样式 */
.top-message-box {
    margin-top: -30% !important;
}
</style>