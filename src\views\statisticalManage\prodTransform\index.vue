<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="渠道类型" prop="channelIds">
        <el-select v-model="queryParams.channelIds" placeholder="渠道类型" clearable filterable multiple collapse-tags
          size="small">
          <el-option :value="item.id" :label="item.id+'----'+item.channelName" v-for="(item,index) in channelIdList "
            :key="index">
          </el-option>

        </el-select>
      </el-form-item>
      <el-form-item label="流量场景" prop="positionStatus">
        <el-select v-model="queryParams.positionStatus" placeholder="流量场景" @change="handleQuery" clearable size="small">
          <el-option :value="1" label="H5"></el-option>
          <el-option :value="2" label="APP"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="客户端" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="客户端" @change="handleQuery" clearable size="small">
          <el-option :value="1" label="苹果"></el-option>
          <el-option :value="2" label="安卓"></el-option>

        </el-select>
      </el-form-item>
      <el-form-item label="产品类型" prop="productType">
        <el-select v-model="queryParams.productType" placeholder="产品类型" @change="handleQuery" clearable size="small">
          <el-option value="1" label="表单产品"></el-option>
          <el-option value="2" label="接口产品"></el-option>
          <el-option value="3" label="线上产品"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="产品名称" prop="productIds">
        <el-select v-model="queryParams.productIds" placeholder="产品名称" multiple filterable collapse-tags clearable
          @change="handleQuery" size="small">
          <el-option :value="item.id" :label="item.id+'-----'+item.name" v-for="item in productList" :key="item.id">
          </el-option>

        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>

    <el-table border :data="dataList">
      <el-table-column label="产品名称" prop="productName" align="center">
        <template slot-scope="{row}">
          <div @click="handleUrl(row)" class="text">
            {{row.productName}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="曝光用户数" prop="seeUserNum" align="center" />
      <el-table-column label="点击用户数" prop="clickUserNum" align="center" />
      <el-table-column label="点击率" align="center">
        <template slot-scope="{row}">
          <div>
            {{row.seeProductClickRate}}%
          </div>
        </template>
      </el-table-column>
      <el-table-column label="成功进件用户数" prop="successUserNum" align="center">

      </el-table-column>
      <el-table-column label="进件率" align="center">
        <template slot-scope="{row}">
          <div>
            {{row.successProductRate}}%
          </div>
        </template>
      </el-table-column>
      <el-table-column label="预估收益" prop="profit" align="center" />
      <el-table-column label="单点击价值" prop="clickProductRate" align="center" />

    </el-table>
  </div>
</template>

<script>
import { getProductStatsList, getProdChannelList, getProdProductList } from "@/api/statisticalManage";
export default {

  data() {
    return {
      dataList: [

      ],
      channelIdList: [],
      productList: [],
      queryParams: {
        productName: "",
        positionStatus: "",
        channelIds: [],
        deviceType: '',
        productType: "",
        productIds: []
      }
    }
  },
  methods: {
    handleQuery() {
      this.getList()
    },
    handleUrl(row) {
      this.$router.push(`/statisticalManage/prodTransformDetail?id=${row.productId}&productName=${row.productName}`)
    },
    getList() {
      getProductStatsList(this.queryParams).then(res => {
        this.dataList = res.data
      })
    }
  },
  mounted() {
    this.getList()
    getProdChannelList().then(res => {

      this.channelIdList = res.data
    })
    getProdProductList().then(res => {

      this.productList = res.data
    })
  }
}
</script>

<style lang="scss" scoped>
.text {
  color: var(--current-color);
  text-decoration: underline;
  font-weight: bold;
  cursor: pointer;
}
</style>
