
import request from '@/utils/request'

//获取配置项列表
export function getFormConfigure(data) {
  return request({
    url: "/loan/product/form/conf",
    params: data
  })
}

//获取线上产品列表
export function getProductList(data = {}) {
  return request({
    url: "/loan/product/form/products",
    params: data
  })
}

//新增或者修改
export function handleConfigOne(data) {
  return request({
    url: "/loan/product/form/save",
    method: "post",
    data
  })
}
//新增或者修改
export function delFormConfigOne(data) {
  return request({
    url: "/loan/product/form/delete",
    method: "post",
    data
  })
}

//配置项
export function saveOptions(data) {
  return request({
    url: "/loan/product/form/save/option",
    method: "post",
    data
  })
}


//删除产品
export function delProductOne(data) {
  return request({
    url: "/loan/product/form/product/remove",
    method: "post",
    data
  })
}

//获取可添加产品
export function getProductAddList(data) {
  return request({
    url: "/loan/product/form/product/select",
    method: "get",
    params: data
  })
}

//新增配置产品
export function addProductList(data) {
  return request({
    url: "/loan/product/form/product/add",
    method: "post",
    data
  })
}
