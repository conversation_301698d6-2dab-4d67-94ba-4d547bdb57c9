import request from '@/utils/request'
//获取api配置列表
export const getIdentificateList = (data) => {
    return request({
        url: "/loan/identification/list",
        method: 'get',
        params: data
    })
}
//新增api配置列表
export const addIdentificateOne = (data) => {
    return request({
        url: "/loan/identification/add",
        method: 'post',
        data
    })
}
//获取api配置列表
export const editIdentificateOne = (data) => {
    return request({
        url: "/loan/identification/update",
        method: 'post',
        data
    })
}
//获取api配置列表
export const delIdentificateOne = (data) => {

    return request({
        url: "/loan/identification/delete",
        method: 'post',
        data
    })
}