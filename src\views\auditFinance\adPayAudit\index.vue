<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          size="small"
          clearable
          placeholder="请输入订单号"
        ></el-input>
      </el-form-item>
      <el-form-item label="订单号" prop="orderNo">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          value-format="yyyy-MM-dd"
          size="small"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="small"
          @click="handleAdd"
          >新增退款</el-button
        >
      </el-form-item>
    </el-form>
    <el-table :data="tableList">
      <el-table-column label="订单号" prop="orderNo" align="center" />
      <el-table-column label="第三方订单号" prop="tradeNo" align="center" />
      <el-table-column
        label="退款流水号"
        prop="refundSerialNo"
        align="center"
      />
      <el-table-column label="退款金额" prop="refundAmount" align="center" />
      <el-table-column
        label="退款状态"
        align="center"
        :formatter="(row) => statusJson[row.status]"
      />
      <el-table-column label="交易手续费" prop="tradeCharges" align="center" />
      <el-table-column
        label="支付方式"
        align="center"
        :formatter="(row) => payJson[row.payType]"
      />
      <el-table-column label="退款创建时间" prop="createTime" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button
              type="text"
              @click="handleQueryDetail(row)"
              v-if="row.status != 1"
              >查询</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="新增退款"
      :visible.sync="refundAvisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="formData.orderNo" placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="Adapay支付ID" prop="paymentId">
          <el-input
            v-model="formData.paymentId"
            placeholder="请输入Adapay支付ID"
          />
        </el-form-item>
        <el-form-item label="退款金额" prop="refundAmount">
          <el-input
            v-model="formData.refundAmount"
            oninput="value=value.replace(/[^0-9.]/g,'')"
            placeholder="请输入退款金额"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getRefundOrderList,
  applyRefundOrderOne,
  queryRefund,
} from "@/api/auditFinance/adpay";
export default {
  data() {
    var validateNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error("退款金额不能为空"));
      } else if (!/^(([1-9]{1}\d{0,3})|(0{1}))(\.\d{1,2})?$/.test(value)) {
        callback(new Error("输入不合法"));
      } else {
        callback();
      }
    };
    return {
      total: 0,
      tableList: [],
      dateRange: null,
      statusJson: {
        0: "退款中",
        1: "退款成功",
        2: "订单交易成功",
        3: "订单未支付",
        4: "订单支付中",
        5: "订单已撤回",
        6: "退款失败",
      },
      payJson: {
        0: "后台充值",
        1: "智付",
        2: "AdPay",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        endTime: "",
        startTime: "",
        orderNo: "",
      },
      formData: {
        orderNo: "",
        paymentId: "",
        refundAmount: "",
      },
      refundAvisible: false,
      rules: {
        orderNo: [{ required: true, message: "请输入订单号", trigger: "blur" }],
        paymentId: [
          { required: true, message: "请输入Adapay支付ID", trigger: "blur" },
        ],
        refundAmount: [
          { required: true, validator: validateNumber, trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.dateRange && this.dateRange.length) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    getList() {
      getRefundOrderList(this.queryParams).then((res) => {
        this.tableList = res.rows;
        this.total = res.total;
      });
    },
    handleAdd() {
      this.refundAvisible = true;
      this.formData = {
        orderNo: "",
        paymentId: "",
        refundAmount: "",
      };
    },
    cancel() {
      this.formData = {
        orderNo: "",
        paymentId: "",
        refundAmount: "",
      };
      this.refundAvisible = false;
      this.$refs.formData.resetFields();
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          console.log(valid);
          applyRefundOrderOne(this.formData).then((res) => {
            this.getList();
            this.refundAvisible = false;
          });
        }
      });
    },
    handleQueryDetail(row) {
      queryRefund({
        refundSerialNo: row.refundSerialNo,
        orderNo: row.orderNo,
      }).then((res) => {
        if (res.code == 200) {
          this.getList();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
}

.m-l10 {
  margin-left: 10px;
}

.check-info1 {
  margin-top: 10px;
  max-height: 500px;
  overflow: auto;
}
</style>
