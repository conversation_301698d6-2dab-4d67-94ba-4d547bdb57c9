<script>
import { SESAME_CREDIT_TYPES, SEX_TYPES } from '@/const/userQualification'
import {
  fetchSubPlatformConfig,
  fetchSubPlatformProduct,
  matchProduct,
  matchSpecialProduct,
  pushProduct,
  pushSpecialProduct
} from '@/api/tencentXJ/userManagement'
import { PLATFORM_TYPES } from '@/const/platformId'
import throttle from '@/utils/throttle'
import EditUser from './editUser.vue'

export default {
  name: 'MatchAndPush',

  components: {
    EditUser
  },

  data() {
    return {
      userData: {},
      visible: false,
      matchedProducts: [],
      activeNames: [],
      checkList: [],
      subPlatformConfig: [],
      subPlatformProduct: [],
      form: {
        productType: 'special'
      },
      platformActiveNames: [],
    }
  },

  methods: {
    handleProductTypeChange() {
      this.matchedProducts = [];
      this.checkList = [];
    },

    async fetchSubPlatformConfig() {
      const res = await fetchSubPlatformConfig()

      this.subPlatformConfig = res.data;
    },

    async open(user) {
      this.userData = { ...user };
      this.visible = true;
      await this.fetchSubPlatformConfig();
      await this.fetchSubPlatformProduct();
    },

    closeDialog() {
      this.visible = false;
      this.userData = {};
      this.matchedProducts = [];
      this.checkList = [];

      this.$emit('close');
    },

    matchProduct: throttle(
      async function () {
        try {
          const api = this.form.productType == 'normal' ? matchProduct : matchSpecialProduct;

          const response = await api({
            clueId: this.userData.clueId
          });

          // 清除掉之前的匹配结果和选择
          this.matchedProducts = [];
          this.checkList = [];

          this.handleSubPlatformConfig(response.data);

          this.matchedProducts = response.data;

          // 设置所有折叠面板为展开状态
          this.activeNames = this.matchedProducts.map(item => item.outputConfig.platformName);
          this.$forceUpdate();

          if (this.matchedProducts.length) {
            this.$nextTick(() => {
              this.$refs.matchedProductsCard.$el.scrollIntoView({ behavior: 'smooth' });
            });
          }
        } catch (error) {
          console.error('匹配产品失败:', error);
        }
      }
    ),

    handleSubPlatformConfig(arr) {
      arr.forEach(item => {
        const subPlatformConfig = this.subPlatformConfig.find(sub => sub.platformId == item.outputConfig.platformType);

        if (subPlatformConfig) {
          const tencentGroup = subPlatformConfig.groupProductList.find(group => group.groupName == '蹊径');

          if (tencentGroup) {
            item.result.forEach(product => {
              product.isTencent = tencentGroup.productIds.includes(product.productId);
            })
          }
        }
      });
    },

    getPushParams() {
      const params = {
        outPutSource: '',
        productId: '',
        name: this.userData.name,
        channelId: '',
        clueId: this.userData.clueId,
        price: ''
      };

      const selectedTag = this.checkList[0];

      this.matchedProducts.forEach(item => {
        item.result.forEach(product => {
          if (selectedTag === `${item.outputConfig.platformType}_&&_${product.productId}`) {
            params.outPutSource = item.outputSource;
            params.productId = product.productId;
            params.productName = product.productName;
            params.channelId = item.channelId;
            params.price = product.price;
          }
        });
      });

      return params;
    },

    pushProduct: throttle(
      async function () {
        if (!this.checkList.length) {
          this.$message.warning('请先选择一个产品');
          return;
        }

        const params = this.getPushParams();

        try {
          const api = this.form.productType == 'normal' ? pushProduct : pushSpecialProduct;

          await api(params);
          this.$emit('success');
          this.closeDialog();
          this.$message.success('产品推送成功');
        } catch (error) {
          console.error('推送产品失败:', error);
        }
      }
    ),

    handleEdit(user) {
      this.$refs.editUserDialog.open(user);
    },

    handleEditSuccess(user) {
      this.userData = {
        ...this.userData,
        ...user
      };

      this.fetchSubPlatformProduct();
    },

    async fetchSubPlatformProduct() {
      const res = await fetchSubPlatformProduct({
        cityName: this.userData.city
      })

      res.data.forEach(item => {
        const subPlatformConfig = this.subPlatformConfig.find(sub => sub.platformId == item.platformId);

        if (subPlatformConfig) {
          const tencentGroup = subPlatformConfig.groupProductList.find(group => group.groupName == '蹊径');

          if (tencentGroup) {
            item.productList.forEach(product => {
              product.isTencent = tencentGroup.productIds.includes(product.id);
            })
          }
        }
      });

      this.subPlatformProduct = res.data;

      // 设置所有在线城市机构折叠面板为展开状态
      this.platformActiveNames = this.subPlatformProduct.map(platform => platform.platformId);

      this.$forceUpdate();
    },

    getPlatformName(platformId) {
      return PLATFORM_TYPES[platformId] || platformId;
    },

    /**
     * 根据资质 id 获取资质名称
     * @param {String} field 资质类型字段
     * @param {String} value 资质 id
     */
    getDisplayValue(field, value) {
      // 性别
      if (field === 'sex') {
        return SEX_TYPES[value] || value;
      }
      // 芝麻分
      else if (field === 'sesameId') {
        return SESAME_CREDIT_TYPES[value] || value;
      }
      // 房产
      else if (field === 'houseId') {
        return value == 131 ? '无' : '有'
      }
      // 车产
      else if (field === 'vehicleId') {
        return value == 138 ? '无' : '有'
      }
      // 公积金
      else if (field === 'providentId') {
        return value == 150 ? '无' : '有'
      }
      // 社保
      else if (field === 'socialId') {
        return value == 156 ? '无' : '有'
      }
      // 逾期
      else if (field === 'overdueId') {
        return ['122', '123', '124'].includes(String(value)) ? '无' : '有'
      }
      // 营业执照
      else if (field === 'vocationId') {
        return value == 127 ? '无' : '有'
      }
      // 保险
      else if (field === 'insureId') {
        return value == 145 ? '无' : '有'
      }
    },

    getDisplayValueMultiple(field, value) {
      if (!value) {
        return '无';
      }

      const valueArr = value.split(',');
      // 房产
      if (field === 'houseId') {
        const noRequire = valueArr.includes('131');
        if (noRequire) {
          return '无'
        }
        else {
          const userHas = this.userData.houseId != '131';
          if (userHas) {
            return '有'
          }
          else {
            return `<span style="color: red;">有</span>`
          }
        }
      }
      // 车产
      else if (field === 'vehicleId') {
        const noRequire = valueArr.includes('138');
        if (noRequire) {
          return '无'
        }
        else {
          const userHas = this.userData.vehicleId != '138';
          if (userHas) {
            return '有'
          }
          else {
            return `<span style="color: red;">有</span>`
          }
        }
      }
      // 公积金
      else if (field === 'providentId') {
        const noRequire = valueArr.includes('150');
        if (noRequire) {
          return '无'
        }
        else {
          const userHas = this.userData.providentId != '150';
          if (userHas) {
            return '有'
          }
          else {
            return `<span style="color: red;">有</span>`
          }
        }
      }
      // 社保
      else if (field === 'socialId') {
        const noRequire = valueArr.includes('156');
        if (noRequire) {
          return '无'
        }
        else {
          const userHas = this.userData.socialId != '156';
          if (userHas) {
            return '有'
          }
          else {
            return `<span style="color: red;">有</span>`
          }
        }
      }
      // 保险
      else if (field === 'insureId') {
        const noRequire = valueArr.includes('145');
        if (noRequire) {
          return '无'
        }
        else {
          const userHas = this.userData.insureId != '145';
          if (userHas) {
            return '有'
          }
          else {
            return `<span style="color: red;">有</span>`
          }
        }
      }
      // 营业执照
      else if (field === 'vocationId') {
        const noRequire = valueArr.includes('127');
        if (noRequire) {
          return '无'
        }
        else {
          const userHas = this.userData.vocationId != '127';
          if (userHas) {
            return '有'
          }
          else {
            return `<span style="color: red;">有</span>`
          }
        }
      }
      // 芝麻分
      else if (field === 'sesameId') {
        // 机构的最低要求
        const minSesameId = Math.min(...value.split(',').map(Number));
        // 用户的芝麻分是否满足机构的最低要求
        const isPass = minSesameId <= Number(this.userData.sesameId);
        if (isPass) {
          return this.getDisplayValue('sesameId', this.userData.sesameId)
        }
        else {
          return `<span style="color: red;">${this.getDisplayValue('sesameId', minSesameId)}</span>`
        }
      }
      // 逾期
      else if (field === 'overdueId') {
        const notOverdueIds = ['122', '123', '124'];
        const notOverdue = valueArr.some(id => notOverdueIds.includes(id));

        if (notOverdue) {
          const userHasOverdue = !notOverdueIds.includes(String(this.userData.overdueId));
          if (userHasOverdue) {
            return '<span style="color: red;">无</span>';
          } else {
            return '无';
          }
        } else {
          return '有';
        }
      }
    }
  }
}
</script>

<template>
  <el-drawer title="" :visible.sync="visible" @close="closeDialog" direction="rtl" :size="1200"
    :wrapperClosable="false">
    <div style="padding: 15px;">
      <el-card style="margin-bottom: 15px;">
        <template #header>
          <div style="display: flex; justify-content: space-between;align-items: center;">
            用户信息
            <el-button type="primary" style="" size="mini" @click="handleEdit(userData)">编辑</el-button>
          </div>
        </template>

        <el-descriptions :column="3" border size="mini" title="基础资料" style="margin-bottom: 20px;">
          <el-descriptions-item label="对话 id">{{ userData.talkId }}</el-descriptions-item>
          <el-descriptions-item label="用户姓名">{{ userData.name }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ userData.phone }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ getDisplayValue('sex', userData.sex) }}</el-descriptions-item>
          <el-descriptions-item label="年龄">{{ userData.age }}</el-descriptions-item>
          <el-descriptions-item label="城市">{{ userData.city }}</el-descriptions-item>
        </el-descriptions>

        <el-descriptions :column="3" border size="mini" title="资质情况">
          <el-descriptions-item label="房产">{{ getDisplayValue('houseId', userData.houseId) }}</el-descriptions-item>
          <el-descriptions-item label="车产">{{ getDisplayValue('vehicleId', userData.vehicleId) }}</el-descriptions-item>
          <el-descriptions-item label="公积金">{{ getDisplayValue('providentId', userData.providentId)
            }}</el-descriptions-item>
          <el-descriptions-item label="社保">{{ getDisplayValue('socialId', userData.socialId) }}</el-descriptions-item>
          <el-descriptions-item label="保险">{{ getDisplayValue('insureId', userData.insureId) }}</el-descriptions-item>
          <el-descriptions-item label="逾期">{{ getDisplayValue('overdueId', userData.overdueId) }}</el-descriptions-item>
          <el-descriptions-item label="营业执照">{{ getDisplayValue('vocationId', userData.vocationId)
            }}</el-descriptions-item>
          <el-descriptions-item label="芝麻分">{{ getDisplayValue('sesameId', userData.sesameId)
            }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card header="在线城市机构" v-if="subPlatformProduct.length">
        <el-collapse v-model="platformActiveNames">
          <el-collapse-item v-for="platform in subPlatformProduct" :key="platform.platformId"
            :title="`${getPlatformName(platform.platformId)}在线城市机构`"
            :name="platform.platformId">
            <el-table :data="platform.productList" style="width: 100%" border size="mini">
              <el-table-column label="机构名称" prop="name" width="200">
                <template v-slot="{ row }">
                  <el-tag v-if="row.isTencent" size="mini">蹊径</el-tag>
                  <el-tag v-if="row.directLine" size="mini" type="danger">专线</el-tag>
                  【{{ row.id }}】
                  {{ row.name }}
                </template>
              </el-table-column>
              <el-table-column label="合作价格" prop="cooperationCost"></el-table-column>
              <el-table-column label="房产">
                <template v-slot="{ row }">
                  <div v-if="row.ruleHasDisabled">无</div>
                  <div v-else v-html="getDisplayValueMultiple('houseId', row.houseIds)"></div>
                </template>
              </el-table-column>
              <el-table-column label="车产">
                <template v-slot="{ row }">
                  <div v-if="row.ruleHasDisabled">无</div>
                  <div v-else v-html="getDisplayValueMultiple('vehicleId', row.vehicleIds)"></div>
                </template>
              </el-table-column>
              <el-table-column label="公积金">
                <template v-slot="{ row }">
                  <div v-if="row.ruleHasDisabled">无</div>
                  <div v-else v-html="getDisplayValueMultiple('providentId', row.providentIds)"></div>
                </template>
              </el-table-column>
              <el-table-column label="社保">
                <template v-slot="{ row }">
                  <div v-if="row.ruleHasDisabled">无</div>
                  <div v-else v-html="getDisplayValueMultiple('socialId', row.socialIds)"></div>
                </template>
              </el-table-column>
              <el-table-column label="保险">
                <template v-slot="{ row }">
                  <div v-if="row.ruleHasDisabled">无</div>
                  <div v-else v-html="getDisplayValueMultiple('insureId', row.insureIds)"></div>
                </template>
              </el-table-column>
              <el-table-column label="逾期">
                <template v-slot="{ row }">
                  <div v-if="row.ruleHasDisabled">无</div>
                  <div v-else v-html="getDisplayValueMultiple('overdueId', row.overdueIds)"></div>
                </template>
              </el-table-column>
              <el-table-column label="营业执照">
                <template v-slot="{ row }">
                  <div v-if="row.ruleHasDisabled">无</div>
                  <div v-else v-html="getDisplayValueMultiple('vocationId', row.vocationIds)"></div>
                </template>
              </el-table-column>
              <el-table-column label="芝麻分">
                <template v-slot="{ row }">
                  <div v-if="row.ruleHasDisabled">无</div>
                  <div v-else v-html="getDisplayValueMultiple('sesameId', row.sesameIds)"></div>
                </template>
              </el-table-column>
              <el-table-column label="匹配规则" width="125">
                <template v-slot="{ row }">
                  <!-- 规则状态(0选中满足任意一条即可 1选中都要满足)只包括资产筛选 -->
                  <span v-if="row.ruleStatus == 0">满足任意一条即可</span>
                  <span v-else-if="row.ruleStatus == 1">全部满足</span>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </el-card>

      <el-form inline :model="form" size="mini" style="margin-top: 15px;">
        <el-form-item>
          <el-select v-model="form.productType" placeholder="请选择产品类型" @change="handleProductTypeChange">
            <el-option label="普通产品" value="normal"></el-option>
            <el-option label="专线产品" value="special"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button :type="form.productType == 'normal' ? 'primary' : 'danger'" @click="matchProduct">匹配产品</el-button>
          <el-button :type="form.productType == 'normal' ? 'primary' : 'danger'" @click="pushProduct">推送</el-button>
        </el-form-item>
      </el-form>

      <el-card :header="form.productType == 'normal' ? '普通产品' : '专线产品'" v-if="matchedProducts.length"
        ref="matchedProductsCard">
        <el-checkbox-group v-model="checkList" :max="1">
          <el-collapse v-model="activeNames">
            <el-collapse-item v-for="item in matchedProducts" :key="item.outputConfig.platformName"
              :title="`平台：${item.outputConfig.platformName}`" :name="item.outputConfig.platformName">
              <el-table :data="item.result" style="width: 100%" border size="mini">
                <el-table-column label="产品名称">
                  <template slot-scope="scope">
                    <el-checkbox :label="`${item.outputConfig.platformType}_&&_${scope.row.productId}`">
                      <span style="font-weight: normal;">
                        <el-tag v-if="scope.row.isTencent" size="mini">蹊径</el-tag>
                        【{{ scope.row.productId }}】
                        {{ scope.row.productName }}
                      </span>
                    </el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="机构名称" prop="institutionName"></el-table-column>
                <el-table-column label="价格" prop="price"></el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-checkbox-group>
      </el-card>
    </div>

    <edit-user ref="editUserDialog" @success="handleEditSuccess" />
  </el-drawer>
</template>

<style scoped></style>
