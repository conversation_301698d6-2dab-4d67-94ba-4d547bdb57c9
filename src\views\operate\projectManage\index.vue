<template>
  <div class="app-container">
    <el-button
      type="primary"
      style="margin-bottom: 20px"
      @click="addAgreementType"
      size="small"
      >新增项目</el-button
    >

    <el-table :data="list" border>
      <el-table-column label="ID" align="center" prop="projectId" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="安卓官方包链接" align="center" prop="androidUrl" />
      <el-table-column label="苹果官方包链接" align="center" prop="iosUrl" />

      <el-table-column label="添加时间" prop="createTime" align="center" />
      <el-table-column label="修改时间" prop="updateTime" align="center" />
      <el-table-column label="备注" prop="remark" align="center" />
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button type="text" @click="handleEdit(row)" icon="el-icon-edit"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="isadd ? '新增项目' : '修改项目'"
      :visible.sync="agreementTypeAvisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="安卓官方包链接" prop="androidUrl">
          <el-input v-model="formData.androidUrl" placeholder="请输入安卓官方包链接" />
        </el-form-item>
        <el-form-item label="苹果官方链接" prop="iosUrl">
          <el-input v-model="formData.iosUrl" placeholder="请输入苹果官方链接" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="formData.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getProjectList,
  AddProjectOne,
  editProjectOne,
  delProjectOne,
} from "@/api/operate/project";
export default {
  data() {
    return {
      agreementTypeAvisible: false,
      isadd: true,
      total: 0,
      list: [],
      formData: {
        name: "",
        remark: "",
         androidUrl: "",
        iosUrl: "",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      rules: {
        name: [{ required: true, message: "请输入项目名称", trigger: "blur" }],
        androidUrl: [{ required: true, message: "请输入安卓官方包链接", trigger: "blur" }],
        iosUrl: [{ required: true, message: "请输入苹果官方包链接", trigger: "blur" }],
      },
    };
  },
  methods: {
    addAgreementType() {
      this.agreementTypeAvisible = true;
      this.isadd = true;
      if (this.formData.projectId) {
        delete this.formData.projectId;
      }
    },
    getList() {
      getProjectList(this.queryParams).then((res) => {
        this.list = res.rows;
        this.total = res.total;
        if (this.list.length == 0 && this.queryParams.pageNum > 1) {
          this.queryParams.pageNum = this.queryParams.pageNum - 1;
          this.getList();
          return;
        }
      });
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isadd) {
            AddProjectOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.cancel();
                this.getList();
              }
            });
          } else {
            editProjectOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.cancel();
                this.getList();
              }
            });
          }
        }
      });
    },
    cancel() {
      this.agreementTypeAvisible = false;
      this.formData = {
        name: "",
        remark: "",
        androidUrl: "",
        iosUrl: "",
      };
      this.$refs.formData.resetFields();
    },
    handleEdit(row) {
      this.agreementTypeAvisible = true;
      this.isadd = false;
      this.formData.name = row.name;
      this.formData.projectId = row.projectId;
      this.formData.remark = row.remark;
      this.formData.androidUrl = row.androidUrl;
      this.formData.iosUrl = row.iosUrl;
    },
    handleDelete(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delProjectOne(row.projectId).then((res) => {
            if (res.code == 200) {
              this.$message.success("删除成功");
              this.getList();
            }
          });
        })
        .catch((err) => {});
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
