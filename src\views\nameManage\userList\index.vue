<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item label="手机号">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入手机号"
          size="small"
          clearable
          v-model="queryParams.phone"
        ></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="queryParams.status" clearable  size="small" placeholder="请选择状态" value='0'>
          <el-option
            v-for="item in options"
            :key="item.businessId"
            :label="item.businessName"
            :value="item.businessId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="身份证">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入身份证"
          size="small"
          clearable
          v-model="queryParams.idCard"
        ></el-input>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-bottom"
          size="mini"
          @click="exportFormData('failForm')"
          v-hasPermi="['loan:appUserList:export']"
          >未接单咨询统计</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-bottom"
          size="mini"
          @click="exportFormData('applyForm')"
          v-hasPermi="['loan:appUserList:export']"
          >导出未申请咨询单统计</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-bottom"
          size="mini"
          @click="exportFormData('cityForm')"
          v-hasPermi="['loan:formandcity:export']"
          >城市商户咨询接单统计</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-bottom"
          size="mini"
          @click="exportFormData('successForm')"
          v-hasPermi="['loan:applysuccesscityform:export']"
          >咨询成功城市商户统计</el-button
        >
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border>
      <!-- <el-table-column label="用户身份证" align="center" prop="idCard" /> -->
      <el-table-column label="关联手机号" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ row.phone }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" align="center" prop="registerTime" />
      <el-table-column
        label="注册渠道ID"
        align="center"
      >
        <template slot-scope="{ row }">
          <div>
            {{ row.channelId }}-{{row.channelName}}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="最近登录渠道ID"
        align="center"
      >
        <template slot-scope="{ row }">
          <div>
            {{ row.behavioralChannelId }}-{{row.behaveChannelName}}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="最后登录时间"
        align="center"
        prop="lastLoginTime"
      ></el-table-column>
      <el-table-column label="申请记录" align="center">
        <template slot-scope="{ row }">
          <div
            v-if="row.applyProductRecord && row.applyProductRecord.length > 0"
          >
            {{ row.applyProductRecord.join(",") }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="咨询记录" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.pushProductRecord && row.pushProductRecord.length > 0">
            {{ row.pushProductRecord.join(",") }}
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="是否会员" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-tag type="success" v-if="row.isMember">是</el-tag>
            <el-tag type="waring" v-else>否</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否付费" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-tag type="success" v-if="row.isPay">是</el-tag>
            <el-tag type="waring" v-else>否</el-tag>
          </div>
        </template>
      </el-table-column> -->

      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button size="mini" @click="recovery(row)" type="text"
              >拉黑</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    /> -->
    <el-dialog
      title="导出"
      :visible.sync="exportVisible"
      center
      width="800px"
      append-to-body
      @close="cancle"
      :close-on-click-modal="false"
    >
      <el-form label-position="right" label-width="180px">
        <el-form-item label="请选择导出时间段">
          <el-date-picker
            v-model="value1"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
          >
          </el-date-picker>
        </el-form-item>
        <el-button
          type="primary"
          style="margin-left: 200px"
          @click="exportSubmit"
          >确认导出</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAppUserList,
  getrecoverOne,
  exportData,
  exportFormAndCity,
  exportSuccesscity,
  exportApplyForm,
  getUserPhoneStatusOption
} from "@/api/nameManage";
import xlsx from "xlsx";
export default {
  name: "UserList",
  data() {
    return {
      options:[],
      exportVisible: false,
      exportType: null,
      balckForm: {
        name: "",
      },
      value1: [],
      total: 0,
      dataList: [],
      queryParams: {
        phone: "",
        idCard: "",
        status:""
      },
      formData: {
        startTime: "",
        stopTime: "",
      },
    };
  },
  methods: {
    handleQuery() {
      if(!this.queryParams.phone) return this.$message.error("请输入收手机号")
      if(!String(this.queryParams.status)) return this.$message.error("请选择状态")
      this.getList();
    },

    recovery(row) {
      this.$confirm(`确定拉黑${row.phone}此用户吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          getrecoverOne(row.appUserId).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              this.getList();
            }
          });
        })
        .catch(() => {});
    },

    getList() {
      getAppUserList(this.queryParams).then((res) => {
        this.dataList = res.rows;
      });
    },
    exportSubmit() {
      if (!this.value1 || !this.value1.length) {
        this.$message.error("请选择导出时间段");
        return;
      }
      if (this.value1 !== null) {
        this.formData.startTime = this.value1[0];
        this.formData.stopTime = this.value1[1];
      } else {
        this.formData.startTime = "";
        this.formData.stopTime = "";
      }

      switch (this.exportType) {
        case "failForm":
          exportData(this.formData).then((res) => {
            this.createXlsx("未接单咨询统计", res);
          });
          break;
        case "cityForm":
          exportFormAndCity(this.formData).then((res) => {
            this.createXlsx("城市商户咨询接单统计", res);
          });
          break;
        case "applyForm":
          exportApplyForm(this.formData).then((res) => {
            this.createXlsx("导出未申请咨询单统计", res);
          });
          break;
        case "successForm":
          exportSuccesscity(this.formData).then((res) => {
            this.createXlsx("咨询成功城市商户统计", res);
          });
          break;
        default:
          null;
      }
    },
    exportFormData(type) {
      this.exportType = type;
      this.exportVisible = true;
    },

    createXlsx(title, res) {
      let a = document.createElement("a");
      let blob = new Blob([res], { type: "application/vnd.ms-excel" });
      let objectUrl = URL.createObjectURL(blob);
      a.setAttribute("href", objectUrl);
      a.setAttribute("download", `${title}.xlsx`);
      a.click();
      this.cancle();
      this.$message.success("导出成功");
    },

    cancle() {
      this.value1 = [];
      this.formData.startTime = "";
      this.formData.stopTime = "";
      this.exportVisible = false;
      this.export = false;
    },
  },

  created() {
    this.queryParams.status = 0
  },
  mounted() {
    getUserPhoneStatusOption().then(res=>{
      this.options=res.data
    })
  },
};
</script>

<style lang="scss" scoped></style>
