import request from '@/utils/request'
import qs from 'qs'
//获取apiq渠道列表
export const getOutApiChannelList = (data) => {
  let formData=new FormData();
  for(let ket in data){
    formData.append(ket,data[ket]);
  }
  return request({
    url: "/loan/output/api/config/channel/list",
    method: "post",
    data: formData,
    headers: { 'Content-Type': 'x-www-form-urlencoded' },
  })
}
//新增渠道
export const addApiChannelOne = (data) => {
  return request({
    url: "/loan/api/config/channel/add",
    method: "post",
    data
  })
}

///查渠道配置详情
export const getApiChannelOne = (data) => {
  return request({
    url: `/loan/api/config/channel/query/${data}`,
    method: "post"
  })
}
///修改道配置状态
export const changeOutApiChannelOne = (data) => {
  return request({
    url: `/loan/output/api/config/channel/updateStatus`,
    method: "post",
    data
  })
}
//编辑渠道
export const editOutApiChannelOne = (data) => {
  return request({
    url: "/loan/output/api/config/channel/updateBasicsChannel",
    method: "post",
    data
  })
}
