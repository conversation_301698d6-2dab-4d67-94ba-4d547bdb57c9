<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item label="渠道id">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入渠道id"
          size="small"
          clearable
          v-model="queryParams.channelId"
        ></el-input>
      </el-form-item>
      <el-form-item label="产品ID">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入产品ID"
          size="small"
          clearable
          v-model.number="queryParams.productId"
        ></el-input>
      </el-form-item>
      <el-form-item label="推送时间" prop="pushDate">
        <el-date-picker
          v-model="queryParams.pushDate"
          type="date"
          size="small"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          placeholder="选择日期"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border>
      <el-table-column
        label="渠道ID"
        align="center"
        prop="id"
      ></el-table-column>
      <el-table-column
        label="推送时间"
        align="center"
        prop="pushDay"
      ></el-table-column>
      <el-table-column
        label="异地空号数量"
        align="center"
        prop="emptyNum"
      ></el-table-column>
      <el-table-column
        label="加微信数量"
        align="center"
        prop="addWXNum"
      ></el-table-column>
      <el-table-column
        label="无意向数量"
        align="center"
        prop="noIntentionNum"
      ></el-table-column>

      <el-table-column
        label="未接通数量"
        align="center"
        prop="blockCallNum"
      ></el-table-column>
      <el-table-column
        label="有资质数量"
        align="center"
        prop="talentedNum"
      ></el-table-column>
      <el-table-column
        label="合计"
        align="center"
        prop="totalNum"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.id != '合计'">
            <el-button type="text" size="mini" @click="handleDetail(row)"
              >查看详情</el-button
            >
            <!-- <el-button type="text" @click="handleDetail(row)"></el-button> -->
            <el-button type="text" size="mini" @click="exportData(row)" v-hasPermi="['loan:partya:statistics:export']"
              >导出数据</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :visible.sync="dialogVisible"
      title="详情"
      width="80%"
      append-to-body
    >
      <el-table :data="detailList" border>
        <el-table-column
          label="产品ID"
          align="center"
          prop="id"
        ></el-table-column>
        <el-table-column
          label="产品名称"
          align="center"
          prop="productName"
        ></el-table-column>
        <el-table-column
          label="推送时间"
          align="center"
          prop="pushDay"
        ></el-table-column>
        <el-table-column
          label="异地空号数量"
          align="center"
          prop="emptyNum"
        ></el-table-column>
        <el-table-column
          label="加微信数量"
          align="center"
          prop="addWXNum"
        ></el-table-column>
        <el-table-column
          label="无意向数量"
          align="center"
          prop="noIntentionNum"
        ></el-table-column>

        <el-table-column
          label="未接通数量"
          align="center"
          prop="blockCallNum"
        ></el-table-column>
        <el-table-column
          label="有资质数量"
          align="center"
          prop="talentedNum"
        ></el-table-column>
        <el-table-column
          label="合计"
          align="center"
          prop="totalNum"
        ></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { exportChannelUserStatus, getPushStatistics, getPushstatisticsDetails } from '@/api/nameManage'
export default {
  data() {
    return {
      total: 0,
      dialogVisible: false,
      dataList: [],
      detailList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        channelId: "",
        pushDate: "",
        productId: "",
      },
      // 当前表格数据的参数，用于导出
      exportParams: {},
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now() ||
            time.getTime() < Date.now() - 3600 * 3 * 24 * 1000
          );
        },
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    getList() {
      this.exportParams = { ...this.queryParams };
      getPushStatistics(this.queryParams).then((res) => {
        this.dataList = res.data.pageResult.rows;
        if (this.dataList.length == 0) return;
        this.dataList.push({
          id: "合计",
          pushDay: "",
          addWXNum: res.data.total.addWXNum,
          blockCallNum: res.data.total.blockCallNum,
          emptyNum: res.data.total.emptyNum,
          talentedNum: res.data.total.talentedNum,
          noIntentionNum: res.data.total.noIntentionNum,
          totalNum: res.data.total.totalNum,
        });
        this.total = res.data.pageResult.total;
      });
    },
    handleDetail(row) {
      this.detailList = [];
      getPushstatisticsDetails({
        channelId: row.id,
        pushDate: this.queryParams.pushDate,
      }).then((res) => {
        this.detailList = res.data;
        this.dialogVisible = true;
      });
    },

    async exportData(row) {
      const res = await exportChannelUserStatus({
        channelId: row.id,
        pushDate: row.pushDay,
        productId: this.exportParams.productId,
      })
      const fileName = `渠道ID ${row.id} 状态统计_${row.pushDay}`
      this.createXlsx(fileName, res);
    },

    createXlsx(title, res) {
      let a = document.createElement("a");
      let blob = new Blob([res], { type: "application/vnd.ms-excel" });
      let objectUrl = URL.createObjectURL(blob);
      a.setAttribute("href", objectUrl);
      a.setAttribute("download", `${title}.xlsx`);
      a.click();
      this.$message.success("导出成功");
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped></style>
