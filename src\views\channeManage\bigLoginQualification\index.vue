<template>
  <div class="app-container">
    <!-- 新增筛选表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" size="small">
      <el-form-item label="渠道名称" prop="channelName">
        <el-input
          clearable
          placeholder="请输入渠道名称"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道ID" prop="channelId">
        <el-input
          clearable
          placeholder="请输入渠道ID"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
          v-model="queryParams.channelId"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-refresh"
          @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="list" size="mini">
      <el-table-column label="渠道ID" prop="id" align="center" width="100" />
      <el-table-column label="渠道名称" prop="channelName" align="center" />
      <el-table-column label="渠道类型" prop="type" align="center" width="100">
        <template slot-scope="{row}">
          <span>{{ getChannelType(row.type) }}</span>
        </template>
      </el-table-column>

      <!-- 线下产品配置 -->
      <el-table-column label="线下产品配置" align="center">
        <el-table-column label="线下产品数" prop="matchCount" align="center" width="140">
          <template slot-scope="scope">
            <el-input
              v-model.number="scope.row.matchCount"
              type="number"
              :min="0"
              style="width: 110px"
              size="mini"
              @change="val => handleNumberChange(scope.row, 'matchCount', val, '线下产品数')"
            />
          </template>
        </el-table-column>
        <el-table-column label="银行机构数" prop="bankCount" align="center" width="140">
          <template slot-scope="scope">
            <el-input
              v-model.number="scope.row.bankCount"
              type="number"
              :min="0"
              style="width: 110px"
              size="mini"
              @change="val => handleNumberChange(scope.row, 'bankCount', val, '银行机构数')"
            />
          </template>
        </el-table-column>
        <el-table-column label="全国机构数" prop="countryCount" align="center" width="140">
          <template slot-scope="scope">
            <el-input
              v-model.number="scope.row.countryCount"
              type="number"
              :min="0"
              style="width: 110px"
              size="mini"
              @change="val => handleNumberChange(scope.row, 'countryCount', val, '全国机构数')"
            />
          </template>
        </el-table-column>
        <el-table-column label="表单最低单价" prop="maxPrice" align="center" width="140">
          <template slot-scope="scope">
            <el-input
              v-model.number="scope.row.maxPrice"
              type="number"
              :min="0"
              step="0.01"
              style="width: 110px"
              size="mini"
              @change="val => handleNumberChange(scope.row, 'maxPrice', val, '表单最低单价')"
            >
            </el-input>
          </template>
        </el-table-column>
      </el-table-column>

      <!-- 线上产品配置 -->
      <el-table-column label="线上产品配置" align="center">
        <el-table-column label="企微最低单价" prop="qwMaxPrice" align="center" width="140">
          <template slot-scope="scope">
            <el-input
              v-model.number="scope.row.qwMaxPrice"
              type="number"
              :min="0"
              style="width: 110px"
              size="mini"
              @change="val => handleNumberChange(scope.row, 'qwMaxPrice', val, '企微最低单价')"
            />
          </template>
        </el-table-column>
        <el-table-column label="企微机构数" prop="qwCount" align="center" width="140">
          <template slot-scope="scope">
            <el-input
              v-model.number="scope.row.qwCount"
              type="number"
              :min="0"
              style="width: 110px"
              size="mini"
              @change="val => handleNumberChange(scope.row, 'qwCount', val, '企微机构数')"
            />
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="同时满足标识" prop="andFlag" align="center" width="210">
        <template slot-scope="{row}">
          <el-switch
            v-model="row.andFlag"
            :active-value="1"
            :inactive-value="2"
            active-text="同时满足"
            inactive-text="满足一个"
            @change="val => handleAndFlagChange(row, val)"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination 
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { 
  getBigLoginQualificationList,
  updateBigLoginQualification
} from '@/api/channeManage/bigLoginQualification'

export default {
  name: 'BigLoginQualification',
  data() {
    return {
      // 查询参数
      queryParams: {
        channelName: '',
        channelId: '',
        pageNum: 1,
        pageSize: 10
      },
      list: [], // 列表数据
      total: 0, // 总条数
    }
  },
  methods: {
    // 新增查询和重置方法
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    // 获取列表数据
    async getList() {
      const res = await getBigLoginQualificationList(this.queryParams)
      this.list = res.rows || []
      this.total = res.total || 0
    },
    // 修改配置
    async handleUpdate(row) {
      try {
        await this.$confirm(`确定要更新${row.channelName}的配置吗？`, '提示', {
          type: 'warning'
        })
        
        const { code } = await updateBigLoginQualification(row)
        if (code == 200) {
          this.$message.success('更新成功')
          this.getList()
        }
      } catch (error) {
        // 错误已在拦截器统一处理
      }
    },
    getChannelType(type) {
      const typeMap = {
        1: '联登',
        2: '半流程', 
        3: 'API',
        4: '全流程UV',
        5: '信息流'
      }
      return typeMap[type] || '-'
    },
    // 处理满足条件切换
    async handleAndFlagChange(row, newValue) {
      const oldValue = row.andFlag
      try {
        await this.$confirm(
          `确定要切换为${newValue === 1 ? '同时满足' : '满足一个'}吗？`,
          '提示',
          { type: 'warning' }
        )
        
        const params = {
          ...row,
          andFlag: newValue
        }
        
        const { code } = await updateBigLoginQualification(params)
        if (code == 200) {
          this.$message.success('状态更新成功')
          this.getList() // 统一通过重新获取数据来更新状态
        } else {
          row.andFlag = oldValue
        }
      } catch (error) {
        row.andFlag = oldValue
        this.getList() // 确保状态同步
      }
    },
    handleNumberChange(row, field, value, fieldName) {
      if (value < 0) {
        this.$message.warning('不能输入负数')
        row[field] = 0
        return
      }
      
      this.$confirm(
        `${row.channelName}(ID:${row.id}) 的${fieldName}将修改为 ${value}？`,
        '提示',
        { type: 'warning' }
      ).then(async () => {
        const params = { ...row, [field]: value }
        const { code } = await updateBigLoginQualification(params)
        if (code == 200) {
          this.$message.success('修改成功')
          this.getList()
        }
      }).catch(() => {
        this.getList() // 取消修改时刷新数据还原状态
      })
    }
  },
  mounted() {
    this.getList()
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input {
  input[type="number"] {
    padding-right: 0px;
    appearance: textfield;
    // -moz-appearance: textfield;
    // -webkit-appearance: textfield;
    // 解决el-input设置类型为number时，中文输入法光标上移问题
    line-height: 1px !important;
  }
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    appearance: none;
    // -webkit-appearance: none;
    margin: 0;
  }
}
</style> 