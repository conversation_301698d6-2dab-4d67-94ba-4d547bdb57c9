<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" size="small">
      <el-form-item label="推广名称">
        <el-input
          size="small"
          @keyup.enter.native="handleQuery"
          clearable
          placeholder="请输入推广名称"
          v-model="queryParams.productName"
        ></el-input>
      </el-form-item>
      <el-form-item label="产品是否在线">
        <el-select
          v-model="queryParams.productStatus"
          size="small"
          clearable
          style="width: 140px"
        >
          <el-option :value="1" label="在线"></el-option>
          <el-option :value="2" label="不在"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商户名称">
        <el-input
          v-model="queryParams.partyName"
          @keyup.enter.native="handleQuery"
          size="small"
          clearable
          placeholder="请输入商户名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="城市">
        <el-cascader
          v-model="cityArr"
          :options="cityList"
          @change="handleQuery"
          clearable
          filterable
          size="small"
        >
        </el-cascader>
      </el-form-item>
      <el-form-item label="商务">
        <el-select
          v-model="queryParams.phoneNumber"
          size="small"
          clearable
          filterable
          style="width: 140px"
        >
          <el-option
            v-for="item in businessUserList"
            :key="item.phonenumber"
            :value="item.phonenumber"
            :label="item.nickName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="平台类型">
        <el-select
          v-model="queryParams.platformType"
          size="small"
          clearable
          multiple
          collapse-tags
          @change="handleQuery"
          style="width: 240px"
        >
          <el-option
            v-for="item in platformList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="handleQuery"
          :clearable="false"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table
      :data="dataList"
      border
      :default-sort="{prop: 'cooperationCost', order: 'descending'}"
      size="small"
    >
      <el-table-column label="平台名称" align="center" width="90">
        <template slot-scope="{ row }">
          <div>
            <el-tag
              :style="{ backgroundColor: getTagType(row.platformType), color: '#fff', border: 'none' }"
              size="mini"
            >
              {{ getPlatformName(row.platformType) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="推广名称" prop="productName" align="center" />
      <el-table-column label="投放城市" prop="cityNames" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.cityNames && row.cityNames.split(',').length > 2">
            <div>
              <el-popover
                placement="top-start"
                width="400"
                trigger="hover"
                :content="row.cityNames"
              >
                <el-button slot="reference" type="text">查看城市</el-button>
              </el-popover>
            </div>
          </div>
          <div v-else>
            {{ row.cityNames == "全国" ? "全国" : row.cityNames }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="商户名称"
        prop="partyName"
        align="center"
      ></el-table-column>
      <el-table-column label="商务" prop="sysUserNickName" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-tag v-if="row.isTransfer == 1">转</el-tag>
            {{ row.sysUserNickName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="推广价格"
        align="center"
        prop="cooperationCost"
        sortable
        width="100"
      ></el-table-column>
      <el-table-column
        label="接入单价"
        align="center"
        prop="matchingPriceSort"
        sortable
        width="100"
      >
        <template slot-scope="{ row }">
          <div
            :class="[
              row.matchingPriceSort == null ||
              row.matchingPriceSort == row.cooperationCost
                ? ''
                : 'red',
            ]"
          >
            {{ row.matchingPriceSort }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="UV" align="center" prop="applyCount" sortable width="100"></el-table-column>
      <el-table-column label="接入数" align="center" prop="accessProductRecord.accessNum" sortable width="110">
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.accessProductRecord.accessNum"
            :min="0"
            :max="999999"
            :controls="false"
            style="width: 80px"
            size="small"
            :disabled="!canEditAccessNum(scope.row)"
            @blur="handleAccessNumBlur(scope.row)"
            @focus="handleAccessNumFocus(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="录入时UV" align="center" prop="accessProductRecord.uvNum" sortable width="110"></el-table-column>
      <el-table-column label="UV收益" align="center" prop="accessProductRecord.accessPrice" sortable width="120"></el-table-column>
      <el-table-column label="总收益" align="center" prop="accessProductRecord.assessProfit" sortable width="100"></el-table-column>
      <el-table-column label="接入率" align="center" prop="accessProductRecord.accessRate" sortable width="100">
        <template slot-scope="{ row }">
          <div>
            {{ row.accessProductRecord && row.accessProductRecord.accessRate ? `${row.accessProductRecord.accessRate}%` : '' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="录入时间" align="center" prop="accessProductRecord.createTime" sortable width="150"></el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="{ row }">
          <el-button-group style="display: flex; gap: 10px;">
            <el-button
            type="text"
            size="small"
            @click="handleRelease(row)"
          >投放规则</el-button>
          <el-button
            type="text"
            size="small"
            @click="handleChannelLimit(row)"
          >渠道限制</el-button>
          <el-button
            type="text"
            size="small"
            @click="handleChannelProportion(row)"
          >渠道分布</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :pageSizes="[20, 30, 50, 100]"
      @pagination="getList"
    />
    <RuleDetail 
      v-model="releaseVisible"
      :id="currentId"
      :platformType="currentPlatformType"
      @close="handleCloseRelease"
    />
    <ChannelLimit
      v-model="channelLimitVisible"
      :id="currentId"
      :platformId="currentPlatformType"
      :productName="currentProductName"
      :channelList="channelList"
      :width="screenWidth ? '50%' : '95%'"
      @close="handleCloseChannelLimit"
      @submit="submitChannelLimit"
    />
    <ChannelProportion
      :visible.sync="channelProportionVisible"
      :dataList="channelProportionData"
    />
  </div>
</template>

<script>
import { getCityList, getNewPlatformProductList } from "@/api/statisticalManage";
import { getAllUser } from "@/api/system/user";
import { getPlatformList } from "@/api/xmxrChannelManage/channelList";
import { getSubPlatformChannels, updateSubPlatformChannel, saveAccessProductRecord } from "@/api/productManage/product";
import { getChannelProportion } from "@/api/xmxrChannelManage/channelProportion";
import RuleDetail from "./components/ruleDetail";
import ChannelLimit from "./components/ChannelLimit";
import ChannelProportion from "./components/ChannelProportion";
import dayjs from "dayjs";
import { t } from "element-ui/src/locale";

export default {
  components: {
    RuleDetail,
    ChannelLimit,
    ChannelProportion
  },
  data() {
    return {
      platformList: [],
      cityList: [],
      cityArr: [],
      dataList: [],
      businessUserList: [],
      dateRange: [
        dayjs().format('YYYY-MM-DD 00:00:00'),
        dayjs().format('YYYY-MM-DD 23:59:59')
      ],
      queryParams: {
        partyName: "",
        productName: "",
        city: "",
        productStatus: 1,
        phoneNumber: "",
        platformType: [],
        pageNum: 1,
        pageSize: 100,
      },
      total: 0,
      releaseVisible: false,
      currentId: "",
      currentPlatformType: "",
      channelLimitVisible: false,
      channelList: [],
      currentProductName: "",
      screenWidth: true,
      currentAccessNum: 0,
      channelProportionVisible: false,
      channelProportionData: []
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      if (this.cityArr[1]) {
        this.queryParams.city = this.cityArr[1];
      } else {
        this.queryParams.city = "";
      }
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
        this.$message.warning("请选择日期范围");
        return;
      }

      getNewPlatformProductList(this.queryParams).then((res) => {
        this.dataList = res.rows.map(item => {
          if (!item.accessProductRecord) {
            item.accessProductRecord = {
              uvNum: '',
              accessNum: '',
              accessPrice: '',
              assessProfit: '',
              accessRate: '',
              createTime: ''
            };
          }
          return item;
        });
        this.total = res.total;
      });
    },
    // 添加获取标签类型的方法
    getTagType(platformType) {
      // 预定义的基础颜色数组 - 适中的配色
      const baseColors = [
        '#52C41A',  // 清新绿
        '#4B7BE5',  // 优雅蓝
        '#FA8C16',  // 温暖橙
        '#F15A5A',  // 玫瑰红
        '#13A8A8',  // 青蓝色
        '#597EF7',  // 靛蓝色
        '#2F9688',  // 翠绿色
        '#E8943C',  // 琥珀色
        '#3B7EC9',  // 宝蓝色
        '#D48806'   // 赭石色
      ];
      
      // 使用平台ID作为索引来选择颜色
      const colorIndex = (platformType - 1) % baseColors.length;
      return baseColors[colorIndex];
    },
    getPlatformName(platformType) {
      const platform = this.platformList.find(item => item.id === platformType);
      return platform ? platform.name : '';
    },
    handleRelease(row) {
      this.currentId = row.productId;
      this.currentPlatformType = row.platformType;
      this.currentProductName = row.productName;
      this.releaseVisible = true;
    },
    handleCloseRelease() {
      this.releaseVisible = false;
      this.currentId = "";
      this.currentPlatformType = "";
      this.currentProductName = "";
    },
    // 打开渠道限制抽屉
    handleChannelLimit(row) {
      this.currentId = row.productId;
      this.currentPlatformType = row.platformType;
      this.currentProductName = row.productName;
      this.getChannelList(row.platformType);
      this.channelLimitVisible = true;
    },
    
    // 获取渠道列表
    getChannelList(platformId) {
      getSubPlatformChannels(platformId).then(res => {
        if (res.code === 200) {
          this.channelList = res.data || [];
        }
      });
    },
    
    // 提交渠道限制
    submitChannelLimit(form) {
      updateSubPlatformChannel(form).then(res => {
        if (res.code === 200) {
          this.$message.success("设置成功");
          this.channelLimitVisible = false;
          this.getList();
        }
      });
    },
    
    // 关闭渠道限制抽屉
    handleCloseChannelLimit() {
      this.channelLimitVisible = false;
      this.currentId = "";
      this.currentPlatformType = "";
      this.currentProductName = "";
      this.channelList = [];
    },
    handleAccessNumFocus(row) {
      this.currentAccessNum = row.accessProductRecord.accessNum;
    },
    handleAccessNumBlur(row) {
      if (row.accessProductRecord.accessNum == this.currentAccessNum) return;

      if (!row.accessProductRecord.accessNum) {
        row.accessProductRecord.accessNum = this.currentAccessNum || 0;
        return;
      }

      this.$confirm('确定修改接入数吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          productId: row.productId,
          productName: row.productName,
          platformType: row.platformType,
          uvNum: row.applyCount,
          accessNum: row.accessProductRecord.accessNum,
          matchingPriceSort: row.matchingPriceSort,
          lastSaveTime: row.accessProductRecord.createTime
        }
        const res = await saveAccessProductRecord(params)
        if (res.code == 200) {
          this.$message.success('更新成功')
          this.getList()
        }
      }).catch(() => {
        row.accessProductRecord.accessNum = this.currentAccessNum;
      })
    },
    handleChannelProportion(row) {
      this.currentId = row.productId;
      this.currentPlatformType = row.platformType;
      this.getChannelProportionData();
      this.channelProportionVisible = true;
    },
    getChannelProportionData() {
      const params = {
        platformType: this.currentPlatformType,
        productId: this.currentId,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1]
      };
      getChannelProportion(params).then(res => {
        if (res.code === 200) {
          this.channelProportionData = res.data || [];
        }
      });
    },
    handleCloseChannelProportion() {
      this.channelProportionVisible = false;
      this.currentId = "";
      this.currentPlatformType = "";
      this.channelProportionData = [];
    },
    canEditAccessNum(row) {     
      return !!(row.productId && 
               row.productName && 
               row.platformType && 
               row.applyCount && 
               row.matchingPriceSort);
    }
  },
  mounted() {
    this.screenWidth = document.body.clientWidth > 768 ? true : false;
    this.getList();
    getAllUser().then(res => {
      this.businessUserList = res.data || [];
    });
    getPlatformList().then(res => {
      this.platformList = res.data || [];
    });
    getCityList().then((res) => {
      let data = null;
      data = res.data.map((item) => {
        if (item.citys) {
          {
            return {
              value: item.code,
              label: item.name,
              disabled: false,
              children: [
                ...item.citys.map((citem) => {
                  return {
                    value: citem.code,
                    label: citem.name,
                    disabled: false,
                  };
                }),
              ],
            };
          }
        } else {
          return {
            value: item.code,
            label: item.name,
            disabled: false,
          };
        }
      });
      this.cityList = JSON.parse(JSON.stringify(data));
    });
  },
};
</script>

<style lang="scss">
input[aria-hidden="true"] {
  display: none !important;
}
</style>
