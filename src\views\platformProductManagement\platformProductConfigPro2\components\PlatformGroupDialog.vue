<template>
  <el-dialog 
    :title="title" 
    :visible.sync="dialogVisible" 
    width="1200px" 
    @close="handleClose"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <el-form 
        :model="form" 
        :rules="rules" 
        ref="form" 
        label-position="top"
      >
        <el-form-item label="选择产品" prop="selectedProducts">
          <Transfer
            filterable
            v-model="form.selectedProducts"
            :data="productList"
            :titles="['待选产品', '已选产品']"
            :props="{
              key: 'key',
              label: 'label',
              disabled: 'disabled'
            }"
            :show-all-check="false"
          ></Transfer>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSave">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
import Transfer from '@/components/transfer/main.vue'

export default {
  name: 'PlatformGroupDialog',

  components: {
    Transfer
  },

  props: {
    // 弹窗是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗标题
    title: {
      type: String,
      default: '新增分组'
    },
    // 产品列表数据
    productList: {
      type: Array,
      default: () => []
    },
    // 编辑时的初始数据
    editData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      dialogVisible: false,
      form: {
        name: '',
        renderKey: '',
        selectedProducts: [],
        sort: 0
      },
      rules: {
        selectedProducts: [
          { required: true, message: '请至少选择一个产品', trigger: 'blur' }
        ]
      }
    }
  },

  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    },
    editData: {
      handler(val) {
        if (Object.keys(val || {}).length) {
          // 编辑现有分组
          this.form = {
            name: val.groupName || '',
            renderKey: val.renderKey || uuidv4(),
            // 使用预处理的 key 字段
            selectedProducts: (val.productList || []).map(item => {
              if (!item.platformType || !item.productId) {
                console.warn('产品数据缺少必要字段:', item);
                return null;
              }
              return `${item.platformType}-${item.productId}`;
            }).filter(key => key !== null),
            sort: val.sort || 0
          }
        } else {
          // 创建新分组
          this.form = {
            name: '',
            renderKey: uuidv4(),
            selectedProducts: [],
            sort: 0
          }
        }
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    // 处理保存
    handleSave() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const formData = {
            renderKey: this.form.renderKey,
            groupName: this.editData.groupName || '',
            productList: this.form.selectedProducts.map(combinedKey => {
              const product = this.productList.find(item => item.key === combinedKey);
              if (!product) {
                console.error('找不到对应的产品数据:', combinedKey);
                return null;
              }
              return {
                platform: product.platform,
                platformType: product.platformType,
                productName: product.productName,
                productId: product.productId
              }
            }).filter(item => item !== null),
            sort: this.form.sort
          }
          // 等待父组件的处理结果
          this.$emit('save', formData, (success) => {
            if (success) {
              this.dialogVisible = false
            }
          })
        }
      })
    },

    // 处理关闭
    handleClose() {
      this.$refs.form?.resetFields()
      this.form = {
        name: '',
        renderKey: uuidv4(),
        selectedProducts: [],
        sort: 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0;

  ::v-deep .el-transfer {
    display: flex;
    justify-content: center;
    align-items: center;

    .el-transfer-panel {
      flex: 1;
    }
  }
}
</style> 