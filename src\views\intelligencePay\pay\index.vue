<template>
    <div class="app-container">
        <el-form>
            <el-form-item>
                <el-button type="primary" @click="handleAdd">新增</el-button>
                <el-button type="primary" @click="handleBaseInfo">补充基本信息</el-button>
                <el-button type="primary" @click="handleContact">修改联系方式</el-button>
                <el-button type="primary" @click="handleSettlement">修改结算信息</el-button>
                <el-button type="primary" @click="handleCheck">分账账号提交审核</el-button>
                <el-button type="primary" @click="handleUploadFile">账号上传图片
                </el-button>
                <el-button type="primary" @click="handleStatus">查询审核状态
                </el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableList">

            <el-table-column label="手机号" align="center" prop="phone" />
            <el-table-column label="商户编号" align="center" prop="merchantCode" />
            <el-table-column label="分账商户ID" align="center" prop="subMerchantId" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="创建时间" align="center" prop="createTime">
                <template slot-scope="{row}">
                    <div>
                        {{ codeType[row.status] }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="用户名称" align="center" prop="userName" />
        </el-table>
        <el-dialog title="新增账号" :visible.sync="addShow" width="600px" @close="cancelAdd" append-to-body>
            <el-form ref="addFormData" :model="accountFormData" :rules="addrules" @submit.native.prevent
                label-width="100px">

                <el-form-item label="手机号" prop="mobile">
                    <el-input v-model="accountFormData.mobile" maxlength="11"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitAddForm">确 定</el-button>
                <el-button @click="cancelAdd">取 消</el-button>
            </div>
        </el-dialog>
        <el-dialog title="补充基本信息" :visible.sync="baseShow" width="1200px" :close-on-click-modal="false" append-to-body>
            <el-form label-width="150px" ref="baseForm" :model="baseFormData" :rules="baseAddRules">
                <el-row>
                    <el-col :span="12">

                        <el-form-item label="详细地址" prop="address">
                            <el-input v-model="baseFormData.address" placeholder="详细地址"></el-input>
                        </el-form-item>

                        <el-form-item label="城市" prop="proviceId">
                            <el-cascader :props="cascader_props" @change="handleCityChange" placeholder="城市" clearable>
                            </el-cascader>
                        </el-form-item>
                        <el-form-item label="补充资料" prop="branchSupplementData">
                            <el-input v-model="baseFormData.branchSupplementData" placeholder="补充资料"></el-input>
                        </el-form-item>
                        <el-form-item label="证件有效期" prop="certExpdate">
                            <el-input v-model="baseFormData.certExpdate" placeholder="证件有效期"> </el-input>
                        </el-form-item>
                        <el-form-item label="证件号码" prop="certNo">
                            <el-input v-model="baseFormData.certNo" placeholder="证件号码"></el-input>
                        </el-form-item>
                        <el-form-item label="证件类型" prop="certType">
                            <el-input v-model="baseFormData.certType" placeholder="证件类型"></el-input>
                        </el-form-item>
                        <el-form-item label="商家全称" prop="companyName">
                            <el-input v-model="baseFormData.companyName" placeholder="商家全称"></el-input>
                        </el-form-item>
                        <el-form-item label="法人姓名" prop="corporation">
                            <el-input v-model="baseFormData.corporation" placeholder="法人姓名"></el-input>
                        </el-form-item>
                        <el-form-item label="ICP备案号" prop="icpNo">
                            <el-input v-model="baseFormData.icpNo" placeholder="ICP备案号"></el-input>
                        </el-form-item>
                        <el-form-item label="手持证件照片" prop="idcardHoldPic">
                            <el-input v-model="baseFormData.idcardHoldPic" placeholder="手持证件照片（个体工商户必传）"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="手持证件照片(反)" prop="idcardNegativePic">
                            <el-input v-model="baseFormData.idcardNegativePic"
                                placeholder="证件反面，传入上传图片接口中智付返回的媒体文件标识Id">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="手持证件照片(正)" prop="idcardPositivePic">
                            <el-input v-model="baseFormData.idcardPositivePic"
                                placeholder="证件正面，传入上传图片接口中智付返回的媒体文件标识Id">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="统一社会信用代码" prop="license">
                            <el-input v-model="baseFormData.license" placeholder="统一社会信用代码"></el-input>
                        </el-form-item>
                        <el-form-item label="工商营业执照有效期" prop="licenseExpdate">
                            <el-input v-model="baseFormData.licenseExpdate" placeholder="工商营业执照有效期"></el-input>
                        </el-form-item>
                        <el-form-item label="营业执照图片" prop="licensePic">
                            <el-input v-model="baseFormData.licensePic" placeholder="营业执照图片"></el-input>
                        </el-form-item>
                        <el-form-item label="商家性质" prop="merchantType">
                            <el-input v-model="baseFormData.merchantType" placeholder="商家性质：0：企业 1：个人 2：个体工商户">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="商户简称" prop="shortName">
                            <el-input v-model="baseFormData.shortName" placeholder="商户简称">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="经营场所门头照" prop="storeHeadPic">
                            <el-input v-model="baseFormData.storeHeadPic" placeholder="经营场所门头照">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="经营场所店内照" prop="storeInnerPic">
                            <el-input v-model="baseFormData.storeInnerPic" placeholder="经营场所店内照">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="子商户id" prop="subMerchantId">
                            <el-input v-model="baseFormData.subMerchantId" placeholder="子商户id">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="网站名称" prop="webstationName">
                            <el-input v-model="baseFormData.webstationName" placeholder="网站名称">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="网站网址" prop="webstationUrl">
                            <el-input v-model="baseFormData.webstationUrl" placeholder="网站网址">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitBaseForm">确 定</el-button>
                <el-button @click="cancelBase">取 消</el-button>
            </div>
        </el-dialog>
        <el-dialog title="修改联系方式" :visible.sync="contactShow" width="600px" append-to-body
            :close-on-click-modal="false">
            <el-form label-width="100px" ref="contactForm" :model="contactFormData" :rules="contactRules">

                <el-form-item label="联系人手机" prop="contactsMobile">
                    <el-input v-model="contactFormData.contactsMobile" maxlength="11" placeholder="联系人手机"></el-input>
                </el-form-item>

                <el-form-item label="联系人姓名" prop="contactsName">
                    <el-input v-model="contactFormData.contactsName" placeholder="联系人姓名"></el-input>
                </el-form-item>

                <el-form-item label="子商户id" prop="subMerchantId">
                    <el-input v-model="contactFormData.subMerchantId" placeholder="子商户id"></el-input>
                </el-form-item>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitContactForm">确 定</el-button>
                <el-button @click="contactShow = false">取 消</el-button>
            </div>
        </el-dialog>
        <el-dialog title="修改结算信息" :visible.sync="settlementShow" width="600px" append-to-body
            :close-on-click-modal="false">
            <el-form label-width="130px" ref="settlementForm" :model="settlement">

                <el-form-item label="账户类型" prop="accountType" required>
                    <el-input v-model="settlement.accountType" placeholder="账户类型：1企业；0个人"></el-input>
                </el-form-item>
                <el-form-item label="子商户id" prop="subMerchantId" required>
                    <el-input v-model="settlement.subMerchantId" placeholder="子商户id"></el-input>
                </el-form-item>

                <el-form-item label="银行卡号" prop="bankCard" required>
                    <el-input v-model="settlement.bankCard" placeholder="银行卡号(RSA-S加密传输,使用加密业务的公钥签名)"></el-input>
                </el-form-item>

                <el-form-item label="户名" prop="bankCardName" required>
                    <el-input v-model="settlement.bankCardName" placeholder="户名(RSA-S加密传输,使用加密业务的公钥签名)"></el-input>
                </el-form-item>

                <el-form-item label="银行code" prop="bankCode" required>
                    <el-select v-model="settlement.bankCode" clearable filterable remote style="width: 100%"
                        reserve-keyword placeholder="请搜索银行code" :remote-method="remoteMethod" :loading="loading">
                        <el-option v-for="item in bankCodeOptions" :key="item.bankCode" :label="item.bankName"
                            :value="item.bankCode" />
                    </el-select>
                </el-form-item>
                <el-form-item label="结算账户证明" prop="pictureInfo" required>
                    <el-input v-model="settlement.pictureInfo"
                        placeholder="结算账户证明：对公账户传 开户许可证/基本存款账户 信息照片，对私账户传银行 卡正面照片（多张以竖线分割 例如 aaaa|bbbb|cccc）">
                    </el-input>
                </el-form-item>
                <el-form-item label="支行联行号" prop="uniteBankId" required>
                    <el-select v-model="settlement.uniteBankId" clearable filterable remote style="width: 100%"
                        reserve-keyword placeholder="支行联行号(账户类型为企业必传) （请参考附录-支行联行号.XLSX）"
                        :remote-method="remoteMethodUniteBank">
                        <el-option v-for="item in uniteBankIdOptions" :key="item.id" :label="item.name"
                            :value="item.id" />
                    </el-select>
                </el-form-item>



            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitSettlementForm">确 定</el-button>
                <el-button @click="settlementShow = false">取 消</el-button>
            </div>
        </el-dialog>
        <el-dialog title="分账账号提交审核" :visible.sync="checkShow" width="600px" :close-on-click-modal="false"
            append-to-body>
            <el-form ref="checkForm" :model="subledgerFormData" label-width="100px">

                <el-form-item label="子商户id" prop="subMerchantId" required>
                    <el-input v-model="subledgerFormData.subMerchantId" placeholder="子商户id"></el-input>
                </el-form-item>
                <el-form-item label="登录用户名" prop="userName" required>
                    <el-input v-model="subledgerFormData.userName" placeholder="登录用户名"></el-input>
                </el-form-item>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitCheckForm">确 定</el-button>
                <el-button @click="checkShow = false">取 消</el-button>
            </div>
        </el-dialog>
        <el-dialog title="账号上传图片" :visible.sync="UploadShow" width="600px" append-to-body :close-on-click-modal="false">
            <el-form label-width="100px">

                <el-form-item label="子商户id" prop="subMerchantId">
                    <el-input v-model="uploadFormData.subMerchantId" placeholder="子商户id"></el-input>
                </el-form-item>
                <el-form-item label="图片">
                    <el-upload class="avatar-uploader" action="" :show-file-list="false" :auto-upload="false"
                        :on-change="changeUpImg">
                        <img v-if="imageUrl" style="vertical-align: middle; max-height: 100px" :src="imageUrl" />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </el-form-item>
                {{ messageInfoID }}
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitUploadForm">确 定</el-button>
                <el-button @click="cancelUpload">取 消</el-button>
            </div>
        </el-dialog>
        <el-dialog title="查询审核状态" :visible.sync="statusShow" width="600px" append-to-body :close-on-click-modal="false">
            <el-form label-width="100px">

                <el-form-item label="子商户id" prop="subMerchantId">
                    <el-input v-model="subMerchantId" placeholder="子商户id"></el-input>
                </el-form-item>

                状态码：{{ codeType[checkInfo] }}
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitStatusForm">查询</el-button>

            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    getStbAccountList,
    stbMerchantRegister,
    addStbMerchantBasic,
    getCity,
    editStbUpload,
    editStbContactMer,
    getBankList,
    getUniteBankList,
    eidtStbSettleMer,
    checkAccount,
    getSubmitMerStatus
} from "@/api/intelligencePay"
export default {

    data() {
        const _this = this;
        return {
            imageUrl: "",
            subMerchantId: "",
            codeType: {
                "00": "待审核",
                "01": "审核中",
                "02": "审核通过",
                "03": "审核未通过",
                "04": "其他状态",
            },
            tableList: [],
            addShow: false,
            baseShow: false,
            contactShow: false,
            settlementShow: false,
            checkShow: false,
            UploadShow: false,
            loading: false,
            statusShow: false,
            messageInfoID: "",
            checkInfo: "",
            provinceOptions: [],
            cityOptions: [],
            areaOptions: [],
            bankCodeOptions: [],
            uniteBankIdOptions: [],
            accountFormData: {
                mobile: ""
            },
            contactFormData: {
                "contactsMobile": "",
                "contactsName": "",
                "subMerchantId": ""
            },
            settlement: {
                "accountType": "",
                "bankCard": "",
                "bankCardName": "",
                "bankCode": "",
                "pictureInfo": "",
                "subMerchantId": "",
                "uniteBankId": "",
            },
            subledgerFormData: {
                "subMerchantId": "",
                "userName": ""
            },
            uploadFormData: {
                file: "",
                subMerchantId: ""
            },
            cascader_props: {
                lazy: true,
                lazyLoad(node, resolve) {
                    const { level } = node;
                    getCity({ parentCode: node.value }).then(res => {
                        const data = res.data;

                        if (level == 0) {
                            _this.provinceOptions = res.data
                        }
                        if (level == 1) {
                            _this.cityOptions = res.data
                        }
                        if (level == 2) {
                            _this.areaOptions = res.data
                        }
                        data.forEach(item => {
                            item.value = item.code;
                            item.label = item.name;
                            // 最后一层选择
                            if (level === 2) { item.leaf = level >= 2; }
                        })
                        resolve(data);
                    })
                }
            },
            baseFormData: {
                "address": "",
                "areaId": "",
                "branchSupplementData": "",
                "certExpdate": "",
                "certNo": "",
                "certType": "",
                "cityId": "",
                "companyName": "",
                "corporation": "",
                "icpNo": "",
                "idcardHoldPic": "",
                "idcardNegativePic": "",
                "idcardPositivePic": "",
                "license": "",
                "licenseExpdate": "",
                "licensePic": "",
                "merchantType": "",
                "proviceId": "",
                "shortName": "",
                "storeHeadPic": "",
                "storeInnerPic": "",
                "subMerchantId": "",
                "webstationName": "",
                "webstationUrl": ""
            },
            addrules: {
                mobile: [
                    { required: true, trigger: "blur", message: "请输入你的手机号" },
                ],
            },
            contactRules: {
                contactsMobile: [
                    { required: true, trigger: "blur", message: "请输入你的手机号" },
                ],
                contactsName: [
                    { required: true, trigger: "blur", message: "请输入姓名" },
                ],
                subMerchantId: [
                    { required: true, trigger: "blur", message: "请输入ID" },
                ],
            },
            baseAddRules: {
                subMerchantId: [
                    { required: true, trigger: "blur", message: "请输入子商户ID" },
                ],
                merchantType: [{
                    required: true, trigger: "blur", message: "请输入商家性质"
                }],
                licensePic: [{
                    required: true, trigger: "blur", message: "请输入营业执照图片"
                }],
                storeHeadPic: [{
                    required: true, trigger: "blur", message: "请输入经营场所门头照"
                }],
                storeInnerPic: [{
                    required: true, trigger: "blur", message: "请输入经营场所店内照"
                }],
                companyName: [{
                    required: true, trigger: "blur", message: "请输入商家全称"
                }],
                shortName: [{
                    required: true, trigger: "blur", message: "请输入商家简称"
                }],
                address: [{
                    required: true, trigger: "blur", message: "请输入详细地址"
                }],
                license: [{
                    required: true, trigger: "blur", message: "请输入统一社会信用代码"
                }],
                licenseExpdate: [{
                    required: true, trigger: "blur", message: "请输入工商营业执照有效期"
                }],
                corporation: [{
                    required: true, trigger: "blur", message: "请输入法人姓名"
                }],
                certType: [{
                    required: true, trigger: "blur", message: "请输入证件类型 "
                }],
                certNo: [{
                    required: true, trigger: "blur", message: "请输入证件号码 "
                }],

                certExpdate: [{
                    required: true, trigger: "blur", message: "请输入证件有效期 "
                }],
                idcardPositivePic: [{
                    required: true, trigger: "blur", message: "请输入证件正面 "
                }],
                idcardNegativePic: [{
                    required: true, trigger: "blur", message: "请输入证件反面 "
                }],
                proviceId: [{
                    required: true, trigger: "blur", message: "请选择省市区"
                }],

            }
        }
    },
    methods: {
        handleAdd() {
            this.addShow = true
        },
        submitAddForm() {
            this.$refs.addFormData.validate((valid) => {
                if (valid) {
                    stbMerchantRegister(this.accountFormData).then(res => {
                        this.getList()
                        this.cancelAdd()
                        this.$message.success("新增成功")
                    })
                }
            })
        },
        submitUploadForm() {
            let data = new FormData()
            for (let k in this.uploadFormData) {
                data.append(k, this.uploadFormData[k])
            }
            editStbUpload(data).then(res => {
                this.messageInfoID = res.data
                this.$message.success("操作成功")
            })
        },
        submitBaseForm() {
            this.$refs.baseForm.validate((valid) => {
                if (valid) {
                    addStbMerchantBasic(this.baseFormData).then(res => {
                        this.$message.success("操作成功")
                        this.cancelBase()
                    })
                }
            })
        },
        submitContactForm() {
            this.$refs.contactForm.validate((valid) => {
                if (valid) {
                    editStbContactMer(this.contactFormData).then(res => {
                        this.$message.success("操作成功")
                        this.contactShow = false
                    })
                }
            })
        },
        submitSettlementForm() {
            this.$refs.settlementForm.validate((valid) => {
                if (valid) {
                    eidtStbSettleMer(this.settlement).then(res => {
                        this.$message.success("操作成功")
                        this.settlementShow = false
                    })
                }
            })
        },
        submitCheckForm() {
            this.$refs.checkForm.validate((valid) => {
                if (valid) {
                    checkAccount(this.subledgerFormData).then(res => {
                        this.$message.success("操作成功")
                        this.checkShow = false
                    })
                }
            })
        },
        submitStatusForm() {
            getSubmitMerStatus({ subMerchantId: this.subMerchantId }).then(res => {

                this.checkInfo = res.data
            })
        },
        cancelAdd() {
            this.addShow = false
            this.accountFormData = {
                "mobile": ""
            }
        },

        cancelBase() {
            this.baseFormData = {
                "address": "",
                "areaId": "",
                "branchSupplementData": "",
                "certExpdate": "",
                "certNo": "",
                "certType": "",
                "cityId": "",
                "companyName": "",
                "corporation": "",
                "icpNo": "",
                "idcardHoldPic": "",
                "idcardNegativePic": "",
                "idcardPositivePic": "",
                "license": "",
                "licenseExpdate": "",
                "licensePic": "",
                "merchantType": "",
                "proviceId": "",
                "shortName": "",
                "storeHeadPic": "",
                "storeInnerPic": "",
                "subMerchantId": "",
                "webstationName": "",
                "webstationUrl": ""
            }
            this.baseShow = false
            this.$refs.baseForm.resetFields()
        },
        cancelUpload() {
            this.UploadShow = false
            this.imageUrl = ""
            this.uploadFormData = {
                file: "",
                subMerchantId: ""
            }
            this.messageInfoID = ""
        },
        handleBaseInfo() {
            this.baseShow = true

        },
        handleCityChange(e) {
            if (!e.length) {
                this.baseFormData.proviceId = ""
                this.baseFormData.cityId = ""
                this.baseFormData.areaId = ""
                return
            }
            this.baseFormData.proviceId = e[0]
            this.baseFormData.cityId = e[1]
            this.baseFormData.areaId = e[2]
        },
        handleContact() {
            this.contactShow = true
        },
        handleSettlement() {
            this.settlementShow = true
        },
        handleCheck() {
            this.checkShow = true
        },
        handleStatus() {
            this.statusShow = true
        },
        changeUpImg(e) {
            const isJPG =
                e.raw.type === "image/jpeg" ||
                e.raw.type === "image/jpg" ||
                e.raw.type === "image/png";
            const isLt2M = e.size / 1024 / 1024 < 5;

            if (!isJPG) {
                this.$message.error("上传头像图片只能是 JPG/JPEG/PNG 格式!");
                return;
            }
            if (!isLt2M) {
                this.$message.error("上传头像图片大小不能超过 2MB!");
                return;
            }

            this.imageUrl = URL.createObjectURL(e.raw);
            this.uploadFormData.file = e.raw
        },
        handleUploadFile() {
            this.UploadShow = true
        },
        remoteMethod(e) {
            getBankList({
                bankName: e
            }).then(res => {

                this.bankCodeOptions = res.data
            })
        },
        remoteMethodUniteBank(e) {
            getUniteBankList({ name: e }).then(res => {

                this.uniteBankIdOptions = res.data
            })
        },
        getList() {
            getStbAccountList().then(res => {
                this.tableList = res.data
            })
        }
    },
    mounted() {
        this.getList()
    }
}
</script>

<style lang="scss" scoped>
::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    // border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

::v-deep .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
}
</style>
