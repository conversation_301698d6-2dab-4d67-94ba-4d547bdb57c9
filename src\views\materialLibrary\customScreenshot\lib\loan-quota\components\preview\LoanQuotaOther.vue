<template>
  <div class="loan-quota-other" v-if="configData.otherQuotas && configData.otherQuotas.length > 0">
    <div class="other-header">
      其他额度
    </div>
    <div class="other-content">
      <div class="other-item" v-for="(item, index) in configData.otherQuotas" :key="index">
        <div class="other-item-label">
          <img class="other-item-label-icon" :src="item.iconUrl" alt="">
          <div class="other-item-label-text">{{ item.name }}</div>
        </div>
        <div class="other-item-value">
          <div class="other-item-value-text">{{ item.desc }}</div>
          <img class="other-item-value-icon" src="https://jst.oss-utos.hmctec.cn/common/path/6915502722fc4387b7c17b0d0e894616.png" alt="">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: '<PERSON>anQuotaOther',
  props: {
    configData: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped lang="scss">
.loan-quota-other {
  margin: 0 auto 16px;
  padding: 24px 0 3px;
  width: 695px;
  // height: 375px;
  background: #FFFFFF;
  border-radius: 20px 20px 20px 20px;

  .other-header {
    padding: 0 20px;
    margin-bottom: 25px;
    font-weight: 500;
    font-size: 32px;
    color: #333333;
    line-height: 46px;
  }

  .other-content {
    padding: 0 20px;

    .other-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 25px 0;
      border-bottom: 1px solid #F3F3F3;

      &:last-child {
        border-bottom: none;
      }

      .other-item-label {
        display: flex;
        align-items: center;
        gap: 15px;

        .other-item-label-icon {
          width: 40px;
          height: 40px;
        }

        .other-item-label-text {
          font-weight: 400;
          font-size: 28px;
          color: #333333;
          line-height: 41px;
        }
      }

      .other-item-value {
        display: flex;
        align-items: center;
        gap: 10px;

        .other-item-value-text {
          font-weight: 400;
          font-size: 28px;
          color: #9F9F9F;
          line-height: 41px;
        }

        .other-item-value-icon {
          width: 15px;
          height: 23.5px;
          transform: translateY(3px);
        }
      }
    }
  }
}
</style> 