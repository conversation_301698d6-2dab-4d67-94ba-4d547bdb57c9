<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="展位名称" prop="name">
        <el-input size="small" clearable placeholder="请输入展位名称" @keyup.enter.native="handleQuery"
          v-model="queryParams.name"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" size="small" clearable @change="handleQuery">
          <el-option label="启用" :value="0"></el-option>
          <el-option label="禁用" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" v-hasPermi="['loan:product_package:query']"
          @click="handleQuery">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" size="mini" v-hasPermi="['loan:product_package:add']"
          @click="handleadd">新增展位</el-button>
      </el-form-item>
    </el-form>
    <el-table  :data="packageList" :row-key="(row) => row.id">

      <el-table-column label="展位名称" prop="name" align="center" />
      <el-table-column label="状态" prop="status1" width="140" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-switch v-hasPermi="['loan:product_package:update_status']" v-model="row.status"
              :active-value="0" :inactive-value="1" active-text="启用" inactive-text="禁用"
              @change="changePackageStatus($event, row.id)">
            </el-switch>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="广告投放展位期限" prop="cycle" align="center" />
      <el-table-column label="单日订单咨询量" prop="maxDayNum" align="center" />
      <el-table-column label="展位投放价格" prop="totalPrice" align="center" />
      <el-table-column label="最大订单咨询量" prop="maxTotalNum" align="center" />
      <!-- <el-table-column label="投放城市" prop="citys" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button v-hasPermi="['loan:product_package:query_city']" @click="getPackCity(row)" type="text">查看城市
            </el-button>
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <span class="f_c005 c-p"  v-hasPermi="['loan:product_package:update']"
              @click="handleEdit(row)">修改</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog :title="isadd ? '新增CPD展位设置' : '修改CPD展位设置'" :visible.sync="packageAvisible" @close="cancle" :width="screenWidth ? '814px' : '90%'"
      append-to-body center :close-on-click-modal="false">
      <el-form ref="formData" :model="formData" :rules="rules" label-width="140px">
        <el-row :gutter="24">
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="展位名称" prop="name">
              <el-input size="small" maxlength="20" placeholder="请输入展位名称" v-model="formData.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="广告投放展位期限" prop="cycle">
              <el-input size="small" maxlength="2" oninput="value=value.replace(/[^0-9]/g,'')"
                v-model.number="formData.cycle" placeholder="请输入1-31之间"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="单日订单咨询量" prop="maxDayNum">
              <el-input size="small" maxlength="5" oninput="value=value.replace(/[^0-9]/g,'')"
                v-model.number="formData.maxDayNum" placeholder="请输入单日最大量"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="最大订单咨询量" prop="maxTotalNum">
              <el-input size="small" maxlength="5" oninput="value=value.replace(/[^0-9]/g,'')"
                v-model.number="formData.maxTotalNum" placeholder="请输入最大订单咨询量"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="展位投放价格" prop="totalPrice">
              <el-input size="small" maxlength="6" oninput="value=value.replace(/[^0-9]/g,'')"
                v-model.number="formData.totalPrice" placeholder="请输入展位投放价格"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="投放城市">
              <BaseCascader class="cascader" :options="cityList" :is_deep="true" :isDisable="false" :has_all_select="true"
                @getOptions="getCityCodes" :back_options="formData.cityCodes" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancle">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="packName + '的下属城市'" :visible.sync="cityAvisible" width="600px" append-to-body center
      @close="cityCancle" :close-on-click-modal="false">
      <div v-if="packCity.length">
        <el-tag v-for="item in packCity" :key="item" style="margin: 10px">{{
            item
        }}</el-tag>
      </div>
      <div class="noData" v-else>暂无数据</div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPackageList,
  getFlowCityAll,
  addPackagetOne,
  getPackgetOne,
  changePackagetOne,
  editPackagetOne,
  getPackCity,
} from "@/api/productManage/product";
import BaseCascader from "@/components/cascader";
export default {
  name: "",
  data() {
    var imgCycle = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入周期或输入不合法"));
      } else if (this.formData.cycle * 1 < 1 || this.formData.cycle * 1 > 31) {
        callback(new Error("已经超出范围"));
      } else {
        callback();
      }
    };
    return {
      packageAvisible: false,
      isadd: true,
      screenWidth: true,
      cityAvisible: false,
      cityList: [],
      packageList: [],
      packCity: [],
      total: 0,
      packName: "",
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: "",
        status: "",
      },
      formData: {
        name: "",
        cycle: "",
        maxDayNum: "",
        maxTotalNum: "",
        totalPrice: "",
        cityCodes: [],
      },
      rules: {
        name: [{ required: true, message: "请输入展位名称", trigger: "blur" }],
        cycle: [{ required: true, validator: imgCycle, trigger: "blur" }],
        maxDayNum: [
          { required: true, message: "请输入每日订单咨询量", trigger: "blur" },
        ],
        maxTotalNum: [
          { required: true, message: "请输入最大订单咨询量", trigger: "blur" },
        ],
        totalPrice: [
          { required: true, message: "请输入展位投放价格", trigger: "blur" },
        ],
      },
    };
  },

  components: {
    BaseCascader,
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    //新增
    handleadd() {
      this.isadd = true;
      this.packageAvisible = true;
      this.getCity();
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    //获取城市code
    getCityCodes(e) {
      this.formData.cityCodes = e;
    },
    //提交
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.cityCodes = this.formData.cityCodes.map((item) => {
            if (item.length > 1) {
              return item[1];
            } else {
              return item[0];
            }
          });
          if (this.isadd) {
            addPackagetOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.getList();
                this.cancle();
              }
            });
          } else {
            editPackagetOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.getList();
                this.cancle();
              }
            });
          }
        }
      });
    },
    //查看城市
    getPackCity(row) {
      getPackCity(row.id).then((res) => {
        this.cityAvisible = true;
        this.packName = row.name;
        this.packCity = res.data.map((item) => item.name);
      });
    },
    cancle() {
      this.formData = {
        name: "",
        cycle: "",
        maxDayNum: "",
        maxTotalNum: "",
        totalPrice: "",
        cityCodes: [],
      };
      this.packageAvisible = false;
      this.$refs.formData.resetFields();
    },
    //取消查看城市
    cityCancle() {
      this.packName = "";
      this.packCity = [];
      this.cityAvisible = false;
    },
    //编辑
    handleEdit(row) {
      this.isadd = false;
      this.getCity(row);
    },
    //修改状态
    changePackageStatus(e, id) {
      this.$confirm("是否修流量包状态?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          changePackagetOne({ status: e, id }).then((res) => {
            if (res.code == 200) {
              this.getList();
              this.$message.success("更新成功");
            }
          });
        })
        .catch(() => {
          this.getList();
        });
    },
    getCity(row) {
      getFlowCityAll().then((res) => {
        let data = null;
        data = res.data.map((item) => {
          if (item.citys) {
            {
              return {
                value: item.code,
                label: item.name,
                children: [
                  ...item.citys.map((citem) => {
                    return {
                      value: citem.code,
                      label: citem.name,
                    };
                  }),
                ],
              };
            }
          } else {
            return {
              value: item.code,
              label: item.name,
            };
          }
        });
        this.cityList = JSON.parse(JSON.stringify(data));
        if (this.isadd) return;
        getPackgetOne(row.id).then((res) => {
          this.packageAvisible = true;
          this.formData.id = row.id;
          this.formData.name = row.name;
          this.formData.cycle = row.cycle;
          this.formData.maxDayNum = row.maxDayNum;
          this.formData.maxTotalNum = row.maxTotalNum;
          this.formData.totalPrice = row.totalPrice;
          if (res.data.cityCodes[0] != 1) {
            let arr = [];
            res.data.cityCodes.forEach((item) => {
              this.cityList.forEach((i) => {
                if (i.children) {
                  i.children.forEach((citem) => {
                    if (citem.value == item) {
                      arr = [...arr, [i.value, item]];
                    }
                  });
                }
              });
            });

            this.formData.cityCodes = arr;
          } else {
            this.formData.cityCodes = [[1]];
          }
        });
      });
    },

    getList() {
      getPackageList(this.queryParams).then((res) => {
        this.packageList = res.rows;
        this.total = res.total;
      });
    },
  },
  mounted() {
    this.getList();
    this.screenWidth = document.body.clientWidth > 768 ? true : false;
  },
};
</script>

<style lang="scss" scoped>
.noData {
  text-align: center;
}
</style>
