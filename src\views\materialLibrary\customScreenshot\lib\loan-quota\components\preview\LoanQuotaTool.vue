<template>
  <div class="loan-quota-tool">
    <div class="tool-header">
      额度工具
    </div>
    <div class="tool-content">
      <div class="tool-item">
        <img class="tool-item-icon"
          src="https://jst.oss-utos.hmctec.cn/common/path/c3e06798e0834da4a69d79700ce66fc7.png" alt="">
        <div class="tool-item-label">
          使用明细
        </div>
      </div>
      <div class="tool-item">
        <img class="tool-item-icon"
          src="https://jst.oss-utos.hmctec.cn/common/path/dd4c76647c1049668af431c2d4c33cd3.png" alt="">
        <div class="tool-item-label">
          额度记录
        </div>
      </div>
      <div class="tool-item">
        <img class="tool-item-icon"
          src="https://jst.oss-utos.hmctec.cn/common/path/18b04072bbd04b85b97b808d6051ae36.png" alt="">
        <div class="tool-item-label">
          额度管理
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoanQuotaTool'
}
</script>

<style scoped lang="scss">
.loan-quota-tool {
  margin: 0 auto 16px;
  padding: 25px 0;
  width: 695px;
  height: 233px;
  background: #FFFFFF;
  border-radius: 20px 20px 20px 20px;

  .tool-header {
    padding: 0 20px;
    margin-bottom: 53px;
    font-weight: 500;
    font-size: 32px;
    color: #333333;
    line-height: 46px;
  }

  .tool-content {
    padding: 0 77px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;

    .tool-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .tool-item-icon {
        width: 38px;
        height: 38px;
      }

      .tool-item-label {
        font-weight: 400;
        font-size: 26px;
        color: #333333;
        line-height: 38px;
      }
    }
  }
}
</style> 