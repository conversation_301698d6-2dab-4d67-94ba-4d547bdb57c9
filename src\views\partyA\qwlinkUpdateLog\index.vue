<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px" size="small">
      <el-form-item label="平台类型" prop="platformType"
                    :rules="{required: true, message: '平台不能为空', trigger: 'blur'}">
        <el-select v-model="queryParams.platformType" placeholder="请选择平台类型" clearable>
          <el-option v-for="item in platformList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="产品名称" prop="productId" :rules="{required: true, message: '产品名字不能为空', trigger: 'blur'}">
        <el-select v-model="queryParams.productId" placeholder="请输入产品名称"  filterable @focus="queryProductName()"  clearable >
          <el-option
            v-for="item in productNameList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
            <span style="float: left">{{ item.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="rebateList"  border style="width: 100%" size="small" :default-expand-all="true">
      <el-table-column  align="center" type="expand">
        <template slot-scope="scope">
          <el-form label-position="left" inline class="demo-table-expand">
            <el-table :data="scope.row.updateRecords" border stripe style="width: 100%">
              <el-table-column type="index" />
              <el-table-column prop="productId" label="产品ID" />
              <el-table-column prop="productName" label="产品名称" />
              <el-table-column prop="beforeLink" label="旧链接" />
              <el-table-column prop="updatedLink" label="新链接" />
              <el-table-column prop="createTime" label="时间" />
              <el-table-column prop="createBy" label="修改者" />
            </el-table>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column label="平台类型" align="center" prop="platformType">
        <template v-slot="{ row }">
          {{ formatPlatformName(row.platformType) }}
        </template>
      </el-table-column>
      <el-table-column label="产品ID" align="center" prop="productId" />
      <el-table-column label="产品名字" align="center" prop="productName" />
      <el-table-column label="最后修改时间" align="center" prop="latestUpdateTime" />
      <el-table-column label="修改次数" align="center" prop="updateCount"/>
    </el-table>

<!--    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"-->
<!--      @pagination="getList" />-->

    <!-- 查看明细弹窗 -->
  </div>
</template>

<script>
import {getQwlinkUpdateLogList,getQwlinkUpdateLogProductNameList} from '@/api/qwlinkUpdateLog'
import { getPlatformList } from '@/api/xmxrChannelManage/channelList'

export default {
  name: 'PlatformRebateExamine',
  data() {
    return {
      // 审核状态映射
      auditStatusMap: {
        0: '待审核',
        1: '通过',
        2: '驳回'
      },
      // 总条数
      total: 0,
      // 返点列表
      rebateList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startTime: null,
        endTime: null,
        productId: null,
        applyBy: null,
        platformType: null
      },
      // 平台列表
      platformList: [],
      //产品名称
      productNameList: [],
      // 查看明细弹窗
      detailDialog: false,
      detailData: {
        rebateDate: '',
        detailList: []
      },
      // 审核弹窗
      auditDialog: false,
      auditForm: {
        id: null,
        type: null,
        remarks: '',
        platformType: null,
        auditType: null,
        rowRemark: ''
      },
      currentRow: null
    }
  },
  created() {
    this.getPlatformList()
  },
  methods: {
    formatPlatformName(platformType) {
      console.log(platformType)
      return this.platformList.find(item => item.id == platformType).name
    },
    queryProductName() {
      // console.log('queryProductName',this.queryParams.platformType)
      if (this.queryParams.platformType == null||this.queryParams.platformType == '') {
        this.$message({
          type: 'warning',
          message: '请先选择平台'
        });
        return false;
      }
      getQwlinkUpdateLogProductNameList(this.queryParams).then(res => {
        this.productNameList = res.data
      })
    },
    /** 查询列表 */
    getList() {
      getQwlinkUpdateLogList(this.queryParams).then(res => {
        console.log("res",res.data.length)
        this.rebateList = res.data
      })
      console.log(this.rebateList)
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.$refs['queryForm'].validate((valid) => {
        console.log(valid)
        if (valid) {
          this.queryParams.pageNum = 1
          this.getList()
        } else {
          return false;
        }
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.$refs.queryForm.resetFields()
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.handleQuery()
    },
    /** 获取平台列表 */
    getPlatformList() {
      getPlatformList().then(res => {
        this.platformList = res.data
      })
    },
    /** 重置审核表单 */
    resetAuditForm() {
      this.$refs.auditForm.resetFields()
    },
    /** 下载文件 */
    async handleDownload(row) {
      const url = row.fileName
      const fileName = row.fileName.substring(row.fileName.lastIndexOf('/') + 1)

      fetch(url).then(res => {
        return res.blob()
      }).then(res => {
        this.$download.saveAs(res, fileName)
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.detail-item {
  margin-bottom: 15px;

  .label {
    display: inline-block;
    width: 80px;
    color: #606266;
  }
}

.mt-20 {
  margin-top: 20px;
}

.audit-info {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 4px;

  .info-item {
    margin-bottom: 10px;
    font-size: 14px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      display: inline-block;
      width: 80px;
      color: #606266;
    }

    .value {
      font-weight: 500;
      color: #303133;

      &.red {
        color: #f56c6c;
      }
    }
  }
}
</style>
