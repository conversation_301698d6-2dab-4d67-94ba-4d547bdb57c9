<template>
  <el-dialog
    title="查询验证码"
    :visible.sync="visible"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <div class="wechat-test-dialog">
      <!-- 加载中状态 -->
      <div v-if="loading" class="loading-content">
        <el-icon class="el-icon-loading"></el-icon>
        <p>正在查询验证码，请稍候...</p>
      </div>
      <!-- 查询结果状态 -->
      <div v-else class="query-content">
        <div v-if="!verificationCode" class="input-content">
          <el-form :model="phoneForm" ref="phoneForm" :rules="phoneRules" label-width="100px">
            <el-form-item label="手机号" prop="phone">
              <el-input 
                v-model="phoneForm.phone" 
                placeholder="请输入手机号"
                maxlength="11"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div v-else class="success-content">
          <i class="el-icon-success" style="font-size: 50px; color: #67C23A;"></i>
          <p>验证码查询成功！</p>
          <el-tag type="success" style="margin-bottom: 10px; font-size: 20px;">{{ verificationCode }}</el-tag>
          <el-button type="text" icon="el-icon-document-copy" @click="copyCode">复制验证码</el-button>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer" style="text-align: center;">
      <el-button @click="handleClose">关 闭</el-button>
      <el-button 
        v-if="!verificationCode"
        type="primary" 
        @click="queryCode"
        :loading="loading"
      >查 询</el-button>
      <el-button 
        v-else
        type="primary" 
        @click="handleRequery"
      >再次查询</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTestPhoneCode } from "@/api/distributeStatistics/productLinkTest";
import clipboard from "@/utils/clipboard";

export default {
  name: 'PhoneCodeDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      phoneForm: {
        phone: ''
      },
      phoneRules: {
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ]
      },
      verificationCode: ''
    }
  },
  methods: {
    handleClose() {
      this.resetForm()
      this.$emit('update:visible', false)
    },
    resetForm() {
      this.phoneForm.phone = ''
      this.verificationCode = ''
      if (this.$refs.phoneForm) {
        this.$refs.phoneForm.resetFields()
      }
    },
    async queryCode() {
      this.$refs.phoneForm.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            const response = await getTestPhoneCode({
              phone: this.phoneForm.phone
            })
            
            if (response.code === 200) {
              this.verificationCode = response.data
              this.$message.success('查询成功')
            }
          } finally {
            this.loading = false
          }
        }
      })
    },
    async copyCode() {
      try {
        await clipboard.copyText(this.verificationCode);
        this.$message.success('验证码已复制');
      } catch (error) {
        this.$message.error('复制失败，请手动复制');
      }
    },
    // 再次查询
    handleRequery() {
      this.verificationCode = ''
      this.phoneForm.phone = ''
      if (this.$refs.phoneForm) {
        this.$refs.phoneForm.resetFields()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.wechat-test-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;

  .loading-content,
  .success-content,
  .query-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    width: 100%;

    p {
      margin: 0;
      font-size: 16px;
      color: #606266;
    }
  }

  .input-content {
    width: 100%;
    padding: 0 20px;
    
    .el-form {
      width: 100%;
    }
  }

  .el-icon-loading {
    font-size: 50px;
    color: #409EFF;
  }

  .success-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    width: 100%;

    p {
      margin: 0;
      font-size: 16px;
      color: #606266;
    }

    .el-button {
      padding: 0;
      font-size: 14px;
    }
  }
}
</style> 