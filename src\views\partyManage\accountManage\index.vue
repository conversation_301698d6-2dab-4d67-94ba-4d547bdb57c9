<template>
  <div class="app-container">
    <div class="addbtn">
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="addUserManege"
        >新增</el-button
      >
    </div>
    <el-table border :data="manageData">
      <el-table-column label="ID" prop="userId" align="center" />
      <el-table-column label="昵称" prop="nickName" align="center" />
      <el-table-column label="账号" prop="userName" align="center" />
      <el-table-column label="短信手机号" prop="phonenumber" align="center" />
      <el-table-column label="接单状态" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-switch
        
              v-model="row.pushStatus"
              active-value="0"
              inactive-value="1"
              active-text="启用"
              inactive-text="禁用"
              @change="changePushStatus($event, row.userId)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="账号状态" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-switch

              v-model="row.status"
              active-value="0"
              inactive-value="1"
              active-text="启用"
              inactive-text="禁用"
              @change="changeStatus($event, row.userId)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              size="mini"
              @click="handleEdit(row)"
              type="text"
              icon="el-icon-edit"
            >
              编辑
            </el-button>
            <el-button
              @click="handleAdmin(row)"
              size="mini"
              type="text"
              icon="el-icon-edit"
            >
              分配权限
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加 -->
    <el-dialog
      :title="isAdd ? '添加账号' : '修改账号'"
      :visible.sync="userAvisible"
      @close="cancel"
      width="500px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="昵称" prop="name">
          <el-input
            maxlength="40"
            v-model.trim="formData.name"
            placeholder="请输入昵称"
            clearable
          />
        </el-form-item>
        <el-form-item label="账号" prop="userName">
          <el-input
            maxlength="40"
            :disabled="!isAdd"
            v-model.trim="formData.userName"
            placeholder="请输入账号"
            clearable
          />
        </el-form-item>
        <el-form-item
          label="密码"
          prop="password"
          :rules="{
            required: isAdd ? true : false,
            message: '请输入密码',
            trigger: 'blur',
          }"
        >
          <el-input
            maxlength="20"
            type="password"
            show-password
            v-model.trim="formData.password"
            placeholder="请输入密码"
            clearable
          />
        </el-form-item>
        <el-form-item label="通知手机号" prop="phonenumber">
          <el-input
            maxlength="11"
            v-model.trim="formData.phonenumber"
            placeholder="请输入通知手机号"
            clearable
          />
        </el-form-item>
        <el-form-item label="账号状态" prop="status" v-if="isAdd">
          <el-radio v-model="formData.status" label="0">启用</el-radio>
          <el-radio v-model="formData.status" label="1">禁用</el-radio>
        </el-form-item>
        <el-form-item label="接单状态" prop="pushStatus" v-if="isAdd">
          <el-radio v-model="formData.pushStatus" label="0">启用</el-radio>
          <el-radio v-model="formData.pushStatus" label="1">禁用</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="分配权限"
      :visible.sync="adminAvisible"
      @close="adminCancel"
      width="500px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formAdminData" :model="formAdminData" label-width="100px">
        <el-form-item prop="menuIds">
          <el-checkbox-group v-model="formAdminData.menuIds">
            <el-checkbox
              v-for="item in adminList"
              :key="item.id"
              :label="item.id"
              >{{ item.label }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAdminForm">确 定</el-button>
        <el-button @click="adminCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  AddUsermanageOne,
  getUsermanageList,
  editUsermanageStatus,
  editUsermanageOne,
  getPartyAdminRole,
  editPartyAdminRole,
  editUsermanagePushStatus,
} from "@/api/partyManage";
export default {
  name: "Account",
  data() {
    var validatePhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("手机号不能为空"));
      } else if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
        callback(new Error("手机号格式错误"));
      } else {
        callback();
      }
    };
    return {
      isAdd: true,
      userAvisible: false,
      adminAvisible: false,
      formAdminData: {
        menuIds: [],
      },
      adminList: [],
      manageData: [],
      total: 0,
      formData: {
        password: "",
        phonenumber: "",
        status: "0",
        userName: "",
        name: "",
        pushStatus: "0",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      rules: {
        name: [{ required: true, message: "请输入昵称", trigger: "blur" }],
        userName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        // password: [],
        phonenumber: [
          { required: true, validator: validatePhone, trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    //添加显示弹窗
    addUserManege() {
      this.isAdd = true;
      this.userAvisible = true;
      if (this.formData.userId) {
        delete this.formData.userId;
      }
    },
    getList() {
      getUsermanageList(this.queryParams).then((res) => {
        this.manageData = res.rows;
        this.total = res.total;
      });
    },
    //取消
    cancel() {
      this.formData = {
        password: "",
        phonenumber: "",
        status: "0",
        userName: "",
        name: "",
        pushStatus: "0",
      };
      this.userAvisible = false;
      this.$refs.formData.resetFields();
    },
    changeStatus(e, id) {
      this.$confirm(`确定${e == "1" ? "禁用" : "启用"}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          editUsermanageStatus({
            status: e,
            userId: id,
          }).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.getList();
            }
          });
        })
        .catch((err) => {
          this.getList();
        });
    },
    changePushStatus(e, id) {
      this.$confirm(`确定${e == "1" ? "禁用" : "启用"}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          editUsermanagePushStatus({
            status: e,
            userId: id,
          }).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.getList();
            }
          });
        })
        .catch((err) => {
          this.getList();
        });
    },
    //提交表单
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            AddUsermanageOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("添加成功");
              }
            });
          } else {
            delete this.formData.userName;
            editUsermanageOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("修改成功");
              }
            });
          }
        }
      });
    },
    //编辑弹窗
    handleEdit(row) {
      this.isAdd = false;
      this.userAvisible = true;
      this.formData.password = "";
      this.formData.phonenumber = row.phonenumber;
      this.formData.userName = row.userName;
      this.formData.status = row.status;
      this.formData.userId = row.userId;
      this.formData.name = row.nickName;
    },
    handleAdmin(row) {
      getPartyAdminRole({ userId: row.userId }).then((res) => {
        this.adminList = [res.menus[0].children[1]];
        this.formAdminData.menuIds = res.checkedKeys;
        this.formAdminData.userId = row.userId;
        this.adminAvisible = true;
      });
    },
    adminCancel() {
      this.adminList = [];
      this.formAdminData.menuIds = [];
      this.formAdminData.userId = "";
      this.adminAvisible = false;
    },
    submitAdminForm() {
      editPartyAdminRole(this.formAdminData).then((res) => {
        if (res.code == 200) {
          this.adminAvisible = false;
          this.$message.success("分配成功");
          this.getList();
        }
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.addbtn {
  margin-bottom: 20px;
}
</style>
