<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      @submit.native.prevent
      :inline="true"
    >
      <el-form-item label="合作方名称" prop="partnerName">
        <el-input
          clearable
          placeholder="请输入合作方名称"
          size="small"
          @change="handleQuery"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.partnerName"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道来源" prop="source">
        <el-input
          clearable
          @change="handleQuery"
          placeholder="请输入渠道来源"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.source"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="我方推广渠道号" prop="channelId">
        <el-input clearable placeholder="请输入我方推广渠道号" size="small" oninput="value=value.replace(/[^0-9]/g,'')"
          @change="handleQuery" @keyup.enter.native="handleQuery" v-model.number="queryParams.channelId"></el-input>
      </el-form-item> -->
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" @change="handleQuery" clearable>
          <el-option label="启用" value="0"></el-option>
          <el-option label="禁用" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <!-- <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="addDomain"
          v-hasPermi="['loan:api_channel:add']"
          >新增渠道接口</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="roundStr"
          >生成随机字符</el-button
        > -->
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" border :data="dataList">
      <el-table-column label="ID" prop="id" align="center" width="50" />
      <el-table-column
        label="合作方名称"
        prop="partnerName"
        align="center"
        width="100"
      />

      <el-table-column
        label="渠道来源"
        prop="channelId"
        align="center"
        width="100"
      />

      <el-table-column label="状态" width="150" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-hasPermi="['loan:output_api_channel:update_status']"
              v-model="row.status"
              active-text="启用"
              inactive-text="禁用"
              active-value="0"
              inactive-value="1"
              @change="changeStatus($event, row.id)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="匹配地址" prop="matchUrl" align="center" />
      <el-table-column label="申请地址" prop="applyUrl" align="center" />
      <el-table-column
        label="渠道名称"
        prop="channelIdNames"
        align="center"
        show-overflow-tooltip
      >
        <!-- <template slot-scope="{ row }">
          <div>
            <div v-for="item in row.channelIdNames.split(',')" :key="item">
              {{ item }}
            </div>
          </div>
        </template> -->
      </el-table-column>
      <el-table-column
        label="修改人"
        prop="updateBy"
        align="center"
        width="80"
      />
      <el-table-column
        label="修改时间"
        prop="updateTime"
        align="center"
        width="160"
      />

      <el-table-column label="备注" prop="remark" align="center" />

      <el-table-column align="center" width="160" label="操作">
        <template slot-scope="{ row }">
          <div>
            <!-- <el-button
              type="text"
              @click="handleDetail(row)"
              icon="el-icon-document"
              v-hasPermi="['loan:api_channel:query']"
              >查看详情</el-button
            >
            <el-button
              type="text"
              v-hasPermi="['loan:api_channel:update']"
              @click="handleEdit(row)"
              icon="el-icon-edit-outline"
              >修改</el-button
            > -->
            <el-button
              type="text"
              v-hasPermi="['loan:output_api_channel:updateChannel']"
              @click="handleRule(row)"
              icon="el-icon-edit-outline"
              >配置规则</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加 -->
    <el-dialog
      :title="title"
      :visible.sync="domainAvisible"
      @close="cancel"
      width="800px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="160px"
      >
        <el-form-item label="渠道来源" prop="channelId">
          <el-input
            v-model="formData.channelId"
            :disabled="
              title == '接口渠道配置详情' || title == '修改接口渠道配置'
            "
            placeholder="请输入渠道来源"
          />
        </el-form-item>
        <el-form-item label="合作方名称" prop="partnerName">
          <el-input
            :disabled="title == '接口渠道配置详情'"
            v-model="formData.partnerName"
            placeholder="请输入合作方名称"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="isAdd">
          <el-radio
            v-model="formData.status"
            :disabled="title == '接口渠道配置详情'"
            label="0"
            >启用</el-radio
          >
          <el-radio
            v-model="formData.status"
            :disabled="title == '接口渠道配置详情'"
            label="1"
            >禁用</el-radio
          >
        </el-form-item>
        <el-form-item label="类型" prop="typeIds">
          <el-select
            v-model="formData.typeIds"
            :disabled="title == '接口渠道配置详情'"
            clearable
            multiple
          >
            <el-option label="撞库" value="0"></el-option>
            <el-option label="半流程" value="1"></el-option>
            <el-option label="注册" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="第三方AES_IV" prop="apiAesIv">
          <el-input
            v-model="formData.apiAesIv"
            :disabled="title == '接口渠道配置详情'"
            type="textarea"
            :rows="4"
            placeholder="请输入第三方AES_IV"
          />
        </el-form-item>
        <el-form-item label="第三方AES_KEY" prop="apiAesKey">
          <el-input
            v-model="formData.apiAesKey"
            :disabled="title == '接口渠道配置详情'"
            type="textarea"
            :rows="4"
            placeholder="请输入第三方AES_KEY"
          />
        </el-form-item>

        <el-form-item label="H5跳转链接" prop="h5Url">
          <el-input
            v-model="formData.h5Url"
            :disabled="title == '接口渠道配置详情'"
            placeholder="请输入H5跳转链接"
          />
        </el-form-item>
        <el-form-item label="我方AES_IV" prop="myAesIv">
          <el-input
            v-model="formData.myAesIv"
            :disabled="title == '接口渠道配置详情'"
            type="textarea"
            :rows="4"
            placeholder="请输入我方AES_IV"
          />
        </el-form-item>
        <el-form-item label="我方AES_KEY" prop="myAesKey">
          <el-input
            v-model="formData.myAesKey"
            :disabled="title == '接口渠道配置详情'"
            type="textarea"
            :rows="4"
            placeholder="请输入我方AES_KEY"
          />
        </el-form-item>
        <el-form-item label="我方落地页渠道号" prop="myChannelId">
          <el-input
            oninput="value=value.replace(/[^0-9]/g,'')"
            :disabled="title == '接口渠道配置详情'"
            v-model.number="formData.myChannelId"
            placeholder="请输入我方落地页渠道号"
          />
        </el-form-item>
        <el-form-item label="其它参数" prop="otherParam">
          <div style="display: flex">
            <el-input
              type="textarea"
              disabled
              :rows="5"
              v-model="formData.otherParam"
              placeholder="请输入其它参数"
            />
            <el-button size="mini" style="height: 30px" @click="handleConfigKey"
              >配置</el-button
            >
          </div>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            :disabled="title == '接口渠道配置详情'"
            placeholder="请输入备注"
          />
        </el-form-item>

        <el-form-item label="白名单IP" prop="whitelistIp">
          <el-input
            type="textarea"
            :rows="3"
            :disabled="title == '接口渠道配置详情'"
            v-model="formData.whitelistIp"
            placeholder="请输入白名单IP"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
        v-if="title != '接口渠道配置详情'"
      >
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="生成随机字符"
      :visible.sync="strAvisible"
      width="400px"
      append-to-body
      center
      @close="cancelCopy"
      :close-on-click-modal="false"
    >
      <el-input
        v-model.number="roundStrs"
        clearable=""
        placeholder="请输入字符"
      />
      <div class="handle-str">
        <el-button type="primary" @click="handleStr">生成</el-button>
        <el-button type="primary" @click="handleCope">复制</el-button>
      </div>
      <div class="handle-str">{{ str }}</div>
    </el-dialog>
    <el-dialog
      title="其他参数"
      :visible.sync="otherAvisible"
      width="800px"
      append-to-body
      center
      @close="otherAvisible = false"
      :close-on-click-modal="false"
    >
      <el-form label-width="200px">
        <el-form-item
          :label="item[0]"
          v-for="(item, index) in paramData"
          :key="index"
        >
          <div style="display: flex">
            <el-input v-model="paramJson[item[0]]" placeholder="请输入" />
            <el-button
              type="info"
              size="small"
              @click="hanldeDatail(index, item[0])"
              >删除</el-button
            >
            <el-button
              @click="hanldeAddKey"
              type="primary"
              v-if="paramData.length - 1 == index"
              size="small"
              >添加key
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="hanldeAddKey"
          type="primary"
          v-if="!paramData.length"
          size="small"
          >添加key
        </el-button>
        <el-button type="primary" @click="submitJson">确 定</el-button>
        <el-button @click="cancelKey">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="添加key"
      width="900px"
      :visible.sync="keyvisible"
      append-to-body
      center
      :close-on-click-modal="false"
      @close="keyName = ''"
    >
      <el-form label-width="200px">
        <el-form-item label="key名称">
          <el-input v-model="keyName" placeholder="请输入key名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitJsonKey">确 定</el-button>
      </div>
    </el-dialog>
    <limit
      v-model="limitVisible"
      :rows="rows"
      width="50%"
      @close="limitVisible = false"
      @update="getList"
    />
  </div>
</template>

<script>
import {
  getOutApiChannelList,
  addApiChannelOne,
  getApiChannelOne,
  editOutApiChannelOne,
  changeOutApiChannelOne,
} from "@/api/channeManage/outConfiguration";
import limit from "./components/limit.vue";
export default {
  data() {
    return {
      total: 0,
      limitVisible: false,
      rows: {},
      loading: false,
      isAdd: true,
      domainAvisible: false,
      strAvisible: false,
      otherAvisible: false,
      keyvisible: false,
      dataList: [],
      title: "",
      roundStrs: "",
      str: "",
      keyName: "",
      paramData: [],
      paramJson: {},
      types: {
        0: "撞库",
        1: "半流程",
        2: "注册",
      },
      queryParams: {
        source: "",

        pageNum: 1,
        pageSize: 10,
        partnerName: "",
        status: "",
      },
      formData: {
        apiAesIv: "",
        apiAesKey: "",
        channelId: "",
        h5Url: "",
        myAesIv: "",
        myAesKey: "",
        myChannelId: "",
        otherParam: "",
        partnerName: "",
        remark: "",
        status: "0",
        typeIds: [],
        whitelistIp: "",
      },
      rules: {
        channelId: [
          {
            required: true,
            message: "请输入渠道来源",
            trigger: "blur",
          },
        ],
        partnerName: [
          {
            required: true,
            message: "请输入合作方名称",
            trigger: "blur",
          },
        ],
        status: [
          {
            required: true,
            message: "请选择状态",
            trigger: "blur",
          },
        ],
        typeIds: [
          {
            required: true,
            message: "请输入类型",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    addDomain() {
      this.domainAvisible = true;
      this.isAdd = true;
      this.title = "新增接口渠道配置";
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    handleConfigKey() {
      this.otherAvisible = true;
      this.paramData = Object.entries(
        JSON.parse(this.formData.otherParam || "{}")
      );
      this.paramJson = JSON.parse(this.formData.otherParam || "{}");
    },
    hanldeAddKey() {
      this.keyvisible = true;
    },

    cancelKey() {
      this.paramData = [];
      this.paramJson = {};
      this.otherAvisible = false;
    },
    hanldeAddKey() {
      this.keyvisible = true;
    },
    submitJson() {
      this.formData.otherParam = Object.keys(this.paramJson).length
        ? JSON.stringify(this.paramJson)
        : "";
      this.otherAvisible = false;
    },
    hanldeDatail(index, key) {
      this.paramData.splice(index, 1);
      delete this.paramJson[key];
    },
    submitJsonKey() {
      if (!this.keyName) return this.$message.error("key不能为空");
      this.paramData.push([this.keyName, ""]);
      this.keyvisible = false;
    },
    //获取列表
    getList() {
      getOutApiChannelList(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total;

        // if (this.dataList.length == 0 && this.queryParams.pageNum > 1) {
        //   this.queryParams.pageNum = this.queryParams.pageNum - 1;
        //   this.loading = false;
        //   this.getList();
        //   return;
        // }
      });
    },
    handleEdit(row) {
      this.title = "修改接口渠道配置";
      this.formData.id = row.id;
      this.isAdd = false;
      getApiChannelOne(row.id).then((res) => {
        this.domainAvisible = true;
        for (let key in res.data) {
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = res.data[key];
          }
        }
      });
    },
    handleDetail(row) {
      this.title = "接口渠道配置详情";
      this.domainAvisible = false;
      getApiChannelOne(row.id).then((res) => {
        this.domainAvisible = true;
        for (let key in res.data) {
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = res.data[key];
          }
        }
      });
    },
    cancel() {
      this.formData = {
        apiAesIv: "",
        apiAesKey: "",
        channelId: "",
        h5Url: "",
        myAesIv: "",
        myAesKey: "",
        myChannelId: "",
        otherParam: "",
        partnerName: "",
        remark: "",
        status: "0",
        typeIds: [],
        whitelistIp: "",
      };
      this.title = "";
      this.isAdd = true;
      this.domainAvisible = false;
      this.$refs.formData.resetFields();
    },
    //生成随机字符
    roundStr() {
      this.strAvisible = true;
    },
    handleCope() {
      let oInput = document.createElement("input");
      oInput.value = this.str;
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象;

      document.execCommand("Copy"); // 执行浏览器复制命令
      this.$message({
        message: "复制成功",
        type: "success",
      });
      oInput.remove();
    },
    handleStr() {
      let strarr = [
        "0",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n",
        "o",
        "p",
        "q",
        "r",
        "s",
        "t",
        "u",
        "v",
        "w",
        "x",
        "y",
        "z",
      ];
      this.str = "";

      for (var i = 0; i < this.roundStrs; i++) {
        this.str += strarr[parseInt(Math.random() * strarr.length)];
      }
    },
    cancelCopy() {
      this.str = "";
      this.roundStrs = "";
    },
    changeStatus(e, id) {
      this.$confirm("是否修改投放状态?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          changeOutApiChannelOne({ status: e, id: id }).then((res) => {
            if (res.code == 200) {
              this.$message.success("状态跟新成功");
              this.getList();
            }
          });
        })
        .catch(() => {
          this.getList();
        });
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addApiChannelOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("添加成功");
              }
            });
          } else {
            editOutApiChannelOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("修改成功");
              }
            });
          }
        }
      });
    },
    handleRule(row) {
      this.rows = row;
      this.limitVisible = true;
    },
  },
  components: {
    limit,
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.handle-str {
  display: flex;
  justify-content: center;

  margin-top: 10px;
}

</style>
<style lang="css">

  .el-tooltip__popper{font-size: 14px; max-width:50% }

</style>
