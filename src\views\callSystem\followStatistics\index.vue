<template>
  <div class="app-container">
    <el-form ref="queryForm" :inline="true" label-width="100px">
      <el-form-item label="平台" prop="platformId">
        <el-select v-model="platformId" placeholder="请选择平台" clearable>
          <el-option v-for="platform in platformList" :key="platform.platformId" :label="platform.platformName" :value="platform.platformId" />
        </el-select>
      </el-form-item>
      <el-form-item label="统计时间">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table ref="dataTable" :data="list" border @sort-change="handleSortChange">
      <el-table-column label="统计日期" align="center" prop="date" />
      <el-table-column label="发送总数" align="center" prop="total" sortable="custom" />
      <el-table-column label="低价数量" align="center" prop="poor" sortable="custom"/>
      <el-table-column label="低价占比" align="center" prop="poorRate" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.poorRate }}%
        </template>
      </el-table-column>
      <el-table-column label="高价数量" align="center" prop="quality" sortable="custom"/>
      <el-table-column label="高价占比" align="center" prop="qualityRate" sortable="custom">
        <template slot-scope="scope">
          {{ scope.row.qualityRate }}%
        </template>
      </el-table-column>
    </el-table>


  </div>
</template>

<script>
import { getFollowStatistics, getPlatforms } from '@/api/callSystem'
import { getDefaultDateRange } from '@/utils'

export default {
  name: 'FollowStatistics',
  data() {
    return {
      // 跟进统计列表
      list: [],
      // 原始数据列表（用于排序）
      originalList: [],
      // 平台列表
      platformList: [],
      // 平台ID
      platformId: '',
      // 日期范围
      dateRange: getDefaultDateRange()
    }
  },
  created() {
    this.getPlatformList()
    this.getList()
  },
  methods: {
    /** 查询跟进统计列表 */
    getList() {
      // 校验平台必选
      if (!this.platformId) {
        this.$message.warning('请先选择平台')
        return
      }

      const params = {
        platformId: this.platformId
      }
      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }

      console.log('请求参数:', params)
      getFollowStatistics(params).then(response => {
        console.log('接口返回数据:', response)
        this.originalList = response.data || []
        this.list = [...this.originalList]
        // 重置表格排序状态
        this.$nextTick(() => {
          if (this.$refs.dataTable) {
            this.$refs.dataTable.clearSort()
          }
        })
      }).catch(error => {
        console.error('接口调用失败:', error)
      })
    },
    /** 获取平台列表 */
    getPlatformList() {
      getPlatforms().then(response => {
        this.platformList = response.data || []
      }).catch(error => {
        console.error('获取平台列表失败:', error)
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.platformId = ''
      this.dateRange = getDefaultDateRange()
      this.handleQuery()
    },
    /** 处理表格排序 */
    handleSortChange({ prop, order }) {
      if (!prop || !order) {
        // 取消排序，恢复原始顺序
        this.list = [...this.originalList]
        return
      }

      // 分离汇总行和数据行
      const summaryRow = this.originalList.find(item => item.date === '汇总')
      const dataRows = this.originalList.filter(item => item.date !== '汇总')

      // 对数据行进行排序
      const sortedDataRows = dataRows.sort((a, b) => {
        let aVal = a[prop]
        let bVal = b[prop]

        // 处理数值类型的排序
        if (typeof aVal === 'string' && !isNaN(parseFloat(aVal))) {
          aVal = parseFloat(aVal)
        }
        if (typeof bVal === 'string' && !isNaN(parseFloat(bVal))) {
          bVal = parseFloat(bVal)
        }

        if (order === 'ascending') {
          return aVal > bVal ? 1 : aVal < bVal ? -1 : 0
        } else {
          return aVal < bVal ? 1 : aVal > bVal ? -1 : 0
        }
      })

      // 重新组合数据：汇总行始终在第一位
      this.list = summaryRow ? [summaryRow, ...sortedDataRows] : sortedDataRows
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
