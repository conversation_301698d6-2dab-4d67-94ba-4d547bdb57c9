import request from '@/utils/request'
//获取协议列表
export const getProtocolTypeList = (data) => {
  return request({
    url: "/loan/protocolSig/list",
    method: "get",
    params: data,
  })
}
//新增协议类型
export const addProtocolTypeOne = (data) => {
  return request({
    url: "/loan/protocolSig/add",
    method: "post",
    data,
  })
}
//修改协议类型
export const editProtocolTypeOne = (data) => {
  return request({
    url: "/loan/protocolSig/edit",
    method: "post",
    data,
  })
}
//修改协议类型
export const delProtocolTypeOne = (data) => {
  return request({
    url: `/loan/protocolSig/del/${data}`,
    method: "post",

  })
}

//新增协议
export const addProtocolOne = (data) => {
  return request({
    url: "/loan/protocol/add",
    method: "post",
    data
  })
}
//获取协议列表
export const getProtocolList = (data) => {
  return request({
    url: "/loan/protocol/list",
    method: "get",
    params: data
  })
}
//获取协议列表
export const getProtocolOne = (data) => {
  return request({
    url: `/loan/protocol/query/${data}`,
    method: "get",

  })
}
//修改协议
export const editProtocolOne = (data) => {
  return request({
    url: "/loan/protocol/edit",
    method: "post",
    data
  })
}
//修改协议
export const delProtocolOne = (data) => {
  return request({
    url: `/loan/protocol/del/${data}`,
    method: "post"
  })
}

//获取协议标识
export const getListSig = () => {
  return request({
    url: "/loan/protocol/listSig",
    method: 'get'
  })
}
//获取项目归属
export const getlistProject = () => {
  return request({
    url: "/loan/protocol/listProject",
    method: 'get'
  })
}

//获取历史协议
export const getHistoryprotocol = (data) => {
  return request({
    url: "/loan/historyprotocol/list",
    method: "get",
    params: data
  })
}
//获取历史协议
export const getHistoryprotocolDetail = (protocolId, versionNo) => {
  return request({
    url: `/loan/historyprotocol/query/${protocolId}/${versionNo}`,
    method: "get",

  })
}


//获取客户协议
export const getUserProtocol = (data) => {
  return request({
    url: "/consumer/protocolversion/list",
    method: "get",
    params: data
  })
}

//获取客户协议
export const getUserProtocolDeatil = (consumerId, protocolId, versionNo) => {
  return request({
    url: `/consumer/protocolversion/query/${consumerId}/${protocolId}/${versionNo}`,
    method: "get",

  })
}
