<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="短信名称" prop="status">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入短信名称"
          size="small"
          clearable
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="addNote"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-table border :data="tableList">
      <el-table-column label="模板名称" prop="name" align="center" />
      <el-table-column label="模板内容" prop="content" align="center" />
      <el-table-column label="模块Key" prop="templateKey" align="center" />

      <el-table-column label="短信配置名称" prop="templateName" align="center" />
      <el-table-column label="状态" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.status == 1 ? "启用" : "禁用" }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="参数数量" align="center" prop="paramNum" />

      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              type="text"
              size="small"
              icon="el-icon-edit-outline"
              @click="handleEdit(row)"
              >修改</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="isAdd ? '新增短信模板' : '修改短信模板'"
      :visible.sync="noteAvisible"
      @close="cancel"
      width="700px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        label-width="120px"
        label-position="left"
        :rules="rules"
      >
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="模板Key" prop="templateKey">
          <el-input
            v-model="formData.templateKey"
            placeholder="请输入模块Key"
          ></el-input>
        </el-form-item>
        <el-form-item label="短信配置ID" prop="configId">
          <el-select
            style="width: 100%"
            filterable
            clearable
            v-model="formData.configId"
          >
            <el-option
              v-for="item in configOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="短信状态" prop="status" v-if="formData.status">
          <el-radio v-model="formData.status" :label="1">启用</el-radio>
          <el-radio v-model="formData.status" :label="2">禁用</el-radio>
        </el-form-item>

        <el-form-item label="短信内容" prop="content">
          <el-input
            type="textarea"
            v-model="formData.content"
            placeholder="请输入短信类容"
          />
        </el-form-item>

        <el-form-item label="参数数量" prop="paramNum">
          <el-input
            v-model.number="formData.paramNum"
            placeholder="请输入参数数量"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSmsTemplateList,
  addSmsTemplateOne,
  getConfigList,
  editSmsTemplateOne,
} from "@/api/saas/smsManage";
export default {
  name: "Note",
  data() {
    return {
      noteAvisible: false,
      isAdd: true,
      total: 0,

      isAddNote: false,
      tableList: [],
      configOptions: [],
      queryParams: {
        name: "",
        pageNum: 1,
        pageSize: 10,
      },

      formData: {
        content: "",
        name: "",
        templateKey: "",
        status: 1,
        paramNum: null,
        configId: "",
      },
      rules: {
        content: [
          { required: true, message: "请输入短信类容", trigger: "blur" },
        ],
        configId: [
          { required: true, message: "请选择短信配置ID", trigger: "blur" },
        ],
        templateKey: [
          { required: true, message: "请输入key", trigger: "blur" },
        ],
        name: [{ required: true, message: "请输入模板名称", trigger: "blur" }],
        paramNum: [
          { required: true, message: "请输入参数数量", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    getList() {
      getSmsTemplateList(this.queryParams).then((res) => {
        this.tableList = res.rows;
        this.total = res.total;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    cancel() {
      this.formData = {
        content: "",
        name: "",
        templateKey: "",
        status: 1,
        paramNum: null,
        configId: "",
      };
      this.noteAvisible = false;
      this.$refs.formData.resetFields();
    },
    addNote() {
      this.noteAvisible = true;
      this.isAdd = true;
      if (this.formData.id) {
        delete this.formData.id;
      }
      getConfigList().then((res) => {
        this.configOptions = res.data;
      });
    },

    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addSmsTemplateOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("新增成功");
              }
            });
          } else {
            editSmsTemplateOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("修改成功");
              }
            });
          }
        }
      });
    },

    handleEdit(row) {
      getConfigList().then((res) => {
        this.configOptions = res.data;
      });
      for (let k in row) {
        if (Object.keys(this.formData).includes(k)) {
          this.formData[k] = row[k];
        }
      }

      this.noteAvisible = true;
      this.isAdd = false;
      this.formData.id = row.id;
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-textarea__inner {
  height: 200px;
}
</style>
