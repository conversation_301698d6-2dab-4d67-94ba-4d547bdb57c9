---
description: 
globs: 
alwaysApply: true
---
# 代码规范与最佳实践

## Vue 组件规范
- 单文件组件使用 `.vue` 扩展名
- 组件名使用 PascalCase 命名
- Props 定义应尽可能详细，指定类型和默认值
- 避免在模板中使用复杂表达式，应提取为计算属性
- 使用 `v-if` 代替 `v-show`（除非有特殊需求）

## 网络请求规范
- 使用预配置的 axios 实例进行请求，无需在组件中处理 catch 错误
- 无需判断 `res.code === 200`（已在拦截器中统一处理）
- 无需使用 try-catch 包裹请求
- 页面中不需要设置 loading（全局拦截器已处理）

## Element-UI 使用规范
- 页面筛选表单使用 `el-form` 组件，每个控件使用单独的 `el-form-item`
- 表单组件 `el-form-item` 都需要设置 `prop` 属性
- 不需要设置组件的 `size` 属性
- 分页功能使用 `src/components/Pagination/index.vue` 组件
- 状态显示使用不同类型的 `el-tag` 组件
- 模态框宽度设置在 400px 到 1200px 之间
- 关闭模态框时需要重置表单字段和校验

## 代码比较
- 推荐使用 `==` 进行值比较
- 避免使用 `===`

## 页面结构
- 页面结构应尽可能简洁，不要使用 `el-card` 包裹
- 避免过于复杂的布局和不必要的元素嵌套
- 默认禁止使用自定义 CSS

## 数据处理
- 尽量避免使用 `undefined` 和 `null`，必要时请添加注释说明
- 表格排序优先使用 `el-table` 的默认排序功能

## 日期时间处理
- 使用 dayjs 处理日期时间
- 日期时间筛选默认选中当天（开始时间：00:00:00，结束时间：23:59:59）
- 日期时间范围选择使用 `el-date-picker` 组件：
  ```js
  value-format="yyyy-MM-dd HH:mm:ss"
  :default-time="['00:00:00', '23:59:59']"
  ```

