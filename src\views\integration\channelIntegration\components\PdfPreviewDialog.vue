<template>
  <el-dialog
    title="PDF预览"
    :visible.sync="visible"
    width="80%"
    :close-on-click-modal="false"
    :before-close="handleClose"
    append-to-body
    class="pdf-preview-dialog"
  >
    <div class="pdf-container" v-if="pdfUrl">
      <!-- PDF工具栏 -->
      <div class="pdf-toolbar">
        <div class="toolbar-left">
          <el-button size="small" @click="prevPage" :disabled="currentPage <= 1">
            <i class="el-icon-arrow-left"></i> 上一页
          </el-button>
          <span class="page-info">
            第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
          </span>
          <el-button size="small" @click="nextPage" :disabled="currentPage >= totalPages">
            下一页 <i class="el-icon-arrow-right"></i>
          </el-button>
        </div>
        <div class="toolbar-right">
          <!-- 缩放功能已移除 -->
        </div>
      </div>

      <!-- PDF内容区域 -->
      <div class="pdf-content" ref="pdfContent">
        <div v-if="loading" class="loading-container">
          <i class="el-icon-loading"></i>
          <span>正在加载PDF...</span>
        </div>
        <div v-else-if="error" class="error-container">
          <i class="el-icon-warning"></i>
          <span>{{ error }}</span>
          <el-button size="small" @click="reload" style="margin-left: 10px">重新加载</el-button>
        </div>
        <pdf
          v-else
          :src="pdfSrc"
          :page="currentPage"
          @num-pages="totalPages = $event"
          @page-loaded="handlePageLoaded"
          @error="handleError"
          class="pdf-page"
          :style="{ position: 'relative', zIndex: 1 }"
        />
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="downloadPdf">
        <i class="el-icon-download"></i> 下载
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import pdf from 'vue-pdf'

export default {
  name: 'PdfPreviewDialog',
  components: {
    pdf
  },
  data() {
    return {
      visible: false,
      pdfUrl: '',
      pdfSrc: null,
      currentPage: 1,
      totalPages: 0,
      loading: false,
      error: null,
      useOnlineViewer: false
    }
  },
  methods: {
    /** 打开PDF预览弹窗 */
    open(url) {
      this.pdfUrl = url
      this.visible = true
      this.currentPage = 1
      this.error = null
      this.loadPdf()
    },

    /** 加载PDF */
    async loadPdf() {
      if (!this.pdfUrl) return

      this.loading = true
      this.error = null

      try {
        // 方法1：尝试直接加载
        this.pdfSrc = pdf.createLoadingTask({
          url: this.pdfUrl,
          disableWorker: true,
          disableAutoFetch: true,
          disableStream: true
        })

        this.loading = false
      } catch (error) {
        console.error('PDF直接加载失败，尝试代理方式:', error)
        this.loadPdfWithProxy()
      }
    },

    /** 使用代理方式加载PDF */
    async loadPdfWithProxy() {
      try {
        // 方法2：使用fetch获取PDF数据
        const response = await fetch(this.pdfUrl, {
          mode: 'cors',
          credentials: 'omit'
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const arrayBuffer = await response.arrayBuffer()
        this.pdfSrc = pdf.createLoadingTask({
          data: arrayBuffer,
          disableWorker: true
        })

        this.loading = false
      } catch (error) {
        console.error('PDF代理加载失败:', error)
        // 方法3：使用在线预览服务
        this.loadPdfWithOnlineViewer()
      }
    },

    /** 使用在线预览服务 */
    loadPdfWithOnlineViewer() {
      this.error = null
      this.loading = false
      // 使用iframe嵌入在线PDF预览
      this.useOnlineViewer = true
    },

    /** 重新加载PDF */
    reload() {
      this.loadPdf()
    },

    /** 上一页 */
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--
      }
    },

    /** 下一页 */
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++
      }
    },

    /** 页面加载完成 */
    handlePageLoaded() {
      this.loading = false
    },

    /** 处理错误 */
    handleError(error) {
      console.error('PDF渲染错误:', error)
      this.error = 'PDF渲染失败，请尝试重新加载'
      this.loading = false
    },

    /** 下载PDF */
    downloadPdf() {
      if (this.pdfUrl) {
        const link = document.createElement('a')
        link.href = this.pdfUrl
        link.download = `发票_${Date.now()}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    },

    /** 关闭弹窗 */
    handleClose() {
      this.visible = false
      this.pdfUrl = ''
      this.pdfSrc = null
      this.currentPage = 1
      this.totalPages = 0
      this.loading = false
      this.error = null
    }
  }
}
</script>

<style>
/* 全局样式：确保PDF预览弹窗的层级最高 */
.pdf-preview-dialog .el-dialog__wrapper {
  z-index: 3000 !important;
}

.pdf-preview-dialog .el-overlay {
  z-index: 2999 !important;
}
</style>

<style scoped>
.pdf-preview-dialog {
  .el-dialog {
    margin-top: 5vh !important;
  }
  
  .el-dialog__body {
    padding: 10px 20px;
    max-height: 80vh;
    overflow: hidden;
  }
}

/* 确保PDF预览弹窗在最高层级 */
.pdf-preview-dialog.el-dialog__wrapper {
  z-index: 3000 !important;
}

/* 确保PDF预览弹窗的遮罩层在合适位置 */
.pdf-preview-dialog .el-dialog__wrapper {
  z-index: 3000 !important;
}

.pdf-container {
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 10px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-info {
  font-size: 14px;
  color: #606266;
  margin: 0 10px;
}

.pdf-content {
  flex: 1;
  overflow: auto;
  text-align: center;
  background-color: #f5f5f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.loading-container i,
.error-container i {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-container {
  color: #f56c6c;
}

.pdf-page {
  margin: 20px auto;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  background: white;
}

.dialog-footer {
  text-align: right;
}
</style>
