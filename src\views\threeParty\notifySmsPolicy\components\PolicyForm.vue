<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="600px"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="策略名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入策略名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="渠道" prop="channelIds">
        <el-select
          v-model="form.channelIds"
          placeholder="请选择渠道"
          style="width: 100%"
          filterable
          clearable
          multiple
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.id"
            :label="`${item.id}-${item.channelName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="短信策略" prop="policies">
        <div class="policies-header">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            @click="addPolicyItem"
            >添加策略</el-button
          >
        </div>

        <el-table :data="form.policies" border style="width: 100%">
          <el-table-column label="短信模板">
            <template slot-scope="scope">
              <el-form-item
                :prop="'policies.' + scope.$index + '.smsKey'"
                :rules="{
                  required: true,
                  message: '请选择短信模板',
                  trigger: 'change',
                }"
                label-width="0"
                style="margin-bottom: 0px"
              >
                <el-select
                  v-model="scope.row.smsKey"
                  placeholder="请选择短信模板"
                  style="width: 100%"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="template in smsTemplateOptions"
                    :key="template.smsTemplateKey"
                    :label="template.name"
                    :value="template.smsTemplateKey"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="权重(%)" width="120">
            <template slot-scope="scope">
              <el-form-item
                :prop="'policies.' + scope.$index + '.weight'"
                :rules="[
                  { required: true, message: '请输入权重', trigger: 'blur' },
                  {
                    type: 'number',
                    message: '权重必须为数字',
                    trigger: 'blur',
                  },
                  { validator: validateWeight, trigger: 'blur' },
                ]"
                label-width="0"
                style="margin-bottom: 0px"
              >
                <el-input-number
                  v-model="scope.row.weight"
                  :min="0"
                  :max="100"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="启用" width="80" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.enabled"
                :active-value="true"
                :inactive-value="false"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="80">
            <template slot-scope="scope">
              <el-button
                v-if="form.policies.length > 1"
                type="danger"
                icon="el-icon-delete"
                circle
                size="mini"
                @click="removePolicyItem(scope.$index)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  addSmsPolicy,
  updateSmsPolicy,
} from "@/api/threeParty/notifySmsPolicy";

export default {
  name: "PolicyForm",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "添加策略",
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    channelOptions: {
      type: Array,
      default: () => [],
    },
    smsTemplateOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    const validateWeight = (rule, value, callback) => {
      if (value < 0 || value > 100) {
        callback(new Error("权重必须在0-100之间"));
      } else {
        callback();
      }
    };

    return {
      dialogVisible: false,
      form: {
        id: undefined,
        name: "",
        channelIds: [],
        policies: [
          {
            smsKey: "",
            weight: 100,
            enabled: true,
          },
        ],
      },
      rules: {
        name: [
          { required: true, message: "请输入策略名称", trigger: "blur" },
          { max: 50, message: "策略名称长度不能超过50个字符", trigger: "blur" },
        ],
        channelIds: [
          { required: true, message: "请选择渠道", trigger: "change" },
        ],
        policies: [
          { required: true, message: "至少添加一项短信策略", trigger: "blur" },
        ],
      },
      validateWeight,
    };
  },
  computed: {
    totalWeight() {
      return this.form.policies.reduce(
        (sum, item) => sum + Number(item.weight || 0),
        0
      );
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit("update:visible", val);
    },
    formData: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.form = JSON.parse(JSON.stringify(val));

          // 处理渠道IDs
          if (typeof this.form.channelIds === "string") {
            try {
              this.form.channelIds = JSON.parse(this.form.channelIds);
            } catch (e) {
              this.form.channelIds = [];
              console.error("解析渠道IDs失败", e);
            }
          } else if (!this.form.channelIds) {
            this.form.channelIds = [];
          }

          // 确保策略是数组
          if (
            !this.form.policies ||
            !Array.isArray(this.form.policies) ||
            this.form.policies.length === 0
          ) {
            this.form.policies = [{ smsKey: "", weight: 100, enabled: true }];
          } else {
            // 兼容旧数据，补充 enabled 字段
            this.form.policies = this.form.policies.map((item) => ({
              ...item,
              enabled: typeof item.enabled === "boolean" ? item.enabled : true,
            }));
          }
        } else {
          this.resetForm();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleClose() {
      // 使用 nextTick 确保弹窗完全关闭后再重置
      this.$nextTick(() => {
        this.resetForm();
      });
    },
    resetForm() {
      // 先重置表单验证，此时表单数据还存在
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
      // 然后重置表单数据
      this.form = {
        id: undefined,
        name: "",
        channelIds: [],
        policies: [{ smsKey: "", weight: 100, enabled: true }],
      };
    },
    addPolicyItem() {
      const newWeight = Math.max(0, 100 - this.totalWeight);
      this.form.policies.push({
        smsKey: "",
        weight: newWeight,
        enabled: true,
      });
      // 兜底，确保 policies 始终为非空数组
      if (
        !Array.isArray(this.form.policies) ||
        this.form.policies.length === 0
      ) {
        this.form.policies = [{ smsKey: "", weight: 100, enabled: true }];
      }
    },
    removePolicyItem(index) {
      this.form.policies.splice(index, 1);
      // 兜底，确保 policies 始终为非空数组
      if (
        !Array.isArray(this.form.policies) ||
        this.form.policies.length === 0
      ) {
        this.form.policies = [{ smsKey: "", weight: 100, enabled: true }];
      }
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          return;
        }

        // 检查是否有重复的短信模板
        const smsKeys = this.form.policies.map((item) => item.smsKey);
        const uniqueSmsKeys = [...new Set(smsKeys)];
        if (smsKeys.length !== uniqueSmsKeys.length) {
          this.$message({
            message: "短信模板不能重复",
            type: "warning",
          });
          return;
        }

        // 只提取接口需要的字段
        const submitData = {
          name: this.form.name,
          channelIds: this.form.channelIds,
          policies: this.form.policies.map((item) => ({
            smsKey: item.smsKey,
            weight: item.weight,
            enabled: item.enabled,
          })),
        };

        // 如果是编辑模式，添加id字段
        if (this.form.id) {
          submitData.id = this.form.id;
        }

        const action = this.form.id ? updateSmsPolicy : addSmsPolicy;
        action(submitData).then((response) => {
          this.$message({
            message: this.form.id ? "修改成功" : "添加成功",
            type: "success",
          });
          this.dialogVisible = false;
          this.$emit("submit");
        });
      });
    },
  },
};
</script>

<style scoped>
.policies-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
</style>
