<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      @submit.native.prevent
      :inline="true"
    >
      <el-form-item label="链接名称" prop="channel_name">
        <el-input
          clearable
          placeholder="请输入链接名称"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.name"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="addDomain"
          >新增域名</el-button
        >
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" border :data="domianList">
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="链接名称" prop="name" align="center" />
      <el-table-column label="链接地址" prop="url" align="center" />
      <el-table-column label="主体" prop="subject" align="center" />
      <el-table-column label="链接状态" prop="condition" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.condition == 1 ? "正常" : "禁用" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort" align="center" />
      <el-table-column label="创建时间" prop="unknown" align="center" />
      <el-table-column align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              type="text"
              @click="handleEdit(row)"
              icon="el-icon-edit-outline"
              >修改</el-button
            >
            <el-button
              type="text"
              @click="handleDelete(row)"
              icon="el-icon-delete"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加 -->
    <el-dialog
      title="添加域名"
      :visible.sync="domainAvisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="链接名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入链接名称" />
        </el-form-item>
        <el-form-item label="链接" prop="url">
          <el-input v-model="formData.url" placeholder="请输入链接" />
        </el-form-item>
        <el-form-item label="主体" prop="subject">
          <el-input v-model="formData.subject" placeholder="请输入主体" />
        </el-form-item>
        <el-form-item label="排序" prop="sort" v-if="!isAdd">
          <el-input-number v-model="formData.sort" :min="1"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="condition" v-if="!isAdd">
          <el-radio v-model="formData.condition" :label="1">启用</el-radio>
          <el-radio v-model="formData.condition" :label="2">禁用</el-radio>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addDomainOne,
  editDomainOne,
  getDomainList,
  delDomainOne,

} from "@/api/channeManage/domain";
export default {
  name: "Domain",
  data() {
    return {
      total: 0,
      loading: false,
      isAdd: true,
      domainAvisible: false,
      domianList: [],

      queryParams: {
        name: "",
        pageNum: 1,
        pageSize: 10,
      },
      formData: {
        name: "",
        url: "",
        sort: "",
        condition: 1,
        subject: "",

      },
      rules: {
        url: [
          {
            required: true,
            message: "请输入链接",
            trigger: "blur",
          },
        ],
        name: [
          {
            required: true,
            message: "请输入链接名称",
            trigger: "blur",
          },
        ],
        sort: [
          {
            required: true,
            message: "请输入排序",
            trigger: "blur",
          },
        ],
        condition: [
          {
            required: true,
            message: "请选择状态",
            trigger: "blur",
          },
        ],
        subject: [
          {
            required: true,
            message: "请输入主体",
            trigger: "blur",
          },
        ],

      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    addDomain() {
      this.domainAvisible = true;
      this.isAdd = true;
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    //获取列表
    getList() {
      this.loading = true;
      getDomainList(this.queryParams).then((res) => {
        this.domianList = res.rows;
        this.total = res.total;
        this.loading = false;
        if (this.domianList.length == 0 && this.queryParams.pageNum > 1) {
          this.queryParams.pageNum = this.queryParams.pageNum - 1;
          this.loading = false;
          this.getList();
          return;
        }
      });
    },
    handleEdit(row) {
      this.isAdd = false;
      this.domainAvisible = true;
      this.formData.id = row.id;
      this.formData.name = row.name;
      this.formData.url = row.url;
      this.formData.sort = row.sort;
      this.formData.condition = row.condition;
      this.formData.subject = row.subject;
    },
    handleDelete(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delDomainOne({ id: row.id })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {});
        })
        .catch((err) => {});
    },
    cancel() {
      this.formData = {
        name: "",
        url: "",
        sort: "",
        subject: "",
        condition: 1,
      };
      this.isAdd = true;
      this.domainAvisible = false;
      this.$refs.formData.resetFields();
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            delete this.formData.sort;
            delete this.formData.condition;
            addDomainOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("添加成功");
              }
            });
          } else {
            this.formData.sort = this.formData.sort * 1;
            editDomainOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("修改成功");
              }
            });
          }
        }
      });
    },
  },
  mounted() {
    this.getList();

  },
};
</script>

<style lang="scss" scoped>
</style>
