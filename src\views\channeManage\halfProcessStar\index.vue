<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" size="small">
      <el-form-item label="日期" prop="date">
        <el-date-picker v-model="queryParams.date" type="date" size="small" value-format="yyyy-MM-dd"
          placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道名称">
        <el-input v-model="queryParams.channelName" placeholder="请输入渠道名称"
          @keyup.enter.native="fetchTableData"></el-input>
      </el-form-item>
      <el-form-item label="渠道id">
        <el-input v-model="queryParams.channelId" placeholder="请输入渠道id" @keyup.enter.native="fetchTableData"></el-input>
      </el-form-item>
      <el-form-item label="产品名称">
        <el-input v-model="queryParams.productName" placeholder="请输入产品名称"
          @keyup.enter.native="fetchTableData"></el-input>
      </el-form-item>
      <el-form-item label="产品id">
        <el-input v-model="queryParams.productId" placeholder="请输入产品id" @keyup.enter.native="fetchTableData"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="fetchTableData">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" style="width: 100%" row-key="rowKey" :tree-props="{
      children: 'children',
    }">
      <el-table-column label="渠道ID" align="center" prop="channelId">
        <template slot-scope="{ row }">
          <span v-if="!row.platformId">
            {{ row.channelId }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="channelName" label="渠道名称">
        <template slot-scope="{ row }">
          <el-tag size="small" v-if="!row.platformId" style="white-space: normal; word-break: break-all;">{{
            row.channelName
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="platformName" label="平台名称">
        <template slot-scope="{ row }">
          <el-tag type="info" v-if="row.platformId">{{ row.platformName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="0星数量" align="center" prop="zeroStar"></el-table-column>
      <el-table-column label="1星数量" align="center" prop="oneStar"></el-table-column>
      <el-table-column label="2星数量" align="center" prop="twoStar"></el-table-column>
      <el-table-column label="3星数量" align="center" prop="threeStar"></el-table-column>
      <el-table-column label="4星数量" align="center" prop="fourStar"></el-table-column>
      <el-table-column label="5星数量" align="center" prop="fiveStar"></el-table-column>
      <el-table-column label="合计" align="center" prop="total"></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button v-if="row.platformId" @click="handleDetail(row)" type="text">查看详情</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :visible.sync="dialogVisible" title="详情" width="80%" append-to-body>
      <el-table :data="detailList" border>
        <el-table-column label="产品ID" align="center" prop="productId"></el-table-column>
        <el-table-column label="产品名称" align="center" prop="productName"></el-table-column>
        <el-table-column label="推送时间" align="center" prop="pushTime"></el-table-column>
        <el-table-column label="0星数量" align="center" prop="zeroStar"></el-table-column>
        <el-table-column label="1星数量" align="center" prop="oneStar"></el-table-column>
        <el-table-column label="2星数量" align="center" prop="twoStar"></el-table-column>
        <el-table-column label="3星数量" align="center" prop="threeStar"></el-table-column>
        <el-table-column label="4星数量" align="center" prop="fourStar"></el-table-column>
        <el-table-column label="5星数量" align="center" prop="fiveStar"></el-table-column>
        <el-table-column label="合计" align="center" prop="total"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  getChannelListStatistics,
  getProductListStatistics,
} from "@/api/channeManage/halfProcessStar";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

export default {
  name: "HalfProcessStar",

  data() {
    return {
      total: 0,
      dialogVisible: false,
      detailList: [],
      currentQueryParams: null,
      queryParams: {
        date: "",
        channelName: "",
        channelId: "",
        platformId: "",
        productName: "",
        productId: "",
      },
      tableData: [],
    };
  },

  mounted() {
    this.setDefaultDate();
    this.fetchTableData();
  },

  methods: {
    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format("YYYY-MM-DD");
      this.queryParams.date = startTime;
    },

    resetQuery() {
      this.queryParams = {
        date: dayjs().format("YYYY-MM-DD"),
        channelName: "",
        channelId: "",
        platformId: "",
        productName: "",
        productId: "",
      };
      this.fetchTableData();
    },

    handleTableData(arr) {
      arr.forEach((row) => {
        row.rowKey = uuidv4();
        if (row.children && row.children.length) {
          row.children.forEach((innerRow) => {
            innerRow.rowKey = uuidv4();
          });
        }
      });
    },

    async fetchTableData() {
      const res = await getChannelListStatistics(this.queryParams);
      if (res.code == 200) {
        this.currentQueryParams = { ...this.queryParams };
        this.handleTableData(res.data);
        this.tableData = res.data;
      }
    },

    handleDetail(row) {
      this.detailList = [];
      getProductListStatistics({
        ...this.currentQueryParams,
        platformId: row.platformId,
        channelId: row.channelId,
        channelName: row.channelName,
      }).then((res) => {
        if (res.code == 200) {
          this.detailList = res.data;
          this.dialogVisible = true;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-tag {
  height: auto;
  padding: 2px 5px;
  line-height: 18px;
}
</style>