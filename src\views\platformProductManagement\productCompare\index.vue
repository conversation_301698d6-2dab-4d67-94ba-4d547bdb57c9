<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" ref="queryForm" size="small">
      <el-form-item label="时间范围" prop="timeRange">
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item label="平台" prop="platformIds">
        <el-select
          v-model="queryParams.platformIds"
          multiple
          clearable
          placeholder="请选择平台"
        >
          <el-option
            v-for="item in platformOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="平台名称" prop="platformName">
        <el-input
          v-model="queryParams.platformName"
          placeholder="请输入平台名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="产品ID" prop="productId">
        <el-input
          v-model="queryParams.productId"
          placeholder="请输入产品ID"
          clearable
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border style="width: 100%">
      <!-- <el-table-column prop="platformId" label="平台ID" align="center" width="80"/> -->
      <el-table-column prop="platformName" label="平台名称" align="center" />
      <el-table-column prop="productId" label="产品ID" align="center" />
      <el-table-column prop="productName" label="产品名称" align="center" />
      <el-table-column prop="productPrice" label="产品价格" align="center" sortable>
        <template slot-scope="scope">{{ scope.row.productPrice }}</template>
      </el-table-column>
      <el-table-column prop="productType" label="产品类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.productType == 1 ? "线上" : "线下" }}
        </template>
      </el-table-column>
      <el-table-column prop="configId" label="配置id" align="center" />
      <el-table-column prop="configName" label="配置名称" align="center" />
      <!-- <el-table-column prop="configKey" label="配置key" align="center" /> -->
      <el-table-column prop="checkTotal" label="撞库总数" align="center" sortable />
      <el-table-column
        prop="checkSuccessNum"
        label="撞库成功数量"
        align="center"
        sortable
      />
      <el-table-column
        prop="checkFailureNum"
        label="撞库失败数量"
        align="center"
        sortable
      />
      <el-table-column
        prop="pushSuccessNum"
        label="推送成功数量"
        align="center"
        sortable
      />
      <el-table-column
        prop="checkSuccessRate"
        label="撞库成功率"
        align="center"
        sortable
      >
        <template slot-scope="scope">
          <span :style="{ color: scope.row.checkSuccessRate < 10 ? '#ff4949' : ''}">
            {{ scope.row.checkSuccessRate.toFixed(2) }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="applySuccessRate"
        label="申请成功率"
        align="center"
        sortable
      >
        <template slot-scope="scope">
          <span :style="{ color: scope.row.applySuccessRate < 10 ? '#ff4949' : ''}">
            {{ scope.row.applySuccessRate.toFixed(2) }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="exposeNum" label="曝光数" align="center" sortable />
      <el-table-column prop="applyNum" label="申请数" align="center" sortable />
      <el-table-column prop="notApplyNum" label="未申请数量" align="center" sortable />
      <el-table-column prop="applyFailNum" label="申请失败数量" align="center" sortable />
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleViewFailRecords(scope.row)"
          >查看失败记录</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 失败记录弹窗组件 -->
    <fail-record-dialog
      :visible.sync="failRecordDialogVisible"
      :query-params="failRecordQueryParams"
    />
  </div>
</template>

<script>
import { getProductCheckPushStatistics } from "@/api/platformProductManagement/productCompare";
import { getPlatformList } from "@/api/xmxrChannelManage/channelList";
import FailRecordDialog from './components/failRecordDialog.vue';
import dayjs from "dayjs";

export default {
  name: "ProductCompare",
  components: {
    FailRecordDialog
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        platformIds: [],
        platformName: "",
        productId: "",
        productName: "",
        startTime: "",
        endTime: "",
      },
      // 保存最后一次成功查询的参数
      lastSuccessQueryParams: null,
      // 时间范围
      timeRange: [
        dayjs().format("YYYY-MM-DD 00:00:00"),
        dayjs().format("YYYY-MM-DD 23:59:59"),
      ],
      // 平台选项
      platformOptions: [],
      // 表格数据
      tableData: [],
      // 失败记录弹窗
      failRecordDialogVisible: false,
      // 失败记录查询参数
      failRecordQueryParams: {},
    };
  },
  created() {
    this.getPlatformOptions();
    this.getList();
  },
  methods: {
    // 获取平台选项
    async getPlatformOptions() {
      const res = await getPlatformList();
      if (res.data) {
        this.platformOptions = res.data;
      }
    },
    // 查询列表
    getList() {
      // 处理时间范围
      if (this.timeRange && this.timeRange.length === 2) {
        [this.queryParams.startTime, this.queryParams.endTime] = this.timeRange;
      } else {
        this.$message.warning("请选择时间范围");
        return;
      }

      getProductCheckPushStatistics(this.queryParams).then((res) => {
        // 保存成功查询的参数
        this.lastSuccessQueryParams = { ...this.queryParams };
        // 计算推送成功率并添加到表格数据中
        this.tableData = res.data.map(item => {
            // 撞库成功率 = 撞库成功数 / 撞库总数
            const checkSuccessRate = item.checkSuccessNum ? 
            (item.checkSuccessNum / item.checkTotal * 100) : 0;

            // 申请成功率 = 推送成功数 / (申请数+申请失败数量)
            const applyTotal = item.applyNum + item.applyFailNum;
            const applySuccessRate = applyTotal > 0 ? 
            (item.pushSuccessNum / applyTotal * 100) : 0;
            
          
          return {
            ...item,
            checkSuccessRate,
            applySuccessRate
          };
        });
      });
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList();
    },
    // 重置按钮操作
    resetQuery() {
      this.timeRange = [
        dayjs().format("YYYY-MM-DD 00:00:00"),
        dayjs().format("YYYY-MM-DD 23:59:59"),
      ];
      this.$refs.queryForm.resetFields();
      this.handleQuery();
    },
    // 查看失败记录
    handleViewFailRecords(row) {
      this.failRecordQueryParams = {
        startTime: this.lastSuccessQueryParams.startTime,
        endTime: this.lastSuccessQueryParams.endTime,
        productName: row.productName,
        partyName: row.partyName
      };
      this.failRecordDialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped></style>
