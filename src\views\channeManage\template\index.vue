<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      @submit.native.prevent
      :inline="true"
    >
      <el-form-item label="标识名称" prop="styleName">
        <el-input
          clearable
          placeholder="请输入标识名称"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.name"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="addDomain"
          >新增模板</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="domianList">
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="模板名称" prop="name" align="center" />
      <el-table-column label="模板标识" prop="templateSig" align="center" />
      <el-table-column label="短信KEY" prop="smsKey" align="center" />
      <el-table-column label="状态" prop="status" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.status"
              active-text="正常"
              inactive-text="禁用"
              active-value="1"
              inactive-value="2"
              @change="changeStatus($event, row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="备注" prop="remark" align="center" />
      <el-table-column align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button
              type="text"
              @click="handleEdit(row)"
              icon="el-icon-edit-outline"
              >修改</el-button
            >
            <el-button
              type="text"
              @click="handleDelete(row)"
              icon="el-icon-delete"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加 -->
    <el-dialog
      :title="isAdd ? '添加模板' : '修改模板'"
      :visible.sync="domainAvisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="渠道模板标识" prop="templateSig">
          <el-input
            :disabled="!isAdd"
            v-model="formData.templateSig"
            placeholder="请输入渠道模板标识"
          />
        </el-form-item>
        <el-form-item label="短信KEY" prop="smsKey">
          <el-select v-model="formData.smsKey" filterable clearable style="width: 100%;">
            <el-option v-for="item in smsTemplateList" :key="item.smsTemplateKey" :label="item.keyName" :value="item.smsTemplateKey"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="formData.remark"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getChannelTypeList,
  addChannelTypeList,
  delChannelTypeOne,
  updateChannelTypeOne,
  updatedetailChannelTypeOne,
} from "@/api/channeManage/template";

import { getSmsTemplateList } from "@/api/threeParty/note";
export default {
  name: "Domain",
  data() {
    return {
      total: 0,
      isAdd: true,
      domainAvisible: false,
      domianList: [],
      channleType: [],
      smsTemplateList: [],
      queryParams: {
        name: "",
        pageNum: 1,
        pageSize: 10,
      },
      formData: {
        name: "",
        remark: "",
        templateSig: "",
        smsKey:"",
      },
      rules: {
        name: [
          {
            required: true,
            message: "请输入模板名称",
            trigger: "blur",
          },
        ],
        templateSig: [
          {
            required: true,
            message: "请输入模板标识",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    addDomain() {
      this.domainAvisible = true;
      this.isAdd = true;
      if (this.formData.id) {
        this.formData.id;
      }
    },
    changeStatus(e, row) {
      this.$confirm(`确定${row.states == "2" ? "禁用" : "启用"}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          updateChannelTypeOne({ status: e, id: row.id })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {});
        })
        .catch((err) => {
          this.getList();
        });
    },
    //获取列表
    getList() {
      getChannelTypeList(this.queryParams).then((res) => {
        this.domianList = res.rows;
        this.total = res.total;
        if (this.domianList.length == 0 && this.queryParams.pageNum > 1) {
          this.queryParams.pageNum = this.queryParams.pageNum - 1;
          this.getList();
          return;
        }
      });
    },
    handleEdit(row) {
      this.isAdd = false;
      this.domainAvisible = true;

      this.formData.name = row.name;
      this.formData.remark = row.remark;
      this.formData.templateSig = row.templateSig;
      this.formData.id = row.id;
      this.formData.smsKey = row.smsKey;
    },
    handleDelete(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delChannelTypeOne(row.id)
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {});
        })
        .catch((err) => {});
    },
    cancel() {
      this.formData = {
        name: "",
        remark: "",
      };
      this.isAdd = true;
      this.domainAvisible = false;
      this.$refs.formData.resetFields();
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addChannelTypeList(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("添加成功");
              }
            });
          } else {
            updatedetailChannelTypeOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("修改成功");
              }
            });
          }
        }
      });
    },
  },
  mounted() {
    this.getList();
    getSmsTemplateList().then((res) => {
      this.smsTemplateList = res.data;
      console.log(res.data);
    });
  },
};
</script>

<style lang="scss" scoped></style>
