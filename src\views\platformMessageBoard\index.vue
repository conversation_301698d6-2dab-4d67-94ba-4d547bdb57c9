<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="搜索内容" prop="searchText">
        <el-input
          v-model="queryParams.searchText"
          placeholder="请输入标题或内容"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="messageList" border>
      <el-table-column label="序号" type="index" width="50" />
      <el-table-column label="标题" prop="title" min-width="150" show-overflow-tooltip />
      <el-table-column label="内容" prop="content" min-width="200" show-overflow-tooltip />
      <el-table-column label="平台ID" prop="platformId" width="100" />
      <el-table-column label="用户ID" prop="userId" width="100" />
      <el-table-column label="类型" prop="type" width="80">
        <template slot-scope="scope">
          {{ getTypeText(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column label="小马读取状态" prop="adminReadable" width="120" align="center">
        <template slot-scope="scope">
          <span :class="getReadStatusClass(scope.row.adminReadable)">
            {{ getReadStatusText(scope.row.adminReadable) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="CRM读取状态" prop="crmReadable" width="120" align="center">
        <template slot-scope="scope">
          <span :class="getReadStatusClass(scope.row.crmReadable)">
            {{ getReadStatusText(scope.row.crmReadable) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" width="120" />
      <el-table-column label="创建时间" prop="created" width="180">
        <template slot-scope="scope">
          {{ formatDate(scope.row.created) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-chat-line-round"
            @click="handleViewChat(scope.row)"
          >留言记录</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 留言记录抽屉 -->
    <MessageRecordDrawer ref="messageRecordDrawer" />
  </div>
</template>

<script>
import { searchPlatformMessageBoard } from '@/api/platformMessageBoard'
import Pagination from '@/components/Pagination'
import MessageRecordDrawer from './components/MessageRecordDrawer.vue'
import dayjs from 'dayjs'

export default {
  name: 'PlatformMessageBoard',
  components: {
    Pagination,
    MessageRecordDrawer
  },
  data() {
    return {
      // 总条数
      total: 0,
      // 留言列表
      messageList: [],
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        searchText: ''
      },
      // 类型配置
      typeConfig: {
        0: { text: '产品建议' },
        1: { text: '质量反馈' }
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询留言列表 */
    getList() {
      const params = {
        page: this.queryParams.page, // 后端从1开始
        size: this.queryParams.size,
        searchText: this.queryParams.searchText
      }
      searchPlatformMessageBoard(params).then(response => {
        if (response.success && response.data) {
          this.messageList = response.data.records || []
          this.total = response.data.total || 0
        } else {
          this.messageList = []
          this.total = 0
        }
      }).catch(() => {
        this.messageList = []
        this.total = 0
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 格式化日期 */
    formatDate(date) {
      if (!date) return ''
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    /** 查看留言记录 */
    handleViewChat(row) {
      this.$refs.messageRecordDrawer.open(row)
    },
    /** 获取类型文本 */
    getTypeText(type) {
      return this.typeConfig[type]?.text || '未知'
    },
    /** 获取读取状态文本 */
    getReadStatusText(status) {
      return status === 1 ? '已读' : '未读'
    },
    /** 获取读取状态样式类 */
    getReadStatusClass(status) {
      return status === 1 ? 'status-read' : 'status-unread'
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

/* 读取状态样式 */
.status-read {
  color: #67c23a;
  font-weight: 500;
}

.status-unread {
  color: #f56c6c;
  font-weight: 500;
}
</style>
