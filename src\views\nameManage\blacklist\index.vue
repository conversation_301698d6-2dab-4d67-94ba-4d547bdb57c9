<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item label="手机号">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入手机号"
          size="small"
          clearable
          v-model="queryParams.phone"
        ></el-input>
      </el-form-item>
      <el-form-item label="关联姓名">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="关联姓名"
          size="small"
          clearable
          v-model="queryParams.name"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="身份证">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入身份证"
          size="small"
          clearable
          v-model="queryParams.idCard"
        ></el-input>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增黑名单</el-button
        >
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border>
      <!-- <el-table-column label="用户身份证" align="center" prop="idCard" /> -->
      <el-table-column label="关联手机号" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.phone }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="关联姓名"
        align="center"
        prop="blockName"
      ></el-table-column>
      <el-table-column
        label="添加时间"
        align="center"
        prop="createTime"
      ></el-table-column>
      <el-table-column
        label="注册时间"
        align="center"
        prop="registerTime"
      ></el-table-column>
      <el-table-column
        label="注册渠道ID"
        align="center"
        prop="channelId"
      ></el-table-column>
      <el-table-column
        label="最后登录时间"
        align="center"
        prop="lastLoginTime"
      ></el-table-column>

      <el-table-column label="申请记录" align="center">
        <template  slot-scope="{row}">
          <div
            v-if="row.applyProductRecord && row.applyProductRecord.length > 0"
          >
            {{ row.applyProductRecord.join(",") }}
          </div>
        </template>
      </el-table-column>
           <el-table-column
        label="备注"
        align="center"
        prop="remark"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button size="mini" @click="recovery(row)" type="text"
              >恢复账号</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="新增黑名单"
      :visible.sync="balckAvisible"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="cancle"
    >
      <el-form
        ref="balckForm"
        :model="balckForm"
        :rules="balckRules"
        label-width="80px"
        @submit.native.prevent
      >
        <el-form-item label="手机号" prop="phone">
          <el-input
            size="small"
            maxlength="11"
            v-model="balckForm.phone"
            placeholder="请输入手机号"
          ></el-input>
        </el-form-item>
        <el-form-item label="名字" prop="name">
          <el-input
            size="small"
            maxlength="20"
            v-model="balckForm.name"
            placeholder="请输入名字"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancle">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAppUserBlackList,
  getAppUserBlackOne,
  addBlackByName,
} from "@/api/nameManage";
export default {
  name: "Blackist",
  data() {
    return {
      total: 0,
      balckAvisible: false,
      balckRules: {
        phone: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
        ],
        name: []
      },
      balckForm: {
        name: "",
        phone: ""
      },
      dataList: [],
      queryParams: {
        phone: "",
        idCard: "",
        pageNum: 1,
        pageSize: 10,
        name:""
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    recovery(row) {
      this.$confirm("确定恢复此用户吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          getAppUserBlackOne(row.blockId).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              this.getList();
            }
          });
        })
        .catch(() => {});
    },
    handleAdd() {
      this.balckAvisible = true;
    },
    submitForm() {
      this.$refs.balckForm.validate((valid) => {
        if (valid) {
          addBlackByName({
            name: this.balckForm.name,
            phone: this.balckForm.phone
          }).then((res) => {
            if (res.code == 200) {
              this.$message.success("新增成功");
              this.cancle();
              this.getList();
            }
          });
        }
      });
    },
    cancle() {
      this.balckAvisible = false;
      this.balckForm = {
        name: "",
        phone: ""
      };
      this.$refs.balckForm.resetFields();
    },

    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    getList() {
      getAppUserBlackList(this.queryParams).then((res) => {
        this.dataList = res.rows;

        this.total = res.total;
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
