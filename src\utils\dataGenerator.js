/**
 * 生成指定长度的随机字符串
 * @param {number} length - 要生成的字符串长度
 * @param {string} [characters] - 可选的字符集，默认包含数字和小写字母
 * @returns {string} 生成的随机字符串
 */
export function generateRandomString(length, characters = '0123456789abcdefghijklmnopqrstuvwxyz') {
  let result = '';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}
