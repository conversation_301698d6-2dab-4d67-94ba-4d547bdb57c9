import request from '@/utils/request'
//查询标签列表
export function getTagList(data) {
  return request({
    url: '/saas/lable/getlist',
    method: "get",
    params: data
  })
}


//新增标签
export function addTagOne(data) {
  return request({
    url: "/saas/lable/addone",
    method: "post",
    data
  })
}

//修改标签状态
export function changeTagStatus(data) {
  return request({
    url: '/saas/lable/updateStatus',
    method: "post",
    data
  })
}

//修改标签名称
export function editTagOne(data) {
  return request({
    url: "/saas/lable/updateone",
    method: "post",
    data
  })
}
