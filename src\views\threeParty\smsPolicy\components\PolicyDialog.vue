<template>
  <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogVisible" @close="handleClose" @open="handleOpen" width="1000px">
    <el-form
      ref="dataForm"
      :rules="rules"
      :model="temp"
      label-position="left"
      label-width="120px"
    >
      <el-form-item label="策略名称" prop="name">
        <el-input v-model="temp.name" style="width: 300px"/>
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input v-model="temp.remark" type="textarea" style="width: 300px"/>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="temp.type" placeholder="请选择" style="width: 300px">
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发送量" prop="limit">
        <template slot="label">
          发送量
          <el-tooltip content="每批次最大短信发送量" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <el-input-number v-model="temp.limit" :min="1" style="width: 300px"/>
      </el-form-item>
      <el-form-item label="状态">
        <el-radio-group v-model="temp.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <!-- 用户资质筛选 -->
      <el-divider content-position="left">用户资质筛选</el-divider>
      <el-form-item label="开始时间(分钟)" prop="qualificationCondition.intervalMinutes">
        <el-input-number
          style="width: 300px"
          v-model="temp.qualificationCondition.intervalMinutes" 
          :min="0" 
          placeholder="请输入开始时间" 
        />
      </el-form-item>
      
      <el-form-item label="结束时间(分钟)" prop="qualificationCondition.intervalMinutesForNow">
        <el-input-number 
          style="width: 300px"
          v-model="temp.qualificationCondition.intervalMinutesForNow" 
          :min="0" 
          placeholder="请输入结束时间" 
        />
      </el-form-item>
      
      <el-form-item label="芝麻分">
        <el-select v-model="temp.qualificationCondition.sesameIds" multiple placeholder="请选择" style="width: 300px">
          <el-option label="无" value="108" />
          <el-option label="550-579" value="109" />
          <el-option label="600分以下" value="110" />
          <el-option label="600-619" value="111" />
          <el-option label="600-650分" value="112" />
          <el-option label="650-700分" value="113" />
          <el-option label="700-749" value="114" />
          <el-option label="700分以上" value="115" />
        </el-select>
      </el-form-item>

      <el-form-item label="信用情况">
        <el-select v-model="temp.qualificationCondition.overdues" multiple placeholder="请选择" style="width: 300px">
          <el-option label="信用良好，无逾期" value="122" />
          <el-option label="无信用卡或贷款" value="123" />
          <el-option label="近1年无逾期" value="124" />
          <el-option label="1年内逾期少于3次且少于90天" value="125" />
          <el-option label="1年内逾期超过3次或者90天" value="126" />
        </el-select>
      </el-form-item>

      <el-form-item label="包含城市">
        <el-cascader
          style="width: 600px"
          v-model="temp.qualificationCondition.inCities"
          :options="cityOptions"
          :props="{
            multiple: true,
            checkStrictly: false,
            value: 'name',
            label: 'name',
            children: 'citys',
            emitPath: false,
            leaf: true
          }"
          clearable
          filterable
          placeholder="请选择城市"
        />
      </el-form-item>

      <el-form-item label="排除城市">
        <el-cascader
          style="width: 600px"
          v-model="temp.qualificationCondition.notInCities"
          :options="cityOptions"
          :props="{
            multiple: true,
            checkStrictly: false,
            value: 'name',
            label: 'name',
            children: 'citys',
            emitPath: false,
            leaf: true
          }"
          clearable
          filterable
          placeholder="请选择城市"
        />
      </el-form-item>

      <el-form-item label="包含渠道">
        <el-select
          v-model="temp.qualificationCondition.inChannelIds"
          multiple
          filterable
          placeholder="请选择包含的渠道"
          style="width: 600px"
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.id"
            :label="`${item.id} - ${item.channelName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="排除渠道">
        <el-select
          v-model="temp.qualificationCondition.notInChannelIds"
          multiple
          filterable
          placeholder="请选择排除的渠道"
          style="width: 600px"
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.id"
            :label="`${item.id} - ${item.channelName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="年龄条件">
        <template slot="label">
          年龄条件
          <el-tooltip content="年龄范围：0-150岁" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <el-row :gutter="10">
          <el-col :span="4">
            <el-select v-model="temp.qualificationCondition.age.type" placeholder="请选择" @change="handleAgeTypeChange">
              <el-option label="小于" :value="0" />
              <el-option label="小于等于" :value="1" />
              <el-option label="大于" :value="2" />
              <el-option label="大于等于" :value="3" />
              <el-option label="等于" :value="4" />
              <el-option label="区间" :value="5" />
              <el-option label="包含" :value="6" />
              <el-option label="不包含" :value="7" />
            </el-select>
          </el-col>
          <el-col :span="16">
            <template v-if="temp.qualificationCondition.age.type == 5">
              <el-input-number 
                v-model="ageRange.start" 
                :min="0" 
                :max="150"
                :precision="0"
                @change="handleAgeRangeChange"
                style="width: 120px"
              />
              <span style="margin: 0 10px">至</span>
              <el-input-number 
                v-model="ageRange.end" 
                :min="0" 
                :max="150"
                :precision="0"
                @change="handleAgeRangeChange"
                style="width: 120px"
              />
            </template>
            <template v-else-if="temp.qualificationCondition.age.type == 6 || temp.qualificationCondition.age.type == 7">
              <el-select
                v-model="temp.qualificationCondition.age.vars"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请输入年龄，可多选"
                style="width: 100%"
                @change="handleAgeSelectChange"
              >
                <el-option
                  v-for="age in temp.qualificationCondition.age.vars"
                  :key="age"
                  :label="age"
                  :value="age"
                />
              </el-select>
            </template>
            <template v-else>
              <el-input-number 
                v-model="singleAge" 
                :min="0" 
                :max="150"
                :precision="0"
                @change="handleSingleAgeChange"
                style="width: 120px"
              />
            </template>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 渠道控量配置 -->
      <el-divider content-position="left">渠道控量配置</el-divider>
      <el-table :data="temp.channelLimitConfigs" style="width: 100%">
        <el-table-column label="短信模板" width="200">
          <template slot-scope="{row, $index}">
            <el-form-item 
              label-width="0px"
              :prop="'channelLimitConfigs.' + $index + '.templateId'"
              :rules="{
                required: true,
                message: '请选择短信模板',
                trigger: 'change'
              }"
            >
              <el-select v-model="row.templateId" placeholder="请选择" style="width: 100%" filterable>
                <el-option
                  v-for="item in templateOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        
        <el-table-column label="营销渠道" width="200">
          <template slot-scope="{row, $index}">
            <el-form-item 
              label-width="0px"
              :prop="'channelLimitConfigs.' + $index + '.channelId'"
              :rules="{
                required: true,
                message: '请选择营销渠道',
                trigger: 'change'
              }"
            >
              <el-select v-model="row.channelId" placeholder="请选择" style="width: 100%" filterable>
                <el-option
                  v-for="item in channelOptions"
                  :key="item.id"
                  :label="`${item.id} - ${item.channelName}`"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        
        <el-table-column label="发送占比" width="200">
          <template slot-scope="{row, $index}">
            <el-form-item 
              label-width="0px"
              :prop="'channelLimitConfigs.' + $index + '.sendRate'"
              :rules="{
                required: true,
                message: '请输入发送占比',
                trigger: 'blur'
              }"
            >
              <el-input-number
                v-model="row.sendRate"
                :precision="2"
                :step="0.1"
                :max="1"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </template>
        </el-table-column>
        
        <el-table-column label="状态">
          <template slot-scope="{row, $index}">
            <el-form-item 
              label-width="0px"
              :prop="'channelLimitConfigs.' + $index + '.hasEnable'"
            >
              <el-switch
                v-model="row.hasEnable"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="{$index}">
            <el-form-item label-width="0px" style="text-align: start;">
              <el-button 
                type="danger" 
                icon="el-icon-delete" 
                circle 
                @click="removeChannelConfig($index)" 
              />
              <el-button 
                v-if="$index == temp.channelLimitConfigs.length - 1"
                type="primary" 
                icon="el-icon-plus" 
                circle 
                @click="addChannelConfig" 
              />
            </el-form-item>
          </template>
        </el-table-column>

        <template slot="empty">
          <div style="text-align: center; padding: 20px;">
            <p>暂无数据</p>
            <el-button type="primary" @click="addChannelConfig">添加渠道配置</el-button>
          </div>
        </template>
      </el-table>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">
        取消
      </el-button>
      <el-button type="primary" @click="handleSubmit">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getCityList } from '@/api/statisticalManage'
import { SMS_POLICY_TYPE_OPTIONS } from '@/const/smsPolicy'

export default {
  name: 'PolicyDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogStatus: {
      type: String,
      default: 'create'
    },
    rowData: {
      type: Object,
      default: () => ({})
    },
    templateOptions: {
      type: Array,
      default: () => []
    },
    channelOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      cityOptions: [],
      typeOptions: SMS_POLICY_TYPE_OPTIONS,
      cityLoading: false,
      ageRange: {
        start: 0,
        end: 0
      },
      singleAge: 0,
      temp: {
        id: undefined,
        name: '',
        remark: '',
        status: 0,
        limit: 1,
        type: undefined,
        qualificationCondition: {
          intervalMinutesForNow: 0,
          intervalMinutes: 0,
          sesameIds: [],
          inCities: [],
          notInCities: [],
          inChannelIds: [],
          notInChannelIds: [],
          overdues: [],
          age: {
            vars: [],
            type: 0
          }
        },
        channelLimitConfigs: []
      },
      textMap: {
        update: '编辑策略',
        create: '新增策略'
      },
      rules: {
        name: [{ required: true, message: '策略名称不能为空', trigger: 'blur' }],
        remark: [{ required: true, message: '描述不能为空', trigger: 'blur' }],
        type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
        limit: [{ required: true, message: '每批次最大短信发送量不能为空', trigger: 'blur' }],
        'qualificationCondition.age.vars': [{ required: true, message: '年龄条件不能为空', trigger: 'change' }],
        'channelLimitConfigs': [{
          required: true,
          validator: (rule, value, callback) => {
            if (!value || value.length == 0) {
              callback(new Error('请至少添加一个渠道配置'))
            } else {
              callback()
            }
          },
          trigger: 'change'
        }]
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    }
  },
  created() {
    this.getCityOptions()
  },
  methods: {
    handleOpen() {
      if (Object.keys(this.rowData).length > 0) {
        this.temp = Object.assign({}, this.rowData)
        this.handleAgeDataDisplay()
      } else {
        this.resetTemp()
        this.resetAgeData()
      }
    },
    // 处理年龄数据的显示
    handleAgeDataDisplay() {
      const age = this.temp.qualificationCondition.age
      const ageType = age.type
      const ageVars = age.vars || []

      // 重置年龄相关数据
      this.resetAgeData()

      // 根据年龄类型处理显示
      switch (ageType) {
        case 5: // 区间
          if (ageVars.length >= 2) {
            this.ageRange = {
              start: parseInt(ageVars[0]) || 0,
              end: parseInt(ageVars[1]) || 0
            }
          }
          break
        case 6: // 包含
        case 7: // 不包含
          break
        default: // 其他类型（小于、大于等）
          this.singleAge = parseInt(ageVars[0]) || 0
          break
      }
    },
    // 重置年龄相关数据
    resetAgeData() {
      this.ageRange = {
        start: 0,
        end: 0
      }
      this.singleAge = 0
    },
    getCityOptions() {
      getCityList().then(response => {
        const cityData = response.data || []
        // 处理省份数据，添加省份名到选项中
        this.cityOptions = cityData.map(province => {
          if (province.citys) {
            province.citys = province.citys.map(city => ({
              ...city,
              name: city.name // 确保城市有name属性
            }))
          }
          return {
            ...province,
            name: province.name // 确保省份有name属性
          }
        })
      })
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        remark: '',
        status: 0,
        limit: 1,
        type: undefined,
        qualificationCondition: {
          intervalMinutesForNow: 0,
          intervalMinutes: 0,
          sesameIds: [],
          inCities: [],
          notInCities: [],
          inChannelIds: [],
          notInChannelIds: [],
          overdues: [],
          age: {
            vars: [],
            type: 0
          }
        },
        channelLimitConfigs: []
      }
    },
    addChannelConfig() {
      this.temp.channelLimitConfigs.push({
        templateId: undefined,
        hasEnable: true,
        sendRate: 1,
        channelId: undefined
      })
    },
    removeChannelConfig(index) {
      this.temp.channelLimitConfigs.splice(index, 1)
    },
    handleClose() {
      this.$refs['dataForm'].clearValidate()
      this.resetTemp()
      this.resetAgeData()
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$emit('submit', this.temp)
        }
      })
    },
    handleAgeRangeChange() {
      this.temp.qualificationCondition.age.vars = [
        this.ageRange.start.toString(),
        this.ageRange.end.toString()
      ]
    },
    handleSingleAgeChange() {
      this.temp.qualificationCondition.age.vars = [this.singleAge.toString()]
    },
    handleAgeSelectChange(value) {
      // 确保输入的都是有效的整数
      this.temp.qualificationCondition.age.vars = value
        .map(v => {
          const num = parseInt(v)
          // 验证是否为整数
          if (isNaN(num) || num < 0 || num > 150 || num.toString() !== v.trim()) {
            return null
          }
          return num.toString()
        })
        .filter(v => v != null)
    },
    // 处理年龄类型切换
    handleAgeTypeChange(type) {
      // 使用resetAgeData方法重置年龄相关的值
      this.resetAgeData()
      this.temp.qualificationCondition.age.vars = []

      // 根据类型设置默认值
      switch (type) {
        case 5: // 区间
          this.temp.qualificationCondition.age.vars = ['0', '0']
          break
        case 6: // 包含
        case 7: // 不包含
          break
        default: // 其他类型（小于、大于等）
          this.temp.qualificationCondition.age.vars = ['0']
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style> 