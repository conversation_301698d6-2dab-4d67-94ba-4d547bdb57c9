import request from '@/utils/request'

// 搜索平台广告
export function searchPlatformAdvertising(data) {
  return request({
    url: '/platformAdvertising/search',
    method: 'post',
    data: data
  })
}

// 添加平台广告
export function addPlatformAdvertising(data) {
  return request({
    url: '/platformAdvertising',
    method: 'post',
    data: data
  })
}

// 更新平台广告
export function updatePlatformAdvertising(id, data) {
  return request({
    url: `/platformAdvertising/${id}`,
    method: 'put',
    data: data
  })
}

// 更新平台广告状态
export function updatePlatformAdvertisingStatus(id, status) {
  return request({
    url: `/platformAdvertising/updateStatus/${id}/${status}`,
    method: 'post'
  })
}

// 删除平台广告
export function deletePlatformAdvertising(id) {
  return request({
    url: `/platformAdvertising/${id}`,
    method: 'delete'
  })
}
