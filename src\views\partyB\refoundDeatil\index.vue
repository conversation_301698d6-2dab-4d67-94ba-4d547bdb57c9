<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams">
      <el-form-item label="时间">
        <el-col :span="11">
          <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item label="状态">
        <el-select clearable size="small" v-model="queryParams.status">
          <el-option :value="0" label="待审核"> </el-option>
          <el-option :value="1" label="已通过"> </el-option>
          <el-option :value="2" label="已驳回"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery" size="small">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableList" border>
      <el-table-column label="申请时间" align="center" prop="applyTime" />
      <el-table-column label="申请金额" align="center" prop="refundMoney" />
      <el-table-column label="审核状态" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ statusJson[row.status] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="appUserName" />
      <el-table-column label="当前审核人" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.postName  }} <br> {{ row.userNames }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="退款凭证" align="center" prop="partyUsername">
        <template  slot-scope="{row}">
          <div v-if="row.fileName">
            <el-image style="width: 100px; height: 100px" :src="row.fileName" :preview-src-list="[row.fileName]">
            </el-image>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { getRefundList } from "@/api/partyB"
export default {
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        partybId: "",
        status: "",
        startTime: "",
        stopTime: ""
      },
      tableList: [],
      dateRange: [],
      total: 0,
      statusJson: {
        0: '待审核', 1: '已通过', 2: '已驳回'
      }
    }
  },
  methods: {
    getList() {
      getRefundList(this.queryParams).then(res => {

        this.tableList = res.rows
        this.total = res.total
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      if (this.dateRange && this.dateRange.length) {
        this.queryParams.startTime = this.dateRange[0]
        this.queryParams.stopTime = this.dateRange[1]
      } else {
        this.queryParams.startTime = ""
        this.queryParams.stopTime = ""
      }
      this.getList()
    }
  },
  mounted() {
    if (this.$route.query.id) {
      this.queryParams.partybId = this.$route.query.id
    } else {
      return
    }
    this.getList()
  }
}
</script>

<style lang="scss" scoped>
</style>
