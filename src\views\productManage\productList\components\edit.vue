<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :width="width"
    @close="handleCloseDialog"
    :close-on-click-modal="false"
  >
    <div class="dialog-wrap">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :rules="rules"
        label-position="top"
      >
        <el-row :gutter="32">
          <el-col :span="24">
            <div class="title">基本信息</div>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="LOGO" prop="file">
              <el-upload
                class="avatar-uploader"
                action
                :auto-upload="false"
                :on-change="changeUpImg"
                :show-file-list="false"
              >
                <img
                  v-if="imageUrl"
                  style="vertical-align: middle; max-height: 100px"
                  :src="imageUrl"
                />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div class="tips">上传图片只能是JPG/JPEG/PNG，控制在2M以内</div>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="推广名称" prop="name">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入推广名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24"></el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="商户类型" prop="mid">
              <el-select
                clearable
                v-model="queryParams.mid"
                placeholder="请选择商户类型"
                style="width: 100%"
              >
                <el-option label="银行机构" :value="1"> </el-option>
                <el-option label="线上-贷超" :value="2"> </el-option>
                <el-option label="线上持牌机构" :value="3"> </el-option>
                <el-option label="一级机构" :value="4"> </el-option>
                <el-option label="二级机构" :value="5"> </el-option>
                <el-option label="三级机构" :value="6"> </el-option>
                <el-option label="四级机构" :value="7"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :lg="12"
            :xl="12"
            :md="24"
            :xs="24"
            v-if="
              (queryParams.mid == 2 || queryParams.mid == 3) &&
              queryParams.cooperationMode != 1 &&
              queryParams.mid &&
              queryParams.cooperationMode
            "
          >
            <el-form-item label="所属接口" prop="productApiId">
              <el-select
                clearable
                v-model="queryParams.productApiId"
                placeholder="请选择所属接口"
                style="width: 100%"
              >
                <el-option
                  v-for="item in prodApiList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :lg="12"
            :xl="12"
            :md="24"
            :xs="24"
            v-if="
              queryParams.mid == 1 ||
              queryParams.mid == 4 ||
              queryParams.mid == 5 ||
              queryParams.mid == 6 ||
              queryParams.mid == 7
            "
          >
            <el-form-item label="接单类型" prop="loanId">
              <el-select
                clearable
                filterable
                v-model="queryParams.loanId"
                placeholder="请选择接单类型"
                @focus="getloanList"
                style="width: 100%"
              >
                <el-option
                  :label="item.name"
                  :value="item.id"
                  :key="item.id"
                  v-for="item in LoanList"
                >
                  {{ item.name }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :lg="12"
            :xl="12"
            :md="24"
            :xs="24"
            v-if="queryParams.mid == 2 || queryParams.mid == 3"
          >
            <el-form-item label="推广方式" prop="cooperationMode">
              <el-select
                clearable
                v-model="queryParams.cooperationMode"
                placeholder="请选择推广方式"
                style="width: 100%"
              >
                <el-option label="链接合作" :value="1" />
                <el-option label="接口合作" :value="2" />
                <el-option label="撞库+H5" :value="3" />
                <el-option label="撞库+连登H5" :value="4" />
                <el-option label="仅连登" :value="5" />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 非编辑模式下必选甲方/商户信息  -->
          <el-col :lg="12" :xl="12" :md="24" :xs="24" v-if="type != 'edit'">
            <el-form-item label="所属甲方" prop="partyaId">
              <el-select
                @focus="getPartyaId"
                clearable
                filterable
                v-model="queryParams.partyaId"
                placeholder="请选择所属甲方"
                style="width: 100%"
              >
                <el-option
                  :label="item.name"
                  :value="item.partyFirstId"
                  :key="item.partyFirstId"
                  v-for="item in acquirePartyList"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :lg="12"
            :xl="12"
            :md="24"
            :xs="24"
            v-if="showLinkFormItem"
          >
            <el-form-item label="推广链接" prop="cooperationLink">
              <el-input
                v-model="queryParams.cooperationLink"
                placeholder="请输入推广链接"
                clearable
                @blur="onCooperationLinkBlur"
              />
              <div class="el-form-item-tip" v-if="showCooperationLinkTip">
                {{ cooperationLinkCheckResponse.msg }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="推广类型" prop="cooperationType">
              <el-select
                @focus="getPackageList"
                clearable
                filterable
                v-model="queryParams.cooperationType"
                placeholder="请选择推广类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in packageList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="推广描述" prop="describe">
              <el-input
                v-model="queryParams.describe"
                placeholder="请输入推广描述"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="推广价格" prop="cooperationCost">
              <el-input
                type="number"
                :min="0"
                v-model="queryParams.cooperationCost"
                :disabled="
                  this.type == 'edit' && !isDisable && queryParams.mid == 2
                "
                placeholder="请输入推广价格"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="机构名称" prop="organName">
              <el-input
                v-model="queryParams.organName"
                placeholder="请输入机构名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="机构咨询热线" prop="helpline">
              <el-input
                v-model="queryParams.helpline"
                placeholder="请输入机构咨询热线"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="机构简介" prop="synopsis">
              <el-input
                type="textarea"
                v-model="queryParams.synopsis"
                placeholder="请输入机构简介"
                clearable
              />
            </el-form-item>
          </el-col>

          <div
            v-if="
              (queryParams.mid == 2 || queryParams.mid == 3) &&
              this.type == 'edit' &&
              hasBool('productManage:product:ZYXOnlineSetType')
            "
          >
            <el-col :span="24">
              <div class="title">线上类型</div>
            </el-col>
            <el-col :lg="12" :xl="12" :md="24" :xs="24">
              <el-row :gutter="12" class="flex">
                <el-col :span="12">
                  <el-select
                    v-model="onlineType"
                    placeholder="请选择线上类型"
                    style="width: 100%"
                    @change="handleChangeOnlineType"
                  >
                    <el-option
                      :label="item.businessName"
                      :value="item.businessId"
                      v-for="(item, index) in businessTypeOption"
                      :key="index"
                    >
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
            </el-col>
          </div>
          <el-col :span="24">
            <div class="title">推广利率信息</div>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-row :gutter="12" class="flex">
              <el-col :span="12">
                <el-form-item label="额度范围" prop="loanableFundsLittle">
                  <el-input
                    v-model="queryParams.loanableFundsLittle"
                    placeholder="请输入可贷额度小"
                    clearable
                    oninput="value=value.replace(/[^0-9]/g,'')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" style="margin-top: auto">
                <el-form-item
                  :label="isLable ? ' ' : ''"
                  prop="loanableFundsBig"
                >
                  <el-input
                    v-model="queryParams.loanableFundsBig"
                    placeholder="请输入可贷额度大"
                    clearable
                    oninput="value=value.replace(/[^0-9]/g,'')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="还款方式" prop="refundMode">
              <el-select
                clearable
                v-model="queryParams.refundMode"
                placeholder="请选择还款方式"
                style="width: 100%"
              >
                <el-option label="随借随还" :value="1"> </el-option>
                <el-option label="按月还款" :value="2"> </el-option>
                <el-option label="等额本息" :value="3"> </el-option>
                <el-option label="等额本金" :value="4"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="利率单位" prop="interestRateUnit">
              <el-select
                clearable
                v-model="queryParams.interestRateUnit"
                placeholder="请选择利率单位"
                style="width: 100%"
              >
                <!-- <el-option label="日利率" :value="1"> </el-option> -->
                <el-option label="月利率" :value="2"> </el-option>
                <el-option label="年利率" :value="3"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24" class="flex">
            <el-row :gutter="12" class="flex">
              <el-col :span="12">
                <el-form-item label="利率范围" prop="interestRateLittle">
                  <el-input
                    v-model="queryParams.interestRateLittle"
                    placeholder="请输入利率小"
                    clearable
                    :min="0"
                    oninput="value=value.replace(/[^0-9.]/g,'')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" style="margin-top: auto">
                <el-form-item
                  :label="isLable ? ' ' : ''"
                  prop="interestRateBig"
                >
                  <el-input
                    v-model="queryParams.interestRateBig"
                    placeholder="请输入利率大"
                    clearable
                    :min="0"
                    oninput="value=value.replace(/[^0-9.]/g,'')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="贷款期限单位" prop="loanPeriodUnit">
              <el-select
                clearable
                v-model="queryParams.loanPeriodUnit"
                placeholder="请选择利率单位"
                style="width: 100%"
              >
                <el-option label="月" :value="1"> </el-option>
                <!-- <el-option label="天" :value="2"> </el-option> -->
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-row :gutter="12" class="flex">
              <el-col :span="12">
                <el-form-item label="贷款期限" prop="loanPeriodLittle">
                  <el-input
                    type="number"
                    v-model="queryParams.loanPeriodLittle"
                    placeholder="请输入贷款期限开始"
                    clearable
                    :min="0"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" style="margin-top: auto">
                <el-form-item :label="isLable ? ' ' : ''" prop="loanPeriodBig">
                  <el-input
                    type="number"
                    v-model="queryParams.loanPeriodBig"
                    placeholder="请输入贷款期限结束"
                    clearable
                    :min="0"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-form-item label="办理时间单位" prop="handlingTimeUnit">
              <el-select
                clearable
                v-model="queryParams.handlingTimeUnit"
                placeholder="请选择办理时间单位"
                style="width: 100%"
              >
                <el-option label="分钟" :value="1"> </el-option>
                <el-option label="小时" :value="2"> </el-option>
                <el-option label="天" :value="3"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :xl="12" :md="24" :xs="24">
            <el-row :gutter="12" class="flex">
              <el-col :span="12">
                <el-form-item label="办理时间" prop="handlingTimeLittle">
                  <el-input
                    v-model="queryParams.handlingTimeLittle"
                    placeholder="请输入办理时间(开始)"
                    clearable
                    oninput="value=value.replace(/[^0-9.]/g,'')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" style="margin-top: auto">
                <el-form-item
                  :label="isLable ? ' ' : ''"
                  prop="handlingTimeBig"
                >
                  <el-input
                    v-model="queryParams.handlingTimeBig"
                    oninput="value=value.replace(/[^0-9.]/g,'')"
                    placeholder="请输入办理时间(结束)"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCloseDialog">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  getloanModeListsAll,
  getAcquirePartyAall,
  addProductOne,
  getProductOne,
  editProductCompile,
  getCityAll,
  getProductApiList,
  getFlowPackageList,
  getOnlineSetType,
  setOnlineSetType, checkCooperationLink
} from '@/api/productManage/product'
import BaseCascader from "@/components/cascader";
import { hasBool } from "@/directive/permission/hasBool";

export default {
  name: "edit",
  data() {
    let imgRule1 = (rule, value, callback) => {
      if (this.queryParams.file == "" && this.imageUrl == "") {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#F56C6C";
        callback(new Error("请上传LOGO"));
      } else if (this.queryParams.file || this.imageUrl) {
        callback();
      }
    };
    let httpsValidator = (rule, value, callback) => {
      if (!this.queryParams.cooperationLink) {
        callback(new Error("请输入推广链接"));
      } else if (!this.queryParams.cooperationLink.includes("https")) {
        callback(new Error("推广链接必须包含https"));
      } else if (this.queryParams.cooperationLink) {
        callback();
      }
    };
    return {
      hasBool,
      onlineType: undefined,
      showUsers: ["shenxun", "yutong", "sunyang","xupengcheng"],
      visible: false,
      isLable: false,
      title: "新建投放产品",
      imageUrl: "",
      prodApiList: [], // 所属api列表
      LoanList: [], // 助贷列表
      acquirePartyList: [], // 甲方列表
      packageList: [], // 推广类型
      queryParams: {
        name: "",
        mid: "",
        cooperationMode: "",
        cooperationLink: "",
        cooperationType: "",
        cooperationCost: "",
        describe: "",
        // district: [],
        // attribution: [],
        organName: "",
        helpline: "",
        synopsis: "",
        loanPeriodUnit: "",
        loanableFundsLittle: "",
        loanableFundsBig: "",
        interestRateUnit: "",
        interestRateLittle: "",
        interestRateBig: "",
        loanPeriodLittle: "",
        loanPeriodBig: "",
        handlingTimeUnit: "",
        refundMode: "",
        handlingTimeBig: "",
        handlingTimeLittle: "",
        file: "",
        loanId: "",
        productApiId: "",
        partyaId: "",
        vip: false,
      },
      businessTypeOption: [],
      rules: {
        name: [{ required: true, message: "请输入推广名称", trigger: "blur" }],
        vip: [{ required: true, message: "请选择推广类型", trigger: "blur" }],
        file: [{ required: true, message: "请上传LOGO", validator: imgRule1 }],
        mid: [{ required: true, message: "请选择商户类型", trigger: "blur" }],
        loanId: [
          { required: true, message: "请选择接单类型", trigger: "blur" },
        ],
        cooperationMode: [
          { required: true, message: "请选择推广方式", trigger: "blur" },
        ],
        partyaId: [
          { required: true, message: "请选择所属商户", trigger: "blur" },
        ],
        cooperationLink: [
          { required: true, validator: httpsValidator, trigger: "blur" },
        ],
        cooperationType: [
          { required: true, message: "请输入推广类型", trigger: "blur" },
        ],
        describe: [
          { required: true, message: "请输入推广描述", trigger: "blur" },
        ],
        cooperationCost: [
          { required: true, message: "请输入推广价格", trigger: "blur" },
        ],
        loanableFundsLittle: [
          { required: true, message: "请输入可贷额度", trigger: "blur" },
        ],
        loanableFundsBig: [
          { required: true, message: "请输入可贷额度", trigger: "blur" },
        ],
        interestRateUnit: [
          { required: true, message: "请选择利率单位", trigger: "blur" },
        ],
        interestRateLittle: [
          { required: true, message: "请输入利率", trigger: "blur" },
        ],
        interestRateBig: [
          { required: true, message: "请输入利率", trigger: "blur" },
        ],
        loanPeriodUnit: [
          { required: true, message: "请选择贷款期限单位", trigger: "blur" },
        ],
        loanPeriodLittle: [
          { required: true, message: "请选择贷款期限", trigger: "blur" },
        ],
        loanPeriodBig: [
          { required: true, message: "请选择贷款期限", trigger: "blur" },
        ],
        handlingTimeUnit: [
          { required: true, message: "请选择办理时间单位", trigger: "blur" },
        ],
        handlingTimeLittle: [
          { required: true, message: "请选择办理时间", trigger: "blur" },
        ],
        handlingTimeBig: [
          { required: true, message: "请选择办理时间", trigger: "blur" },
        ],
        refundMode: [
          { required: true, message: "请选择还款方式", trigger: "blur" },
        ],
        productApiId: [
          { required: true, message: "请选择所属接口", trigger: "blur" },
        ],
      },
      // 推广链接校验结果，检测当前推广链接是否能被修改
      cooperationLinkCheckResponse: {
        // 状态码，10201: 可修改；10202: 不可修改
        code: '',
        // 错误信息，展示在推广链接输入框下方
        msg: '',
      },
      cooperationLinkChecking: false,
    };
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    isQiWei: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "add",
    },
    width: {
      type: String,
      default: "814px",
    },
    id: {
      type: [Number, String],
      default: "",
    },
  },
  watch: {
    value: {
      handler() {
        this.visible = this.value;
        if (!this.value) return;
        this.init();
      },
      deep: true,
    },
    type() {
      this.title = this.type == "edit" ? "修改投放产品" : "新建投放产品";
    },
  },
  created() {},
  methods: {
    onCooperationLinkBlur() {
      this.$refs.queryForm.validateField("cooperationLink", async(errorMessage) => {
        if (errorMessage) {
          this.cooperationLinkCheckResponse = {
            code: '',
            msg: '',
          }
          return
        }

        try {
          this.cooperationLinkChecking = true;
          const res = await checkCooperationLink({
            cooperationLink: this.queryParams.cooperationLink,
            productId: this.id,
          });

          this.cooperationLinkCheckResponse = res;
        } finally {
          this.cooperationLinkChecking = false;
        }
      })
    },

    /**
     *  @param { Function } init 初始化
     */
    init() {
      // this.getloanList();
      // this.handleGetProductApiList();
      getloanModeListsAll().then((res) => {
        this.LoanList = res.data;
        getProductApiList().then((res) => {
          this.prodApiList = res.data;
          if (this.type == "edit" || this.type == "copy") {
            this.getPartyaId();
            this.getPackageList();
            if (hasBool("productManage:product:ZYXOnlineSetType")) {
              getOnlineSetType().then((res) => {
                this.businessTypeOption = res.data;
              });
            }

            //获取产品详情
            getProductOne({ id: this.id }).then((res) => {
              if (this.type == "edit") {
                this.imageUrl = res.data.logo;
              } else {
                this.imageUrl = "";
              }

              this.queryParams = res.data;
              this.onlineType = res.data.onlineType;
              if (this.isQiWei) {
                this.queryParams.loanId = res.data.loanId ?? "";
              }
              this.queryParams.id = this.id;

              this.queryParams = {
                ...this.queryParams,
                cooperationMode:
                  res.data.cooperationMode == 0
                    ? ""
                    : res.data.cooperationMode || "",
              };

              let index = this.LoanList.findIndex(
                (item) => item.id == this.queryParams.loanId
              );
              if (index == -1) {
                this.queryParams.loanId = "";
              }
              this.queryParams.file = "";
            });
          }
        });
      });
    },

    /**
     *  @param { Function } handleCloseDialog 关闭模态框
     */
    handleCloseDialog() {
      this.cooperationLinkCheckResponse = {
        code: '',
        msg: '',
      }
      this.cooperationLinkChecking = false;
      this.$refs.queryForm.resetFields();
      Object.assign(
        this.$data.queryParams,
        this.$options.data.call(this).queryParams
      );
      this.imageUrl = "";
      this.$emit("close", false);
    },

    /**
     *  @param { Function } handleSubmit 提交表单
     */
    handleSubmit() {
      if (
        this.queryParams.mid == 1 ||
        this.queryParams.mid == 4 ||
        this.queryParams.mid == 5 ||
        this.queryParams.mid == 6 ||
        this.queryParams.mid == 7
      ) {
        delete this.queryParams.cooperationMode;
        if (!this.isQiWei) {
          delete this.queryParams.cooperationLink;
        }
      }
      if (this.queryParams.mid == 2 || this.queryParams.mid == 3) {
        delete this.queryParams.loanId;
        if (this.queryParams.cooperationMode == 1) {
          delete this.queryParams.productApiId;
        }
      }
      if (this.queryParams.district) {
        delete this.queryParams.district;
      }

      let data = new FormData();
      for (let i in this.queryParams) {
        data.append(i, this.queryParams[i]);
      }
      this.$refs.queryForm.validate((valid) => {
        if (valid) {
          if (this.type == "edit") {
            editProductCompile(data).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.handleCloseDialog();
                this.$emit("update");
              }
              // 推广链接校验失败
              else if (res.code == 10201 || res.code == 10202) {
                this.cooperationLinkCheckResponse = res;
              }
            });
          } else {
            addProductOne(data).then((res) => {
              if (res.code == 200) {
                this.$message.success("添加成功");
                this.handleCloseDialog();
                this.$emit("update");
              } else if (res.code == 10201 || res.code == 10202) {
                this.cooperationLinkCheckResponse = res;
              }
            });
          }
        }
      });
    },

    /**
     *  @param { Function }
     */
    changeUpImg(e) {
      const isJPG =
        e.raw.type === "image/jpeg" ||
        e.raw.type === "image/jpg" ||
        e.raw.type === "image/png";
      const isLt2M = e.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG/JPEG/PNG 格式!");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
        return;
      }
      this.queryParams.file = e.raw;
      this.imageUrl = URL.createObjectURL(e.raw);
      if (
        document.getElementsByClassName("el-form-item__error").length > 0 &&
        this.type == "edit"
      ) {
        document.getElementsByClassName(
          "el-form-item__error"
        )[1].style.display = "none";
      }
      if (
        document.getElementsByClassName("el-form-item__error").length > 0 &&
        this.type == "copy"
      ) {
        document.getElementsByClassName(
          "el-form-item__error"
        )[0].style.display = "none";
      }
      if (document.getElementsByClassName("el-upload").length > 0) {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#8c939d";
      }
    },
    //11
    handleChangeOnlineType() {
      setOnlineSetType({
        typeId: this.onlineType,
        productId: this.id,
      }).then((res) => {
        this.$message.success("设置成功");
      });
    },
    //获取助贷
    getloanList() {
      getloanModeListsAll().then((res) => {
        this.LoanList = res.data;
      });
    },

    //获取商户
    getPartyaId() {
      getAcquirePartyAall().then((res) => {
        this.acquirePartyList = res.data;
      });
    },

    //获取推广类型
    getPackageList() {
      getFlowPackageList().then((res) => {
        let arr = [
          {
            name: "点击",
            value: 1,
          },
          {
            name: "CPS",
            value: 2,
          },
          {
            name: "CPC",
            value: 3,
          },
        ];
        let data = res.data.map((item) => {
          return {
            name: item.name,
            value: item.id,
          };
        });
        this.packageList = [...arr, ...data];
      });
    },

    handleGetProductApiList() {
      getProductApiList().then((res) => {
        this.prodApiList = res.data;
      });
    },
  },
  computed: {
    isDisable() {
      return this.showUsers.includes(this.$store.getters.userInfo.userName);
    },

    submitDisabled(){
      return this.cooperationLinkChecking || (this.showLinkFormItem && this.linkCheckFailed);
    },

    showCooperationLinkTip() {
      return this.showLinkFormItem && this.linkCheckFailed;
    },

    linkCheckFailed() {
      return this.cooperationLinkCheckResponse.code === 10202;
    },

    showLinkFormItem() {
      return this.queryParams.mid == 2 || this.queryParams.mid == 3 || this.isQiWei;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-edit {
}

.dialog-wrap {
  max-height: 65vh;
  overflow-x: hidden;
  overflow-y: scroll;
}

.title {
  padding: 24px 0 16px 10px;
  position: relative;
  font-size: 18px;
  text-align: left;

  &::before {
    content: "";
    width: 1px;
    height: 16px;
    border: 2px solid #e37318;
    position: absolute;
    left: 0;
    top: calc(50% - 5px);
  }
}

.tips {
  font-size: 13px;
  color: #999;
}

::v-deep .el-form-item--medium .el-form-item__label {
  letter-spacing: 2px;
  padding-bottom: 0;
}

::v-deep .el-form-item__content {
  line-height: 20px;
}

::v-deep .el-form-item {
  margin-bottom: 10px !important;
}

::v-deep .el-input__inner::placeholder {
  color: #999;
}

.el-form-item-tip {
  position: absolute;
  color: #ff4949;
  font-size: 12px
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
</style>
