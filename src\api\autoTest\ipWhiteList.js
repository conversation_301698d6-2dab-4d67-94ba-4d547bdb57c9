import request from '@/utils/request'

/**
 * 获取IP白名单
 */
export function getIpWhiteList() {
  return request({
    url: '/loan/xm/autoTest/ipWhite',
    method: 'get'
  })
}

/**
 * 更新IP白名单（全量更新）
 * @param {Array} ipList - IP地址列表
 */
export function updateIpWhiteList(ipList) {
  return request({
    url: '/loan/xm/autoTest/ipWhite',
    method: 'put',
    data: ipList
  })
}

/**
 * 获取当前公网IP
 */
export function getCurrentPublicIp() {
  return request({
    url: '/loan/xm/autoTest/getPubIp',
    method: 'get'
  })
} 