<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dataRange"
          :clearable="false"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="dataList">
      <el-table-column label="链接类型" prop="linkType"></el-table-column>
      <!-- <el-table-column label="时间" prop="t" align="center" /> -->
      <el-table-column label="短信数量" prop="smsNum" align="center" />
      <el-table-column label="贷超预估收益" prop="onlineDaoChaoProfit" align="center" />
      <el-table-column label="企微预估收益" prop="onlineWeChatProfit" align="center" />
      <el-table-column label="线下预估收益" prop="offlineProfit" align="center" />
      <el-table-column label="半流程收益" prop="halfProfit" align="center" />
      <el-table-column label="出量收益" prop="outputProfit" align="center" />

      <el-table-column label="挽回用户数" align="center">
        <el-table-column label="9点到12点" align="center" prop="morningNum">
        </el-table-column>
        <el-table-column label="12点到18点" align="center" prop="afternoonNum">
        </el-table-column>
        <el-table-column label="18点到24点" align="center" prop="nightNum">
        </el-table-column>
      </el-table-column>

      <el-table-column label="总收益" align="center">
        <template slot-scope="{ row }">
          <div>
            {{
             row.profit
            }}
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getSmsLinkUserStats } from "@/api/crmReport";

export default {
  data() {
    return {
      dataRange: [this.getTime(), this.getTime()],
      dataList: [],

      queryParams: {
        startDate: `${this.getTime()}`,
        endDate: `${this.getTime()}`,
      },
    };
  },
  methods: {
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    //搜索
    handleQuery() {
      if (this.dataRange !== null) {
        this.queryParams.startDate = this.dataRange[0];
        this.queryParams.endDate = this.dataRange[1];
      } else {
        this.queryParams.startDate = "";
        this.queryParams.endDate = "";
      }
      this.getList();
    },

    getList() {
      getSmsLinkUserStats(this.queryParams).then((res) => {
        this.dataList = res.data;
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped></style>
