<template>
  <el-dialog
    title="失败记录"
    :visible="visible"
    width="80%"
    :before-close="handleClose"
    append-to-body
    @open="handleOpen"
  >
    <div class="fail-record-dialog">
      <div class="operation-bar">
        <el-button type="primary" size="small" @click="handleSaveMD5" :disabled="!selection.length">
          保存选中MD5为txt
        </el-button>
        <el-button type="primary" size="small" @click="handleCopyMD5" :disabled="!selection.length">
          复制选中MD5
        </el-button>
      </div>
      <el-table
        ref="failTable"
        :data="tableData"
        border
        style="width: 100%"
        max-height="500"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
        />
        <el-table-column
          prop="id"
          label="ID"
          width="100"
        />
        <el-table-column
          prop="name"
          label="产品名称"
          min-width="120"
        />
        <el-table-column
          prop="md5Phone"
          label="MD5手机号"
          min-width="180"
        />
        <el-table-column
          prop="failTime"
          label="失败时间"
          min-width="160"
        />
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import { getFullProcessFailList } from '@/api/platformProductManagement/fullProcessAnalysis'
import { saveAs } from 'file-saver'
import clipboard from '@/utils/clipboard'

export default {
  name: 'FailRecordDialog',

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      tableData: [],
      selection: []
    }
  },

  methods: {
    async fetchData() {
      const res = await getFullProcessFailList(this.params)
      this.tableData = res.data || []
    },

    handleOpen() {
      this.fetchData()
    },

    handleClose() {
      this.$refs.failTable.clearSelection()
      this.selection = []
      this.$emit('update:visible', false)
    },

    handleSelectionChange(selection) {
      this.selection = selection
    },

    handleSaveMD5() {
      const md5Phones = this.selection.map(item => item.md5Phone).join('\n')
      const blob = new Blob([md5Phones], { type: 'text/plain;charset=utf-8' })
      saveAs(blob, 'md5_phones.txt')
      this.$message.success('文件保存成功')
    },

    handleCopyMD5() {
      const md5Phones = this.selection.map(item => item.md5Phone).join('\n')
      console.log(md5Phones.length);
      
      if (md5Phones.length > 7000) {
        this.$message.warning('选中的MD5内容过长，请使用保存txt方式')
        return
      }
      
      clipboard.copyText(md5Phones).then(() => {
        this.$message.success('复制成功')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.fail-record-dialog {
  .operation-bar {
    margin-bottom: 16px;
  }
}
</style>
