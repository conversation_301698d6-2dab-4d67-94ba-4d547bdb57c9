<template>
  <el-dialog 
    :title="isEdit ? '编辑分组名称' : '新增分组'" 
    :visible.sync="dialogVisible" 
    width="400px" 
    @close="handleClose"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="group-name-dialog">
      <el-form 
        :model="form" 
        ref="form" 
        :rules="rules" 
        label-width="80px"
      >
        <el-form-item label="分组名称" prop="name">
          <el-input
            maxlength="20"
            v-model="form.name" 
            placeholder="请输入分组名称"
            @keyup.enter.native="handleSave"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSave">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'

export default {
  name: 'GroupNameDialog',

  props: {
    // 弹窗是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 编辑时的初始数据
    editData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      dialogVisible: false,
      form: {
        name: '',
        renderKey: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入分组名称', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ]
      }
    }
  },

  computed: {
    // 是否是编辑模式
    isEdit() {
      return Object.keys(this.editData).length > 0
    }
  },

  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    },
    editData: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.form = {
            name: val.groupName,
            renderKey: val.renderKey || ''
          }
        } else {
          this.form = {
            name: '',
            renderKey: uuidv4()
          }
        }
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    // 处理保存
    handleSave() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const formData = {
            renderKey: this.form.renderKey,
            groupName: this.form.name
          }
          // 等待父组件的处理结果
          this.$emit('save', formData, (success) => {
            if (success) {
              this.dialogVisible = false
            }
          })
        }
      })
    },

    // 处理关闭
    handleClose() {
      this.$refs.form?.resetFields()
      this.form = {
        name: '',
        renderKey: uuidv4()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.group-name-dialog {
  padding: 20px 20px 0;
}
</style> 