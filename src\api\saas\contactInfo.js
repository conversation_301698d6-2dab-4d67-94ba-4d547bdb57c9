import request from '@/utils/request'
//查询标签列表
export function getContactList(data) {
  return request({
    url: '/saas/contact/getlist',
    method: "get",
    params: data
  })
}


//新增标签
export function addContactOne(data) {
  return request({
    url: "/saas/contact/addone",
    method: "post",
    data
  })
}

//修改标签状态
export function changeContactStatus(data) {
  return request({
    url: '/saas/contact/updateStatus',
    method: "post",
    data
  })
}

//修改标签名称
export function editContactOne(data) {
  return request({
    url: "/saas/contact/updateone",
    method: "post",
    data
  })
}
