<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">


      <el-form-item label="日期类型" prop="dateType">
        <el-select v-model="queryParams.dateType" placeholder="日期类型" :disabled="!!dateRange&&dateRange.length>0"
          clearable size="small">
          <el-option value="1" label="今天"></el-option>
          <el-option value="2" label="昨天"></el-option>
          <el-option value="3" label="最近7天"></el-option>
          <el-option value="4" label="最近30天"></el-option>
          <el-option value="5" label="当月"></el-option>
          <el-option value="6" label="上月"></el-option>
          <el-option value="7" label="近半年"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd" :disabled="!!queryParams.dateType"
          type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="客户端" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="客户端" clearable size="small">
          <el-option :value="1" label="苹果"></el-option>
          <el-option :value="2" label="安卓"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="渠道" prop="channelIds">
        <el-select v-model="queryParams.channelIds" placeholder="渠道" clearable size="small">
          <el-option :value="1" label="苹果"></el-option>
          <el-option :value="2" label="安卓"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="用户" prop="userType">
        <el-select v-model="queryParams.channelIds" placeholder="用户" clearable size="small">
          <el-option :value="1" label="新客"></el-option>
          <el-option :value="2" label="老客"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="资源位名称" prop="userType">
        <el-select v-model="queryParams.channelIds" placeholder="资源位名称" clearable size="small">
          <el-option :value="1" label="新客"></el-option>
          <el-option :value="2" label="老客"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>
    <el-table border :data="dataList">
      <el-table-column label="日期" prop="queryDate" align="center" />
      <el-table-column label="App登录用户数" prop="queryDate" align="center" />
      <el-table-column label="曝光用户数" prop="queryDate" align="center" />
      <el-table-column label="点击用户数" prop="queryDate" align="center" />
      <el-table-column label="点击次数" prop="queryDate" align="center" />
      <el-table-column label="点击率" prop="queryDate" align="center" />
      <el-table-column label="详情页点击用户数" prop="queryDate" align="center" />
      <el-table-column label="详情页点击率" prop="queryDate" align="center" />
      <el-table-column label="进件成功用户数" prop="queryDate" align="center" />
      <el-table-column label="进件率" prop="queryDate" align="center" />
      <el-table-column label="预估收益" prop="queryDate" align="center" />
      <el-table-column label="预估ROI" prop="queryDate" align="center" />
    </el-table>
  </div>
</template>
<script>
export default {
  data() {
    return {
      dataList: [],
      queryParams: {
        productName: "",
        channelIds: '',

        deviceType: '',
        productType: ""
      }
    }
  },
  methods: {
    handleQuery() { }
  }
}
</script>

<style lang="scss" scoped>

</style>
