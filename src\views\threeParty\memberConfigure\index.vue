<template>
  <div class="app-container">
    <el-form
      :model="formData"
      :rules="ruels"
      ref="formData"
      label-position="left"
      label-width="130px"
    >
      <el-form-item label="账号" prop="username">
        <el-input
          size="small"
          placeholder="请输入账号"
          v-model="formData.username"
        ></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input
          size="small"
          placeholder="请输入密码"
          v-model="formData.password"
        ></el-input>
      </el-form-item>
      <el-form-item label="支付渠道" prop="paymentChannel">
        <el-input
          size="small"
          placeholder="请输入支付渠道"
          v-model="formData.paymentChannel"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道ID" prop="projectId">
        <el-input
          size="small"
          placeholder="请输入渠道ID"
          v-model="formData.projectId"
        ></el-input>
      </el-form-item>
      <el-form-item label="请求地址" prop="requestUrl">
        <el-input
          size="small"
          placeholder="请输入请求地址"
          v-model="formData.requestUrl"
        ></el-input>
      </el-form-item>
      <el-form-item label="权益地址" prop="rightsUrl">
        <el-input
          size="small"
          placeholder="请输入权益地址"
          v-model="formData.rightsUrl"
        ></el-input>
      </el-form-item>
      <el-form-item label="AES密钥" prop="secret">
        <el-input
          size="small"
          placeholder="请输入AES密钥"
          v-model="formData.secret"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitFormData">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  getMemberConfig,
  editMemberConfig,
} from "@/api/threeParty/radersetting";
export default {
  name: "RadarSetting",
  data() {
    return {
      ruels: {
        username: [{ required: true, message: "请输入账号", trigger: "blur" }],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        paymentChannel: [
          { required: true, message: "请输入支付渠道", trigger: "blur" },
        ],
        projectId: [
          { required: true, message: "请输入渠道ID", trigger: "blur" },
        ],
        requestUrl: [
          { required: true, message: "请输入请求地址", trigger: "blur" },
        ],
        rightsUrl: [
          { required: true, message: "请输入权益地址", trigger: "blur" },
        ],
        secret: [{ required: true, message: "请输入AES密钥", trigger: "blur" }],
      },
      formData: {
        username: "",
        password: "",
        paymentChannel: "",
        projectId: "",
        requestUrl: "",
        rightsUrl: "",
        secret: "",
      },
    };
  },
  methods: {
    initPage() {
      getMemberConfig().then((res) => {

        for (let key in res.data) {
          // 接口请求出来的
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = res.data[key];
          }
        }
      });
    },
    submitFormData() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          editMemberConfig(this.formData).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.initPage();
            }
          });
        }
      });
    },
  },
  mounted() {
    this.initPage();
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input--small .el-input__inner {
  width: 500px;
}
</style>