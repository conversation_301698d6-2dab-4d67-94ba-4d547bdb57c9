<template>
  <el-drawer title="款项管理" :visible.sync="drawer_" size="665px" :direction="direction" @close="handCloseDrawer">
    <div class="drawer-wrap">
      <div class="drawer-tabs flex">
        <div :class="['drawer-tabs-item c-p', type == 1 ? 'active' : '']" @click="handleToggle(1)"
          v-if="hasBool('partyB:requestMoeny:add')">请款申请</div>
        <div :class="['drawer-tabs-item c-p', type == 2 ? 'active' : '']" @click="handleToggle(2)"
          v-if="hasBool('partyb:partyb:refund')">退款申请</div>
      </div>
      <template v-if="type == 1">
        <el-form ref="fundRef" :model="fund" :rules="rulesrefound" label-width="80px" :key="type">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="收款账户" prop="proceedsAccount">
                <el-input v-model="fund.proceedsAccount" placeholder="请输入收款账户" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="开户行" prop="openingBank">
                <el-input v-model="fund.openingBank" placeholder="请输入开户行" size="small" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="收款账号" prop="proceedsNumber">
                <el-input v-model="fund.proceedsNumber" placeholder="请输入收款账号" size="small" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="请款事项" prop="matter">
                <el-select v-model="fund.matter" placeholder="请选择请款事项" style="width:100%" clearable size="small">
                  <el-option :value="1" label="运营费用"></el-option>
                  <el-option :value="2" label="技术费用"></el-option>
                  <el-option :value="3" label="采购费用"></el-option>
                  <el-option :value="4" label="房主物业"></el-option>
                  <el-option :value="5" label="日常支出"></el-option>
                  <el-option :value="6" label="其他"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="请款原因" prop="description">
                <el-input type="textarea" v-model="fund.description" placeholder="请输入请款原因" maxlength="30" show-word-limit
                  size="small" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="是否开票" prop="invoiceType">
                <el-radio-group v-model="fund.invoiceType">
                  <el-radio :label="1">对公无发票</el-radio>
                  <el-radio :label="2">对公有发票</el-radio>
                  <el-radio :label="3">对私有发票</el-radio>
                  <el-radio :label="4">对私无发票</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="请款金额" prop="money">
                <el-input-number v-model="fund.money" style="width:100%" placeholder="输入金额" :min="1"
                  @change="handleCashMony" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="金额大写" prop="moneydaX">
                <el-input v-model="moneydaX" disabled placeholder="请在上输入金额" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="打款主体" prop="remitSubject">
                <el-select v-model="fund.remitSubject" style="width:100%" clearable placeholder="请选择打款主体">
                  <el-option v-for="item in subjectList" :key="item.id" :label="item.subjectName" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
      <template v-if="type == 2">
        <el-form :model="refundForm" label-width="80px" ref="refundRef" :rules="refundFormRules" :key="type">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="退款乙方" prop="partyUsername">
                <el-input v-model="refundForm.partyUsername" disabled placeholder="当前所乙方" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="在线渠道" prop="cooperationType">
                <el-input v-model="refundForm.id" disabled placeholder="在线渠道ID" clearable size="small" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="退款金额" prop="cooperativePrice">
                <el-input-number v-model="refundForm.refundMoney" placeholder="退款金额" :min="0" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="打款主体" prop="sub">
                <el-select v-model="refundForm.remitSubject" clearable placeholder="请选择打款主体">
                  <el-option v-for="item in subjectList" :key="item.id" :label="item.subjectName" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="凭证上传" prop="file">
                <el-upload class="avatar-uploader" action="" :show-file-list="false" :auto-upload="false"
                  :on-change="changeUpImg">
                  <img v-if="imageUrl" style="vertical-align: middle; max-height: 100px" :src="imageUrl" />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="退款原因" prop="refundCause">
                <el-input type="textarea" v-model="refundForm.refundCause" placeholder="请输入退款原因" maxlength="100"
                  show-word-limit size="small" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </template>
    </div>
    <div class="drawer__footer flex align-items-c">
      <el-button type="primary" size="small" @click="handleSubmit"> 确定</el-button>
      <el-button size="small" @click="handCloseDrawer">取 消</el-button>
    </div>
  </el-drawer>
</template>

<script>
import {
  ApplyForRefund,
  getselectOdd,
  PostCashOut,
  getSubjectAll,
} from "@/api/partyB";
import { hasBool } from "@/directive/permission/hasBool"
export default {
  data() {
    var imgRule1 = (rule, value, callback) => {
      if (this.refundForm.file == "" && this.imageUrl == "") {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#F56C6C";
        callback(new Error("请上传凭证"));
      } else if (this.refundForm.file || this.imageUrl) {
        callback();
      }
    };
    return {
      type: 1,
      moneydaX: "",
      imageUrl: "",
      subjectList: [],
      rulesrefound: {
        invoiceType: [
          {
            required: true,
            message: "请选择发票",
            trigger: "change",
          },
        ],
        proceedsAccount: [
          {
            required: true,
            message: "请选择收款账户",
            trigger: "blur",
          },
        ],
        openingBank: [
          {
            required: true,
            message: "请输入开户行",
            trigger: "blur",
          },
        ],
        proceedsNumber: [
          {
            required: true,
            message: "请输入收款账号",
            trigger: "blur",
          },
        ],
        matter: [
          {
            required: true,
            message: "请输入请款事项",
            trigger: "blur",
          },
        ],
        money: [
          {
            required: true,
            message: "请输入请款金额",
            trigger: "blur",
          },
        ],
        remitSubject: [
          {
            required: true,
            message: "请选择打款主体",
            trigger: "change",
          },
        ],
      },
      refundFormRules: {
        file: [
          {
            required: true,
            message: "请上传凭证",
            validator: imgRule1,
          },
        ],
      },
      fund: {
        description: "",
        invoiceType: "",
        matter: "",
        money: '',
        openingBank: '',
        partybId: "",
        proceedsAccount: '',
        proceedsNumber: "",
        remitSubject: ""
      },
      refundForm: {
        partybId: "",
        refundCause: "",
        refundMoney: "",
        remitSubject: "",
        file: "",
        id: ""
      }
    }
  },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    partybId: {
      type: [String, Number],
      default: ''
    },
    row: {
      type: Object,
      default: () => { }
    },
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer
      },
      set(v) {
        this.$emit("update:drawer", false)
      }
    }
  },
  watch: {
    drawer(val) {
      if (!hasBool('partyB:requestMoeny:add')) {
        this.type = 2
        this.handleToggle(2)
      }
      if (val && this.type == 1) {
        this.initFormData()
      }
      if (val) {
        getSubjectAll().then(res => {
          this.subjectList = res.data
        })
      }
    }
  },
  methods: {
    handleToggle(type) {
      this.type = type
      if (type == 2) {
        this.refundForm.partybId = this.partybId
        this.refundForm.partyUsername = this.row.partyUsername
        this.refundForm.id = this.row.id
      }
    },
    handleCashMony(viade) {
      if (viade > 0) {
        var money = viade;
        var fraction = ["角", "分"];
        var digit = [
          "零",
          "壹",
          "贰",
          "叁",
          "肆",
          "伍",
          "陆",
          "柒",
          "捌",
          "玖",
        ];
        var unit = [
          ["元", "万", "亿"],
          ["", "拾", "佰", "仟"],
        ];
        var head = money < 0 ? "欠" : "";
        money = Math.abs(money);
        var s = "";
        for (var i = 0; i < fraction.length; i++) {
          s += (
            digit[Math.floor(money * 10 * Math.pow(10, i)) % 10] + fraction[i]
          ).replace(/零./, "");
        }
        s = s || "整";
        money = Math.floor(money);
        for (var i = 0; i < unit[0].length && money > 0; i++) {
          var p = "";
          for (var j = 0; j < unit[1].length && money > 0; j++) {
            p = digit[money % 10] + unit[1][j] + p;
            money = Math.floor(money / 10);
          }
          s = p.replace(/(零.)*零$/, "").replace(/^$/, "零") + unit[0][i] + s;
        }
        this.moneydaX =
          head +
          s
            .replace(/(零.)*零元/, "元")
            .replace(/(零.)+/g, "零")
            .replace(/^整$/, "零元整");
      } else {
        this.moneydaX = "请输入金额";
      }
    },
    handCloseDrawer() {
      this.moneydaX = "";
      this.imageUrl = ""
      this.refundForm = {
        partybId: "",
        refundCause: "",
        refundMoney: "",
        remitSubject: "",
        file: "",
        id: ""
      }
      this.$refs["refundRef"] && this.$refs["refundRef"].resetFields();
      this.$refs["fundRef"] && this.$refs["fundRef"].resetFields();
      this.type = 1
      this.drawer_ = false
    },
    handleSubmit() {
      if (this.type == 1) {
        this.$refs.fundRef.validate((e) => {
          if (e) {
            console.log(this.fund);
            PostCashOut(this.fund).then((res) => {
              if (res.code == 200) {
                this.$message.success("请款成功");
                this.drawer_ = false
                this.$emit('getList')
              }
            });
          }
        });
      } else {
        this.$refs.refundRef.validate((e) => {

          if (e) {
            let data = {
              partybId: this.refundForm.partybId,
              refundCause: this.refundForm.refundCause,
              refundMoney: this.refundForm.refundMoney,
              remitSubject: this.refundForm.remitSubject,
              file: this.refundForm.file,
            }
            let formData = new FormData();
            for (let i in data) {
              formData.append(i, data[i]);
            }
            ApplyForRefund(formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("操作成功");
                this.drawer_ = false
                this.$emit('getList')
              } else if (res.code == 101) {
                this.$message.info(res.msg);
                this.drawer_ = false
                this.$emit('getList')
              }
            });
          }
        });

      }

    },
    initFormData() {
      getselectOdd({ id: this.partybId }).then(res => {
        this.fund.openingBank = res.data.openAnBank || ''
        this.fund.proceedsAccount = res.data.worldFirst || '';
        this.fund.proceedsNumber = res.data.worldNumber || '';
        this.fund.partybId = res.data.id;
      })
    },
    //上传凭证
    changeUpImg(e) {
      const isJPG =
        e.raw.type === "image/jpeg" ||
        e.raw.type === "image/jpg" ||
        e.raw.type === "image/png";
      const isLt2M = e.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传图片只能是 JPG/JPEG/PNG 格式!");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
        return;
      }
      this.imageUrl = URL.createObjectURL(e.raw);
      this.refundForm.file = e.raw;
      if (document.getElementsByClassName("el-form-item__error").length > 0) {
        document.getElementsByClassName(
          "el-form-item__error"
        )[0].style.display = "none";
      }
      if (document.getElementsByClassName("el-upload").length > 0) {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#8c939d";
      }
    },

  },
  mounted() {
  }
}
</script>

<style lang="scss" scoped>
.drawer-wrap {
  height: calc(100vh - 100px);
  overflow: auto;
  padding: 20px 20px;
}

.drawer-tabs {
  margin-bottom: 30px;

  &-item {
    margin-right: 50px;
    font-size: 18px;
    color: rgba(0, 0, 0, 0.6);

    &.active {
      color: #E37318;
      position: relative;

      &::after {
        content: "";
        display: block;
        position: absolute;
        width: 60%;
        height: 4px;
        border-radius: 2px;
        background: #E37318;
        left: 50%;
        top: 26px;
        transform: translateX(-50%);
      }
    }
  }
}

.drawer__footer {
  position: absolute;
  bottom: 0;
  border-top: 1px solid #DCDFE6;
  width: 100%;
  height: 50px;
  padding-left: 10px;
}
</style>
