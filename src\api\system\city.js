import request from '@/utils/request'
export const getCity = (data) => {
    return request({
        url: "/loan/area/list",
        method: "get",
        params: data
    })
}
export const addCity = (data) => {
    return request({
        url: "/loan/area/add",
        method: "post",
        data
    })
}
export const delCity = (data) => {
    return request({
        url: '/loan/area/delete',
        method: "post",
        data
    })
}
export const exportCity = (data) => {
    return request({
        url: "/loan/area/export",
        method: 'get',
        params: data
    })
}

export const editCity=(data)=>{
    return request({
        url:"/loan/area/update",
        method:"post",
        data
    })
}