<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dateRange"
          clearable
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="商户名称" prop="partyName">
        <el-input
          v-model="queryParams.partyName"
          size="small"
          clearable
          placeholder="请输入商户名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="原商务" v-if="userList.length">
        <el-select
          size="small"
          v-model="queryParams.userId"
          placeholder="请选择商务"
          clearable
        >
          <el-option
            v-for="(item, index) in userList"
            :key="index"
            :label="item.userName"
            :value="item.userId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table :data="dataList">
      <el-table-column label="商户名称" prop="name" align="center" />
      <el-table-column label="电话号码" prop="tel" align="center" />
      <el-table-column
        label="商户类型"
        prop="type"
        align="center"
        :formatter="(row) => typeJosn[row.type]"
      />
      <el-table-column label="原商务" prop="userName" align="center" />
      <el-table-column label="首充时间" prop="firstChargeTime" align="center" />
      <el-table-column label="移入时间" prop="migrationTime" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <el-button
            type="text"
            v-hasPermi="['loan:partyfirst:highSeas:receive']"
            @click="hanldeGetUser(row)"
            >领取</el-button
          >
          <el-button
            type="text"
            v-hasPermi="['loan:partyfirst:highSeas:receiveLog']"
            @click="hanldeGetLog(row)"
            >查看</el-button
          >
          <el-button
            type="text"
            v-hasPermi="['loan:partyfirst:highSeas:black']"
            @click="handleBlack(row)"
            >拉黑</el-button
          >
          <el-button
            type="text"
            v-hasPermi="['loan:partyfirst:highSeas:appoint']"
            @click="handleAppoint(row)"
            >分配用户</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="领取用户"
      width="600px"
      :visible.sync="showInfo"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <div class="info-item">公司名称: {{ row.name }}</div>
      <div class="info-item">电话号码: {{ row.tel }}</div>
      <div class="info-item">商户类型: {{ typeJosn[row.type] }}</div>
      <div class="info-item">原商务: {{ row.userName }}</div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleGetConfirm">确 定</el-button>
        <el-button @click="showInfo = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-drawer title="领取记录" :visible.sync="showLog" size="665px">
      <div style="padding: 20px">
        <el-timeline v-if="logDetail.length">
          <el-timeline-item v-for="(item, index) in logDetail" :key="index">
            <div>
              <div class="item-name">
                {{ item.userName }}({{ item.type == 1 ? "领取" : "移入" }})
              </div>
              <div class="item-time">{{ item.createTime }}</div>
              <div>{{ item.remark || "-" }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
        <div class="emty" v-else>暂无记录</div>
      </div>
    </el-drawer>
    <el-dialog
      title="分配公海用户"
      width="600px"
      :visible.sync="showAppoint"
      append-to-body
      center
      @close="handleAppointCancel"
      :close-on-click-modal="false"
    >
      <div class="info-item">是否确认将{{ row.name }}分配？</div>
      <div class="info-item">
        当前余额: {{ row.availableAmount || 0 }}
        <span style="margin-left: 20px"
          >保证金: {{ row.depositAmount || 0 }}</span
        >
      </div>
      <div class="info-item" style="color: red">
        分配该商户后商户归所选分配人所有
      </div>
      <div class="info-item">
        <div style="margin-bottom: 10px">移交至：</div>
        <el-select
          v-model="appointForm.userId"
          style="width: 100%"
          filterable
          placeholder="请选择商务"
        >
          <el-option
            v-for="item in options"
            :key="item.userId"
            :label="item.nickName"
            :value="item.userId"
          >
          </el-option>
        </el-select>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAppointConfirm"
          >确 定</el-button
        >
        <el-button @click="handleAppointCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPartyFirstHighSeasList,
  partyFirstHighSeasBlack,
  getPartyFirstHighSeasreceive,
  getHighSeasLog,
  getHighSeasUser,
  getHighSeasAppoint,
  gethighSeasUserList,
} from "@/api/partyA";
export default {
  data() {
    return {
      showInfo: false,
      showLog: false,
      typeJosn: {
        0: "门店商户",
        1: "个人商户",
        2: "线上商户",
      },
      options: [],
      total: 0,
      showAppoint: false, //分配公海用户
      row: {},
      appointForm: {
        partyFirstId: "",
        userId: "",
      },
      userList: [],
      dateRange: [],
      dataList: [],
      logDetail: [],
      queryParams: {
        userId: "",
        pageNum: 1,
        pageSize: 10,
        startTime: "",
        endTime: "",
        partyName: "",
      },
    };
  },
  methods: {
    handleQuery() {
      if (this.value1 !== null) {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    hanldeGetUser(row) {
      this.row = row;
      this.showInfo = true;
    },

    //拉黑商户
    handleBlack(row) {
      const h = this.$createElement;
      const newDatas = [];
      newDatas.push(h("p", null, `是否确认拉黑    ${row.name} ？`));
      newDatas.push(
        h("p", null, [
          h("span", null, "当前余额"),
          h(
            "span",
            { style: "color: red;padding:0px 5px" },
            `${row.availableAmount}`
          ),
          h("span", null, "保证金"),
          h(
            "span",
            { style: "color: red;padding:0px 5px" },
            `${row.depositAmount}`
          ),
        ])
      );
      // newDatas.push(h('p', { style: 'color: red' }, "拉黑后商户投放产品将不可上线"))
      this.$confirm("提示", {
        type: "warning",
        message: h("div", null, newDatas),
      })
        .then(() => {
          partyFirstHighSeasBlack({ id: row.partyFirstId }).then((res) => {
            this.$message.success("操作成功");
            this.getList();
          });
        })
        .catch((err) => {});
    },
    handleGetConfirm() {
      getPartyFirstHighSeasreceive({
        partyFirstId: this.row.partyFirstId,
      }).then((res) => {
        this.getList();
        this.showInfo = false;
        this.$message.success("领取成功");
      });
    },
    getList() {
      getPartyFirstHighSeasList(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total;
      });
    },
    hanldeGetLog(row) {
      this.showLog = true;
      this.logDetail = [];
      getHighSeasLog({ partyFirstId: row.partyFirstId }).then((res) => {
        this.logDetail = res.data;
      });
    },
    handleAppoint(row) {
      this.row = row;
      this.showAppoint = true;
    },
    handleAppointConfirm() {
      if (!this.appointForm.userId) return this.$message.error("商务不能为空");
      this.appointForm.partyFirstId = this.row.partyFirstId;
      getHighSeasAppoint(this.appointForm).then((res) => {
        this.$message.success("操作成功");
        this.getList();
        this.handleAppointCancel();
      });
    },
    handleAppointCancel() {
      this.appointForm = {
        partyFirstId: "",
        userId: "",
      };
      this.showAppoint = false;
    },
  },
  mounted() {
    getHighSeasUser().then((res) => {
      this.userList = res.data;
    });
    this.getList();
    gethighSeasUserList().then((res) => {
      this.options = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
.info-item {
  font-size: 20px;
  margin-bottom: 20px;
  color: #222;
}
.item-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.item-time {
  font-size: 14px;
  margin: 10px 0;
}
::v-deep .el-timeline-item {
  padding-bottom: 8px !important;
}
.emty {
  text-align: center;
}
</style>
