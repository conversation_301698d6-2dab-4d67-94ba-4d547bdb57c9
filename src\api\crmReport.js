import request from '@/utils/request'
//查询商务充值消耗
export const getCrmUserRecharge = (data) => {
  return request({
    url: '/partyFirst/user/stats/getUserRecharge',
    method: 'get',
    params: data
  })
}
//查询商务列表
export const getpartyFirstUser = () => {
  return request({
    url: '/partyFirst/user/stats/userList',
    method: 'get'
  })
}
//查询新增列表
export const getToDayAddInfo = (data) => {
  return request({
    url: '/partyFirst/user/stats/getToDayAddInfo',
    method: 'get',
    params: data
  })
}
//查询新增列表
export const getAddInfoDetail = (data) => {
  return request({
    url: '/partyFirst/user/stats/getAddInfoDetail',
    method: 'get',
    params: data
  })
}
//查询商户余额
export const getUserProfitStats = (data) => {
  return request({
    url: '/partyFirst/user/stats/getUserProfitStats',
    method: 'get',
    params: data
  })
}
//查询返点统计
export const getRebateStatistics = (data) => {
  return request({
    url: '/loan/statistics/rebateStatistics',
    method: 'get',
    params: data
  })
}
//查询返点统计
export const getRebateStatisticsList = (data) => {
  return request({
    url: '/loan/statistics/rebateStatistics/list',
    method: 'get',
    params: data
  })
}
//查询返点统计
export const getRebateChannelList = (data) => {
  return request({
    url: '/loan/statistics/rebate/channel/log',
    method: 'get',
    params: data
  })
}
//埋点统计
export const getPointList = (data) => {
  return request({
    url: '/loan/point/list',
    method: 'get',
    params: data
  })
}
//埋点统计
export const editPointSwitch = (data) => {
  return request({
    url: '/loan/point/pointSwitch',
    method: 'get',
    params: data
  })
}
//获取
export const getPointSwitch = (data) => {
  return request({
    url: '/loan/point/pointSwitchStatus',
    method: 'get',
    params: data
  })
}
//获取
export const getPointDetailInfo = (data) => {
  return request({
    url: '/loan/point/operation/' + data,
    method: 'get',
    // params: data
  })
}
//获取
export const exportlist = (data) => {
  return request({
    url: '/stats/user/form/list',
    method: 'get',
    params: data
  })
}
export const exportDetail = (data) => {
  return request({
    url: '/stats/user/form/detail',
    method: 'get',
    params: data
  })
}
export const exportChannelList = () => {
  return request({
    url: '/stats/user/form/getChannelList',
    method: 'get',

  })
}
export const getSmsLinkUserStats = (data) => {
  return request({
    url: 'stats/user/form/getSmsLinkUserProfitStats',
    method: 'get',
    params: data
  })
}
export const getConsumeTotal = (data) => {
  return request({
    url: '/partyFirst/user/stats/getConsumeTotal',
    method: 'get',
    params: data
  })
}
