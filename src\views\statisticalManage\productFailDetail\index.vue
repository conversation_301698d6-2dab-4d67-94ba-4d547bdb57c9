<template>
  <div class="app-container">
    <div class="name">推广名称： {{ $route.query.productName }}</div>
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="dataList">
      <el-table-column
        label="失败原因"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="errorFail"
        align="center"
      />
      <el-table-column label="数量" prop="total" align="center" />
      <el-table-column label="百分比" prop="percentage" align="center">
        <template  slot-scope="{row}">
          <div v-if="row.percentage">{{ row.percentage }}%</div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { getMatchingDeatil } from "@/api/statisticalManage";

export default {
  data() {
    return {
      value1: [this.$route.query.startTime, this.$route.query.endTime],
      dataList: [],
      total: 0,
      queryParams: {
        ...this.$route.query,
      },
    };
  },
  methods: {
    // getTime() {
    //   var date = new Date();
    //   var YY = date.getFullYear();
    //   //获取月份
    //   var MM =
    //     date.getMonth() + 1 < 10
    //       ? "0" + (date.getMonth() + 1)
    //       : date.getMonth() + 1;
    //   //获取日期
    //   var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
    //   return YY + "-" + MM + "-" + DD;
    // },

    //搜索
    handleQuery() {
      if (this.value1 !== null) {
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }

      this.getList();
    },

    getList() {
      getMatchingDeatil(this.queryParams).then((res) => {
        this.dataList = res.data;
      });
    },
  },

  mounted() {
    this.getList();
    console.log();
  },
};
</script>

<style lang="scss" scoped>
.name {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  color: red;
}
</style>
