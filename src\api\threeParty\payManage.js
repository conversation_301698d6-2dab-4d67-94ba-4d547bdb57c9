import request from '@/utils/request'
//获取支付列表
export const getPayManageList = (data) => {
    return request({
        url: '/loan/payment/list',
        method: "get",
        params: data
    })
}
//新增支付类型
export const addPayManangeOne = (data) => {
    return request({
        url: "/loan/payment/add",
        method: "post",
        data
    })
}
//修改支付类型
export const editPayManangeOne = (data) => {
    return request({
        url: "/loan/payment/revamp",
        method: "post",
        data
    })
}
//修改支付类型
export const delPayManangeOne = (data) => {
    console.log(data)
    return request({
        url: "/loan/payment/remove",
        method: "get",
        params: data
    })
}