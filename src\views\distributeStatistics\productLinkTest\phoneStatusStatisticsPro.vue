<template>
  <div class="app-container">
    <div class="title">手机状态标识统计</div>
    <!-- 筛选表单 -->
    <el-form :inline="true" :model="queryParams">
      <el-form-item label="渠道">
        <el-select
          v-model="queryParams.channelId"
          placeholder="请选择渠道"
          filterable
          clearable
          style="width: 200px"
          @change="handleQuery"
        >
          <el-option
            v-for="item in channelList"
            :key="item.id"
            :label="`${item.id}-${item.channelName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="日期">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="handleQuery"
          :clearable="false"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      border
      row-key="renderKey"
      :tree-props="{
        children: 'list'
      }"
    >
      <el-table-column prop="channelId" label="渠道ID" width="120">
        <template slot-scope="{ row }">
          <span>{{ row.statusName ? '' : row.channelId }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="channelName" label="渠道名称" min-width="180">
        <template slot-scope="{ row }">
          <span>{{ row.statusName ? '' : row.channelName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态名称" min-width="150">
        <template slot-scope="{ row }">
          <span>{{ row.statusName || '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="statusCount" label="数量" width="120" align="center"></el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { getPhoneStatusListV2 } from "@/api/distributeStatistics/phoneStatus";
import { getAllChannelList } from '@/api/channeManage/channelList'
import Pagination from "@/components/Pagination";
import { v4 as uuidv4 } from 'uuid';

export default {
  name: "PhoneStatusStatistics",
  components: {
    Pagination
  },
  data() {
    return {
      dateRange: null,
      queryParams: {
        startDateTime: undefined,
        endDateTime: undefined,
        channelId: undefined,
        pageNum: 1,
        pageSize: 10
      },
      channelList: [],
      tableData: [],
      total: 0
    };
  },
  created() {
    this.setDefaultDate();
    this.getChannelList();
    this.getList();
  },
  methods: {
    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format("YYYY-MM-DD 00:00:00");
      const endTime = today.format("YYYY-MM-DD 23:59:59");

      this.dateRange = [startTime, endTime];
      this.queryParams.startDateTime = startTime;
      this.queryParams.endDateTime = endTime;
    },
    handleQuery() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        this.$message.warning('请选择日期范围');
        return;
      }
      this.queryParams.startDateTime = this.dateRange[0];
      this.queryParams.endDateTime = this.dateRange[1];
      this.getList();
    },
    resetQuery() {
      this.setDefaultDate();
      this.queryParams.channelId = undefined;
      this.getList();
    },
    async getList() {
      const res = await getPhoneStatusListV2({
        startDateTime: this.queryParams.startDateTime,
        endDateTime: this.queryParams.endDateTime,
        channelId: this.queryParams.channelId,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize
      });
      if (res.code == 200) {
        const data = res.rows || [];
        data.forEach(item => {
          item.renderKey = uuidv4();
          if (item.list && item.list.length) {
            item.list.forEach(subItem => {
              subItem.renderKey = uuidv4();
            });
          }
        });
        this.tableData = data;
        this.total = res.total;
      }
    },
    async getChannelList() {
      const res = await getAllChannelList();
      if (res.code == 200) {
        this.channelList = res.data || [];
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
  }
}
</style> 