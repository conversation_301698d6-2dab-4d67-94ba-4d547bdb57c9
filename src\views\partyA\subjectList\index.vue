<template>
  <div class="app-container">
    <el-form :inline="true">
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" size="mini" v-hasPermi="['loan:subject:add']"
          @click="addsubject">添加</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="subjectList">
      <el-table-column label="主体名称" align="center" prop="subjectName" />
      <el-table-column label="开户行" align="center" prop="bankName" />
      <el-table-column label="银行卡号" align="center" prop="bankCardNo" />
      <el-table-column label="排序" prop="orderNum" align="center" />
      <el-table-column label="操作" align="center" width="300" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <span class="f_c005 c-p" style="margin-right:10px" @click="handleUpdate(scope.row)"
            v-hasPermi="['loan:subject:edit']">修改</span>
          <span class="f-fail c-p" @click="handleDelete(scope.row)" v-hasPermi="['loan:subject:remove']">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog :title="isAdd ? '新增主体' : '修改主体'" :visible.sync="subjectAvisible" @close="cancel" width="1000px"
      append-to-body  :close-on-click-modal="false">
      <el-form ref="formData" :model="subjectFormData" :rules="rules" label-width="100px">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="主体名称" prop="subjectName">
              <el-input v-model="subjectFormData.subjectName" placeholder="请输入主体名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户行" prop="bankName">
              <el-input v-model="subjectFormData.bankName" placeholder="请输入开户行" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12"> <el-form-item label="银行卡号" prop="bankCardNo">
              <el-input v-model="subjectFormData.bankCardNo" :change="
                (subjectFormData.bankCardNo = subjectFormData.bankCardNo.replace(
                  /[^\d.]/g,
                  ''
                ))
              " :maxlength="19" placeholder="请输入银行卡号" />
            </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="orderNum">
              <el-input type="number" v-model="subjectFormData.orderNum" placeholder="请输入排序" />
            </el-form-item>
          </el-col>
        </el-row>



      </el-form>
      <div slot="footer" class="justify-content-c">
        <el-button type="primary" @click="submitSubjectAForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSubjectList,
  addSubjectOne,
  editSubjectOne,
  getSubjectOne,
  delSubjectOne,
} from "@/api/partyA";
import { formatDate } from "@/utils";
export default {
  name: "SubjectList",
  data() {
    return {
      total: 0,
      isAdd: true,
      subjectAvisible: false,
      loading: false,
      subjectList: [],
      subjectFormData: {
        subjectName: "",
        bankCardNo: "",
        bankName: "",
        orderNum: 0,
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      rules: {
        subjectName: [
          { required: true, message: "主体名称不能为空", trigger: "blur" },
        ],
        bankName: [
          { required: true, message: "开户不能为空", trigger: "blur" },
        ],

        bankCardNo: [
          { required: true, message: "银行卡号不能为空", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    getList() {
      this.loading = true;
      getSubjectList(this.queryParams)
        .then((res) => {
          this.subjectList = res.rows;
          this.total = res.total;
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //添加弹窗
    addsubject() {
      this.isAdd = true;
      this.subjectAvisible = true;
      if (this.subjectFormData.id) {
        delete this.subjectFormData.id;
      }
    },
    cancel() {
      this.subjectAvisible = false;
      this.subjectFormData = {
        subjectName: "",
        bankCardNo: "",
        bankName: "",
        orderNum: 0,
      };
      this.$refs.formData.resetFields();
    },
    //打开修改弹窗
    handleUpdate(e) {
      this.isAdd = false;
      this.subjectAvisible = true;
      getSubjectOne(e.id).then((res) => {
        this.subjectFormData.subjectName = res.data.subjectName;
        this.subjectFormData.bankCardNo = res.data.bankCardNo;
        this.subjectFormData.bankName = res.data.bankName;
        this.subjectFormData.id = res.data.id;
      });
    },
    //编辑或者增加
    submitSubjectAForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addSubjectOne(this.subjectFormData).then((res) => {
              if (res.code == 200) {
                this.$message.success("添加成功");
                this.subjectAvisible = false;
                this.getList();
                this.cancel();
              }
            });
          } else {
            editSubjectOne(this.subjectFormData).then((res) => {
              if (res.code == 200) {
                this.$message.success("编辑成功");
                this.subjectAvisible = false;
                this.getList();
                this.cancel();
              }
            });
          }
        }
      });
    },
    handleDelete(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {

          delSubjectOne(row.id)
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              this.$message({
                message: "删除失败",
                type: "error",
              });
            });
        })
        .catch((err) => { });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped></style>
