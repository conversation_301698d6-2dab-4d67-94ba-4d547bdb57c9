<template>
  <el-dialog
    :title="title"
    :visible.sync="dialog"
    width="500px"
    append-to-body
    destroy-on-close
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="计划名称" prop="planName">
        <el-input v-model="form.planName" placeholder="请输入计划名称" size="small" />
      </el-form-item>
      
      <el-form-item label="匹配价格" prop="matchPrice">
        <el-input-number
          v-model="form.matchPrice"
          :min="0"
          :max="99999999.00"
          :precision="2"
          :step="0.1"
          :controls="false"
          size="small"
          placeholder="请输入匹配价格"
        />
      </el-form-item>
      
      <el-form-item label="企微链接" prop="weChatLink">
        <el-input v-model="form.weChatLink" placeholder="请输入企微链接" size="small" />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-switch
          v-model="form.status"
          :active-value="1"
          :inactive-value="2"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
      
      <el-form-item label="指定测试渠道" prop="channelId">
        <el-select
          v-model="form.channelId"
          placeholder="请选择渠道"
          size="small"
          filterable
        >
          <el-option
            v-for="item in channelList"
            :key="item.id"
            :label="`${item.id} - ${item.channelName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="时间点" prop="timeRange">
        <el-time-picker
          is-range
          v-model="form.timeRange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="HH:mm:ss"
          size="small"
        />
      </el-form-item>
      
      <el-form-item label="控量" prop="applyControl">
        <el-input-number
          v-model="form.applyControl"
          :min="0"
          :precision="0"
          :controls="false"
          :step="1"
          size="small"
          placeholder="请输入控量"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="submitForm">确 定</el-button>
      <el-button size="small" @click="dialog = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { add, update } from '@/api/platformProductManagement/qwTestPlan'
import { getAllChannelList } from '@/api/channeManage/channelList'

export default {
  name: 'QwTestPlanFormDialog',
  data() {
    return {
      dialog: false,
      title: '',
      channelList: [],
      form: {
        planName: '',
        matchPrice: undefined,
        weChatLink: '',
        status: 1,
        channelId: undefined,
        timeRange: [],
        applyControl: undefined
      },
      rules: {
        planName: [
          { required: true, message: '请输入计划名称', trigger: 'blur' }
        ],
        matchPrice: [
          { required: true, message: '请输入匹配价格', trigger: 'blur' },
          { type: 'number', min: 0, message: '价格必须大于0', trigger: 'blur' },
          { type: 'number', max: 99999999.00, message: '价格不能超过99999999.00', trigger: 'blur' }
        ],
        weChatLink: [
          { required: true, message: '请输入企微链接', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
        ],
        channelId: [
          { required: true, message: '请选择渠道', trigger: 'change' }
        ],
        timeRange: [
          { required: true, message: '请选择时间范围', trigger: 'change' }
        ],
        applyControl: [
          { type: 'number', min: 0, message: '控量必须大于等于0', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getChannelList()
  },
  methods: {
    /** 获取渠道列表 */
    async getChannelList() {
      const res = await getAllChannelList()
      this.channelList = res.data || []
    },

    /** 打开新增弹窗 */
    openAdd() {
      this.reset()
      this.dialog = true
      this.title = '新增测试计划'
    },

    /** 打开编辑弹窗 */
    openEdit(row) {
      this.reset()
      this.dialog = true
      this.title = '编辑测试计划'
      this.$nextTick(() => {
        const formData = { ...row }
        // 处理时间点数据
        if (formData.startPoint && formData.endPoint) {
          formData.timeRange = [formData.startPoint, formData.endPoint]
          delete formData.startPoint
          delete formData.endPoint
        }
        this.form = formData
      })
    },

    /** 重置表单 */
    reset() {
      this.form = {
        planName: '',
        matchPrice: undefined,
        weChatLink: '',
        status: 1,
        channelId: undefined,
        timeRange: null,
        applyControl: undefined
      }
      this.$refs.form && this.$refs.form.resetFields()
    },

    /** 提交表单 */
    submitForm() {
      this.$refs.form.validate(async valid => {
        if (!valid) return
        
        try {
          const params = { ...this.form }
          // 处理时间范围
          if (params.timeRange && params.timeRange.length === 2) {
            params.startPoint = params.timeRange[0]
            params.endPoint = params.timeRange[1]
            delete params.timeRange
          }
          
          if (params.id) {
            await update(params)
          } else {
            await add(params)
          }
          
          this.$message.success('操作成功')
          this.dialog = false
          this.$emit('success')
        } catch (error) {
          // 错误会被全局拦截器处理，这里不需要处理
        }
      })
    }
  }
}
</script>

<style scoped>
.text-center {
  text-align: center;
  line-height: 32px;
}
</style>