import request from '@/utils/request'
//获取所有乙方用户
export const queryAllPartyB = () => {
    return request({
        url: "/loan/PartyB/queryAllPartyB",
        method: "get"
    })
}

export const getChannelList = (data) => {
    return request({
        url: "/loan/PartyB/channelList",
        method: "get",
        params: data
    })
}
//新增渠道
export const addChannleOne = (data) => {
    return request({
        url: "/loan/PartyB/makeChanne",
        method: "post",
        data
    })
}
//新增渠道链接
export const addedUrlOne = (data) => {
    return request({
        url: "/loan/PartyB/addedUrl",
        method: "post",
        data
    })
}
//复制渠道
export const copyChannleOne = (data) => {
    return request({
        url: '/loan/PartyB/channeDetailed',
        method: "get",
        params: data
    })
}

//修改当前链接的qq和微信状态
export const editUrlStatus = (data) => {
    return request({
        url: "/loan/PartyB/channeCondition",
        method: "post",
        data
    })
}
// 一键生成
export const autoCreateUrl = (data) => {
    return request({
        url: "/loan/PartyB/dNSPod",
        method: "post",
        data
    })
}
//修改渠道状态
export const editChannleStatus = (data) => {
    return request({
        url: "/loan/PartyB/channelStatus",
        method: "post",
        data
    })
}
//修改渠道状态
export const editChannleCheckStatus = (data) => {
    return request({
        url: "loan/PartyB/channel/checkStatus",
        method: "post",
        data
    })
}
//修改渠道状态
export const editChannleCityStatus = (data) => {
  return request({
      url: "/loan/PartyB/channel/cityCrashStatus",
      method: "post",
      data
  })
}
//获取渠道详情信息
export const getDetailedChannel = (data) => {
    return request({
        url: '/loan/backstageChannel/detailedChannel',
        method: "get",
        params: data
    })
}
//获取渠道详情信息
export const editDetailedChannel = (data) => {
    console.log(data);
    return request({
        url: '/loan/backstageChannel/revampChannel',
        method: "post",
        data
    })
}
export const exportChannelFail=(data)=>{
    return request({
        url:'/loan/backstageChannel/exportChannelFail',
        method:"get",
        responseType:"arraybuffer",
        params:data
    })
}

// 获取所有渠道
export const getAllChannelList = () => {
    return request({
      url: "/loan/productRule/getAllChannelList",
      method: "get"
    })
  }

  // 获取渠道模板配置的渠道列表
  export const getChannelTemplateChannels = () => {
    return request({
      url: '/loan/xm/channel/template/channels',
      method: 'get'
    })
  }
