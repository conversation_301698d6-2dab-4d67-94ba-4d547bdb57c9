<template>
  <div class="app-container">
    <div class="header">
      <div class="info">
        渠道ID:{{ $route.query.channelId }}---渠道名称：{{ $route.query.name }}
      </div>
      <div>
        <el-form :model="queryParams" ref="queryForm" :inline="true">
          <el-form-item label="日期" prop="time">
            <el-date-picker
              v-model="value1"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>

    <el-table border :data="dataList">
      <el-table-column label="失败原因" prop="reason" align="center" />
      <el-table-column label="数量" prop="total" align="center" />
      <el-table-column label="百分比" prop="scale" align="center">
        <template  slot-scope="{row}">
          <div>{{ row.scale }}%</div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getForNonAdmittanceDeatils } from "@/api/statisticalManage";
export default {
  name: "FailDetail",
  data() {
    return {
      queryParams: {
        channelId: "",
        startTime: "",
        endTime: "",
      },
      value1: [],
      dataList: [],
    };
  },
  methods: {
    handleQuery() {
      if (this.value1 !== null) {
        // this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      getForNonAdmittanceDeatils(this.queryParams).then((res) => {
        this.dataList = res.data;
      });
    },
  },
  mounted() {
    for (let key in this.$route.query) {
      if (Object.keys(this.queryParams).includes(key)) {
        this.queryParams[key] = this.$route.query[key];
      }
    }
    this.value1 = [this.queryParams.startTime, this.queryParams.endTime];
    getForNonAdmittanceDeatils(this.queryParams).then((res) => {
      this.dataList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
.info {
  font-size: 28px;
  font-weight: 800;
  margin-bottom: 20px;
  margin-right: 30px;
  color: #333;
}
.header {
  display: flex;
  align-items: center;
}
</style>
