<template>
  <div class="codemirror-editor" :class="{ 'fullscreen': isFullscreen }">
    <div class="editor-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <span class="editor-title">
          <i :class="modeIcon"></i>
          {{ modeLabel }}
        </span>
      </div>
      <div class="toolbar-right">
        <el-button-group v-if="mode === 'json'">
          <el-tooltip content="格式化JSON" placement="top">
            <el-button
              size="mini"
              icon="fas fa-indent"
              @click="formatContent"
            />
          </el-tooltip>
          <el-tooltip content="压缩JSON" placement="top">
            <el-button
              size="mini"
              icon="fas fa-compress-alt"
              @click="compressContent"
            />
          </el-tooltip>
        </el-button-group>
        
        <el-button-group class="config-actions">
          <el-tooltip :content="isFullscreen ? '退出全屏' : '全屏编辑'" placement="top">
            <el-button
              size="mini"
              :icon="isFullscreen ? 'fas fa-compress' : 'fas fa-expand'"
              @click="toggleFullscreen"
              plain
            />
          </el-tooltip>
          <el-tooltip content="重置配置" placement="top">
            <el-button
              size="mini"
              icon="fas fa-undo"
              @click="handleReset"
              plain
            />
          </el-tooltip>
          <el-tooltip content="保存配置" placement="top">
            <el-button
              size="mini"
              type="primary"
              icon="fas fa-check"
              @click="handleSave"
              :loading="saving"
            />
          </el-tooltip>
        </el-button-group>
      </div>
    </div>
    
    <div class="editor-container">
      <textarea
        ref="textarea"
        :value="value"
        :placeholder="placeholder"
        @input="handleInput"
      />
    </div>
  </div>
</template>

<script>
import CodeMirror from 'codemirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/theme/material.css'
import 'codemirror/mode/javascript/javascript'
import 'codemirror/addon/edit/closebrackets'
import 'codemirror/addon/edit/matchbrackets'
import 'codemirror/addon/fold/foldcode'
import 'codemirror/addon/fold/foldgutter'
import 'codemirror/addon/fold/brace-fold'
import 'codemirror/addon/fold/foldgutter.css'
import 'codemirror/addon/lint/lint'
import 'codemirror/addon/lint/lint.css'

export default {
  name: 'CodeMirrorEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    mode: {
      type: String,
      default: 'json', // json 或 text
      validator: (value) => ['json', 'text'].includes(value)
    },
    placeholder: {
      type: String,
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    },
    showToolbar: {
      type: Boolean,
      default: true
    },
    height: {
      type: String,
      default: '300px'
    },
    saving: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      codemirror: null,
      isFullscreen: false
    }
  },
  
  computed: {
    modeIcon() {
      return this.mode === 'json' ? 'fas fa-file-code' : 'fas fa-edit'
    },
    
    modeLabel() {
      return this.mode === 'json' ? 'JSON 配置' : '文本配置'
    }
  },
  
  mounted() {
    this.initCodeMirror()
  },
  
  beforeDestroy() {
    if (this.codemirror) {
      this.codemirror.toTextArea()
      this.codemirror = null
    }
  },

  
  watch: {
    value(newVal) {
      if (this.codemirror && newVal !== this.codemirror.getValue()) {
        this.codemirror.setValue(newVal || '')
      }
    },
    
    mode() {
      this.updateMode()
    }
  },
  
  methods: {
    // 自定义 JSON lint 函数
    jsonLint(text) {
      const errors = []
      
      if (!text.trim()) {
        return errors
      }
      
      try {
        JSON.parse(text)
      } catch (error) {
        // 尝试解析错误位置
        let line = 0
        let ch = 0
        
        // 简单的错误位置解析
        const lines = text.split('\n')
        const errorMessage = error.message
        
        // 尝试从错误消息中提取位置信息
        const positionMatch = errorMessage.match(/position (\d+)/)
        if (positionMatch) {
          const position = parseInt(positionMatch[1])
          let currentPos = 0
          
          for (let i = 0; i < lines.length; i++) {
            if (currentPos + lines[i].length >= position) {
              line = i
              ch = position - currentPos
              break
            }
            currentPos += lines[i].length + 1 // +1 for newline
          }
        }
        
        errors.push({
          from: { line, ch },
          to: { line, ch: ch + 1 },
          message: errorMessage,
          severity: 'error'
        })
      }
      
      return errors
    },

    initCodeMirror() {
      const options = {
        lineNumbers: true,
        lineWrapping: true,
        theme: 'material',
        readOnly: this.readonly,
        autoCloseBrackets: true,
        matchBrackets: true,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],

      }
      
      if (this.mode === 'json') {
        options.mode = { name: 'javascript', json: true }
        options.lint = { getAnnotations: this.jsonLint }
      } else {
        options.mode = 'text/plain'
      }
      
      this.codemirror = CodeMirror.fromTextArea(this.$refs.textarea, options)
      this.codemirror.setValue(this.value || '')
      this.codemirror.setSize(null, this.height)
      
      this.codemirror.on('change', (cm) => {
        const content = cm.getValue()
        this.$emit('input', content)
      })
      
      this.codemirror.on('focus', () => {
        this.$emit('focus')
      })
      
      this.codemirror.on('blur', () => {
        this.$emit('blur')
      })
    },
    
    updateMode() {
      if (!this.codemirror) return
      
      if (this.mode === 'json') {
        this.codemirror.setOption('mode', { name: 'javascript', json: true })
        this.codemirror.setOption('lint', { getAnnotations: this.jsonLint })
      } else {
        this.codemirror.setOption('mode', 'text/plain')
        this.codemirror.setOption('lint', false)
      }
    },
    
    formatContent() {
      if (this.mode !== 'json' || !this.codemirror) return
      
      try {
        const content = this.codemirror.getValue()
        const parsed = JSON.parse(content)
        const formatted = JSON.stringify(parsed, null, 2)
        this.codemirror.setValue(formatted)
        this.$message.success('JSON已格式化')
      } catch (error) {
        this.$message.warning('JSON格式错误，无法格式化')
      }
    },
    
    compressContent() {
      if (this.mode !== 'json' || !this.codemirror) return
      
      try {
        const content = this.codemirror.getValue()
        const parsed = JSON.parse(content)
        const compressed = JSON.stringify(parsed)
        this.codemirror.setValue(compressed)
        this.$message.success('JSON已压缩')
      } catch (error) {
        this.$message.warning('JSON格式错误，无法压缩')
      }
    },
    
    handleInput(event) {
      this.$emit('input', event.target.value)
    },
    
    focus() {
      if (this.codemirror) {
        this.codemirror.focus()
      }
    },
    
    blur() {
      if (this.codemirror) {
        this.codemirror.getInputField().blur()
      }
    },
    
    setValue(value) {
      if (this.codemirror) {
        this.codemirror.setValue(value || '')
      }
    },
    
    getValue() {
      return this.codemirror ? this.codemirror.getValue() : this.value
    },
    
    handleReset() {
      this.$emit('reset')
    },
    
    handleSave() {
      this.$emit('save')
    },
    
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      
      // 全屏时调整编辑器尺寸
      this.$nextTick(() => {
        if (this.codemirror) {
          this.codemirror.refresh()
          if (this.isFullscreen) {
            this.codemirror.setSize(null, 'calc(100vh - 80px)')
          } else {
            this.codemirror.setSize(null, this.height)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.codemirror-editor {
  border: 1px solid #e8eaed;
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1500;
    border-radius: 0;
    border: none;
  }
  
  .editor-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e8eaed;
    
    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .editor-title {
        font-weight: 600;
        color: #202124;
        display: flex;
        align-items: center;
        
        i {
          margin-right: 6px;
          color: #2196f3;
        }
      }
    }
    
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .el-button-group {
        .el-button {
          border-color: #e8eaed;
          color: #5f6368;
          
          &:hover {
            border-color: #2196f3;
            color: #2196f3;
          }
        }
      }
      
      .config-actions {
        .el-button {
          border-color: #e8eaed !important;
          
          &.is-plain {
            color: #5f6368 !important;
            background-color: transparent !important;
            
            &:hover {
              border-color: #ff9800 !important;
              color: #ff9800 !important;
              background-color: rgba(255, 152, 0, 0.1) !important;
            }
          }
          
          &.el-button--primary {
            background-color: #4caf50 !important;
            border-color: #4caf50 !important;
            color: #ffffff !important;
            
            &:hover {
              background-color: #45a049 !important;
              border-color: #45a049 !important;
            }
            
            &:focus {
              background-color: #4caf50 !important;
              border-color: #4caf50 !important;
            }
          }
        }
      }
    }
  }
  
  .editor-container {
    position: relative;
    
    ::v-deep .CodeMirror {
      height: auto;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.6;
      border: none;
      border-radius: 0;
      
      .CodeMirror-lines {
        padding: 8px 0;
      }
      
      .CodeMirror-line {
        padding: 0 12px;
      }
      
      .CodeMirror-gutters {
        background: #f8f9fa;
        border-right: 1px solid #e8eaed;
      }
      
      .CodeMirror-linenumber {
        color: #9aa0a6;
        padding: 0 8px;
      }
      
      .CodeMirror-cursor {
        border-left: 2px solid #2196f3;
      }
      
      .CodeMirror-selected {
        background: rgba(33, 150, 243, 0.1);
      }
      
      .CodeMirror-focused .CodeMirror-selected {
        background: rgba(33, 150, 243, 0.2);
      }
      
      // JSON 语法高亮
      .cm-string { color: #4caf50; }
      .cm-number { color: #ff9800; }
      .cm-property { color: #2196f3; }
      .cm-keyword { color: #9c27b0; }
      .cm-atom { color: #f44336; }
      .cm-def { color: #00bcd4; }
      .cm-bracket { color: #607d8b; }
      
      // 错误提示
      .CodeMirror-lint-mark-error {
        background: rgba(255, 0, 0, 0.3);
        border-bottom: 2px wavy #f44336;
      }
      
      .CodeMirror-lint-marker-error {
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16'%3E%3Cpath fill='%23f44336' d='M8 0a8 8 0 100 16A8 8 0 008 0zM7 3v6h2V3H7zm0 8v2h2v-2H7z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: center;
        cursor: pointer;
      }
      
      .CodeMirror-lint-tooltip {
        background: #fff;
        border: 1px solid #f44336;
        border-radius: 4px;
        color: #f44336;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        max-width: 300px;
        padding: 6px 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        z-index: 10000;
      }
    }
    
  }
}
</style> 