<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <!-- <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="状态"
          clearable
          size="small"
        >
          <el-option value="" label="全部"></el-option>
          <el-option value="1" label="正常"></el-option>
          <el-option value="2" label="禁用"></el-option>
        </el-select>
      </el-form-item> -->

      <el-form-item label="渠道名称" prop="channelName">
        <el-input
          clearable
          placeholder="请输入渠道名称"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道ID" prop="channelId">
        <el-input
          clearable
          placeholder="请输入渠道ID"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model.number="queryParams.channelId"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 渠道列表 -->

    <el-table :data="channelList" style="width: 100%;">
      <el-table-column label="渠道ID" prop="id" align="center" />
      <el-table-column label="渠道名称" prop="channelName" align="center" />
      <el-table-column label="价格" prop="uvPrice" align="center" width="120">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.uvPrice"
            :min="0"
            class="input_number"
            style="text-align: center"
            :ref="'valueinput' + scope.$index"
            @blur="handleChangePrice(scope.row)"
            @focus="handleFocusChangePrice(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column label="自动控量状态" prop="userName" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.loginAutoControlStatus"
              :active-text="row.loginAutoControlStatus == 1 ? '开启' : ''"
              :inactive-text="row.loginAutoControlStatus == 2 ? '关闭' : ''"
              :active-value="1"
              :inactive-value="2"
              @change="changeAutoCheckStatus($event, row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="当前控量比例%"
        prop="inputRatio"
        width="120px"
        align="center"
      >
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.inputRatio"
            :min="0"
            class="input_number"
            style="text-align: center"
            :ref="'valueinput' + scope.$index"
            @blur="handleChangeInputRatio(scope.row)"
            @focus="handleFocusInputRatio(scope.row)"
          />
        </template>
      </el-table-column>
      <!-- <el-table-column label="状态" prop="status" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.status"
              :active-text="row.status == 1 ? '合作中' : ''"
              :inactive-text="row.status == 2 ? '已停用' : ''"
              :active-value="1"
              :inactive-value="2"
              @change="changeStatus($event, row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column> -->

      <el-table-column label="撞库状态" prop="checkStatus" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.checkStatus"
              :active-text="row.checkStatus == 1 ? '开启' : ''"
              :inactive-text="row.checkStatus == 2 ? '关闭' : ''"
              :active-value="1"
              :inactive-value="2"
              @change="changeCheckStatus($event, row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="城市撞库" prop="cityCrashStatus" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.cityCrashStatus"
              :active-text="row.cityCrashStatus == 1 ? '开启' : ''"
              :inactive-text="row.cityCrashStatus == 2 ? '关闭' : ''"
              :active-value="1"
              :inactive-value="2"
              @change="changeCityStatus($event, row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="匹配产品数量" prop="matchCount" align="center" width="120">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.matchCount"
            type="number"
            :min="0"
            @change="handleChangeMatchCount(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="银行机构数量" prop="bankCount" align="center" width="120">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.bankCount"
            type="number"
            :min="0"
            @change="handleChangeBankCount(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="全国机构数量" prop="countryCount" align="center" width="120">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.countryCount"
            type="number"
            :min="0"
            @change="handleChangeCountryCount(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="产品总价格" prop="priceSum" align="center" width="120">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.priceSum"
            type="number"
            :min="0"
            @change="handleChangePriceSum(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="银行最低单价" prop="maxPrice" align="center" width="120">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.maxPrice"
            type="number"
            :min="0"
            @change="handleChangeMaxPrice(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="企微最低单价" prop="weChatMaxPrice" align="center" width="120">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.weChatMaxPrice"
            type="number"
            :min="0"
            @change="handleChangeWeChatMaxPrice(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import {
  getUnionList,
  editChangeAutoStatus,
  editChangeUvPrice,
  editInputRatio,
} from "@/api/channeManage/channelUnion";
import { editChannleCheckStatus ,editChannleCityStatus} from "@/api/channeManage/channelList";
import { updateCityLoginControl } from "@/api/channeManage/cityLoginControl";
export default {
  name: "channeList",
  data() {
    return {
      total: 0,
      currentPrice: 0,
      currentRatio: 0,
      channelList: [],
      queryParams: {
        status: "",
        commerce: "",
        channelName: "",
        partyUsername: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    getList() {
      getUnionList(this.queryParams).then((res) => {
        this.channelList = res.rows;
        this.total = res.total;
      });
    },
    //查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    changeAutoCheckStatus(e, row) {
      this.$confirm(
        `确定${row.checkStatus == 2 ? "关闭" : "启用"}吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          editChangeAutoStatus({ id: row.id, status: e })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch((err) => {
          this.getList();
        });
    },
    changeCheckStatus(e, row) {
      this.$confirm(
        `确定${row.checkStatus == 2 ? "关闭" : "启用"}吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          editChannleCheckStatus({ id: row.id, status: e })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch((err) => {
          this.getList();
        });
    },
    handleChangePrice(row) {
      if (this.currentPrice == row.uvPrice * 1) return;
      if (row.uvPrice * 1 <= 0)
        return this.$message.error("请输入正确的UV价格");
      if (!/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(row.uvPrice * 1))
        return this.$message.error("请输入两位小数的UV价格");
      this.$confirm(`确定修改吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          editChangeUvPrice({ id: row.id, num: row.uvPrice * 1 })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch((err) => {
          this.getList();
        });
    },
    handleFocusChangePrice(row) {
      this.currentPrice = row.uvPrice * 1;
    },
    handleChangeInputRatio(row) {
      if (this.currentRatio == row.inputRatio * 1) return;
      if (row.inputRatio * 1 <= 0) return this.$message.error("请输入正确比例");
      console.log(!/^[1-9]\d{0,2}$/.test(row.inputRatio * 1));
      if (!/^[1-9]\d{0,2}$/.test(row.inputRatio * 1))
        return this.$message.error("请输入正确比例");
      this.$confirm(`确定修改吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          editInputRatio({ id: row.id, num: row.inputRatio * 1 })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch((err) => {
          this.getList();
        });
    },
    handleFocusInputRatio(row) {
      this.currentRatio = row.inputRatio * 1;
    },
    changeCityStatus(e, row) {
      this.$confirm(
        `确定${row.cityCrashStatus == 2 ? "关闭" : "启用"}吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          editChannleCityStatus({ id: row.id, status: e })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch((err) => {
          this.getList();
        });
    },
    handleChangeMatchCount(row) {
      this.handleCommonChange(row, 'matchCount', '匹配产品数量');
    },
    handleChangePriceSum(row) {
      this.handleCommonChange(row, 'priceSum', '产品总价格');
    },
    handleChangeMaxPrice(row) {
      this.handleCommonChange(row, 'maxPrice', '银行最低单价');
    },
    handleChangeWeChatMaxPrice(row) {
      this.handleCommonChange(row, 'weChatMaxPrice', '企微最低单价');
    },
    handleChangeBankCount(row) {
      this.handleCommonChange(row, 'bankCount', '银行机构数量');
    },
    handleChangeCountryCount(row) {
      this.handleCommonChange(row, 'countryCount', '全国机构数量');
    },
    handleCommonChange(row, field, fieldName) {
      if (row[field]) {
        row[field] = parseInt(row[field])
      }

      this.$confirm(
        `${row.channelName}(ID:${row.id}) ${fieldName}改为 ${row[field]}？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          updateCityLoginControl(row)
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch(() => {
              this.getList();
            });
        })
        .catch(() => {
          this.getList();
        });
    },
  },
  async mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.el-switch__label.el-switch__label--left.is-active {
  color: #333;
}
.el-input.el-input--medium {
  text-align: center !important;
}

::v-deep .el-input {
  input[type="number"] {
    padding-right: 0px;
    appearance: textfield;
    line-height: 1px !important;
  }
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    appearance: none;
    margin: 0;
  }
}
</style>
