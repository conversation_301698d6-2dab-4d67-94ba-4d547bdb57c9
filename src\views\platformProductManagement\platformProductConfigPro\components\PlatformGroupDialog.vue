<template>
  <el-dialog 
    :title="title" 
    :visible.sync="dialogVisible" 
    width="1200px" 
    @close="handleClose"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="dialog-content">
      <el-form 
        :model="form" 
        :rules="rules" 
        ref="form" 
        label-position="top"
      >
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分组名称"></el-input>
        </el-form-item>
        <el-form-item label="选择平台" prop="selectedPlatforms">
          <el-transfer
            filterable
            v-model="form.selectedPlatforms"
            :data="platformList"
            :titles="['待选平台', '已选平台']"
            :props="{
              key: 'id',
              label: 'name',
              disabled: 'disabled'
            }"
          ></el-transfer>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSave">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'

export default {
  name: 'PlatformGroupDialog',

  props: {
    // 弹窗是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗标题
    title: {
      type: String,
      default: '新增分组'
    },
    // 平台列表数据
    platformList: {
      type: Array,
      default: () => []
    },
    // 编辑时的初始数据
    editData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      dialogVisible: false,
      form: {
        name: '',
        renderKey: '',
        selectedPlatforms: [],
        sort: 0
      },
      rules: {
        name: [
          { required: true, message: '请输入分组名称', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ],
        selectedPlatforms: [
          { required: true, message: '请至少选择一个平台', trigger: 'blur' }
        ]
      }
    }
  },

  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    },
    editData: {
      handler(val) {
        if (Object.keys(val).length) {
          this.form = {
            name: val.groupName,
            renderKey: val.renderKey || '',
            selectedPlatforms: val.productList.map(item => item.platformType),
            sort: val.sort || 0
          }
        } else {
          this.form = {
            name: '',
            renderKey: uuidv4(),
            selectedPlatforms: [],
            sort: 0
          }
        }
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    // 处理保存
    handleSave() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const formData = {
            renderKey: this.form.renderKey,
            groupName: this.form.name,
            productList: this.form.selectedPlatforms.map(type => {
              const platform = this.platformList.find(item => item.id == type)
              return {
                platform: platform.name,
                platformType: platform.id
              }
            }),
            sort: this.form.sort
          }
          // 等待父组件的处理结果
          this.$emit('save', formData, (success) => {
            if (success) {
              this.dialogVisible = false
            }
          })
        }
      })
    },

    // 处理关闭
    handleClose() {
      this.$refs.form?.resetFields()
      this.form = {
        name: '',
        renderKey: uuidv4(),
        selectedPlatforms: [],
        sort: 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px 20px 0;

  ::v-deep .el-transfer {
    display: flex;
    justify-content: center;
    align-items: center;

    .el-transfer-panel {
      flex: 1;
    }
  }
}
</style> 