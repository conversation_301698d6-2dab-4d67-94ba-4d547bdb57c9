<template>
  <div class="app-container">
    <el-form ref="queryForm" :inline="true">
      <el-form-item label="今日:" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="昨日:" prop="time">
        <el-date-picker
          v-model="value2"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border style="width: 100%; margin-top: 20px">
      <el-table-column prop="platformName" label="平台" align="center"></el-table-column>
      <el-table-column prop="todayRevenues" label="今日" align="center">
        <template slot-scope="scope">
          <span :style="{ color: getCompareColor(scope.row.todayRevenues, scope.row.yesterdayRevenues) }">
            {{ scope.row.todayRevenues }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="yesterdayRevenues" label="昨日" align="center"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getComparisonData } from "@/api/xmxrChannelManage/profitCompare";
import { sum } from "@/utils/calculate";

export default {
  data() {
    return {
      value1: [this.getTodayTime() + " 00:00:00", this.getTodayTime(true)],
      value2: [this.getYesTime() + " 00:00:00", this.getYesTime(true)],
      tableData: [], // 新增表格数据数组
      queryParams: {
        todayStartDate: this.getTodayTime() + " 00:00:00",
        todayEndDate: this.getTodayTime(true),
        yesterdayStartDate: this.getYesTime() + " 00:00:00",
        yesterdayEndDate: this.getYesTime(true),
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams = {
        todayStartDate: this.value1[0],
        todayEndDate: this.value1[1],
        yesterdayStartDate: this.value2[0],
        yesterdayEndDate: this.value2[1],
      };
      this.getData();
    },

    getTodayTime(flag) {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      let hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
      let minute =
        date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      let second =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      if (!flag) {
        return YY + "-" + MM + "-" + DD;
      } else {
        return `${YY}-${MM}-${DD} ${hour}:${minute}:${second}`;
      }
    },

    getYesTime(flag) {
      var date = new Date(new Date().valueOf() - 86400 * 1000);

      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      let hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
      let minute =
        date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      let second =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      if (!flag) {
        return YY + "-" + MM + "-" + DD;
      } else {
        return `${YY}-${MM}-${DD} ${hour}:${minute}:${second}`;
      }
    },
    getData() {
      getComparisonData(this.queryParams).then((res) => {
        if (res.data.length > 0) {
          // 构建表格数据
          this.tableData = [
            {
              platformName: "小马总额度",
              todayRevenues: sum(res.data.map((item) => item.todayRevenues)),
              yesterdayRevenues: sum(res.data.map((item) => item.yesterdayRevenues)),
            },
            ...res.data
          ];
        }
      });
    },
    getCompareColor(today, yesterday) {
      if (today > yesterday) {
        return '#ff4d4f' // 红色
      } else if (today < yesterday) {
        return '#52c41a' // 绿色
      }
      return '' // 相等时使用默认颜色
    },
  },
  mounted() {
    this.getData();
  },
};
</script>

<style lang="scss"></style>
