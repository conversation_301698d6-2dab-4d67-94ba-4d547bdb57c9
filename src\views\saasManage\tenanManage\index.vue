<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="租户ID" prop="tenantId">
        <el-input v-model.number="queryParams.tenantId" placeholder="请输入租户ID" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="平台名称" prop="platformName">
        <el-input v-model="queryParams.platformName" placeholder="平台名称" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableList" border>
      <el-table-column label="租户流量平台ID" width="130" align="center" prop="tenantPlatformId" />
      <el-table-column label="租户ID" width="100" align="center" prop="tenantId" />
      <el-table-column label="租户主体名称" align="center" prop="subjectName" />
      <el-table-column label="平台名称" align="center" prop="platformName" />
      <el-table-column label="私钥" align="center" prop="signKey" />
      <el-table-column label="AES密钥" align="center" prop="signKey">
        <template slot-scope="{row}">
          <div>
            {{row.signKey.slice(0,16)}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ statusType[row.status] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="申请开通时间" align="center" prop="applyTime" />
      <el-table-column label="操作">
        <template slot-scope="{row}">
          <div v-if="row.status == '-1'">
            <el-button size="mini" type="text" icon="el-icon-edit-outline" @click="handleDistribution(row)">分配
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { getPlatformList, platformUpdateAllot } from "@/api/saas/tenant";
export default {
  name: "SaasTenant",
  data() {
    return {
      tableList: [],
      open: false,
      total: 0,
      statusType: {
        "-1": "未分配",
        0: "未开通",
        1: "审核中",
        2: "未接单",
        3: "接单中",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: "",
        platformName: "",
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleDistribution(row) {
      this.$confirm(`确定分配${row.subjectName}吗？`, { type: "warning" }).then(
        (res) => {
          platformUpdateAllot(row.tenantPlatformId).then((res) => {
            this.$message.success("分配成功");
            this.getList();
          });
        }
      );
    },
    getList() {
      getPlatformList(this.queryParams).then((res) => {
        this.tableList = res.rows;
        this.total = res.total;
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>

</style>
