<template>
    <el-drawer :title="text" :visible.sync="show" append-to-body :size="width" :wrapperClosable="false"
        @close="handleCloseDrawer">
        <main class="container-release">
            <header class="header flex align-items-c">
                <div :class="['menu active']">基础规则</div>
                <div :class="['menu', { 'active': active }]">订单规则</div>
            </header>
            <section class="wrap" v-show="!active">
                <el-form :rules="baseRule" ref="baseForm" size="medium" :model="baseForm">
                    <el-row :gutter="16">
                        <el-col :xl="12" :lg="12" :md="24" :xs="24">
                            <el-form-item label="城市筛选方式:" prop="cityStatus">
                                <el-select clearable v-model="baseForm.cityStatus" placeholder="请选择城市筛选方式"
                                    style="width: 100%;">
                                    <el-option value="0" label="全部"> </el-option>
                                    <el-option value="1" label="仅展示配置城市"> </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xl="12" :lg="12" :md="24" :xs="24">
                            <el-form-item label="城市:" v-if="baseForm.cityStatus == '1' || baseForm.cityStatus == '2'">
                                <BaseCascader :options="cityList" :is_deep="true" :has_all_select="true"
                                    @getOptions="disableEare" :back_options="baseForm.district" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </section>
            <section class="wrap" v-show="active">
                <!-- <div class="tags">基本筛选</div> -->
                <el-form size="medium" label-position="top" ref="form">
                    <el-row :gutter="16">
                        <el-col :xl="24" :lg="24" :md="24" :xs="24">
                            <el-form-item label="规则名称">
                                <div class="align-items-c">
                                    <el-input v-model="formData.ruleName" placeholder="请输入规则名称"></el-input>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :xl="24" :lg="24" :md="24" :xs="24">
                            <el-form-item label="年龄范围">
                                <div class="align-items-c">
                                    <el-input placeholder="请输入最小年龄" v-model="formData.startAge" type="number" />
                                    <div class="tap">至</div>
                                    <el-input placeholder="请输入最大年龄" v-model="formData.endAge" type="number" />
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :xl="24" :lg="24" :md="24" :xs="24">
                            <el-form-item label="需求额度范围">
                                <div class="align-items-c">
                                    <el-input placeholder="请输入最小范围" v-model="formData.startQuota" type="number" />
                                    <div class="tap">至</div>
                                    <el-input placeholder="请输入最大范围" v-model="formData.endQuota" type="number" />
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item v-for="item in basic_typeList" :key="item.dictId" :label="item.dictName">
                        <el-checkbox-group v-model="checkList" class="flex-wrap">
                            <el-checkbox v-for="citem in item.dataRules"
                                :label="item.paramName.split('_')[item.paramName.split('_').length - 1] + 'Ids-' + citem.dictCode"
                                :key="citem.dictCode">
                                {{ citem.dictLabel }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    <div class="tags">资质筛选</div>
                    <el-form-item v-for="item in assets_typeList" :key="item.dictId" :label="item.dictName">
                        <el-checkbox-group v-model="checkList" class="flex-wrap">
                            <el-checkbox v-for="citem in item.dataRules"
                                :label="item.paramName.split('_')[item.paramName.split('_').length - 1] + 'Ids-' + citem.dictCode"
                                :key="citem.dictCode">
                                {{ citem.dictLabel }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="条件选择">
                        <el-radio v-model="formData.ruleStatus" label="0">选中的满足任意一条即可</el-radio>
                        <el-radio v-model="formData.ruleStatus" label="1">选中的都需要满足</el-radio>
                    </el-form-item>
                    <div class="tags">价格匹配</div>
                    <el-form-item label="价格">
                        <div class="align-items-c" style="width: 500px;">
                        <el-input placeholder="请输入价格" v-model="formData.matchingPriceSort" 
                            type="text"
                            size="small"
                            @input="onInputPrice"
                            @keypress="(e) => { if(e.key === '.' || e.key === '+' || e.key === '-' || e.key === 'e' || e.key === 'E') { e.preventDefault() } }"
                            />
                            <span style="margin-left: 10px;">元</span>
                        </div>
                    </el-form-item>
                    <div class="tags">状态</div>
                    <el-form-item>
                        <el-radio v-model="formData.status" label="0">启用</el-radio>
                        <el-radio v-model="formData.status" label="1">停用</el-radio>
                    </el-form-item>
                </el-form>
            </section>
            <footer class="justify-content-c">
                <template v-if="!active">
                    <el-button type="default" @click="handleCloseDrawer">取 消</el-button>
                    <el-button type="primary" @click="handleNext">下一步</el-button>
                </template>
                <template v-if="active">
                    <el-button type="default" @click="previousStep">上一步</el-button>
                    <el-button type="primary" @click="handleSubmit">提 交</el-button>
                </template>
            </footer>
        </main>
    </el-drawer>
</template>

<script>
import {
    getProductRule,
    addProductRule,
    getChannelList,
    getProductCity,
    baseProductRule,
} from "@/api/productManage/product"
import {
    updateQwProductRule,
    qwProductTemplate
} from "@/api/addRegulationProducts/productRule";

import BaseCascader from "@/components/cascader"
import object from 'element-resize-detector/src/detection-strategy/object';
export default {
    name: 'release',
    data() {
        return ({
          text:'',
            show: false,
            active: 0,
            basic_typeList: [],
            assets_typeList: [],
            checkList: [],
            cityList: [],
            channelList: [],
            baseForm: {
                channelStatus: 0,
                deviceTypeStatus: 0,
                cityStatus: '0',
                mobileTypes: 0,
                channelIds: [],
                district: [],
            },
            baseRule: {
                channelStatus: [
                    { required: true, message: "请选择渠道筛选方式", trigger: "blur" },
                ],
                deviceTypeStatus: [
                    { required: true, message: "请选择设备筛选方式", trigger: "blur" },
                ],
                cityStatus: [
                    { required: true, message: "请选择城市筛选方式", trigger: "blur" },
                ],
                mobileTypes: [
                    { required: true, message: "请选择运营商类型", trigger: "blur" },
                ],
            },
            formData: {
                status: "0",
                ruleStatus: "0",
                startAge: "",
                endAge: "",
                startQuota: "",
                endQuota: "",
                overdue6Min: "",
                overdue6Max: "",
                overdue3Min: "",
                overdue3Max: "",
                productId: "",
                loanScoreMin: "",
                loanScoreMax: "",
                overdue6M0Min: "",
                matchingPriceSort: "",
                repaymentRateMin: "",
                b22170052Min: "",
                b22170045Min: "",
            },
        })
    },
    props: {
        value: {
            type: Boolean,
            default: false
        },
        width: {
            type: String,
            default: '80%'
        },
        id: {
            type: [Number, String],
            default: ''
        },
        datas: {
            type: Object,
            default: ''
        }
    },
    watch: {
        value: {
            handler() {
                this.show = this.value
                if (this.show && this.id) {
                    this.init()
                }
            },
            deep: true
        },
    },
    components: {
        BaseCascader
    },
    created() {

    },
    methods: {
        /**
         *  @param { Function } init 初始化
         */
        init() {
            this.formData.productId = this.id
            this.active = 0
            getProductCity().then((res) => {
                let data = null;
                data = res.data.map((item) => {
                    if (item.citys) {
                        {
                            return {
                                value: item.code,
                                label: item.name,
                                children: [
                                    ...item.citys.map((citem) => {
                                        return {
                                            value: citem.code,
                                            label: citem.name,
                                        };
                                    }),
                                ],
                            };
                        }
                    } else {
                        return {
                            value: item.code,
                            label: item.name,
                        };
                    }
                });
                this.cityList = JSON.parse(JSON.stringify(data));
                this.update(this.datas)
            });

            getChannelList().then((res) => {
                this.channelList = res.data;
            });
        },
        update(res) {
            console.log('投放规则', res.ruleBasics);

            this.basic_typeList = res.basic_typeList;
            this.assets_typeList = res.assets_typeList;
            this.xy_typeList = res.xy_typeList;
            if (res.rule) {
              console.log(res.rule);
              this.text=res.rule.ruleName+'更新'
                this.formData = res.rule;
                // this.formData.status = res.rule.status;
                // this.formData.ruleStatus = res.rule.ruleStatus;
                // this.formData.startAge = res.rule.startAge;
                // this.formData.endAge = res.rule.endAge;
                // this.formData.startQuota = res.rule.startQuota;
                // this.formData.endQuota = res.rule.endQuota;
                // this.formData.overdue6Min = res.rule.overdue6Min;
                // this.formData.overdue6Max = res.rule.overdue6Max;
                // this.formData.overdue3Max = res.rule.overdue3Max;
                // this.formData.overdue3Min = res.rule.overdue3Min;
                // this.formData.loanScoreMin = res.rule.loanScoreMin;
                // this.formData.loanScoreMax = res.rule.loanScoreMax;
                // this.formData.overdue6M0Min = res.rule.overdue6M0Min;
                // this.formData.matchingPriceSort = res.rule.matchingPriceSort;
                // this.formData.repaymentRateMin = res.rule.repaymentRateMin;
                // this.formData.b22170052Min = res.rule.b22170052Min;
                // this.formData.b22170045Min = res.rule.b22170045Min;
            }
            if (res.ruleBasics) {
                this.baseForm = {
                    ...res.ruleBasics,
                };
                if (res.ruleBasics.district[0] != 1) {
                    let arr = [];
                    res.ruleBasics.district.forEach((item) => {
                        this.cityList.forEach((i) => {
                            if (i.children) {
                                i.children.forEach((citem) => {
                                    if (citem.value == item) {
                                        arr = [...arr, [i.value, item]];
                                    }
                                });
                            }
                        });
                    });

                    this.baseForm.district = arr;
                } else {
                    this.baseForm.district = [[1]];
                }
            }
            let obj = {};
            let ids = Object.keys(res.rule);
            let newIds = ids.filter((item) => item.includes("Ids"));
            newIds.forEach((item) => {
                obj[item] = res.rule[item];
            });
            for (let key in obj) {
                if (obj[key]) {
                    if (typeof obj[key] === 'string') {
                        obj[key].split(",").forEach((i) => {
                            this.checkList.push(key + "-" + i);
                        });
                    }
                    else if (Array.isArray(obj[key])) {
                        obj[key].forEach((i) => {
                            this.checkList.push(key + "-" + i);
                        });
                    }
                }
            }
        },

        /**
         *  @param { Function } 选择城市
         */
        disableEare(e) {
            this.baseForm.district = e;
        },

        /**
         *  @param { Function } handleNext 下一步
         */
        handleNext() {
            this.$refs.baseForm.validate((valid) => {
                if (valid) {
                    this.baseForm.district = this.baseForm.district.map((item) => {
                        if (item.length > 1) {
                            return item[1]
                        } else {
                            return item[0]
                        }
                    })
                    this.active = 1
                }
            })
        },
         onInputPrice(val) {
            // 只保留整数部分，并去除小数点
            let strVal = String(val).replace(/\./g, '');
            let intVal = parseInt(strVal, 10);
            if (isNaN(intVal)) intVal = '';
            this.formData.matchingPriceSort = intVal;
        },

        /**
         *  @param { Function } previousStep 上一步
         */
        previousStep() {
            this.active = 0
            let arr = []
            if (this.baseForm.district[0] == 1) {
                arr = [[1]]
            }
            this.baseForm.district.forEach((item) => {
                this.cityList.forEach((i) => {
                    if (i.children) {
                        i.children.forEach((citem) => {
                            if (citem.value == item) {
                                arr = [...arr, [i.value, item]]
                            }
                        })
                    }
                })
            })
            this.baseForm.district = JSON.parse(JSON.stringify(arr));
        },

        /**
         *  @param { Function } handleSubmit 提交投放规则
         */
        //
        handleSubmit() {
            this.$confirm("若有产品引用该规格，则同步修改", "温馨提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    let arr = {}
                    this.checkList.forEach((item) => {
                        item = item.split("-")
                        if (arr[item[0]]) {
                            arr[item[0]] = [...arr[item[0]], item[1]]
                        } else {
                            arr[item[0]] = [];
                            arr[item[0]] = [item[1]]
                        }
                    })
                    const data = {
                        id: this.id,
                        cityStatus: this.baseForm.cityStatus,
                        district: this.baseForm.district,
                        ruleName: this.formData.ruleName,
                        text: this.formData.text,
                        endAge: this.formData.endAge,
                        startAge: this.formData.startAge,
                        startQuota: this.formData.startQuota,
                        endQuota: this.formData.endQuota,
                        sesameIds: arr.sesameIds?arr.sesameIds:null,
                        overdueIds: arr.overdueIds?arr.overdueIds:null,
                        vocationIds: arr.vocationIds?arr.vocationIds:null,
                        houseIds: arr.houseIds?arr.houseIds:null,
                        vehicleIds: arr.vehicleIds?arr.vehicleIds:null,
                        insureIds: arr.insureIds?arr.insureIds:null,
                        providentIds: arr.providentIds?arr.providentIds:null,
                        socialIds: arr.socialIds?arr.socialIds:null,
                        businessIds: arr.businessIds?arr.businessIds:null,
                        ruleStatus: this.formData.ruleStatus,
                        matchingPriceSort: this.formData.matchingPriceSort,
                        status: this.formData.status,
                    }
                    // console.log(data);

                    updateQwProductRule(data)
                        .then((res) => {
                            console.log('成功', res);

                            if (res.code == 200) {
                                this.$message.success("添加成功");
                                this.handleCloseDrawer()
                                this.$emit('update')
                            }
                        }
                        )
                })
                .catch((err) => { });
        },

        /**
         *  @param { Function } handleCloseDrawer 关闭侧边栏
         */
        handleCloseDrawer() {
            this.$emit('close', false)
            this.$refs.baseForm.resetFields()
            this.$refs.form.resetFields()
            this.checkList = []
        }
    }
}
</script>

<style lang="scss" scoped>
.container-release {
    padding: 16px 24px;
    height: 100%;

    .header {
        padding-right: 32px;

        .menu {
            height: 36px;
            flex: 1;
            background: #E7E7E7;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            cursor: pointer;

            &:nth-of-type(1) {
                clip-path: polygon(0 0, 98% 0%, 100% 50%, 98% 100%, 0% 100%);
            }

            &:nth-of-type(2) {
                clip-path: polygon(0 0, 98% 0%, 100% 50%, 98% 100%, 0% 100%, 2% 50%, 0% 0%);
            }
        }

        .active {
            background: #FF8F1F;
            color: #fff;
        }
    }

    .wrap {
        height: calc(100% - 68px);
        padding: 0 0 24px;
        overflow-y: scroll;
        overflow-x: hidden;

        &::-webkit-scrollbar {
            width: 0;
        }

        &::scrollbar {
            width: 0;
        }

        .tap {
            padding: 0 10px;
        }

        .tags {
            padding: 24px 0 16px 10px;
            position: relative;
            font-size: 18px;
            text-align: left;

            &::before {
                content: "";
                width: 1px;
                height: 16px;
                border: 2px solid #e37318;
                position: absolute;
                left: 0;
                top: calc(50% - 5px);
            }
        }
    }
}

::v-deep .el-cascader {
    width: 100%;
}

::v-deep .el-form--label-top .el-form-item__label {
    padding-bottom: 0;
}

::v-deep .el-form-item__content {
    line-height: 24px;
}
</style>
