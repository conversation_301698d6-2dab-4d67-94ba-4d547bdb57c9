<template>
  <div class="app-container">
    <el-form :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dataRange"
          :clearable="false"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道">
        <el-select
          multiple
          collapse-tags
          v-model="channelIds"
          placeholder="请选择渠道"
          clearable
        >
          <el-option
            v-for="(item, index) in channelIdList"
            :key="index"
            :label="item.id"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <div class="content-box">
      <div class="chart">
        <div class="chart-title">今日返点统计</div>
        <div class="chart-box" ref="echartUv"></div>
      </div>
      <el-table border :data="dataList" max-height="700">
        <el-table-column label="渠道ID" prop="channelId" align="center" />
        <el-table-column label="返点金额" prop="amount" align="center" />
        <el-table-column label="返点原因 " align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <div>
              <span v-if="row.placeOtherRate > 0">
                {{ `异地(${row.placeOtherRate * 100}%),` }}
              </span>
              <span v-if="row.onHookRate > 0">
                {{ `挂机(${row.onHookRate * 100}%),` }}
              </span>
              <span v-if="row.emptyRate > 0">
                {{ `空号(${row.emptyRate * 100}%),` }}
              </span>
              <span v-if="row.otherReasonRate > 0">
                {{ `其他原因(${row.otherReasonRate * 100}%)` }}
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import {
  getRebateStatistics,
  getRebateChannelList,
  getRebateStatisticsList,
} from "@/api/crmReport";
import { getChannelList } from "@/api/productManage/product";
import elementResizeDetectorMaker from "element-resize-detector";
let userName = ["马春梅", "方敏"];
export default {
  data() {
    return {
      dataRange: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      dataList: [],
      userList: [],
      channelIdList: [],
      channelIds: [],
      isPhone: false,
      queryParams: {
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
        // userId: ""
        // channelId: "",
      },
    };
  },
  methods: {
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    //搜索
    handleQuery() {
      if (this.dataRange !== null) {
        this.queryParams.startTime = this.dataRange[0];
        this.queryParams.endTime = this.dataRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    init() {
      getRebateStatisticsList(this.queryParams).then((res) => {
        this.dataList = res.data;
      });
      getRebateChannelList().then((res) => {
        // this.channelIds = res.data.split(",").map((item) => item * 1);
        this.queryParams.channelId = res.data;
        if (res.data) {
          this.channelIds = res.data.split(",").map((item) => item * 1);
        }
        // getRebateStatistics(this.queryParams).then((res) => {

        // });
        this.renderChart();
      });
    },

    getList() {
      if (this.channelIds.length < 2)
        return this.$message.error("最少选择两个");
      this.queryParams.channelId = this.channelIds.join(",");
      this.renderChart();
      getRebateStatisticsList(this.queryParams).then((res) => {
        this.dataList = res.data;
      });
    },
    renderChart() {
      getRebateStatistics(this.queryParams).then((res) => {
        let xData = res.data.map((item) => item.channelId);
        let yData = res.data.map((item) => item.amount);
        let option = {
          backgroundColor: "#fff",
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "none",
            },
            formatter: function (arg) {
              return (
                "渠道ID:" +
                arg[0].name +
                "<br>" +
                arg[0].marker +
                "金额:" +
                arg[0].data
              );
            },
          },
          title: {
            show: true,
            text: "单位(元)",
          },

          grid: {
            left: this.isPhone ? "5%" : "0%",
            containLabel: true, // 这个啥？看下面
          },
          xAxis: {
            type: "category",
            data: xData,
            name: "渠道ID",
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              interval: "0",
              textStyle: {
                color: "#777",
                align: "center",
                whiteSpace: "wrap",
                fontSize: 13,
              },
            },
          },
          yAxis: {
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#777",
                fontSize: 13,
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#F1F3F5",
                type: "solid",
              },
            },
          },

          series: [
            {
              name: "金额",
              type: "line",
              data: yData,
              smooth: true,
              areaStyle: {
                normal: {
                  color: "#fce2be",
                },
              },
              itemStyle: {
                normal: {
                  color: "#f6a944",
                  lineStyle: {
                    color: "#f6a944",
                  },
                },
              },
            },
          ],
        };
        var echartUv = echarts.init(this.$refs.echartUv);
        echartUv.setOption(option);
        let erd = elementResizeDetectorMaker();
        erd.listenTo(this.$refs.echartUv, (element) => {
          this.$nextTick(() => {
            echartUv.resize();
          });
        });
      });
    },
  },
  mounted() {
    this.isPhone = document.body.clientWidth < 750;
    getChannelList().then((res) => {
      this.channelIdList = res.data;
    });
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.content-box {
  display: flex;

  .chart {
    width: 70%;
    margin-right: 20px;
    min-height: clamp(400px, calc(100vh - 200px), calc(100vh - 200px));

    display: flex;
    flex-direction: column;
    padding: 0 30px 10px;
    box-sizing: border-box;

    .chart-title {
      font-size: 30px;
      color: #333;
      font-weight: bold;
    }

    .chart-box {
      margin-top: 40px;
      flex: 1;
    }
  }
}

@media only screen and (max-width: 1400px) {
  .content-box {
    display: flex;
    flex-direction: column;

    .chart {
      width: 100%;
      margin-bottom: 20px;

      .chart-box {
        width: 100%;
      }
    }
  }
}

@media only screen and (max-width: 750px) {
  .content-box {
    display: flex;

    .chart {
      padding: 0;

      .chart-title {
        font-size: 18px;
        color: #333;
        font-weight: bold;
      }
    }
  }
}
</style>
