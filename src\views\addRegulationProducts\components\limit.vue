<template>
  <el-drawer :title="title" :visible.sync="show" append-to-body :size="width" @close="handleCloseDrawer">
    <main class="container-release">
      <el-form :rules="baseRule" ref="baseForm" size="medium" :model="baseForm">
        <el-row :gutter="16">
          <el-col :xl="12" :lg="12" :md="24" :xs="24">
            <el-form-item label="渠道筛选方式:" prop="channelStatus">
              <el-select clearable v-model="baseForm.channelStatus" placeholder="请选择渠道筛选方式" style="width: 100%;">
                <el-option value="0" label="全部"> </el-option>
                <el-option value="1" label="仅展示配置渠道"> </el-option>
                <el-option value="2" label="不展示配置渠道"> </el-option>

              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xl="12" :lg="12" :md="24" :xs="24"
            v-if="baseForm.channelStatus == '1' || baseForm.channelStatus == '2'">
            <el-form-item label="渠道" prop="channelIds">
              <el-select clearable filterable v-model="baseForm.channelIds" multiple placeholder="请选择渠道筛选方式" style="width: 100%;">
                <el-option v-for="item in channelList" :key="item.id" :value="item.id" :label="`ID:${item.id}--`+item.channelName">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <footer class="justify-content-c">
        <template>
          <el-button type="default" @click="handleCloseDrawer">取 消</el-button>
          <el-button type="primary" @click="handleSumit">确认</el-button>
        </template>
      </footer>
    </main>
  </el-drawer>
</template>

<script>
import {
  getChannelList,
  updateBasicsChannel,
  getProductRule
} from "@/api/productManage/product"

export default {
  name: 'release',
  data() {
    return ({
      show: false,
      title: '投放渠道',
      channelList: [],
      baseForm: {
        channelIds: [],
        channelStatus: ""
      },
      baseRule: {
        channelStatus: [
          { required: true, message: "请选择渠道筛选方式", trigger: "change" },
        ],
        channelIds: [
          { required: true, message: "请选择渠道", trigger: "change" },
        ],
      },

    })
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '70%'
    },
    id: {
      type: [Number, String],
      default: ''
    }
  },
  watch: {
    value: {
      handler() {
        this.show = this.value
        if (this.show && this.id) {
          this.init()
        }
      },
      deep: true
    },
  },

  created() {

  },
  methods: {
    /**
     *  @param { Function } init 初始化
     */
    init() {
      this.baseForm.productId = this.id
      getChannelList().then((res) => {
        this.channelList = res.data;
      });
      getProductRule(this.id).then(res => {

        this.baseForm.channelIds = res.ruleBasics.channelIds || []
        this.baseForm.channelStatus = res.ruleBasics.channelStatus || ""
      })
    },
    handleSumit() {
      this.$refs.baseForm.validate((valid) => {
        if (valid) {
          if (this.baseForm.channelStatus == '0') this.baseForm.channelIds = []
          updateBasicsChannel(this.baseForm).then(res => {
            this.handleCloseDrawer()
            this.$emit('update')
            this.$message.success("操作成功")
          })
        }
      })
    },



    /**
     *  @param { Function } handleCloseDrawer 关闭侧边栏
     */
    handleCloseDrawer() {
      this.$emit('close', false)
      this.$refs.baseForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.container-release {
  padding: 16px 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .wrap {
    height: calc(100% - 68px);
    padding: 0 0 24px;
    overflow-y: scroll;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 0;
    }

    &::scrollbar {
      width: 0;
    }

    .tap {
      padding: 0 10px;
    }

    .tags {
      padding: 24px 0 16px 10px;
      position: relative;
      font-size: 18px;
      text-align: left;

      &::before {
        content: "";
        width: 1px;
        height: 16px;
        border: 2px solid #e37318;
        position: absolute;
        left: 0;
        top: calc(50% - 5px);
      }
    }
  }
}

::v-deep .el-cascader {
  width: 100%;
}

::v-deep .el-form--label-top .el-form-item__label {
  padding-bottom: 0;
}

::v-deep .el-form-item__content {
  line-height: 24px;
}
</style>
