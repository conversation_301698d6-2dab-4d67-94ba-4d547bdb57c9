<template>
  <!-- 留言记录抽屉 -->
  <el-drawer
    title="留言记录"
    :visible="visible"
    direction="rtl"
    size="45%"
    :before-close="handleDrawerClose"
    @close="handleClose"
  >
    <div v-if="currentMessage" class="drawer-content">
      <!-- 消息头部 -->
      <MessageHeader :message-data="currentMessage" />

      <!-- 聊天列表 -->
      <ChatList
        ref="chatList"
        :chat-list="chatList"
        :chat-total="chatTotal"
        :page-size="chatQueryParams.size"
        :is-loading-more="isLoadingMore"
        :has-more="hasMoreHistory()"
        :avatar-size="AVATAR_SIZE"
        :scroll-threshold="SCROLL_THRESHOLD"
        @scroll-to-top="loadMoreHistoryAuto"
      />

      <!-- 回复输入 -->
      <ReplyInput
        ref="replyInput"
        :loading="replyLoading"
        @send="handleReply"
      />
    </div>
  </el-drawer>
</template>

<script>
import { getPlatformMessageBoardChat, addPlatformMessageBoardChat } from '@/api/platformMessageBoard'
import MessageHeader from './MessageHeader.vue'
import ChatList from './ChatList.vue'
import ReplyInput from './ReplyInput.vue'

export default {
  name: 'MessageRecordDrawer',
  components: {
    MessageHeader,
    ChatList,
    ReplyInput
  },
  props: {},
  data() {
    return {
      // 抽屉显示状态
      visible: false,
      // 当前留言信息
      currentMessage: null,
      // 留言记录列表
      chatList: [],
      // 留言记录总数
      chatTotal: 0,
      // 留言记录查询参数
      chatQueryParams: {
        page: 1,
        size: 100
      },
      // 回复提交loading
      replyLoading: false,
      // 是否正在加载更多历史消息
      isLoadingMore: false,
      // 自动获取新消息相关
      autoRefreshTimer: null,
      autoRefreshInterval: 5000, // 5秒间隔
      lastMessageId: null, // 记录最后一条消息的ID
      // 常量配置
      SCROLL_THRESHOLD: 50,
      AVATAR_SIZE: 24
    }
  },

  methods: {
    /** 打开抽屉 */
    open(messageData) {
      this.currentMessage = messageData
      this.visible = true
      this.initDrawer()
    },
    /** 关闭抽屉 */
    close() {
      this.visible = false
      this.currentMessage = null
      this.stopAutoRefresh()
    },
    /** 初始化抽屉 */
    initDrawer() {
      this.chatQueryParams.page = 1
      this.lastMessageId = null
      // 延迟加载聊天记录，确保抽屉已经打开
      this.$nextTick(() => {
        this.getChatList(this.currentMessage.id)
        this.focusOnReply()
        this.startAutoRefresh()
      })
    },
    /** 获取留言记录列表 */
    getChatList(boardId) {
      const params = {
        page: this.chatQueryParams.page, // 后端从1开始
        size: this.chatQueryParams.size
      }
      getPlatformMessageBoardChat(boardId, params).then(response => {
        if (response.success && response.data) {
          // 后端返回的数据是按时间倒序的（最新在前），需要反转让最新消息在底部
          this.chatList = (response.data.records || []).reverse()
          this.chatTotal = response.data.total || 0
          // 更新最后一条消息ID
          this.updateLastMessageId()
          // 滚动到底部
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        } else {
          this.resetChatData()
        }
      }).catch(() => {
        this.resetChatData()
      })
    },
    /** 自动加载更多历史消息（滚动触发） */
    loadMoreHistoryAuto() {
      if (this.isLoadingMore) return

      this.isLoadingMore = true
      this.chatQueryParams.page += 1
      const params = {
        page: this.chatQueryParams.page, // 后端从1开始
        size: this.chatQueryParams.size
      }

      getPlatformMessageBoardChat(this.currentMessage.id, params).then(response => {
        if (response.success && response.data) {
          // 后端返回的是按时间倒序的历史消息，需要反转后插入到当前列表前面
          const newMessages = (response.data.records || []).reverse()
          this.chatList = [...newMessages, ...this.chatList]

          // 保持滚动位置，避免跳动
          this.$nextTick(() => {
            this.$refs.chatList.maintainScrollPosition(() => {})
          })
        }
        this.isLoadingMore = false
      }).catch(() => {
        // 加载失败时回退页码
        this.chatQueryParams.page -= 1
        this.isLoadingMore = false
        this.$message.error("加载历史消息失败")
      })
    },
    /** 处理回复 */
    handleReply(content) {
      this.replyLoading = true
      addPlatformMessageBoardChat(this.currentMessage.id, content).then(response => {
        if (response.success) {
          this.$message.success("回复成功")
          this.$refs.replyInput.clearReply()
          // 立即重新加载消息列表显示新发送的消息
          this.resetAndReload()
          // 重启自动刷新
          this.restartAutoRefresh()
        } else {
          this.$message.error(response.msg || "回复失败")
        }
        this.replyLoading = false
      }).catch(() => {
        this.$message.error("回复失败")
        this.replyLoading = false
      })
    },
    /** 检查是否还有更多历史消息 */
    hasMoreHistory() {
      return this.chatQueryParams.page * this.chatQueryParams.size < this.chatTotal
    },
    /** 重置聊天数据 */
    resetChatData() {
      this.chatList = []
      this.chatTotal = 0
    },
    /** 重置到第一页并重新加载 */
    resetAndReload() {
      this.chatQueryParams.page = 1
      this.getChatList(this.currentMessage.id)
      this.focusOnReply()
    },
    /** 滚动到聊天容器底部 */
    scrollToBottom() {
      this.$refs.chatList && this.$refs.chatList.scrollToBottom()
    },
    /** 聚焦到回复输入框 */
    focusOnReply() {
      this.$refs.replyInput && this.$refs.replyInput.focus()
    },
    /** 开始自动刷新 */
    startAutoRefresh() {
      this.stopAutoRefresh() // 确保没有重复的定时器
      this.autoRefreshTimer = setInterval(() => {
        this.checkForNewMessages()
      }, this.autoRefreshInterval)
    },
    /** 停止自动刷新 */
    stopAutoRefresh() {
      if (this.autoRefreshTimer) {
        clearInterval(this.autoRefreshTimer)
        this.autoRefreshTimer = null
      }
    },
    /** 重启自动刷新 */
    restartAutoRefresh() {
      this.stopAutoRefresh()
      this.startAutoRefresh()
    },
    /** 检查新消息 */
    checkForNewMessages() {
      if (!this.currentMessage || !this.currentMessage.id) return
      
      // 获取第一页最新数据
      const params = {
        page: 1,
        size: this.chatQueryParams.size
      }
      
      getPlatformMessageBoardChat(this.currentMessage.id, params).then(response => {
        if (response.success && response.data) {
          const newRecords = (response.data.records || []).reverse()
          if (newRecords.length > 0) {
            this.insertNewMessages(newRecords)
          }
        }
      }).catch(() => {
        // 静默处理错误，不影响用户体验
      })
    },
    /** 插入新消息 */
    insertNewMessages(newRecords) {
      if (!newRecords || newRecords.length == 0) return
      
      const currentMessageIds = new Set(this.chatList.map(item => item.id))
      const newMessages = newRecords.filter(msg => !currentMessageIds.has(msg.id))
      
      if (newMessages.length > 0) {
        // 按时间排序，确保消息顺序正确
        newMessages.sort((a, b) => new Date(a.created) - new Date(b.created))
        
        // 插入新消息到正确位置
        newMessages.forEach(newMsg => {
          this.insertMessageInOrder(newMsg)
        })
        
        // 更新最后一条消息ID
        this.updateLastMessageId()
        
        // 滚动到底部显示新消息
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },
    /** 按时间顺序插入消息 */
    insertMessageInOrder(newMessage) {
      const newMsgTime = new Date(newMessage.created)
      let insertIndex = this.chatList.length
      
      // 从后往前找到正确的插入位置
      for (let i = this.chatList.length - 1; i >= 0; i--) {
        const currentMsgTime = new Date(this.chatList[i].created)
        if (newMsgTime >= currentMsgTime) {
          insertIndex = i + 1
          break
        }
        insertIndex = i
      }
      
      this.chatList.splice(insertIndex, 0, newMessage)
    },
    /** 更新最后一条消息ID */
    updateLastMessageId() {
      if (this.chatList.length > 0) {
        // 找到时间最新的消息
        const latestMessage = this.chatList.reduce((latest, current) => {
          return new Date(current.created) > new Date(latest.created) ? current : latest
        })
        this.lastMessageId = latestMessage.id
      }
    },
    /** 抽屉关闭前的回调 */
    handleDrawerClose(done) {
      // 清空回复内容
      this.$refs.replyInput && this.$refs.replyInput.clearReply()
      this.$refs.replyInput && this.$refs.replyInput.clearValidate()
      // 停止自动刷新
      this.stopAutoRefresh()
      done()
    },
    /** 抽屉关闭事件 */
    handleClose() {
      this.close()
    }
  },
  beforeDestroy() {
    // 组件销毁前清理定时器
    this.stopAutoRefresh()
  }
}
</script>

<style scoped>
.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>