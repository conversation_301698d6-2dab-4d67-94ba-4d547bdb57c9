<template>
  <div class="app-container">
    <el-table border :data="list">
      <el-table-column
        label="退款流水号"
        prop="refundSerialNo"
        align="center"
      />
      <el-table-column label="订单号" prop="orderNo" align="center" />
      <el-table-column
        label="退款时间"
        prop="refundSuccessTime"
        align="center"
      />
      <el-table-column label="退款金额" prop="refundAmount" align="center" />
      <el-table-column label="银行返回信息" prop="tradeMsg" align="center" />
      <el-table-column label="交易手续费" prop="tradeCharges" align="center" />
      <el-table-column label="银行流水号" prop="bankSeqNo" align="center" />
      <el-table-column label="退款创建时间" prop="createTime" align="center" />
      <el-table-column label="退款进度" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ statusType[row.status] }}
          </div>
        </template></el-table-column
      >
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              type="primary"
              size="mini"
              @click="getCheck(row.refundSerialNo)"
              >查询退款进度</el-button
            >
          </div>
        </template></el-table-column
      ></el-table
    >

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

  </div>
</template>

<script>
import { getpartyRefound, getQueryRefundProgess } from "@/api/partyManage";
export default {
  data() {
    return {
      list: [],
      total: 0,
      statusType: {
        0: "退款中",
        1: "退款成功",
        2: "订单交易成功",
        3: "订单未支付",
        4: "订单支付中",
        5: "订单已撤回",
        6: "退款失败",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    getCheck(e) {
      getQueryRefundProgess({ refundSerialNo: e }).then((res) => {
        if (res.code == 200) {
          this.getList();
        }
      });
    },
    getList() {
      getpartyRefound(this.queryParams).then((res) => {
        this.list = res.rows;
        this.total = res.total;
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
