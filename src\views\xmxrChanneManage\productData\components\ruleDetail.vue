<template>
    <el-drawer
        :title="title"
        :visible.sync="show"
        append-to-body
        :size="width"
        @close="handleCloseDrawer"
    >
        <main class="container-release">
            <section class="wrap">
                <div class="tags">基本筛选</div>
                <el-descriptions :column="3" border>
                    <el-descriptions-item label="年龄范围">
                        {{ formData.startAge || '-' }} 至 {{ formData.endAge || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="需求额度范围">
                        {{ formData.startQuota || '-' }} 至 {{ formData.endQuota || '-' }}
                    </el-descriptions-item>
                    <template v-for="(item, index) in basic_typeList">
                        <el-descriptions-item :label="item.dictName" :key="item.dictId">
                            {{ getSelectedItems(item).map(item => item.dictLabel).join('、') || '-' }}
                        </el-descriptions-item>
                    </template>
                </el-descriptions>

                <div class="tags">资质筛选</div>
                <el-descriptions :column="3" border>
                    <template v-for="(item, index) in assets_typeList">
                        <el-descriptions-item :label="item.dictName" :key="item.dictId">
                            {{ getSelectedItems(item).map(item => item.dictLabel).join('、') || '-' }}
                        </el-descriptions-item>
                    </template>
                    <el-descriptions-item label="条件选择">
                        {{ formData.ruleStatus == '0' ? '选中的满足任意一条即可' : '选中的都需要满足' }}
                    </el-descriptions-item>
                </el-descriptions>

                <div class="tags">价格匹配</div>
                <el-descriptions :column="3" border>
                    <el-descriptions-item label="价格">
                        {{ formData.matchingPriceSort || '-' }} 元
                    </el-descriptions-item>
                </el-descriptions>

                <div class="tags">状态</div>
                <el-descriptions :column="1" border>
                    <el-descriptions-item label="状态">
                        {{ formData.status == '0' ? '启用' : '停用' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="推广链接" v-if="formData.promotionallinks">
                        {{ formData.promotionallinks }}
                    </el-descriptions-item>
                </el-descriptions>
            </section>
        </main>
    </el-drawer>
</template>

<script>
import { getProductRuleDetail } from "@/api/productManage/product"

export default {
    name: 'release',
    data () {
        return ({
            show: false,
            title: '投放规则',
            basic_typeList: [],
            assets_typeList: [],
            formData: {
                status: "0",
                ruleStatus: "0",
                startAge: "",
                endAge: "",
                startQuota: "",
                endQuota: "",
                overdue6Min: "",
                overdue6Max: "",
                overdue3Min: "",
                overdue3Max: "",
                productId: "",
                loanScoreMin: "",
                loanScoreMax: "",
                overdue6M0Min: "",
                matchingPriceSort: "",
                repaymentRateMin: "",
                b22170052Min: "",
                b22170045Min: "",
            },
        })
    },
    props: {
        value: {
            type: Boolean,
            default: false
        },
        width: {
            type: String,
            default: '50%'
        },
        id: {
            type: [Number, String],
            default: ''
        },
        platformType: {
            type: [Number, String],
            default: ''
        }
    },
    watch: {
        value: {
            handler() {
                this.show = this.value
                if (this.show && this.id) {
                    this.init()
                }
            },
            deep: true
        },
    },
    methods: {
        init () {
            this.formData.productId = this.id
            const params = {
                productId: this.id,
                platformType: this.platformType
            }

            getProductRuleDetail(params).then((res) => {
                if (res.code == 200) {
                    this.basic_typeList = res.data.basic_typeList;
                    this.assets_typeList = res.data.assets_typeList;
                    this.title = `${res.data.productName}投放规则`;
                    if (res.data.rule) {
                        this.formData = {
                            ...this.formData,
                            ...res.data.rule,
                            promotionallinks: res.data.promotionallinks || ''
                        }
                    }
                }
            })
        },

        getSelectedItems(item) {
            const idField = item.paramName.split('_')[item.paramName.split('_').length - 1] + 'Ids'
            const selectedIds = this.formData[idField]
            if (!selectedIds) return []
            
            return item.dataRules.filter(rule => 
                selectedIds.split(',').includes(rule.dictCode.toString())
            )
        },

        handleCloseDrawer () {
            this.$emit('close', false)
        }
    }
}
</script>

<style lang="scss" scoped>
.container-release {
    padding: 16px 24px;
    height: 100%;
    .wrap {
        height: 100%;
        padding: 0 0 24px;
        overflow-y: scroll;
        overflow-x: hidden;
        &::-webkit-scrollbar{
            width: 0;
        }
        &::scrollbar {
            width: 0;
        }
        .tags {
            padding: 24px 0 16px 10px;
            position: relative;
            font-size: 18px;
            text-align: left;
            &::before {
                content: "";
                width: 1px;
                height: 16px;
                border: 2px solid #3371bc;
                position: absolute;
                left: 0;
                top: calc(50% - 5px);
            }
        }
    }
}

::v-deep .el-descriptions {
    margin-bottom: 20px;
    .el-descriptions-item__label {
        width: 120px;
        background-color: #fafafa;
    }
}
</style>