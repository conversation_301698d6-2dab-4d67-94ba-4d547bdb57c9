<template>
  <div class="app-container">
    <div class="party">
      商户ID：<span class="red">{{ partyFirst.partyFirstId }}</span>
      商户名称：<span class="red">{{ partyFirst.name }}</span>
      商户可用余额：<span class="red">{{ partyFirst.availableAmount }}</span>
    </div>

    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="消耗明细" name="1">
        <div class="title">消费明细：总消费{{ totalDetail.consumeTotal }}</div>
        <el-table v-loading="loading" :data="dataList" border>
          <el-table-column label="日期" align="center" prop="ms" />
          <el-table-column label="产品" align="center" prop="productName" />
          <el-table-column label="消耗金额" prop="cooperationCost" align="center" />
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-tab-pane>
      <el-tab-pane label="充值明细" name="2">
        <div class="title">充值明细：总充值{{ totalDetail.rechargeTotal }}</div>
        <el-table v-loading="loading" :data="dataList" border>
          <el-table-column label="日期" align="center" prop="rechargeDate" />
          <el-table-column label="收款主体" align="center" prop="payeeSubject.subjectName" />
          <el-table-column label="充值金额" prop="price" align="center" />

          <el-table-column label="凭证" prop="price" align="center">
            <template slot-scope="{ row }">
              <div>
                <!-- <el-popover>
                  <el-button slot="reference" type="text">查看凭证</el-button>
                  <img class="replayImage" :src="row.filename" alt="" />
                </el-popover> -->
                <el-image v-if="!row.isOnlinePay && row.filename" style="width: 100px; height: 100px"
                  :src="row.filename" :preview-src-list="[row.filename]">
                </el-image>
                <div v-if="row.isOnlinePay && row.payOrderNo">
                  {{ row.payOrderNo }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="price" align="center">
            <template slot-scope="{ row }">
              <div>
                <el-button type="text" v-if="row.status == 0" @click="cancalCharge(row)">撤销入账</el-button>
                <el-button type="text" v-if="row.status == 2" disabled style="color: red">已撤销</el-button>
                <el-button type="text" v-if="row.status == 1" disabled style="color: #808000">已确认</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-tab-pane>
      <el-tab-pane label="退款明细" name="3">
        <div class="title">退款明细：总退款{{ totalDetail.refundTotal }}</div>
        <el-table v-loading="loading" :data="dataList" border>
          <el-table-column label="日期" align="center" prop="refundDate" />
          <el-table-column label="收款主体" align="center" prop="subjectName" />
          <el-table-column label="退款金额" prop="price" align="center" />
          <el-table-column label="退款状态" prop="price" align="center">
            <template slot-scope="{ row }">
              <div :style="colorType[row.status]">
                {{ typeStatus[row.status] }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="当前审核人" align="center">
            <template  slot-scope="{row}">
              <div>
                {{ row.currentCheckPost }} <br> {{ row.currentCheckUsers }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="查看凭证" prop="price" align="center">
            <template slot-scope="{ row }">
              <div v-if="row.status == 1">
                <!-- <el-popover>
                  <el-button slot="reference" type="text">查看凭证</el-button>
                  <img class="replayImage" :src="row.fileName" alt="" />
                </el-popover> -->
                <el-image v-if="row.fileName" style="width: 100px; height: 100px" :src="row.fileName"
                  :preview-src-list="[row.fileName]">
                </el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="拒绝原因" prop="rejectReason" align="center" />
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-tab-pane>
      <el-tab-pane label="返点明细" name="4">
        <div class="title">返点明细：总返点{{ totalDetail.rebateTotal }}</div>
        <el-table v-loading="loading" :data="dataList" border>
          <el-table-column label="日期" align="center" prop="rebateDate" />
          <el-table-column label="推广名称" align="center" prop="productName" />
          <el-table-column label="返点金额" prop="price" align="center" />
          <el-table-column label="当前审核人" align="center">
            <template  slot-scope="{row}">
              <div>
                {{ row.postName }} <br> {{ row.userNames }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="price" align="center">
            <template slot-scope="{ row }">
              <div v-hasPermi="['loan:partya:rebate:revoke']">
                <el-button v-if="!row.revoked" :disabled="!row.active" type="text" @click="handleRevoke(row)">撤销
                </el-button>
                <el-button v-if="row.revoked" type="text">已撤销</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-tab-pane>
      <el-tab-pane label="保证金明细" name="5">
        <div class="title handle">
          保证金明细:余额{{ totalDetail.depositTotal }}
          <el-button type="primary" size="mini" style="margin-left: 20px" @click="handleAdd" v-if="!isBlackFlag">操作
          </el-button>
        </div>
        <el-table v-loading="loading" :data="dataList" border>
          <el-table-column label="日期" align="center" prop="date" />
          <el-table-column label="金额" align="center" prop="price" />
          <el-table-column label="类型" align="center" prop="type" />
          <el-table-column label="状态" align="center">
            <template  slot-scope="{row}">
              <div :style="colorType[row.status]">
                {{ bondJson[row.status] }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="当前审核人" align="center">
            <template  slot-scope="{row}">
              <div>
                {{ row.currentCheckPost }} <br> {{ row.currentCheckUsers }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作人" align="center" prop="username" />
          <el-table-column label="原因" align="center" prop="reason" />
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-tab-pane>
    </el-tabs>
    <el-dialog :visible.sync="avisible" @close="cancel" width="600px" append-to-body center title="保证金操作"
      :close-on-click-modal="false">
      <el-form ref="formData" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="金额" prop="price">
          <el-input maxlength="6" v-model="formData.price" placeholder="请输入金额"
            oninput="value=value.replace(/[^0-9.]/g,'')" />
        </el-form-item>
        <el-form-item label="操作类型" prop="type">
          <el-select clearable v-model="formData.type" @change="hanldeChangeType" placeholder="请选择操作类型" size="small"
            style="width: 100%">
            <el-option label="缴纳" :value="1"> </el-option>
            <el-option label="扣除" :value="2"> </el-option>
            <!-- <el-option label="退费" :value="3"> </el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item label="入账凭证" prop="file" v-if="formData.type == 1">
          <el-upload class="avatar-uploader" action="" :show-file-list="false" :auto-upload="false"
            :on-change="changeUpImg">
            <img v-if="imageUrl" style="vertical-align: middle; max-height: 100px" :src="imageUrl" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item v-if="formData.type == 1" label="收款主体名称" prop="subjectId" :rules="{
          required: formData.type == 1,
          message: '请输入收款主体名称',
          trigger: 'change',
        }">
          <el-select v-model="formData.subjectId" placeholder="请选择收款主体名称">
            <el-option v-for="item in subjectlist" :key="item.id" :value="item.id" :label="item.subjectName">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="收款账号" v-if="formData.type == 3" prop="bankCardNo" :rules="{
          required: formData.type == 3,
          message: '请输入收款账号',
          trigger: 'blur',
        }">
          <el-input v-model="formData.bankCardNo"></el-input>
        </el-form-item>
        <el-form-item label="收款银行" v-if="formData.type == 3" prop="bankName" :rules="{
          required: formData.type == 3,
          message: '请输入收款银行',
          trigger: 'blur',
        }">
          <el-input v-model="formData.bankName"></el-input>
        </el-form-item> -->

        <el-form-item label="原因" prop="reason" :rules="{
          required: formData.type == 2 || formData.type == 3,
          message: '请输入备注',
          trigger: 'blur',
        }">
          <el-input v-model="formData.reason" placeholder="请输入原因" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPartyDetail,
  getProfitList,
  getRebateList,
  getRechargeList,
  getrefundList,
  getDepositAdd,
  getDepositList,
  cancaleRevoke,
  getSubjectAll,
} from "@/api/partyA";
import { entryUpdateOne } from "@/api/financial";
export default {
  data() {
    const validateMoney = (rule, value, callback) => {
      if (!value) {
        callback(new Error("金额不能为空"));
      } else if (value.indexOf(".") != -1 && value.split(".").length > 2) {
        callback(new Error("请输入正确格式的金额")); //防止输入多个小数点
      } else if (value.indexOf(".") != -1 && value.split(".")[1].length > 2) {
        callback(new Error("请输入正确的小数位数")); //小数点后两位
      } else {
        callback();
      }
    };
    return {
      avisible: false,
      loading: false,
      dataList: [],
      subjectlist: [],
      imageUrl: "",
      activeName: "1",
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      typeStatus: {
        0: "退款中",
        1: "退款成功",
        2: "退款失败",
        3: "退款失败",
        4: "退款中",
      },
      colorType: {
        0: "color:#1E90FF",
        1: "color:#008000",
        2: "color:red",
        3: "color:red",
        4: "color:#1E90FF",
      },
      bondJson: {
        0: "审核中", 1: "已审核", 2: "已驳回"
      },
      formData: {
        price: null,
        type: "",
        reason: "",
        file: "",
        subjectId: "",
      },
      bankInfo: {
        subjectName: "",
        bankCardNo: "",
        bankName: "",
      },
      isBlackFlag: false,
      rules: {
        price: [{ required: true, validator: validateMoney, trigger: "blur" }],
        type: [{ required: true, message: "请选择操作类型", trigger: "change" }],
        file: [{ required: true, message: "请上传凭证", trigger: "change" }],
      },
      totalDetail: {
        consumeTotal: "",
        rebateTotal: "",
        refundTotal: "",
        rechargeTotal: "",
      },
      partyFirst: {
        availableAmount: "",
        name: "",
        partyFirstId: "",
      },
    };
  },
  methods: {
    handleClick() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      };
      this.dataList = [];
      this.getList();
    },
    getProfitLists() {
      this.loading = true;
      getProfitList(this.$route.query.id, this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    //撤销入账
    cancalCharge(row) {
      console.log(row);
      this.$confirm("确定撤销吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          entryUpdateOne({ id: row.id, status: 2 })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.activeName == "2";
                this.getList();
              }
            })
            .catch((err) => { });
        })
        .catch((err) => { });
    },
    cancel() {
      this.avisible = false;
      this.formData = {
        price: null,
        type: "",
        reason: "",
        file: "",
        subjectId: "",
      };

      this.$refs.formData.resetFields();
    },
    //撤销返点
    handleRevoke(row) {
      this.$confirm(
        `是否确认撤销《${row.price}》${row.rebateDate}申请的返点余额${row.price}？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          cancaleRevoke({ id: row.id })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.activeName == "4";
                this.getList();
              }
            })
            .catch((err) => { });
        })
        .catch((err) => { });
    },
    //操作保证金
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.partyFirstId = this.$route.query.id * 1;
          let data = new FormData()
          for (let key in this.formData) {
            data.append(key, this.formData[key])
          }
          getDepositAdd(data).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              this.activeName == "5";
              getPartyDetail(this.$route.query.id).then((res) => {
                this.totalDetail = res.detailTotal;
                this.partyFirst = res.partyFirst;
              });
              this.getList();
              this.cancel();
            }
          });
        }
      });
    },
    //保证金类型
    hanldeChangeType(e) {
      if (e == 2) {
        this.imageUrl = ""
        this.formData.file = ""
      }
    },
    //处理上传凭证
    changeUpImg(e) {
      const isJPG =
        e.raw.type === "image/jpeg" ||
        e.raw.type === "image/jpg" ||
        e.raw.type === "image/png";
      const isLt2M = e.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG/JPEG/PNG 格式!");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
        return;
      }
      this.imageUrl = URL.createObjectURL(e.raw);
      this.formData.file = e.raw;
      this.$refs.formData.clearValidate("file")
    },
    handleAdd() {
      this.avisible = true;
      getSubjectAll().then(res => {
        this.subjectlist = res.data
      })
    },
    getList() {
      if (this.activeName == "1") {
        this.getProfitLists();
      }
      if (this.activeName == "2") {
        getRechargeList(this.$route.query.id, this.queryParams).then((res) => {
          this.dataList = res.rows;
          this.total = res.total;
          this.loading = false;
        });
      }
      if (this.activeName == "3") {
        getrefundList(this.$route.query.id, this.queryParams).then((res) => {
          this.dataList = res.rows;
          this.total = res.total;
          this.loading = false;
        });
      }
      if (this.activeName == "4") {
        getRebateList(this.$route.query.id, this.queryParams).then((res) => {
          this.dataList = res.rows;
          this.total = res.total;
          this.loading = false;
        });
      }
      if (this.activeName == "5") {
        getDepositList(this.$route.query.id, this.queryParams).then((res) => {
          this.dataList = res.rows;
          this.total = res.total;
          this.loading = false;
        });
      }
    },
  },

  mounted() {
    //获取明细
    getPartyDetail(this.$route.query.id).then((res) => {
      this.totalDetail = res.detailTotal;
      this.partyFirst = res.partyFirst;
      this.isBlackFlag = res.partyFirst.isBlackFlag
      this.bankInfo.subjectName = res.partyFirst.subjectName;
      this.bankInfo.bankCardNo = res.partyFirst.bankCardNo;
      this.bankInfo.bankName = res.partyFirst.bankName;
    });
    this.getProfitLists();
  },
};
</script>

<style lang="scss" scoped>
.title {
  padding: 20px 0px;
  font-size: 20px;
}

.handle {
  display: flex;
  align-items: center;
}

.party {
  padding-bottom: 20px;
  font-size: 16px;
  font-weight: 800;
  font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
}

.red {
  font-size: 20px;
  color: red;
}

.replayImage {
  border: 0;
  width: 80vh;
  max-height: 100vh;
}
</style>
