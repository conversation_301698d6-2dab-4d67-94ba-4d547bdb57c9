<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="提交时间">
        <el-date-picker
          size="small"
          v-model="value1"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="收款主体">
        <el-input
          size="small"
          clearable
          v-model="queryParams.subjectKeywords"
          placeholder="请输入收款主体"
          @change="handleQuery"
          @keyup.enter.native="handleQuery"
        ></el-input>
      </el-form-item>
      <el-form-item label="入账商务">
        <el-select
          v-model="queryParams.confirm"
          filterable
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="i in affairsList"
            :key="i.userId"
            :label="i.nickName"
            :value="i.userId"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" border :data="refoundList">
      <el-table-column label="申请时间" prop="refundDate" align="center" />
      <el-table-column label="商户名称" prop="partyFirstName" align="center" />
      <el-table-column
        label="商户退款前余额"
        prop="availableAmount"
        align="center"
      />
      <el-table-column
        label="商户退款前保证金余额"
        prop="depositAmount"
        align="center"
      />
      <el-table-column label="是否退保证金" prop="deposit" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ row.deposit ? "是" : "否" }}
          </div>
        </template></el-table-column
      >
      <el-table-column label="退款金额" prop="price" align="center" />
      <el-table-column label="收款主体" prop="subjectName" align="center" />
      <el-table-column label="开户行" prop="bankName" align="center" />
      <el-table-column label="收款账号" prop="bankCardNo" align="center" />
      <el-table-column label="申请人" prop="refundUser" align="center" />

      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              v-if="row.status == 0"
              icon="el-icon-check"
              type="text"
              @click="confirm(row)"
              >通过</el-button
            >
            <el-button
              v-if="row.status == 0"
              icon="el-icon-close"
              type="text"
              style="color: red"
              @click="refuse(row)"
              >拒绝</el-button
            >
            <el-button v-if="row.status == 1" type="text"> 已退款 </el-button>
            <el-button
              style="color: red"
              v-if="row.status == 2 || row.status == 3"
              type="text"
            >
              已拒绝
            </el-button>
            <el-button v-if="row.status == 4" type="text"> 已确认 </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="退款确认"
      :visible.sync="confireAvisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        label-position="left"
        :model="refoundData"
        :rules="refoundRules"
        label-width="100px"
        ref="refoundData"
      >
        <el-form-item label="退款金额" prop="">
          <el-input disabled v-model="formData.price"></el-input>
        </el-form-item>
        <el-form-item label="商户类型" prop="">
          <el-input disabled v-model="formData.type"></el-input>
        </el-form-item>
        <el-form-item label="商户名称" prop="">
          <el-input disabled v-model="formData.partyFirstName"></el-input>
        </el-form-item>
        <el-form-item label="收款主体" prop="">
          <el-input disabled v-model="formData.subjectName"></el-input>
        </el-form-item>
        <el-form-item label="开户行" prop="">
          <el-input disabled v-model="formData.bankName"></el-input>
        </el-form-item>
        <el-form-item label="收款账号" prop="">
          <el-input disabled v-model="formData.bankCardNo"></el-input>
        </el-form-item>
        <el-form-item label="我方收款主体" prop="">
          <el-input disabled v-model="formData.loanSubject"></el-input>
        </el-form-item>
        <!-- <el-form-item label="凭证上传" prop="file">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :auto-upload="false"
            :on-change="changeUpImg"
          >
            <img
              v-if="imageUrl"
              style="vertical-align: middle; max-height: 100px"
              :src="imageUrl"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="拒绝"
      :visible.sync="rejectAvisible"
      @close="rejectCancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="rejectFormData" :model="rejectFormData" :rules="rules">
        <el-form-item label="拒绝原因" prop="rejectReason">
          <el-input
            type="textarea"
            v-model="rejectFormData.rejectReason"
            placeholder="请输入留言内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="rejectSubmitForm">确 定</el-button>
        <el-button @click="rejectCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { confireRefound } from "@/api/financial";
import { getAffairsList } from "@/api/partyB";
import {
  getPartyaRefundList,
  rejectPartyaRefundOne,
  passPartyaRefundOne,
} from "@/api/partyA";
export default {
  name: "Reply",
  data() {
    var imgRule1 = (rule, value, callback) => {
      if (this.refoundData.file == "" && this.imageUrl == "") {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#F56C6C";
        callback(new Error("请上传凭证"));
      } else if (this.refoundData.file || this.imageUrl) {
        callback();
      }
    };
    return {
      confireAvisible: false,
      rejectAvisible: false,
      imageUrl: "",
      rejectFormData: {
        id: "",
        rejectReason: "",
      },
      formData: {
        type: "",
        price: "",
        partyFirstName: "",
        subjectName: "",
        bankName: "",
        bankCardNo: "",
        loanSubject: "",
      },
      refoundData: { file: "" },
      refoundList: [],
      value1: [],
      subjectList: [],
      affairsList: [],
      rules: {
        rejectReason: [
          { required: true, message: "请输入拒绝原因", trigger: "blur" },
        ],
      },
      refoundRules: {
        file: [
          {
            required: true,
            message: "请上传凭证",
            validator: imgRule1,
          },
        ],
      },
      total: 0,
      loading: false,
      id: "",

      queryParams: {
        confirm: "",
        stopTime: "",
        startTime: "",
        subjectKeywords: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    //查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.value1 != null) {
        this.queryParams.startTime = this.value1[0];
        this.queryParams.stopTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.stopTime = "";
      }
      this.getList();
    },
    // 取消
    cancel() {
      this.formData = {
        type: "",
        price: "",
        partyFirstName: "",
        subjectName: "",
        bankName: "",
        bankCardNo: "",
        filename: "",
        loanSubject:""
      };
      this.imageUrl = "";
      this.refoundData.file = "";
      this.id = "";
      this.confireAvisible = false;
      if (document.getElementsByClassName("el-upload").length > 0) {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#8c939d";
      }
      this.$refs.refoundData.resetFields();
    },
    //确认退款
    submitForm() {
      passPartyaRefundOne({ id: this.id }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.getList();
          this.cancel();
        }
      });
    },
    //拒绝
    refuse(row) {
      this.rejectAvisible = true;
      this.rejectFormData.id = row.id;
    },
    //上传凭证
    changeUpImg(e) {
      const isJPG =
        e.raw.type === "image/jpeg" ||
        e.raw.type === "image/jpg" ||
        e.raw.type === "image/png";
      const isLt2M = e.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传图片只能是 JPG/JPEG/PNG 格式!");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
        return;
      }
      this.imageUrl = URL.createObjectURL(e.raw);
      this.refoundData.file = e.raw;
      if (document.getElementsByClassName("el-form-item__error").length > 0) {
        document.getElementsByClassName(
          "el-form-item__error"
        )[0].style.display = "none";
      }
      if (document.getElementsByClassName("el-upload").length > 0) {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#8c939d";
      }
    },
    //拒绝提交
    rejectSubmitForm() {
      this.$refs.rejectFormData.validate((valid) => {
        if (valid) {
          rejectPartyaRefundOne(this.rejectFormData).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              this.getList();
              this.rejectCancel();
            }
          });
        }
      });
    },
    //拒绝取消
    rejectCancel() {
      this.rejectAvisible = false;
      this.rejectFormData = {
        id: "",
        rejectReason: "",
      };
      this.$refs.rejectFormData.resetFields();
    },
    getList() {
      this.loading = true;
      getPartyaRefundList(this.queryParams).then((res) => {
        this.refoundList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    //确认弹窗
    confirm(row) {
      this.formData = JSON.parse(JSON.stringify(row));
      this.formData.type = this.formData.type == 0 ? "公司" : "个人";
      this.confireAvisible = true;
      this.id = row.id;
    },
  },
  mounted() {
    this.getList();
    //查询商务
    getAffairsList().then((res) => {
      this.affairsList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
.priview {
  width: 300px;
  img {
    width: 85px;
    height: 50px;
    padding: 0 10px;
  }
}
.replayImage {
  border: 0;
  max-height: 100vh;
}
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
</style>
