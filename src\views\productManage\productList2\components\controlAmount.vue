<template>
  <el-drawer
    :title="drawerTitle"
    :visible.sync="show"
    append-to-body
    :size="width"
    @close="handleCloseDrawer"
  >
    <main class="control-container">
      <div class="control-container-title">控量设置</div>
      <header class="header">
        <el-form
          ref="controlform"
          :inline="true"
          :model="controlFormData"
          :rules="rules1"
          :key="controlFormData.executeDay"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="控量有效期" prop="times">
                <el-date-picker
                  value-format="yyyy-MM-dd"
                  v-model="controlFormData.times"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="checkDay"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="是否跨天" prop="executeDay">
                <el-select
                  v-model="controlFormData.executeDay"
                  clearable
                  placeholder="请选择"
                  @change="changeData"
                >
                  <el-option label="是" value="1" :disabled="isDisbale">
                  </el-option>
                  <el-option label="否" value="0"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="每日申请最大值" prop="maxApplyNumber">
                <el-input-number
                  v-model="controlFormData.maxApplyNumber"
                  :min="1"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item
            label="控量时间"
            v-if="
              controlFormData.executeDay == '0' ||
              controlFormData.executeDay == ''
            "
          >
            <el-time-picker
              value-format="HH:mm:ss"
              is-range
              v-model="controlTime"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时间范围"
            >
            </el-time-picker>
            <span class="tag">*非必填,不填默认是一天</span>
          </el-form-item>
          <template v-if="controlFormData.executeDay == '1'">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker
                value-format="HH:mm:ss"
                v-model="controlFormData.startTime"
                placeholder="任意时间点"
              >
              </el-time-picker>
            </el-form-item>
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker
                value-format="HH:mm:ss"
                v-model="controlFormData.endTime"
                placeholder="任意时间点"
              >
              </el-time-picker>
            </el-form-item>
          </template>
          <el-form-item label="不上线时间">
            <el-select
              v-model="intervalAllVal"
              placeholder="请选择"
              style="width: 500px"
              clearable
              multiple
              @clear="hanldeClearTimes"
              filterable
            >
              <el-option
                v-for="item in intervalAllTimes"
                :key="item.date"
                :label="`${item.date}(${item.weekday})`"
                :value="item.date"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="是否智能控量"
            v-if="checkPermi(['loan:productcontrol:filter'])"
          >
            <el-radio v-model="controlFormData.filterFlag" :label="true"
              >是</el-radio
            >
            <el-radio v-model="controlFormData.filterFlag" :label="false"
              >否</el-radio
            >
          </el-form-item>

          <div class="justify-content-c">
            <el-button
              style="width: 200px; margin-top: 12px"
              type="primary"
              @click="handleSubmit"
              >确 定</el-button
            >
          </div>
        </el-form>
      </header>
      <div class="control-container-title">控量信息</div>
      <el-table v-loading="loading" border :data.sync="tables">
        <el-table-column label="创建时间" prop="createTime" align="center" />
        <el-table-column label="有效期" prop="id" align="center">
          <template slot-scope="{ row }">
            <div>
              {{ row.startDate + "至" + row.endDate }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="到期后状态" prop="status" align="center">
          <template slot-scope="{ row }">
            <div>
              {{ row.expireStatus * 1 == 0 ? "在线" : "下线" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="是否跨天" prop="executeDay" align="center">
          <template slot-scope="{ row }">
            <div>
              {{ row.executeDay * 1 == 0 ? "不跨天" : "跨天" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="限量"
          prop="maxApplyNumber"
          width="100"
          align="center"
        />
        <el-table-column label="控量时间" prop="id" align="center">
          <template slot-scope="{ row }">
            <div v-if="row.startTime || row.endTime">
              {{ row.startTime + "至" + row.endTime }}
            </div>
            <div v-else>00:00:00至23:59:59</div>
          </template>
        </el-table-column>
        <el-table-column label="控量状态" prop="id" align="center">
          <template slot-scope="{ row }">
            <div>
              {{ row.status * 1 == 0 ? "有效" : "失效" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="智能控量" prop="id" align="center">
          <template slot-scope="{ row }">
            <div v-if="row.status * 1 == 0">
              <el-switch
                active-text="开启"
                inactive-text="关闭"
                v-model="row.filterFlag"
                active-color="#004DAB"
                :disabled="!checkPermi(['loan:productcontrol:filter'])"
                @change="filterFlagChange(row)"
              >
              </el-switch>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="不上线时间" prop="excludeDate" align="center">
          <template slot-scope="{ row }">
            <el-popover placement="top" width="285" trigger="click">
              <div class="times-list">
                <span
                  v-for="i in row.excludeDate.split(',')"
                  :key="i"
                  style="padding-right: 10px"
                  >{{ i }},</span
                >
              </div>

              <div class="exclude-date" slot="reference">
                {{ row.excludeDate }}
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          label="设置人"
          prop="createBy"
          align="center"
          width="100"
        />
      </el-table>
    </main>
  </el-drawer>
</template>
<script>
import {
  getControlList,
  addControlOne,
  controlFilterControlOne,
} from "@/api/productManage/product";
import { getDayAll } from "@/utils/index";
import { checkPermi } from "@/utils/permission";
export default {
  name: "controlAmount",
  data() {
    return {
      show: false,
      controlFormData: {
        endDate: "",
        endTime: "",
        maxApplyNumber: 0,
        executeDay: "0",
        startDate: "",
        startTime: "",
        times: null,
        excludeDate: "",
        filterFlag: false,
      },
      checkPermi,
      intervalAllTimes: [],
      intervalAllVal: [],
      drawerTitle: "控量设置",
      controlTime: null,
      rules1: {
        times: [
          { required: true, message: "请选择控量有效期", trigger: "blur" },
        ],
        executeDay: [
          { required: true, message: "请选择是否跨天", trigger: "blur" },
        ],
        startTime: [
          { required: true, message: "请选择控量开始时间", trigger: "blur" },
        ],
        endTime: [
          { required: true, message: "请选择控量结束时间", trigger: "blur" },
        ],
        maxApplyNumber: [
          { required: true, message: "请输入每日申请最大值", trigger: "blur" },
        ],
      },
      isDisbale: true,
      tables: [],
      loading: false,
      total: 0,
    };
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: "80%",
    },
    id: {
      type: [Number, String],
      default: "",
    },
    title: {
      value: {
        type: String,
        default: false,
      },
    },
  },
  watch: {
    value: {
      handler() {
        this.show = this.value;
        if (this.show && this.id) {
          this.init();
        }
      },
      deep: true,
    },
  },

  methods: {
    /**
     *  @param { Function } init 初始化 - 获取控量设置列表
     */
    init() {
      this.drawerTitle = `控量设置-${this.title || ""}`;
      getControlList(this.id).then((res) => {
        this.tables = res.rows;
        this.total = res.total;
      });
    },

    /**
     *  @param { Function } handleCloseDrawer 关闭弹窗
     */
    handleCloseDrawer() {
      this.$emit("close", false);
      this.cancelControlData();
    },

    /**
     * @param { Function } handleSubmit 提交设置
     */
    handleSubmit() {
      this.$refs.controlform.validate((valid) => {
        if (valid) {
          // return console.log(this.controlTime, 'controlTime')
          if (this.controlFormData.times) {
            this.controlFormData.startDate = this.controlFormData.times[0];
            this.controlFormData.endDate = this.controlFormData.times[1];
            delete this.controlFormData.times;
          }
          if (this.controlTime) {
            this.controlFormData.startTime = this.controlTime[0];
            this.controlFormData.endTime = this.controlTime[1];
          }
          if (this.intervalAllVal && this.intervalAllVal.length) {
            this.controlFormData.excludeDate = this.intervalAllVal.join(",");
          }
          this.controlFormData.productId = this.id;
          addControlOne(this.controlFormData).then((res) => {
            if (res.code == 200) {
              this.$message.success("设置成功");
              this.cancelControlData();
              this.init();
            }
          });
        }
      });
    },
    //控量时间控制
    changeData() {
      this.controlTime = null;
      this.controlFormData.startTime = "";
      this.controlFormData.endTime = "";
    },

    //取消控量
    cancelControlData() {
      this.controlFormData = {
        endDate: "",
        endTime: "",
        maxApplyNumber: 0,
        startDate: "",
        startTime: "",
        times: null,
        executeDay: "",
        excludeDate: "",
        filterFlag: false,
      };
      this.intervalAllVal = [];
      this.intervalAllTimes = [];
      this.controlTime = null;
      this.$refs.controlform.resetFields();
    },

    checkDay(e) {
      if (
        (this.controlFormData.times != null &&
          this.controlFormData.times.length > 0 &&
          this.controlFormData.times[0] == this.controlFormData.times[1]) ||
        !this.controlFormData.times
      ) {
        this.isDisbale = true;
      } else {
        this.isDisbale = false;
      }
      this.controlFormData.executeDay = "0";
      if (e) {
        this.intervalAllTimes = getDayAll(e[0], e[1]) || [];
      } else {
        this.intervalAllTimes = [];
      }
      this.intervalAllVal = [];
      this.controlFormData.excludeDate = "";
    },
    hanldeClearTimes() {
      this.intervalAllVal = [];
      this.controlFormData.excludeDate = "";
    },
    filterFlagChange(row) {
      this.$confirm("是否操作智能控量", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          controlFilterControlOne({
            filterFlag: row.filterFlag,
            id: row.id,
          }).then((res) => {
            this.$message.success("操作成功");
          });
        })
        .catch(() => {
          row.filterFlag = row.filterFlag ? false : true;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.control-container {
  padding: 0 16px;

  .control-container-title {
    padding: 24px 0 16px 10px;
    position: relative;
    text-align: left;

    &::before {
      content: "";
      width: 1px;
      height: 16px;
      border: 2px solid #e37318;
      position: absolute;
      left: 0;
      top: calc(50% - 5px);
    }
  }

  .header {
    background: #f3f3f3;
    padding: 30px;
  }

  .tag {
    font-size: 12px;
    padding-left: 4px;
    color: #f00;
  }
}

::v-deep .el-select__tags {
  max-height: 60px !important;
  overflow: auto;
}

.exclude-date {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
.times-list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  span {
    width: 33.33333%;
  }
}
</style>
