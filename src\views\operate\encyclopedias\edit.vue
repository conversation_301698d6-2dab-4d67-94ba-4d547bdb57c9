<template>
  <div class="app-container">
    <el-form label-width="100px" ref="formData" :model="formData" :rules="rules">

      <el-form-item label="文章名称" prop="title" size="small">
        <el-input placeholder="请输入文章名称" v-model="formData.title"></el-input>
      </el-form-item>
      <el-form-item label="作者名称" prop="authorName" size="small">
        <el-input placeholder="请输入作者名称" v-model="formData.authorName"></el-input>
      </el-form-item>
      <el-form-item label="发布时间" prop="releaseTime" size="small">
        <el-date-picker v-model="formData.releaseTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
          placeholder="选择日期时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="是否置顶" prop="istopping" size="small">
        <el-radio v-model="formData.istopping" :label="0">否</el-radio>
        <el-radio v-model="formData.istopping" :label="1">是</el-radio>
      </el-form-item>
      <el-form-item label="文章分类" prop="articleClassification" size="small">
        <el-radio v-model="formData.articleClassification" :label="1">小额现金贷</el-radio>
        <el-radio v-model="formData.articleClassification" :label="2">大额分期贷</el-radio>
        <el-radio v-model="formData.articleClassification" :label="3">信用贷</el-radio>
      </el-form-item>
      <!-- <el-form-item label="文章类型" prop="status" size="small">
        <el-input placeholder="请输入作者名称"></el-input>
      </el-form-item> -->
      <el-form-item label="封面图片" prop="status" size="small">
        <el-upload action="#" :auto-upload="false" list-type="picture-card" :file-list="fileLists"
          :on-change="changeImage" :on-remove="removeImage" :limit="3">
          <i class="el-icon-plus"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="文本内容" prop="status" size="small">
        <div id="editor"></div>
      </el-form-item>
      <el-form-item prop="status" size="small">
        <el-button type="primary" icon="el-icon-plus" @click="submitAdd" v-if="!$route.query.id">确认新增</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="submitDraft" v-if="!$route.query.id">存入草稿</el-button>
        <el-button type="primary" icon="el-icon-edit" @click="submitEdit" v-if="$route.query.id">确认编辑</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="submitEditDraft" v-if="$route.query.id">存入草稿</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>

<script>

import editor from "wangeditor";
import { addEncyclopediaOne, getEncyclopediaOne, uploadFile, delLoadFile, editEncyclopediaOne } from "@/api/operate/encyclopedias"
export default {
  data() {
    return {
      imgNewSrc: [],
      imgOldSrc: [],
      fileLists: [],
      formData: {
        articleClassification: "",
        authorName: "",
        files: [],
        istopping: 0,
        releaseTime: "",
        text: "",
        title: "",
        temFiles: [],
        urlList: [],
        articleStatus: ""
      },
      rules: {
        title: [{ required: true, trigger: "blur", message: "请输入文章标题" },],
        authorName: [{ required: true, trigger: "blur", message: "请输入作者名称" }],
        releaseTime: [{ required: true, trigger: "change", message: "请选择发布时间" }],
        articleClassification: [{ required: true, trigger: "change", message: "请选择文章分类" }],
      }
    }
  },
  methods: {
    changeImage(file, fileList) {
      this.formData.temFiles = fileList.filter((item) => item.raw).map(item => item.raw);
      this.formData.urlList = fileList.filter((item) => item.url && !item.raw).map(item => item.url);
      console.log(this.formData.urlList, fileList);
    },
    removeImage(file, fileList) {
      this.formData.temFiles = fileList.filter((item) => item.raw).map(item => item.raw);
      this.formData.urlList = fileList.filter((item) => item.url && !item.raw).map(item => item.url);
      console.log(this.formData.urlList, fileList);
    },
    //新增
    submitAdd() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.articleStatus = 1
          this.formData.text = this.editor.txt.html();
          addEncyclopediaOne(this.formData).then(res => {
            this.$store.dispatch("tagsView/delView", this.$route);
            this.$router.replace({ path: "/operate/wiki/encyclopedias" });
          })
        }
      })

    },
    //编辑
    submitEdit() {
      this.$refs.formData.validate((valid) => {
        if (valid) {

          this.formData.articleStatus = 1
          this.formData.text = this.editor.txt.html();
          this.formData.id = this.$route.query.id
          editEncyclopediaOne(this.formData).then(res => {
            this.$message.success('操作成功')
            this.$store.dispatch("tagsView/delView", this.$route);
            this.$router.replace({ path: "/operate/wiki/encyclopedias" });
          })
        }
      })

    },
    //编辑提交到草稿箱
    submitEditDraft() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.articleStatus = 3
          this.formData.id = this.$route.query.id
          this.formData.text = this.editor.txt.html();
          editEncyclopediaOne(this.formData).then(res => {
            this.$message.success("操作成功")
            this.$store.dispatch("tagsView/delView", this.$route);
            this.$router.replace({ path: "/operate/encyclopedias" });
          })
        }
      })

    },
    //存入草稿
    submitDraft() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.articleStatus = 3
          this.formData.text = this.editor.txt.html();
          addEncyclopediaOne(this.formData).then(res => {
            this.$message.success("操作成功")
            this.$store.dispatch("tagsView/delView", this.$route);
            this.$router.replace({ path: "/operate/encyclopedias" });
          })
        }
      })

    },
    onchange(html) {
      this.formData.text = html
      this.imgOldSrc = this.imgNewSrc
      this.imgNewSrc = this.getSrc(html)
      if ((this.imgNewSrc.length <= this.imgOldSrc.length)) {
        if (!this.imgOldSrc.length) return
        let list = this.imgOldSrc.filter((n) => {
          return this.imgNewSrc.indexOf(n) === -1
        })
        if (list.length) {
          delLoadFile({ list: list })
        }
      }


    },
    /**
     * 取出区域内所有img的src
     */
    getSrc(html) {
      var imgReg = /<img.*?(?:>|\/>)/gi
      // 匹配src属性
      var srcReg = /src=[\\"]?([^\\"]*)[\\"]?/i
      var arr = html.match(imgReg)
      let imgs = []
      if (arr) {
        for (let i = 0; i < arr.length; i++) {
          var src = arr[i].match(srcReg)[1]
          imgs.push(src)
        }
      }
      return imgs
    },

  },
  mounted() {
    if (this.$route.query.id) {
      getEncyclopediaOne({ id: this.$route.query.id }).then(res => {
        this.formData.articleClassification = res.data.articleClassification
        this.formData.authorName = res.data.authorName
        this.formData.istopping = res.data.istopping
        this.formData.releaseTime = res.data.releaseTime
        this.formData.title = res.data.title
        this.formData.text = res.data.text
        this.formData.urlList = res.data.urlList
        this.editor.txt.html(res.data.text)
        this.imgOldSrc = this.getSrc(this.formData.text || "")
        res.data.urlList.forEach(item => {
          this.fileLists.push({
            name: Math.ceil(Math.random() * 100),
            url: item
          })
        });
      })
    }


    this.editor = new editor("#editor");
    this.editor.config.height = 500;
    this.editor.config.zIndex = 10;
    this.editor.config.colors = ["#000000", "#333333", "#666666", "#999999"];
    this.editor.config.fontNames = [
      "黑体",
      "仿宋",
      "楷体",
      "标楷体",
      "华文仿宋",
      "华文楷体",
      "宋体",
      "微软雅黑",
      "Arial",
      "Tahoma",
      "Verdana",
      "Times New Roman",
      "Courier New",
    ];
    this.editor.config.uploadImgMaxSize = 10 * 1024 * 1024
    this.editor.config.customUploadImg = function (resultFiles, insertImgFn) {
      let file = resultFiles[0]
      let data = new FormData()
      data.append('file', file)
      uploadFile(data).then(res => {
        insertImgFn(res.msg)
      })
    }
    this.editor.config.onchange = html => {
      this.onchange(html)
    };

    this.editor.create();
  }
}
</script>

<style lang="scss" scoped>
</style>
