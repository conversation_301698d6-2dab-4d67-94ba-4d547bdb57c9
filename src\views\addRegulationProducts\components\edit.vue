<template>
  <el-dialog :title="title" :visible.sync="visible" :width="width" @close="handleCloseDialog"
    :close-on-click-modal="false">
    <div class="dialog-wrap">
      <el-form :model="queryParams" ref="queryForm" :rules="rules">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="queryParams.ruleName" placeholder="请输入规则名称" clearable />
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleCloseDialog">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  getloanModeListsAll,
  getAcquirePartyAall,
  addProductOne,
  getProductOne,
  editProductCompile,
  getCityAll,
  getProductApiList,
  getFlowPackageList,
  getOnlineSetType,
  setOnlineSetType, checkCooperationLink
} from '@/api/productManage/product'
import BaseCascader from "@/components/cascader";
import { hasBool } from "@/directive/permission/hasBool";
import {
  addQwProductRule,
} from "@/api/addRegulationProducts/productRule";


export default {
  name: "edit",
  data() {
    let imgRule1 = (rule, value, callback) => {
      if (this.queryParams.file == "" && this.imageUrl == "") {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#F56C6C";
        callback(new Error("请上传LOGO"));
      } else if (this.queryParams.file || this.imageUrl) {
        callback();
      }
    };
    let httpsValidator = (rule, value, callback) => {
      if (!this.queryParams.cooperationLink) {
        callback(new Error("请输入推广链接"));
      } else if (!this.queryParams.cooperationLink.includes("https")) {
        callback(new Error("推广链接必须包含https"));
      } else if (this.queryParams.cooperationLink) {
        callback();
      }
    };
    return {
      hasBool,
      onlineType: undefined,
      showUsers: ["shenxun", "yutong", "sunyang", "xupengcheng"],
      visible: false,
      isLable: false,
      title: "新建规则名称",
      imageUrl: "",
      prodApiList: [], // 所属api列表
      LoanList: [], // 助贷列表
      acquirePartyList: [], // 甲方列表
      packageList: [], // 推广类型
      queryParams: {
        ruleName: "",
      },
      businessTypeOption: [],
      rules: {
        ruleName: [{ required: true, message: "请输入规则名称", trigger: "blur" }],
      },
      // 推广链接校验结果，检测当前推广链接是否能被修改
      cooperationLinkCheckResponse: {
        // 状态码，10201: 可修改；10202: 不可修改
        code: '',
        // 错误信息，展示在推广链接输入框下方
        msg: '',
      },
      cooperationLinkChecking: false,
    };
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    isQiWei: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "add",
    },
    width: {
      type: String,
      default: "814px",
    },
    id: {
      type: [Number, String],
      default: "",
    },
  },
  watch: {
    value: {
      handler() {
        this.visible = this.value;
        if (!this.value) return;
        this.init();
      },
      deep: true,
    },
    type() {
      this.title = this.type == "edit" ? "修改规则名称" : "新建规则名称";
    },
  },
  created() { },
  methods: {
    onCooperationLinkBlur() {
      this.$refs.queryForm.validateField("cooperationLink", async (errorMessage) => {
        if (errorMessage) {
          this.cooperationLinkCheckResponse = {
            code: '',
            msg: '',
          }
          return
        }

        try {
          this.cooperationLinkChecking = true;
          const res = await checkCooperationLink({
            cooperationLink: this.queryParams.cooperationLink,
            productId: this.id,
          });

          this.cooperationLinkCheckResponse = res;
        } finally {
          this.cooperationLinkChecking = false;
        }
      })
    },

    /**
     *  @param { Function } init 初始化
     */
    init() {
      // this.getloanList();
      // this.handleGetProductApiList();
      getloanModeListsAll().then((res) => {
        this.LoanList = res.data;
        getProductApiList().then((res) => {
          this.prodApiList = res.data;
          if (this.type == "edit" || this.type == "copy") {
            this.getPartyaId();
            this.getPackageList();
            if (hasBool("productManage:product:ZYXOnlineSetType")) {
              getOnlineSetType().then((res) => {
                this.businessTypeOption = res.data;
              });
            }

            //获取产品详情
            getProductOne({ id: this.id }).then((res) => {
              if (this.type == "edit") {
                this.imageUrl = res.data.logo;
              } else {
                this.imageUrl = "";
              }

              this.queryParams = res.data;
              this.onlineType = res.data.onlineType;
              if (this.isQiWei) {
                this.queryParams.loanId = res.data.loanId ?? "";
              }
              this.queryParams.id = this.id;

              this.queryParams = {
                ...this.queryParams,
                cooperationMode:
                  res.data.cooperationMode == 0
                    ? ""
                    : res.data.cooperationMode || "",
              };

              let index = this.LoanList.findIndex(
                (item) => item.id == this.queryParams.loanId
              );
              if (index == -1) {
                this.queryParams.loanId = "";
              }
              this.queryParams.file = "";
            });
          }
        });
      });
    },

    /**
     *  @param { Function } handleCloseDialog 关闭模态框
     */
    handleCloseDialog() {
      this.cooperationLinkCheckResponse = {
        code: '',
        msg: '',
      }
      this.cooperationLinkChecking = false;
      this.$refs.queryForm.resetFields();
      // Object.assign(
      //   this.$data.queryParams,
      //   this.$options.data.call(this).queryParams
      // );
      this.imageUrl = "";
      this.$emit("close", false);
    },

    /**
     *  @param { Function } handleSubmit 提交表单
     */
    handleSubmit() {
      if (
        this.queryParams.mid == 1 ||
        this.queryParams.mid == 4 ||
        this.queryParams.mid == 5 ||
        this.queryParams.mid == 6 ||
        this.queryParams.mid == 7
      ) {
        delete this.queryParams.cooperationMode;
        if (!this.isQiWei) {
          delete this.queryParams.cooperationLink;
        }
      }
      if (this.queryParams.mid == 2 || this.queryParams.mid == 3) {
        delete this.queryParams.loanId;
        if (this.queryParams.cooperationMode == 1) {
          delete this.queryParams.productApiId;
        }
      }
      if (this.queryParams.district) {
        delete this.queryParams.district;
      }

      let data = new FormData();
      for (let i in this.queryParams) {
        data.append(i, this.queryParams[i]);
      }
      this.$refs.queryForm.validate((valid) => {
        if (valid) {
          if (this.type == "edit") {
            editProductCompile(data).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.handleCloseDialog();
                this.$emit("update");
              }
              // 推广链接校验失败
              else if (res.code == 10201 || res.code == 10202) {
                this.cooperationLinkCheckResponse = res;
              }
            });
          } else {
            addQwProductRule(this.queryParams).then((res) => {
              if (res.code == 200) {
                this.$message.success("添加成功");
                this.handleCloseDialog();
                this.$emit("update");
              } else if (res.code == 10201 || res.code == 10202) {
                this.cooperationLinkCheckResponse = res;
              }
            });
          }
        }
      });
    },

    /**
     *  @param { Function }
     */
    changeUpImg(e) {
      const isJPG =
        e.raw.type === "image/jpeg" ||
        e.raw.type === "image/jpg" ||
        e.raw.type === "image/png";
      const isLt2M = e.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG/JPEG/PNG 格式!");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
        return;
      }
      this.queryParams.file = e.raw;
      this.imageUrl = URL.createObjectURL(e.raw);
      if (
        document.getElementsByClassName("el-form-item__error").length > 0 &&
        this.type == "edit"
      ) {
        document.getElementsByClassName(
          "el-form-item__error"
        )[1].style.display = "none";
      }
      if (
        document.getElementsByClassName("el-form-item__error").length > 0 &&
        this.type == "copy"
      ) {
        document.getElementsByClassName(
          "el-form-item__error"
        )[0].style.display = "none";
      }
      if (document.getElementsByClassName("el-upload").length > 0) {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#8c939d";
      }
    },
    //11
    handleChangeOnlineType() {
      setOnlineSetType({
        typeId: this.onlineType,
        productId: this.id,
      }).then((res) => {
        this.$message.success("设置成功");
      });
    },
    //获取助贷
    getloanList() {
      getloanModeListsAll().then((res) => {
        this.LoanList = res.data;
      });
    },

    //获取商户
    getPartyaId() {
      getAcquirePartyAall().then((res) => {
        this.acquirePartyList = res.data;
      });
    },

    //获取推广类型
    getPackageList() {
      getFlowPackageList().then((res) => {
        let arr = [
          {
            name: "点击",
            value: 1,
          },
          {
            name: "CPS",
            value: 2,
          },
          {
            name: "CPC",
            value: 3,
          },
        ];
        let data = res.data.map((item) => {
          return {
            name: item.name,
            value: item.id,
          };
        });
        this.packageList = [...arr, ...data];
      });
    },

    handleGetProductApiList() {
      getProductApiList().then((res) => {
        this.prodApiList = res.data;
      });
    },
  },
  computed: {
    isDisable() {
      return this.showUsers.includes(this.$store.getters.userInfo.userName);
    },

    submitDisabled() {
      return this.cooperationLinkChecking || (this.showLinkFormItem && this.linkCheckFailed);
    },

    showCooperationLinkTip() {
      return this.showLinkFormItem && this.linkCheckFailed;
    },

    linkCheckFailed() {
      return this.cooperationLinkCheckResponse.code === 10202;
    },

    showLinkFormItem() {
      return this.queryParams.mid == 2 || this.queryParams.mid == 3 || this.isQiWei;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-edit {}

.dialog-wrap {
  max-height: 65vh;
  overflow-x: hidden;
  // overflow-y: scroll;
}

.title {
  padding: 24px 0 16px 10px;
  position: relative;
  font-size: 18px;
  text-align: left;

  &::before {
    content: "";
    width: 1px;
    height: 16px;
    border: 2px solid #e37318;
    position: absolute;
    left: 0;
    top: calc(50% - 5px);
  }
}

.tips {
  font-size: 13px;
  color: #999;
}

::v-deep .el-form-item--medium .el-form-item__label {
  letter-spacing: 2px;
  padding-bottom: 0;
}

::v-deep .el-form-item__content {
  line-height: 20px;
}

::v-deep .el-form-item {
  margin-bottom: 10px !important;
}

::v-deep .el-input__inner::placeholder {
  color: #999;
}

.el-form-item-tip {
  position: absolute;
  color: #ff4949;
  font-size: 12px
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
</style>
