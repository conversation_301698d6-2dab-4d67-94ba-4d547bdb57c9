<script>
import { fetchPlatformStatistics } from "@/api/distributeStatistics/platformStatistics";
import { getPlatformList } from "@/api/xmxrChannelManage/channelList";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
import { sum } from "@/utils/calculate";

export default {
  name: "PlatformStatistics",

  data() {
    return {
      // 搜索条件 渠道名称 渠道id 开始时间 结束时间
      queryParams: {
        channelName: "",
        channelId: "",
        dateRange: [],
        platformType: "",
      },
      openAll: false,
      refreshTable: true,
      tableData: [],
      platformList: [],
    };
  },

  mounted() {
    this.setDefaultDate();
    this.fetchTableData();
    this.fetchPlatformList();
  },

  methods: {
    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format("YYYY-MM-DD 00:00:00");
      const endTime = today.format("YYYY-MM-DD 23:59:59");

      this.queryParams.dateRange = [startTime, endTime];
    },

    async fetchPlatformList() {
      const res = await getPlatformList();
      this.platformList = res.data;
    },

    getParams() {
      const [startTime = "", endTime = ""] = this.queryParams.dateRange || [];
      return {
        startTime,
        endTime,
        channelName: this.queryParams.channelName,
        channelId: this.queryParams.channelId,
        platformType: this.queryParams.platformType,
      };
    },

    handleTableData(arr) {
      arr.forEach((row) => {
        row.rowKey = uuidv4();

        if (row.platformStats && row.platformStats.length) {
          row.platformStats.forEach((innerRow) => {
            innerRow.rowKey = uuidv4();
            innerRow.channelId = "";
            innerRow.channelName = innerRow.channelName.split("_")[1];
            innerRow.isInner = true;
          });
        } else {
          row.platformStats = null;
        }
      });
    },

    createSummaryRow(data) {
      const summaryRow = {
        channelId: "合计",
        successNum: 0,
        successPrice: 0,
        isSummary: true,
      };

      summaryRow.successNum = sum(data.map((item) => item.successNum));
      summaryRow.successPrice = sum(data.map((item) => item.successPrice));

      return summaryRow;
    },

    async fetchTableData() {
      const params = this.getParams();
      const res = await fetchPlatformStatistics(params);

      this.handleTableData(res.data);

      this.tableData = res.data;
      if (this.tableData.length) {
        this.tableData.unshift(this.createSummaryRow(this.tableData));
      }
      /*
     this.tableData 数据格式 ：
     渠道id（channelId）  渠道（channelName）  申请数（successNum）  总金额（successPrice）
平台列表（platformStats）

     **/
    },
    handldeOpen() {
      this.refreshTable = false;
      this.openAll = !this.openAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
  },
};
</script>

<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" size="small">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="queryParams.dateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="fetchTableData"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道名称">
        <el-input
          v-model="queryParams.channelName"
          placeholder="请输入渠道名称"
          @keyup.enter.native="fetchTableData"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道id">
        <el-input
          v-model="queryParams.channelId"
          placeholder="请输入渠道id"
          @keyup.enter.native="fetchTableData"
        ></el-input>
      </el-form-item>
      <el-form-item label="平台">
        <el-select v-model="queryParams.platformType" placeholder="请选择平台" clearable>
          <el-option
            v-for="item in platformList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="fetchTableData">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handldeOpen">展开/收缩</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      style="width: 100%"
      v-if="refreshTable"
      :default-expand-all="openAll"
      row-key="rowKey"
      :tree-props="{
        children: 'platformStats',
      }"
    >
      <el-table-column prop="channelId" label="渠道id"></el-table-column>
      <el-table-column prop="channelName" label="渠道">
        <template slot-scope="{ row }">
          <template v-if="row.channelName">
            <el-tag size="small" type="info" v-if="row.isInner">{{
              row.channelName
            }}</el-tag>
            <el-tag size="small" v-else>{{ row.channelName }}</el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="successNum" label="申请数"></el-table-column>
      <el-table-column prop="successPrice" label="总金额"></el-table-column>
    </el-table>
  </div>
</template>

<style scoped lang="scss"></style>
