import request from '@/utils/request'
//获取雷达配置
export const getxyConfig = () => {
    return request({
        url: "/system/xyConfig/query",
        params: "get"
    })
}
//修改雷达配置
export const editxyConfig = (data) => {
    return request({
        url: "/system/xyConfig/edit",
        method: "post",
        data
    })
}

//三要素获取信息
export const getThreeElementsConfig = () => {
    return request({
        url: "/loan/threeElementsConfig/findOne",
        method: "get"
    })
}
//三要素编辑
export const editThreeElementsConfig = (data) => {
    return request({
        url: "/loan/threeElementsConfig/updateOne",
        method: "post",
        data
    })
}
//获取会员配置
export const getMemberConfig=()=>{
    return request({
        url:"/loan/memberConfig/query",
        method:'get'
    })
}
//编辑会员配置
export const editMemberConfig=(data)=>{
    return request({
        url:"/loan/memberConfig/edit",
        method:'post',
        data
    })
}
//编辑智付配置
export const editdinPayConfig=(data)=>{
    return request({
        url:"/loan/dinPayConfig/edit",
        method:'post',
        data
    })
}
//编辑智付配置
export const getdinPayConfig=()=>{
    return request({
        url:"/loan/dinPayConfig/query",
        method:'get',
    })
}