<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" size="small">
      <el-form-item label="合作方名称" prop="partnerName">
        <el-input
          clearable
          placeholder="请输入合作方名称"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.partnerName"
        ></el-input>
      </el-form-item>
      <el-form-item label="我方推广渠道号" prop="channelId">
        <el-input
          clearable
          placeholder="请输入渠道ID"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model.number="queryParams.channelId"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索
        </el-button>
      </el-form-item>
    </el-form>
    <!-- 渠道列表 -->

    <el-table :data="tableData">
      <el-table-column label="渠道配置id" prop="apiChannelId" align="center" />
      <el-table-column label="合作方名称" prop="partnerName" align="center" />
      <el-table-column
        label="我方落地页渠道号"
        prop="myChannelId"
        align="center"
      />
      <el-table-column label="渠道来源" prop="channelId" align="center" />
      <el-table-column label="平台开关" prop="hasEnable" align="center">
        <template slot-scope="{ row }">
          <div>
            <div
              style="margin-bottom: 5px"
              v-for="item in row.platformSwitch"
              :key="item.platformType"
            >
              <el-tag style="margin-right: 5px">{{ item.platformName }}</el-tag
              ><el-switch
                :value="item.hasEnable"
                active-text="开启"
                inactive-text="关闭"
                @change="changeEnable($event, item, row)"
              >
              </el-switch>
            </div>
          </div>
          <!-- <div>
            <el-switch
              :value="row.hasEnable"
              active-text="开启"
              inactive-text="关闭"
              @change="changeEnable($event, row)"
            >
            </el-switch>
          </div> -->
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="通过比例"
        prop="passRate"
        align="center"
        width="150px"
      >
        <template #header> 通过比例(0-100) </template>
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.passRate"
            style="width: 80px"
            :controls="false"
            @blur="blurPassRate(scope.row)"
            @focus="focusPassRate(scope.row)"
          ></el-input-number>
        </template>
      </el-table-column> -->
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import {
  getCityCrashLibrary,
  updateCityCrashLibrary,
} from "@/api/xmxrChannelManage/cityCrashLibrary";
export default {
  name: "CityCrashLibrary",
  data() {
    return {
      total: 0,
      passRate: "",
      tableData: [],
      queryParams: {
        partnerName: "",
        channelId: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    getList() {
      getCityCrashLibrary(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    //查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    changeEnable(enable, child, row) {
      this.$confirm(`确定${enable ? "启用" : "关闭"}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          row.platformSwitch.forEach(item=>{
            if(item.platformType == child.platformType){
              item.hasEnable = enable;
            }
          })


          updateCityCrashLibrary({
            apiChannelId: row.apiChannelId,
            platformSwitch: row.platformSwitch,
          })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch((err) => {
          this.getList();
        });
    },

    focusPassRate(row) {
      this.passRate = row.passRate;
    },

    blurPassRate(row) {
      if (row.passRate == this.passRate) {
        return;
      }

      const isFloat = String(row.passRate).includes(".");
      const isNotNumber = isNaN(row.passRate);
      const passRateNum = Number(row.passRate);
      const isOutOfRange = passRateNum < 0 || passRateNum > 100;

      if (isNotNumber || isOutOfRange || isFloat) {
        this.$message.error("请输入0-100的整数");
        row.passRate = this.passRate;

        return;
      }

      this.$confirm(`确定修改通过比例吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          updateCityCrashLibrary({
            apiChannelId: row.apiChannelId,
            hasEnable: row.hasEnable,
            passRate: row.passRate,
          })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch((err) => {
          this.getList();
        });
    },
  },
  async mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.el-switch__label.el-switch__label--left.is-active {
  color: #333;
}

.el-input.el-input--medium {
  text-align: center !important;
}
</style>
