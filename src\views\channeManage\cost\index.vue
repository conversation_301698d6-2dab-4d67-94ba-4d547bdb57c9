<template>
  <div class="app-container">
    <div class="tips">渠道ID:{{ $route.query.id }} --- 渠道名称:{{ $route.query.name }}</div>

    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="状态" prop="status">
        <el-date-picker size="small" v-model="value1" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="addCost"
          v-hasPermi="['channeManage:cost:add']">成本录入</el-button>
        <el-button icon="el-icon-upload2" type="primary" size="mini" v-hasPermi="['channeManage:cost:add']"
          @click="upData">一键导入</el-button>
        <el-button icon="el-icon-download" type="primary" size="mini" @click="downLoad">下载渠道模板</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" border :data="costList">
      <el-table-column label="日期" prop="ms" align="center" />
      <el-table-column label="UV" prop="uv" align="center" />
      <el-table-column label="成本" prop="cost" align="center" />
      <el-table-column label="录入人" prop="createUser" align="center" />
      <el-table-column label="修改人" prop="updateUser" align="center" />

      <el-table-column label="操作" align="center" v-hasPermi="['channeManage:cost:modify']">
        <template slot-scope="{row}">
          <div>
            <el-button v-if="row.id" type="text" icon="el-icon-edit-outline" @click="handleEdit(row)">修改成本</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog :title="isAdd ? '新增成本' : '修改成本'" :visible.sync="costAvisible" @close="cancel" width="600px" append-to-body
      center :close-on-click-modal="false">
      <el-form ref="formData" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="日期" prop="ms" v-if="isAdd">
          <el-date-picker size="small" :picker-options="pickerOptions" v-model="formData.ms" value-format="yyyy-MM-dd"
            type="date" :disabled="!isAdd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
        </el-form-item>
        <el-form-item label="UV" prop="uv">
          <el-input v-model="formData.uv"></el-input>
        </el-form-item>
        <el-form-item label="成本" prop="cost">
          <el-input v-model="formData.cost" type="number"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog width="800px" title="批量导入成本" append-to-body center @close="upClose" :visible.sync="upDataAvisiable"
      :close-on-click-modal="false">
      <el-form>
        <el-form-item label="请上传数据">
          <el-upload :on-remove="handleRemove" ref="my-upload" :auto-upload="false" action="" class="upload-demo" drag
            :on-change="handleChange">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将EXCEL拖到此处，或<em>点击上传</em>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUpData">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as xlsx from "xlsx";

import {
  getCostLsit,
  addCostOne,
  editCostOne,
  expotData,
} from "@/api/channeManage/cost";
import { readFile, sheetChar, handleFileChar } from "@/utils/execl-upload";

export default {
  data() {
    return {
      loading: false,
      costAvisible: false,
      isAdd: true,
      upDataAvisiable: false,
      files: "",
      costList: [],
      upDataForm: {
        costs: [],
        channelId: "",
      },
      value1: [],
      total: 0,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startTime: "",
        stopTime: "",
      },
      rules: {
        cost: [
          {
            required: true,
            message: "请输入成本",
            trigger: "blur",
          },
        ],
        ms: [
          {
            required: true,
            message: "请选择时间",
            trigger: "blur",
          },
        ],
        uv: [
          {
            required: true,
            message: "请输入uv",
            trigger: "blur",
          },
        ],
      },
      formData: {
        channelId: "",
        cost: "",
        ms: "",
        uv: "",
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.value1 != null) {
        this.queryParams.startTime = this.value1[0];
        this.queryParams.stopTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.stopTime = "";
      }
      this.getList();
    },
    handleEdit(row) {
      this.costAvisible = true;
      this.isAdd = false;
      this.formData.id = row.id;
      this.formData.cost = row.cost;
      this.formData.uv = row.uv;
    },
    submitForm() {
      this.formData.channelId = this.$route.query.id;
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addCostOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.getList();
                this.cancel();
              }
            });
          } else {
            delete this.formData.channelId;
            delete this.formData.ms;

            editCostOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.getList();
                this.cancel();
              }
            });
          }
        }
      });
    },
    addCost() {
      this.isAdd = true;
      this.costAvisible = true;
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    //单个添加取消
    cancel() {
      this.costAvisible = false;
      this.formData = {
        channelId: "",
        cost: "",
        ms: "",
        uv: "",
      };
      this.isAdd = true;
      this.$refs.formData.resetFields();
    },
    getList() {
      getCostLsit({ id: this.$route.query.id, ...this.queryParams }).then(
        (res) => {
          this.costList = res.rows;
          this.total = res.total;
          if (res.rows.length > 0) {
            this.costList.push({
              ms: "总计",
              cost: res.rows[0].sumCost,
            });
          }
          this.loading = false;
        }
      );
    },
    //一键导入
    upData() {
      this.upDataAvisiable = true;
    },
    //上传exlec
    async handleChange(ev) {
      if (ev.size > 2 * 1024 * 1024) {
        this.$notify.error({
          title: "错误",
          message: "文件大小不能超过2M",
        });
        return;
      }
      if (!/\.(xls|xlsx)$/.test(ev.name.toLowerCase())) {
        this.$notify.error({
          title: "错误",
          message: "上传格式有误",
        });
        this.$refs["my-upload"].clearFiles();
        this.upDataForm.costs = [];
        return;
      }

      let file = ev.raw;
      this.files = file;

      if (!file) return;
      let data = await readFile(file);
      let workbook = xlsx.read(data, { type: "binary" });
      let worksheet1 = workbook.Sheets[workbook.SheetNames[0]];
      let sheets = xlsx.utils.sheet_to_json(worksheet1) || [];
      sheets = handleFileChar(sheets, sheetChar);
      this.upDataForm.costs = sheets;

      this.upDataForm.channelId = this.$route.query.id;
      this.$message.success("上传成功");
    },

    //批量上传
    submitUpData() {
      console.log(this.files);
      if (!this.files) {
        this.$notify({
          type: "warning",
          title: "警告",
          message: "请上传文件",
        });
        return;
      }
      if (this.upDataForm.costs.length == 0) {
        this.$notify({
          type: "warning",
          title: "警告",
          message: "上传文件内容不能为空",
        });

        return;
      }
      //批量上传
      expotData(this.upDataForm).then((res) => {
        if (res.code == 200) {
          this.$message.success("上传成功");
          this.upClose();
          this.getList();
        }
      });
    },

    //关闭上传框
    upClose() {
      this.upDataAvisiable = false;
      this.$refs["my-upload"].clearFiles();
      this.upDataForm.costs = [];
      this.files = "";
    },
    //移除上传文件
    handleRemove() {
      this.$refs["my-upload"].clearFiles();
      this.upDataForm.costs = [];
      this.files = "";
    },
    downLoad() {
      let arrlist = [
        {
          日期: "2022-03-04",
          UV: "42685",
          成本: "4829.4",
        },
        {
          日期: "2022-03-05",
          UV: "19056",
          成本: "1699.8",
        },
        {
          日期: "2022-03-06",
          UV: "19477",
          成本: "1989.6",
        },
      ];
      let sheet = xlsx.utils.json_to_sheet(arrlist);
      let book = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(book, sheet, "sheet1");
      xlsx.writeFile(book, `批量导入模板.xls`);
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.tips {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
  font-weight: bold;
}

.upload-demo {
  display: flex;
  justify-content: center;
}
</style>
