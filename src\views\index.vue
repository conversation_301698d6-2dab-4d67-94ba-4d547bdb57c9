<template>
  <div class="app-container">
    <!-- <div class="echarts" ref="echarts"></div> -->
    <div class="left-wrap">
      <div class="board-list" style="margin-bottom: 20px">
        <div class="board-item mr-20">
          <div>
            <div class="board-item-title">
              本月新增产品({{ nowDay.month }}月)
            </div>
            <div class="board-item-desc">
              仅统计当前日期之前无消耗且第一次消耗日期在统计月中的产品
            </div>
          </div>
          <el-popover placement="right" trigger="hover">
            <el-table :data="productAddList" max-height="400">
              <el-table-column
                prop="userNickName"
                label=""
                width="120"
              ></el-table-column>
              <el-table-column prop="mid1" label="银行机构"></el-table-column>
              <el-table-column prop="mid2" label="线上-贷超"></el-table-column>
              <el-table-column prop="mid3" label="线上持牌"></el-table-column>
              <el-table-column prop="mid4" label="一级机构"></el-table-column>
              <el-table-column prop="mid5" label="二级机构"></el-table-column>
              <el-table-column prop="mid6" label="三级机构"></el-table-column>
            </el-table>
            <template>
              <div slot="reference">
                <div>
                  <div class="board-item-type">
                    <div class="board-logo">
                      <img src="@/assets/index/icon-1.png" alt="" /> 银行机构
                    </div>
                    <div class="board-logo">
                      <img src="@/assets/index/icon-2.png" alt="" /> 线上机构
                    </div>
                    <div class="board-logo">
                      <img src="@/assets/index/icon-3.png" alt="" /> 其他
                    </div>
                  </div>
                  <div class="board-item-type">
                    <div class="board-num">{{ productAddTotal.bank }}</div>
                    <div class="board-num">{{ productAddTotal.online }}</div>
                    <div class="board-num">{{ productAddTotal.other }}</div>
                  </div>
                </div>
              </div>
            </template>
          </el-popover>
        </div>
        <div class="board-item mt-20">
          <div>
            <div class="board-item-title">本月新增商户</div>
            <div class="board-item-desc">
              仅统计当前日期之前无消耗且第一次消耗日期在统计月中的商户
            </div>
          </div>
          <div>
            <div class="board-item-type">
              <div class="board-logo">
                <img src="@/assets/index/icon-4.png" alt="" /> 门店商户
              </div>
              <div class="board-logo">
                <img src="@/assets/index/icon-5.png" alt="" /> 线上商户
              </div>
            </div>
            <div class="board-item-type">
              <div class="board-num">{{ merchantAddTotal.offlineNum }}</div>
              <div class="board-num">{{ merchantAddTotal.onlineNum }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="left-emty" v-if="!isShowMore">
        <img src="@/assets/index/index-icon.png" alt="" />
      </div>
      <template v-else>
        <div class="board-list">
          <div class="board-item1">
            <div class="board-item-title">今日UV总量</div>
            <div class="board-item-desc">UV（投放页面用户访问数）</div>
            <div class="board-detail">
              <div class="board-num">{{ channelUvTotal.todayNum }}</div>
              <div class="board-compare">
                同比
                <span
                  :class="[
                    channelUvTotal.todayNum - channelUvTotal.yesterdayNum > 0
                      ? 'fc-67'
                      : 'fc-54',
                  ]"
                  ><span
                    v-if="
                      channelUvTotal.todayNum - channelUvTotal.yesterdayNum > 0
                    "
                    >+</span
                  >
                  {{
                    channelUvTotal.todayNum - channelUvTotal.yesterdayNum
                  }}</span
                >
                <img
                  :src="
                    require(`@/assets/index/${
                      channelUvTotal.todayNum - channelUvTotal.yesterdayNum > 0
                        ? 'up'
                        : 'down'
                    }.svg`)
                  "
                  alt=""
                />
              </div>
              <div class="board-pre">昨日{{ channelUvTotal.yesterdayNum }}</div>
            </div>
          </div>

          <div class="board-item1 m-10">
            <el-popover placement="right" trigger="hover">
              <el-table :data="productClickList" max-height="400">
                <el-table-column
                  prop="productName"
                  width="180"
                  label="产品名称"
                ></el-table-column>
                <el-table-column
                  prop="successNum"
                  width="120"
                  label="产品成功数量"
                ></el-table-column>
                <el-table-column
                  prop="userNickName"
                  width="120"
                  label="商务"
                ></el-table-column>
              </el-table>
              <template>
                <div slot="reference">
                  <div class="board-item-title">今日咨询订单数量</div>
                  <div class="board-item-desc">咨询成功订单数量</div>
                  <div class="board-detail">
                    <div class="board-num">
                      {{ productClickTotal.todaySuccessNum }}
                    </div>
                    <div class="board-compare">
                      同比
                      <span
                        :class="[
                          productClickTotal.todaySuccessNum -
                            productClickTotal.yesterdaySuccessNum >
                          0
                            ? 'fc-67'
                            : 'fc-54',
                        ]"
                      >
                        <span
                          v-if="
                            productClickTotal.todaySuccessNum -
                              productClickTotal.yesterdaySuccessNum >
                            0
                          "
                          >+</span
                        >
                        {{
                          productClickTotal.todaySuccessNum -
                          productClickTotal.yesterdaySuccessNum
                        }}</span
                      ><img
                        :src="
                          require(`@/assets/index/${
                            productClickTotal.todaySuccessNum -
                              productClickTotal.yesterdaySuccessNum >
                            0
                              ? 'up'
                              : 'down'
                          }.svg`)
                        "
                        alt=""
                      />
                    </div>
                    <div class="board-pre">
                      昨日{{ productClickTotal.yesterdaySuccessNum }}
                    </div>
                  </div>
                </div>
              </template>
            </el-popover>
          </div>
          <div class="board-item1">
            <div class="board-item-title">今日产品消耗</div>
            <div class="board-item-desc">今日产品消耗预估</div>
            <div class="board-detail">
              <div class="board-num">{{ productProfitTotal.todayProfit }}</div>
              <div class="board-compare">
                同比
                <span
                  :class="[
                    productProfitTotal.todayProfit -
                      productProfitTotal.yesterdayProfit >
                    0
                      ? 'fc-67'
                      : 'fc-54',
                  ]"
                >
                  <span
                    v-if="
                      productProfitTotal.todayProfit -
                        productProfitTotal.yesterdayProfit >
                      0
                    "
                    >+</span
                  >
                  {{
                    (
                      productProfitTotal.todayProfit -
                      productProfitTotal.yesterdayProfit
                    ).toFixed(2)
                  }}</span
                ><img
                  :src="
                    require(`@/assets/index/${
                      productProfitTotal.todayProfit -
                        productProfitTotal.yesterdayProfit >
                      0
                        ? 'up'
                        : 'down'
                    }.svg`)
                  "
                  alt=""
                />
              </div>
              <div class="board-pre">
                昨日{{ productProfitTotal.yesterdayProfit }}
              </div>
            </div>
          </div>
        </div>
        <div class="chart">
          <div class="chart-item mr-10 m-b20">
            <div>
              <div class="chart-title">近7日产品消耗</div>
              <div class="chart-desc">
                更多数据可查看商户投放统计或产品消耗统计
              </div>
            </div>
            <el-popover placement="top" trigger="manual" v-model="showTopData">
              <div class="chart-data">总消耗{{ consumeTotal }}</div>
              <el-table :data="productTopList" max-height="400">
                <el-table-column
                  prop="productName"
                  width="180"
                  label="产品名称"
                ></el-table-column>
                <el-table-column
                  prop="profit"
                  width="120"
                  label="收益"
                ></el-table-column>
                <el-table-column
                  prop="userNickName"
                  width="120"
                  label="商务"
                ></el-table-column>
              </el-table>
              <div style="margin-top: 10px" class="fc-67">
                仅展示前五消耗的产品
              </div>
              <template>
                <div slot="reference">
                  <div
                    class="chart-view"
                    ref="echarts"
                    v-clickoutside="hanldeClosePopover"
                  ></div>
                </div>
              </template>
            </el-popover>
          </div>
          <div class="chart-item" v-show="flowFlag">
            <div>
              <div class="chart-title">近14日流量变化</div>
              <div class="chart-desc">
                仅展示从前一日算起14天的数据，更多数据可查看渠道统计
              </div>
            </div>
            <el-popover placement="top" trigger="manual" v-model="showUvData">
              <div class="chart-data">
                <span>uv:{{ flowRate.uvNum }}</span>
                <span>咨询订单量:{{ flowRate.successNum }}</span>
                <span> 转化率:{{ flowRate.flowRate }}%</span>
              </div>
              <el-table :data="rateTopList" max-height="400">
                <el-table-column
                  prop="channelId"
                  align="center"
                  width="120"
                  label="渠道ID"
                ></el-table-column>
                <el-table-column
                  prop="channelName"
                  width="180"
                  align="center"
                  label="渠道名称"
                ></el-table-column>
                <el-table-column
                  prop="flowRate"
                  align="center"
                  :formatter="(row) => row.flowRate + '%'"
                  width="120"
                  label="渠道转化率"
                ></el-table-column>
                <el-table-column
                  prop="successNum"
                  width="120"
                  :formatter="(row) => row.successNum || 0"
                  align="center"
                  label="有效订单"
                ></el-table-column>
                <el-table-column
                  prop="channelCost"
                  width="120"
                  align="center"
                  label="渠道成本"
                ></el-table-column>
              </el-table>
              <template>
                <div slot="reference">
                  <div
                    class="chart-view"
                    ref="echartUv"
                    v-clickoutside="hanldeClosePopover"
                  ></div>
                </div>
              </template>
            </el-popover>
          </div>
        </div>
      </template>
    </div>
    <div class="right-wrap">
      <div class="need-tilte">
        今日待办事项（{{ nowDay.month }}/{{ nowDay.day }}）
      </div>
      <div class="need-wrap" v-if="!needNum && isEmty">
        <img src="@/assets/index/empty.svg" alt="" class="need-empty" />
        <div class="need-tags">今日暂无待办事项哦~请继续保持</div>
      </div>
      <template v-else>
        <div v-for="(item, index) in needList" :key="index">
          <div class="need-item" v-if="item.total">
            <div class="need-logo">
              <img :src="item.icon" alt="" />{{ item.title }}
            </div>
            <div class="need-handle">
              <span class="need-total"
                >共 <span>{{ item.total }}</span> 条</span
              >
              <span class="need-submit" @click="bindToUrl(item)">去处理</span>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import {
  getStatsMonth,
  getStatsDay,
  getStatsDayPending,
  getStatsRecentDays,
  getDayTopProfit,
  getDaysUvList,
} from "@/api/home";
import Clickoutside from "element-ui/src/utils/clickoutside";
import elementResizeDetectorMaker from "element-resize-detector";
export default {
  data() {
    return {
      showUsers: ["shenxun", "yutong", "sunyang", "xupengcheng","wuqinyun", 'dingcan'],
      nowDay: {
        day: "",
        month: "",
        year: "",
      },

      productAddTotal: {
        bank: 0,
        online: 0,
        other: 0,
      },
      productAddList: [],
      merchantAddTotal: {
        offlineNum: 0,
        onlineNum: 0,
      },
      channelUvTotal: {
        todayNum: 0,
        yesterdayNum: 0,
      },
      productClickTotal: {
        todaySuccessNum: 0,
        yesterdaySuccessNum: 0,
      },
      productClickList: [],
      productProfitTotal: {
        todayProfit: 0,
        yesterdayProfit: 0,
      },
      needTotal: {}, //需要做的总数
      isEmty: true,
      needNum: 0, //是否显示空白
      needList: [
        {
          icon: require("@/assets/index/icon-6.png"),
          title: "商户待补充合同",
          total: 0,
          path: "/partyA/list",
          type: "notContractNum",
        },
        {
          icon: require("@/assets/index/icon-7.png"),
          title: "商户待审核",
          total: 0,
          path: "",
          type: "merchant",
        },
        {
          icon: require("@/assets/index/icon-8.png"),
          title: "推广产品待审核",
          total: 0,
          path: "/productManage/examine",
          type: "notProductCheckNum",
        },
        {
          icon: require("@/assets/index/icon-9.png"),
          title: "财务待审核",
          total: 0,
          path: "",
          type: "finance",
        },
        {
          icon: require("@/assets/index/icon-10.png"),
          title: "渠道成本待录入",
          total: 0,
          path: "/channeManage/channeList",
          type: "notCostNum",
        },
        {
          icon: require("@/assets/index/icon-7.png"),
          title: "返点申请待审核",
          total: 0,
          path: "/platform/platformRebate/examine",
          type: "waitApplyRebate",
        },
      ],
      showTopData: false, //点击第一个图表
      showUvData: false, //点击第二个列表
      productTopList: [], //产品前五列表
      rateTopList: [],
      flowRate: {
        flowRate: 0,
        successNum: 0,
        uvNum: 0,
      },
      flowFlag: true, //是否展示流量变化
      userId: "",
      notCostChannelNameList: [],
      consumeTotal: 0,
    };
  },

  directives: { Clickoutside },

  methods: {
    bindToUrl(item) {
      let {
        notCheckContractNum,
        notDepositCheckNum,
        notRebateCheckNum,
        notRefundCheckNum,
        notPartyBCheckNum,
        notRechargeCheckNum,
        notPersonalCashOutCheckNum,
      } = this.needTotal;
      if (item.type == "merchant") {
        if (notCheckContractNum) {
          this.$router.push(`/partyA/list?type=1`);
        }
        if (notDepositCheckNum) {
          this.$router.push("/partyA/ensure");
        }
        if (notRebateCheckNum) {
          this.$router.push("/partyA/rebates");
        }
        return;
      }
      if (item.type == "finance") {
        if (notRefundCheckNum) {
          this.$router.push("/partyA/refund");
        }
        if (notPartyBCheckNum) {
          this.$router.push("/partyB/partybFinancial");
        }
        if (notRechargeCheckNum) {
          this.$router.push("/auditFinance/entryAudit");
        }
        if (notPersonalCashOutCheckNum) {
          this.$router.push("/auditFinance/requestAudit");
        }
        return;
      }
      if (item.type == "notCostNum") {
        this.$router.push(
          this.notCostChannelNameList[0]
            ? `${item.path}?channelName=${this.notCostChannelNameList[0]}`
            : item.path
        );
        return;
      }
      if (item.type == "notContractNum") {
        this.$router.push(`/partyA/list?type=0,3`);
        return;
      }
      if (item.type == "notProductCheckNum") {
        this.$router.push(`/productManage/examine`);
        return;
      }
      if (item.type == "waitApplyRebate") {
        this.$router.push(`/platform/platformRebate/examine`);
        return;
      }
    },
    hanldeClosePopover() {
      this.showTopData = false;
      this.showUvData = false;
    },
    initData() {
      this.isEmty = false;
      //产品类型
      let productType = {
        1: "bank",
        2: "online",
        3: "online",
        4: "other",
        5: "other",
        6: "other",
      };
      let mecType = {
        1: "mid1",
        2: "mid2",
        3: "mid3",
        4: "mid4",
        5: "mid5",
        6: "mid6",
      };
      //获取当月数据
      getStatsMonth().then((res) => {
        let { productMonthAddNumMap, merchantMonthAddNumMap } = res.data;
        //处理产品
        let { bank, online, other } = productMonthAddNumMap.basicList.reduce(
          (pre, cur) => {
            pre[productType[cur.mid]] += cur.num;
            return pre;
          },
          { bank: 0, online: 0, other: 0 }
        );
        this.productAddTotal.bank = bank;
        this.productAddTotal.online = online;
        this.productAddTotal.other = other;
        this.productAddList =
          productMonthAddNumMap.detailList &&
          productMonthAddNumMap.detailList.map((item) => {
            item.basicList.forEach((citem) => {
              item[mecType[citem.mid]] = citem.num;
            });
            return item;
          });
        let { offlineNum, onlineNum } =
          merchantMonthAddNumMap.detailList &&
          merchantMonthAddNumMap.detailList.reduce(
            (pre, cur) => {
              pre.offlineNum += cur.offlineNum;
              pre.onlineNum += cur.onlineNum;
              return pre;
            },
            {
              offlineNum: 0,
              onlineNum: 0,
            }
          );
        this.merchantAddTotal.offlineNum = offlineNum;
        this.merchantAddTotal.onlineNum = onlineNum;
      });

      //今日待办
      getStatsDayPending().then((res) => {
        this.notCostChannelNameList = res.data.notCostChannelNameList || [];
        this.isEmty = true;
        let data = res.data;
        this.needTotal = res.data;
        this.needList.forEach((item) => {
          if (item.type == "merchant") {
            item.total =
              data.notCheckContractNum +
              data.notDepositCheckNum +
              data.notRebateCheckNum;
          } else if (item.type == "finance") {
            item.total =
              data.notRefundCheckNum +
              data.notPartyBCheckNum +
              data.notRechargeCheckNum +
              data.notPersonalCashOutCheckNum;
          } else if (item.type == "waitApplyRebate") {
            item.total = data.waitApplyRebateCount;
          } else {
            item.total = data[item.type];
          }
        });
        this.needNum = this.needList.reduce((pre, cur) => {
          return pre + cur.total;
        }, 0);
      });
    },

    init() {
      // //获取当日数据
      getStatsDay().then((res) => {
        let { channelUvNumMap, productProfitMap, productClickMap } = res.data;
        this.channelUvTotal.yesterdayNum = channelUvNumMap.yesterdayNum || 0;
        this.channelUvTotal.todayNum = channelUvNumMap.todayNum || 0;
        this.productProfitTotal.todayProfit = productProfitMap.todayProfit || 0;
        this.productProfitTotal.yesterdayProfit =
          productProfitMap.yesterdayProfit || 0;
        this.productClickTotal.todaySuccessNum =
          productClickMap.todaySuccessNum || 0;
        this.productClickTotal.yesterdaySuccessNum =
          productClickMap.yesterdaySuccessNum || 0;
        this.productClickList = productClickMap.topClickList || [];
      });

      getStatsRecentDays().then((res) => {
        let { profitDaysList, flowDaysMap, flowFlag, userId } = res.data || {};
        this.userId = userId;
        this.flowFlag = flowFlag;
        let xData = profitDaysList.map((item) => {
          return item.queryDate.split("-").splice(1).join("/");
        });
        let yData = profitDaysList.map((item) => {
          return item.profit;
        });
        var myChart = echarts.init(this.$refs.echarts);
        // 指定配置和数据
        var option = {
          color: ["#004DAB"],
          grid: {
            left: "3%",
            top: "7%",
            right: "0%",
            bottom: "4%",
            containLabel: true,
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "line",
              lineStyle: {
                color: "#EBEEF5",
                width: 2,
                type: "dash",
              },
            },
          },
          xAxis: [
            {
              type: "category",
              data: xData,
              axisTick: {
                show: false,
              },
              axisLabel: {
                textStyle: {
                  color: "#000000",
                  fontSize: "14",
                },
                align: "center",
              },
              axisLine: {
                show: false,
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              axisLabel: {
                textStyle: {
                  color: "#000000",
                  fontSize: "14",
                },
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                lineStyle: {
                  color: ["#EBEEF5"],
                },
              },
            },
          ],

          series: [
            {
              name: "总消耗",
              type: "line",
              smooth: true,
              symbol: "circle", //设定为实心点
              symbolSize: 4,
              data: yData,
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#004DAB",
                  },
                  {
                    offset: 1,
                    color: "#FFF3E8",
                  },
                ]),
              },
            },
          ],
        };
        let xData1 =
          (flowFlag &&
            flowDaysMap.uvList.map((item) => {
              return item.queryDate.split("-").splice(1).join("/");
            })) ||
          [];
        let yData1 =
          (flowFlag &&
            flowDaysMap.uvList.map((item) => {
              return item.num;
            })) ||
          [];
        let yData2 =
          (flowFlag &&
            flowDaysMap.productClickList.map((item) => {
              return item.num;
            })) ||
          [];

        var echartUv = echarts.init(this.$refs.echartUv);
        // 指定配置和数据
        var optionUv = {
          color: ["#004DAB"],
          grid: {
            left: "3%",
            top: "7%",
            right: "0%",
            bottom: "4%",
            containLabel: true,
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "line",
              lineStyle: {
                color: "#EBEEF5",
                width: 2,
                type: "dash",
              },
            },
          },
          xAxis: [
            {
              type: "category",
              data: xData1,
              axisTick: {
                show: false,
              },
              axisLabel: {
                textStyle: {
                  color: "#000000",
                  fontSize: "14",
                },
                align: "center",
              },
              axisLine: {
                show: false,
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              axisLabel: {
                textStyle: {
                  color: "#000000",
                  fontSize: "14",
                },
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                lineStyle: {
                  color: ["#EBEEF5"],
                },
              },
            },
          ],

          series: [
            {
              name: "UV",
              type: "line",
              smooth: true,
              stack: "总量",
              symbol: "circle", //设定为实心点
              symbolSize: 4,
              data: yData1,
              itemStyle: {
                normal: {
                  color: "#6C6CEA", //改变折线点的颜色
                  lineStyle: {
                    color: "#6C6CEA", //改变折线颜色
                  },
                },
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#6C6CEA",
                  },
                  {
                    offset: 1,
                    color: "#D9D9FF",
                  },
                ]),
              },
            },
            {
              name: "有效订单",
              type: "line",
              smooth: true,
              symbol: "circle", //设定为实心点
              stack: "总量",
              symbolSize: 4,
              data: yData2,
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#004DAB",
                  },
                  {
                    offset: 1,
                    color: "#FFF3E8",
                  },
                ]),
              },
            },
          ],
        };

        // 把配置给实例对象
        myChart.setOption(option);
        echartUv.setOption(optionUv);
        let that = this;
        myChart.on("click", function (params) {
          console.log();
          that.consumeTotal = params.data;
          let data = that.nowDay.year + "-" + params.name.split("/").join("-");
          getDayTopProfit({ dateStr: data }).then((res) => {
            that.productTopList = res.data;
            that.showTopData = true;
          });
        });
        echartUv.on("click", function (params) {
          let data = that.nowDay.year + "-" + params.name.split("/").join("-");

          getDaysUvList({ startDate: data }).then((res) => {
            that.flowRate = res.data.flowRate || {};
            that.rateTopList = res.data.rateTopList || [];
            that.showUvData = true;
          });
        });

        let erd = elementResizeDetectorMaker();
        erd.listenTo(this.$refs.echarts, (element) => {
          this.$nextTick(() => {
            myChart.resize();
            echartUv.resize();
          });
        });
      });
    },
  },
  computed: {
    isShowMore() {
      return this.showUsers.includes(this.$store.getters.userInfo.userName);
    },
  },
  mounted() {
    let date = new Date();
    this.nowDay.day = date.getDate();
    this.nowDay.month = date.getMonth() + 1;
    this.nowDay.year = date.getFullYear();
    this.initData();
    if (this.isShowMore) {
      this.init();
    }
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;
  flex-wrap: wrap;
  background: #f5f7fa;

  --flex-75: 0.75;
  --flex-25: 0.25;

  .left-wrap {
    flex: var(--flex-75);
    margin-right: 20px;

    .board-list {
      min-height: 200px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      .board-item {
        height: 220px;
        background: #fff;
        min-width: 400px;
        flex: 1;
        padding: 20px 30px 30px 30px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        &-title {
          font-size: 20px;
          font-weight: bold;
          color: #000000;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            display: block;
            height: 20px;
            width: 5px;
            top: 3px;
            left: -13px;
            background: #004DAB;
          }
        }

        &-desc {
          font-size: 14px;
          font-weight: 500;
          margin-top: 10px;
          color: #99999b;
        }

        &-type {
          display: flex;
        }

        .board-logo {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 16px;
          color: #606266;

          img {
            width: 30px;
            height: 30px;
            margin-right: 10px;
          }
        }

        .board-num {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 40px;
          color: #303133;

          margin-top: 10px;
          font-family: DIN;

          & + .board-num {
            font-family: DIN;
            border-left: 1px solid #dcdfe6;
          }
        }
      }

      .board-item1 {
        flex: 1;
        padding: 20px 30px 30px 30px;
        background: #fff;
        min-width: 350px;
        font-family: DIN;

        .board-detail {
          display: flex;
          flex-direction: column;
          align-items: center;

          .board-num {
            font-size: 40px;
            color: #303133;

            margin-top: 10px;
            margin-bottom: 10px;
          }
        }

        .board-compare {
          display: flex;
          align-items: center;
          color: #909399;
          font-size: 14px;

          img {
            width: 14px;
            height: 14px;
            margin-left: 2px;
          }
        }

        .board-pre {
          color: #303133;
          font-size: 14px;
        }
      }
    }

    .left-emty {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 600px;
    }

    .chart {
      display: flex;
      flex-wrap: wrap;
      min-height: 400px;
      margin-top: 20px;

      .chart-item {
        flex: 1;
        padding: 20px 30px 20px 30px;
        min-width: 400px;
        height: 400px;
        background: #fff;
        display: flex;
        flex-direction: column;

        .chart-title {
          font-size: 20px;
          font-weight: bold;
          color: #000000;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            display: block;
            height: 20px;
            width: 5px;
            top: 3px;
            left: -13px;
            background: #004DAB;
          }
        }

        .chart-desc {
          font-size: 14px;
          font-weight: 500;
          color: #606266;
        }

        .chart-view {
          margin-top: 10px;
          flex: 1;
          height: 320px;
        }
      }
    }
  }

  .right-wrap {
    flex: var(--flex-25);
    background: #fff;
    min-width: 300px;
    min-height: calc(100vh - 150px);
    overflow-y: scroll;
    padding: 20px 30px;
    box-sizing: border-box;
    font-family: DIN;

    &::-webkit-scrollbar {
      display: none;
    }

    .need-wrap {
      display: flex;
      justify-content: center;
      flex-direction: column;
    }

    .need-empty {
      margin: 50px auto 0px;
    }

    .need-tags {
      margin-top: 30px;
      text-align: center;
      font-size: 14px;
      font-weight: 500;
      color: #606266;
    }

    .need-tilte {
      font-size: 20px;
      font-weight: bold;
      color: #000000;
      position: relative;
      margin-bottom: 30px;

      &::after {
        content: "";
        position: absolute;
        display: block;
        height: 20px;
        width: 5px;
        top: 3px;
        left: -13px;
        background: #004DAB;
      }
    }

    .need-item {
      width: 100%;
      border-radius: 8px 8px 8px 8px;
      opacity: 1;
      border: 1px solid #dcdfe6;
      padding: 10px 20px;
      margin-bottom: 16px;

      .need-logo {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: #606266;

        img {
          width: 38px;
          height: 38px;
          margin-right: 10px;
        }
      }

      .need-handle {
        margin-top: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .need-total {
        margin-left: 48px;
        color: #606266;

        span {
          font-size: 30px;
          color: #000000;
          font-weight: bold;
        }
      }

      .need-submit {
        width: 80px;
        height: 32px;
        background: #004DAB;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        border-radius: 4px;
        cursor: pointer;
      }
    }
  }
}

.chart-data {
  color: #515a6e;
  font-weight: 800;
  font-size: 16px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.fc-54 {
  color: #d54135;
  margin-left: 6px;
}

.fc-67 {
  color: #67c23a;
  margin-left: 6px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-10 {
  margin-right: 10px;
}

.m-10 {
  margin: 0 20px;
}

.m-20 {
  margin: 0 20px;
}

.mt-20 {
  margin-top: 0px;
}

.m-b20 {
  margin-bottom: 0px;
}

@media only screen and (max-width: 1620px) {
  .app-container {
    --flex-75: 1;
    --flex-25: 1;
    zoom: 0.9;
  }

  .mr-20 {
    margin-right: 0px !important;
  }

  .mr-10 {
    margin-right: 0px !important;
  }

  .m-b20 {
    margin-bottom: 20px;
  }

  .left-wrap {
    margin-right: 0px;
  }

  .m-10 {
    margin: 20px 0px;
  }

  .mt-20 {
    margin-top: 20px;
  }

  .app-container {
    .left-wrap {
      .board-list {
        .board-item {
          margin-bottom: 0px;
        }

        .board-item1 {
          min-width: 400px;
          background: #fff;
        }
      }
    }
  }
}

@media only screen and (max-width: 1200px) {
  .app-container {
    .left-wrap {
      .left-emty {
        min-height: 0;
      }

      .board-list {
        .board-item {
          margin-bottom: 0px;
        }

        .board-item1 {
          min-width: 300px;
          background: #fff;
        }
      }
    }
  }

  .m-b20 {
    margin-bottom: 20px;
  }

  .m-10 {
    margin-right: 0px;
  }
}

@media only screen and (max-width: 1000px) {
  .app-container {
    .left-wrap {
      .board-list {
        .board-item {
          margin-bottom: 0px;
        }

        .board-item1 {
          min-width: 300px;
          background: #fff;
        }
      }
    }
  }

  .m-b20 {
    margin-bottom: 20px;
  }

  .m-10 {
    margin-right: 0px;
  }
}

@media only screen and (max-width: 800px) {
  .app-container {
    .left-wrap {
      .left-emty {
        display: none;
      }

      .board-list {
        .board-item {
          margin-bottom: 0px;
        }

        .board-item1 {
          min-width: 100%;
          background: #fff;
        }
      }
    }
  }

  .right-wrap {
    min-height: auto;
  }

  .m-10 {
    margin-right: 0px;
  }
}

@media (max-width: 750px) {
  .app-container {
    --flex-75: 1;
    --flex-25: 1;
    overflow-x: auto;
    flex-direction: column;
    flex-direction: column-reverse;

    .left-wrap {
      margin-right: 0px;
      margin-top: 20px;

      .left-emty {
        display: none;
      }

      .board-list {
        .board-item {
          margin-bottom: 0px;
        }

        .board-item1 {
          min-width: 100%;
          background: #fff;
        }
      }
    }

    .right-wrap {
      min-height: auto;
    }
  }

  .m-b20 {
    margin-bottom: 20px;
  }

  .m-10 {
    margin-right: 0px;
  }
}
</style>
