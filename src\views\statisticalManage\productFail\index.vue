<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="推广名称" prop="time">
        <el-input
          size="mini"
          v-model="queryParams.productIdOrName"
          @keyup.enter.native="handleQuery"
          clearable
          placeholder="请输入推广名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="商务" prop="time">
        <el-input
          size="mini"
          v-model="queryParams.nickName"
          @keyup.enter.native="handleQuery"
          clearable
          placeholder="请输入商务名称"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="dataList" @sort-change="handleSortChange">
      <el-table-column
        label="推广ID"
        prop="productId"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        align="center"
      />

      <el-table-column label="推广名称" prop="productName" align="center" />
      <el-table-column label="商务名称" prop="nickName" align="center" />
      <el-table-column
        label="最新推广价格"
        prop="cooperationCost"
        align="center"
      />
      <el-table-column
        label="有效曝光数"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="successTotal"
        align="center"
      />
      <el-table-column
        label="接单失败数"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="failTotal"
        align="center"
      />
      <el-table-column label="失败原因" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              size="mini"
              type="primary"
              @click="getProductDeatil(row)"
            >
              查看详情</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->

    <pagination
      v-show="total > 0"
      :total="total"
      :page-sizes="[30, 50, 80, 100]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script>
import { getMatchingStatisticslist } from "@/api/statisticalManage";

export default {
  data() {
    return {
      value1: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      dataList: [],
      total: 0,
      queryParams: {
        nickName: "",
        pageNum: 1,
        pageSize: 50,
        productIdOrName: "",
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
      },
    };
  },
  methods: {
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },

    //搜索
    handleQuery() {
      if (this.value1 !== null) {
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }

      this.getList();
    },
    getProductDeatil(row) {
      let data = {
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime,
        productId: row.productId,
        productName: row.productName,
      };

      this.$router.push({
        path: "/statisticalManage/productMatchFailDetail",
        query: data,
      });
    },
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    getList() {
      getMatchingStatisticslist(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total;
      });
    },
  },

  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
