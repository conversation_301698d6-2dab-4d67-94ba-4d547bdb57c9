import request from '@/utils/request'

/**
 * 渠道用户资质比例分析查询
 * @param {Object} data 
 * @returns {Promise}
 */
export function getQualificationProportionAnalysis(data) {
  return request({
    url: '/stat/consumer/qualificationProportionAnalysis',
    method: 'post',
    data
  })
}

/**
 * 获取用户资格比例分析设置
 * @returns {Promise}
 */
export function getQualificationProportionAnalysisSetting() {
  return request({
    url: '/stat/consumer/qualificationProportionAnalysisSetting',
    method: 'get'
  })
}

/**
 * 保存用户资格比例分析设置
 * @param {Array} data - 配置项数组
 * @returns {Promise}
 */
export function saveQualificationProportionAnalysisSetting(data) {
  return request({
    url: '/stat/consumer/qualificationProportionAnalysisSetting',
    method: 'post',
    data
  })
} 