<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="状态" clearable size="small">
          <el-option value="" label="全部"></el-option>
          <el-option value="1" label="正常"></el-option>
          <el-option value="2" label="禁用">禁用</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商务" prop="status">
        <el-select v-model="queryParams.commerce" filterable clearable placeholder="请选择商务">
          <el-option v-for="item in affairsList" :key="item.userId" :label="item.nickName" :value="item.userId">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="乙方" prop="partyUsername">
        <el-input v-model="queryParams.partyUsername" placeholder="乙方名称" clearable size="small"
          @keyup.enter.native="handleQuery" />

      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>

      </el-form-item>
    </el-form>
    <el-button icon="el-icon-plus" class="add-btn" type="primary" size="mini" v-hasPermi="['partyb:partybList:add']"
      @click="addPartyB">添加
    </el-button>
    <el-table v-loading="loading" :data="partyBList">
      <el-table-column label="乙方ID" align="center" prop="id" width="100" />
      <el-table-column label="乙方姓名" align="center" prop="partyUsername" />
      <el-table-column label="预付金额" prop="prepaidSum" align="center" />
      <el-table-column label="乙方可用余额" prop="availableBalance" align="center" />
      <el-table-column label="已退金额" prop="refundableAmount" align="center" />
      <el-table-column label="商务" prop="contactName" align="center" />
      <el-table-column label="状态" align="center" width="200">
        <template slot-scope="{ row }">
          <el-switch active-color="#004DAB" v-model="row.status" active-text="正常" inactive-text="禁用" :active-value="1"
            :inactive-value="2" @change="changeStatus($event, row.id)" />
          <div>
            <!-- {{ row.status == 1 ? "正常" : "禁用" }} -->
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="340" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <span size="mini" class="f_c005 c-p mr-8" @click="handleEdit(scope.row)"
            v-hasPermi="['partyb:partyb:edit']">修改信息</span>
          <span size="mini" class="f_c005 c-p mr-8" @click="handleMoney(scope.row)" v-hasPermi="['partyb:partyb:refund']"
            v-if="hasBool('partyb:partyb:refund') || hasBool('partyB:requestMoeny:add')">款项申请</span>
          <span size="mini" class="f_c005 c-p mr-8" @click="handleSkip(scope.row)"
            v-if="hasBool('partyB:requestMoeny:list') || hasBool('loan:finance:refund:list')">款项记录</span>
          <!-- <span size="mini" class="f_c005 c-p" @click="handleRfoundDeatil(scope.row)"
            v-hasPermi="['loan:finance:refund:list']">退款记录</span> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <!-- 新增乙方 -->
    <el-dialog title="新增乙方" :visible.sync="partyBvisible" width="900px" append-to-body @close="addcancel" center
      :close-on-click-modal="false">
      <el-form :model="formData" :rules="rules" label-width="80px" ref="addpartys">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="乙方名称" prop="partyUsername">
              <el-input v-model="formData.partyUsername" placeholder="请输入乙方名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作方式" prop="cooperationType">
              <el-select v-model="formData.cooperationType" style="width:100%" placeholder="请选择合作方式" clearable>
                <el-option :value="1" label="CAP"></el-option>
                <el-option :value="2" label="CPS"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="乙方类型" prop="partyTypes">
              <el-select v-model="formData.partyTypes" placeholder="请选择合作方式" style="width:100%" clearable>
                <el-option :value="1" label="个人"></el-option>
                <el-option :value="2" label="公司"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作价格" prop="cooperativePrice">
              <el-input type="number" v-model="formData.cooperativePrice" placeholder="请输入合作价格" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactName">
              <el-input v-model="formData.contactName" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户行" prop="openAnBank">
              <el-input v-model="formData.openAnBank" placeholder="请输入开户行" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="收款账户" prop="worldFirst">
              <el-input v-model="formData.worldFirst" placeholder="请输入收款账户" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款账号" prop="worldNumber">
              <el-input :change="
                (this.formData.worldNumber =
                  this.formData.worldNumber.replace(/[^\d]/g, ''))
              " :maxlength="50" v-model="formData.worldNumber" placeholder="请输入收款账号" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPartyBForm">确 定</el-button>
        <el-button @click="addcancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 修改乙方信息 -->
    <el-dialog title="修改信息" :visible.sync="partyEdit" width="900px" append-to-body @close="editCancle" center
      :close-on-click-modal="false">
      <el-form ref="editPart" :model="Edit" :rules="rules" label-width="80px">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="乙方名称" prop="partyUsername">
              <el-input v-model="Edit.partyUsername" placeholder="请输入乙方名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作方式" prop="cooperationType">
              <el-select v-model="Edit.cooperationType" style="width:100%" placeholder="请选择合作方式" clearable size="small">
                <el-option :value="1" label="CAP"></el-option>
                <el-option :value="2" label="CPS"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="乙方类型" prop="partyTypes">
              <el-select v-model="Edit.partyTypes" placeholder="请选择合作方式" style="width:100%" clearable size="small">
                <el-option :value="1" label="个人"></el-option>
                <el-option :value="2" label="公司"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合作价格" prop="cooperativePrice">
              <el-input type="number" v-model="Edit.cooperativePrice" placeholder="请输入合作价格" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactName">
              <el-input v-model="Edit.contactName" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户行" prop="openAnBank">
              <el-input v-model="Edit.openAnBank" placeholder="请输入开户行" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="收款账户" prop="worldFirst">
              <el-input v-model="Edit.worldFirst" placeholder="请输入收款账户" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款账号" prop="worldNumber">
              <el-input :maxlength="50" v-model="Edit.worldNumber" placeholder="请输入收款账号" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEdit">确 定</el-button>
        <el-button @click="editCancle">取 消</el-button>
      </div>
    </el-dialog>
    <drawer :drawer.sync="showDrawer" :partybId="partybId" :row="row" @getList="getList"></drawer>
    <drawerList :drawer.sync="showDrawerList" :partybId="partybId" :row="row"></drawerList>
  </div>
</template>

<script>
import {
  getPartyBlilst,
  getAffairsList,
  addPartyBuser,
  ApppartyEdit,
  getselectOdd,
  getIib,
} from "@/api/partyB";
import { hasBool } from "@/directive/permission/hasBool"
import drawer from "./drawer.vue"
import drawerList from "./drawerList.vue"
export default {
  name: "Partyblist",

  data() {

    return {
      loading: false,
      partyBvisible: false,
      partyEdit: false,
      showDrawer: false,
      showDrawerList: false,
      total: 0,
      partybId: "",
      row: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        partyUsername: "",
        commerce: "",
        status: "",
      },
      formData: {
        contactName: "",
        cooperationType: "",
        partyTypes: "",
        partyUsername: "",
        worldFirst: "",
        cooperativePrice: "",
        openAnBank: "",
        worldNumber: "",
      },
      affairsList: [],
      partyBList: [],
      Edit: {
        contactName: "",
        cooperationType: "",
        partyTypes: "",
        partyUsername: "",
        worldFirst: "",
        cooperativePrice: "",
        openAnBank: "",
        worldNumber: "",
      },
      ForRefund: [],
      rules: {
        partyUsername: [
          {
            required: true,
            message: "请输入乙方名称",
            trigger: "blur",
          },
        ],

        partyTypes: [
          {
            required: true,
            message: "请选择乙方类型",
            trigger: "blur",
          },
        ],

        cooperationType: [
          {
            required: true,
            message: "请选择合作方式",
            trigger: "blur",
          },
        ],

        contactName: [
          {
            required: true,
            message: "请输入联系人",
            trigger: "blur",
          },
        ],

        worldFirst: [
          {
            required: true,
            message: "请输入收款账户",
            trigger: "blur",
          },
        ],

        worldNumber: [
          {
            required: true,
            message: "请输入收款账号",
            trigger: "blur",
          },
        ],
        openAnBank: [
          {
            required: true,
            message: "请输入开户行",
            trigger: "blur",
          },
        ],
        cooperativePrice: [
          {
            required: true,
            message: "请输入合作价格",
            trigger: "blur",
          },
        ],
      },
      refund: {
        cooperativePrice: [
          {
            required: true,
            message: "请输入退款金额",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    //跳转ID请款查询
    handleSkip(row) {
      this.showDrawerList = true
      this.row = row
      return
      this.$router.push(`/partyB/request?id=${row.id}`);
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },


    handleMoney(row) {
      this.row = row
      this.partybId = row.id
      this.showDrawer = true
    },


    //状态管理
    changeStatus(e, id) {
      var zhuangtai = {
        id: id,
        status: e,
      };
      if (e == 1) {
        getIib(zhuangtai).then((res) => {
          if (res.code == 200) {
            this.getList();
            this.$message.success("该用户已恢复");
          }
        });
      } else if (e == 2) {
        getIib(zhuangtai).then((res) => {
          if (res.code == 200) {
            this.getList();
            this.$message.success("该用户已停用");
          }
        });
      }
    },
    //乙方修改
    handleEdit(e) {
      this.Edit.id = e.id;
      getselectOdd({ id: e.id }).then((res) => {
        if (res.code == 200) {
          for (let key in res.data) {
            if (Object.keys(this.Edit).includes(key)) {
              this.Edit[key] = res.data[key];
            }
          }
          this.partyEdit = true;
        }
      });
    },
    submitEdit() {
      this.$refs.editPart.validate((e) => {
        if (e) {
          ApppartyEdit(this.Edit).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.$refs["editPart"].resetFields();
              this.partyEdit = false;
              this.getList();
            }
          });
        }
      });
    },

    //确定新增乙方
    submitPartyBForm() {
      this.$refs.addpartys.validate((valid) => {
        if (valid) {
          addPartyBuser(this.formData).then((res) => {
            if (res.code == 200) {
              this.$message.success("添加成功");
              this.partyBvisible = false;
              this.$refs["addpartys"].resetFields();
              this.getList();
            }
          });
        }
      });
    },
    addcancel() {
      this.$refs["addpartys"].resetFields();
      this.partyBvisible = false;
    },
    editCancle() {
      this.$refs["editPart"].resetFields();
      this.partyEdit = false;
    },
    addPartyB() {
      this.partyBvisible = true;
    },
    //获取乙方列表数据
    getList() {
      this.loading = true;

      if (this.$store.getters.userInfo.userName === 'dingcan') {
        this.queryParams.commerce = this.affairsList.find(item => item.nickName === 'dingcan').userId
      }

      getPartyBlilst(this.queryParams)
        .then((res) => {
          this.partyBList = res.rows;
          this.total = res.total;
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          console.log(err);
        });
    },
    handleRfoundDeatil(row) {
      this.$router.push(`/partyB/refoundDeatil?id=${row.id}`)
    },
    async initPage() {
      const res = await getAffairsList({
        name: "",
      })
      this.affairsList = res.data;
    },
  },
  components: {
    drawer,
    drawerList
  },
  async mounted() {
    await this.initPage();
    if (this.$route.query.name) {
      this.queryParams.partyUsername = this.$route.query.name
    }
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.add-btn {
  // margin-bottom: 10px;   0
  transform: translateY(-12px);
}
</style>
