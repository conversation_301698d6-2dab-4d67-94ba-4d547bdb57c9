<script>
import { getMatchingPriceList } from "@/api/productManage/product";
import AddProductMatchingPrice from "@/views/productManage/productMatchingPriceList/components/AddProductMatchingPrice.vue";
import dayjs from 'dayjs'

export default {
  name: "productMatchingPriceList",
  components: { AddProductMatchingPrice },

  data() {
    return {
      form: {
        dateRange: [],
        productId: "",
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      total: 0,
    };
  },

  methods: {
    async handleQuery() {
      this.form.pageNum = 1;
      await this.getTableData();
    },

    handleAdd() {
      this.$refs.addProductMatchingPrice.open();
    },

    async getTableData() {
      const params = this.getParams();
      const res = await getMatchingPriceList(params);
      this.tableData = res.data.rows;
      this.total = res.data.total;
    },

    getParams() {
      return {
        startTime: this.form.dateRange?.[0] || "",
        endTime: this.form.dateRange?.[1] || "",
        productId: this.form.productId || "",
        pageNum: this.form.pageNum,
        pageSize: this.form.pageSize,
      };
    },

    // 设置日期默认值为今天
    setDefaultDate() {
      const start = dayjs().format('YYYY-MM-DD 00:00:00')
      const end = dayjs().format('YYYY-MM-DD 23:59:59')

      this.form.dateRange = [start, end]
    },
  },

  created() {
    this.setDefaultDate()
    this.handleQuery();
  },
};
</script>

<template>
  <div class="app-container">
    <el-form :model="form" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="dateRange" size="small">
        <el-date-picker
          v-model="form.dateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
          @clear="handleQuery"
          placeholder="选择日期"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="产品id" prop="productId">
        <el-input
          v-model.trim="form.productId"
          placeholder="请输入产品id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery"
          >查询</el-button
        >
      </el-form-item>
      <el-form-item v-hasPermi="['productMatchPrice:add']">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border>
      <el-table-column prop="productId" label="产品id"></el-table-column>
      <el-table-column prop="productName" label="产品名称"></el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
      <el-table-column prop="matchPrice" label="匹配价格"></el-table-column>
      <el-table-column prop="createBy" label="创建人"></el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="form.pageNum"
      :limit.sync="form.pageSize"
      @pagination="getTableData"
    />

    <AddProductMatchingPrice ref="addProductMatchingPrice" @closed="getTableData"/>
  </div>
</template>

<style scoped lang="scss"></style>
