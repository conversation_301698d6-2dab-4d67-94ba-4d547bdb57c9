<template>
  <div ref="chartContainer" style="height: 400px; width: 100%; margin-top: 20px;"></div>
</template>

<script>
import * as echarts from 'echarts';
import _ from 'lodash';

export default {
  name: 'StatsChart',
  data() {
    return {
      chart: null,
      _throttledResize: null
    };
  },
  mounted() {
    this.initChart();
    this._throttledResize = _.throttle(this.resizeChart, 200);
    window.addEventListener('resize', this._throttledResize);
  },
  beforeDestroy() {
    this.disposeChart();
    window.removeEventListener('resize', this._throttledResize);
  },
  methods: {
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartContainer);
      }
    },
    resizeChart() {
      this.chart && this.chart.resize();
    },
    disposeChart() {
      if (this.chart) {
        this.chart.dispose();
        this.chart = null;
      }
    },
    renderChart(data) {
      if (!this.chart) return;
      this.chart.resize();
      
      // 处理空数据
      if (!data || data.length === 0) {
        this.chart.clear();
        return;
      }

      const dates = data.map(item => item.groupKey);
      const newValueData = data.map(item => item.newValue);
      const oldValueData = data.map(item => item.oldValue);

      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50,50,50,0.9)',
          borderRadius: 6,
          textStyle: { color: '#fff', fontSize: 14 },
          axisPointer: { type: 'cross', label: { backgroundColor: '#6a7985' } }
        },
        legend: {
          data: ['新资质', '旧资质'],
          left: 'center',
          top: 20,
          icon: 'circle',
          textStyle: { fontWeight: 'bold', fontSize: 14 }
        },
        grid: {
          left: '3%',
          right: '4%',
          top: 60,
          bottom: '8%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: dates,
          axisLabel: {
            fontWeight: 'bold',
            fontSize: 13,
            color: '#666',
            margin: 14,
            interval: dates.length <= 5 ? 0 : Math.ceil(dates.length / 10),
            rotate: 0,
            align: 'center',
            hideOverlap: true
          },
          axisTick: {
            alignWithLabel: true
          },
          axisLine: { lineStyle: { color: '#409EFF', width: 2 } }
        },
        yAxis: {
          type: 'value',
          splitLine: { show: true, lineStyle: { color: '#eee', type: 'dashed' } },
          axisLabel: { fontWeight: 'bold', fontSize: 13, color: '#666' },
          axisLine: { show: false }
        },
        series: [
          {
            name: '新资质',
            type: 'line',
            data: newValueData,
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#409EFF',
              borderColor: '#fff',
              borderWidth: 2,
              shadowColor: 'rgba(64,158,255,0.3)',
              shadowBlur: 8
            },
            lineStyle: {
              color: '#409EFF',
              width: 4
            },
            areaStyle: {
              color: 'rgba(64,158,255,0.08)'
            },
            emphasis: {
              focus: 'series',
              itemStyle: { borderColor: '#409EFF', borderWidth: 4 }
            }
          },
          {
            name: '旧资质',
            type: 'line',
            data: oldValueData,
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#F56C6C',
              borderColor: '#fff',
              borderWidth: 2,
              shadowColor: 'rgba(245,108,108,0.2)',
              shadowBlur: 8
            },
            lineStyle: {
              color: '#F56C6C',
              width: 4
            },
            areaStyle: {
              color: 'rgba(245,108,108,0.08)'
            },
            emphasis: {
              focus: 'series',
              itemStyle: { borderColor: '#F56C6C', borderWidth: 4 }
            }
          }
        ]
      });
    },
    clearChart() {
      this.chart && this.chart.clear();
    }
  }
};
</script> 