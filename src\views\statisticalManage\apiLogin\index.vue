<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" size="small">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道" prop="dateRange">
        <el-input type="text" v-model.number="queryParams.channelId" size="small" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>
    <el-table border :data="dataList">
      <el-table-column label="日期时间戳" prop="createDate" align="center">
        <template slot-scope="{row}">
          <div>
            {{ timestampTotime(row.createDate) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="渠道ID" prop="channelId" align="center" />
      <el-table-column label="总数量" prop="allCount" align="center" />
      <el-table-column label="实际数量" prop="realCount" align="center" />
    </el-table>
  </div>
</template>

<script>
import { getApiRegister } from "@/api/statisticalManage"
export default {
  data() {
    return {
      dateRange: [this.getWeekTime(), this.getTime()],
      dataList: [],
      queryParams: {
        startDate: this.getWeekTime(),
        endDate: this.getTime(),
        channelId:""

      }
    }
  },
  methods: {
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    getWeekTime() {
      let wekDay = Date.now() - 86400 * 7 * 1000;
      var date = new Date(parseInt(wekDay));
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    timestampTotime(time) {
      var date = new Date(time * 1000);
      let year = date.getFullYear();
      let moth = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);
      let day = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate());
      return `${year}-${moth}-${day}`
    },
    getList() {
      getApiRegister(this.queryParams).then(res => {
        this.dataList = res || []
      })
    },
    handleQuery() {
      if (this.dateRange && this.dateRange.length && this.dateRange !== null) {
        this.queryParams.startDate = this.dateRange[0];
        this.queryParams.endDate = this.dateRange[1];
      } else {
        this.queryParams.startDate = "";
        this.queryParams.endDate = "";
      }
      this.getList();
    }
  },
  mounted() {
    this.getList()
  }

}
</script>

<style lang="scss" scoped></style>
