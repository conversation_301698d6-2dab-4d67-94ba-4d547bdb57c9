<template>
  <div class="app-container">
    <el-form
      :model="formData"
      :rules="ruels"
      ref="formData"
      label-position="left"
      label-width="130px"
    >
      <el-form-item label="请求地址" prop="url">
        <el-input
          size="small"
          placeholder="请输入请求地址"
          v-model="formData.url"
        ></el-input>
      </el-form-item>
      <el-form-item label="请求appCode" prop="appCode">
        <el-input
          size="small"
          placeholder="请输入请求appCode"
          v-model="formData.appCode"
        ></el-input>
      </el-form-item>
      <el-form-item label="请求appKey" prop="appKey">
        <el-input
          size="small"
          placeholder="请输入请求appKey"
          v-model="formData.appKey"
        ></el-input>
      </el-form-item>
      <el-form-item label="请求appSecret" prop="appSecret">
        <el-input
          size="small"
          placeholder="请输入请求appSecret"
          v-model="formData.appSecret"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitFormData">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  getThreeElementsConfig,
  editThreeElementsConfig,
} from "@/api/threeParty/radersetting";
export default {
  name: "RadarSetting",
  data() {
    return {
      ruels: {
        url: [{ required: true, message: "请输入请求地址", trigger: "blur" }],
        appCode: [
          { required: true, message: "请输入请求appCode", trigger: "blur" },
        ],
        appKey: [
          { required: true, message: "请输入请求appKey", trigger: "blur" },
        ],
        appSecret: [
          { required: true, message: "请输入请求appSecret", trigger: "blur" },
        ],
      },
      formData: {
        appCode: "",
        appKey: "",
        appSecret: "",
        url: "",
      },
    };
  },
  methods: {
    initPage() {
      getThreeElementsConfig().then((res) => {

        this.formData.url = res.data.url;
        this.formData.appCode = res.data.appCode;
        this.formData.appKey = res.data.appKey;
        this.formData.appSecret = res.data.appSecret;

      });
    },
    submitFormData() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          editThreeElementsConfig(this.formData).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.initPage();
            }
          });
        }
      });
    },
  },
  mounted() {
    this.initPage();
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input--small .el-input__inner {
  width: 500px;
}
</style>