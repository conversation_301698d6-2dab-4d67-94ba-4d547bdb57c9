/**
 * 平台相关标签工具函数
 */

/**
 * 获取平台标签颜色
 * @param {number} platformType 平台类型ID
 * @returns {string} 返回颜色代码
 */
export function getPlatformTagColor(platformType) {
  // 预定义的基础颜色数组 - 适中的配色
  const baseColors = [
    '#52C41A',  // 清新绿
    '#4B7BE5',  // 优雅蓝
    '#FA8C16',  // 温暖橙
    '#F15A5A',  // 玫瑰红
    '#13A8A8',  // 青蓝色
    '#597EF7',  // 靛蓝色
    '#2F9688',  // 翠绿色
    '#E8943C',  // 琥珀色
    '#3B7EC9',  // 宝蓝色
    '#D48806'   // 赭石色
  ];
  
  // 使用平台ID作为索引来选择颜色
  const colorIndex = (platformType - 1) % baseColors.length;
  return baseColors[colorIndex];
} 