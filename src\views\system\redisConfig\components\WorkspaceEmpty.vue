<template>
  <div class="workspace-empty">
    <div class="empty-icon">
      <i :class="icon" :style="iconStyle"></i>
    </div>
    <div class="empty-content">
      <h3>{{ title }}</h3>
      <p>{{ description }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WorkspaceEmpty',
  props: {
    icon: {
      type: String,
      default: 'fas fa-file-alt'
    },
    iconStyle: {
      type: String,
      default: 'font-size: 64px;'
    },
    title: {
      type: String,
      default: '选择配置项'
    },
    description: {
      type: String,
      default: '请从左侧选择一个配置项来查看和编辑详情'
    }
  }
}
</script>

<style lang="scss" scoped>
.workspace-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #9aa0a6;

  .empty-icon {
    margin-bottom: 24px;
  }

  .empty-content {
    text-align: center;

    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 500;
      color: #5f6368;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #9aa0a6;
    }
  }
}
</style>