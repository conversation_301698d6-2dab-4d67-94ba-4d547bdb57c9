import request from '@/utils/request'
//  获取留言列表
export const getReplyList = (data) => {
  return request({
    url: '/loan/reply/list',
    method: "get",
    params: data
  })
}
//回复留言
export const replyOne = (data) => {
  return request({
    url: "/loan/reply/edit",
    method: 'post',
    data
  })
}

//获取留言的联系方式
export const getUserPhone = (data) => {
  return request({
    url: "/loan/reply/getphone",
    method: "get",
    params: data
  })
}

//获取用户申请的产品
export const getUserApplyProduct = (data) => {
  return request({
    url: "/loan/feedback/getApply",
    method: 'get',
    params: data
  })
}

//获取产品关联的商户
export const getPartyFirstAdmin = (data) => {
  return request({
    url: '/loan/feedback/getPartyFirst',
    method: 'get',
    params: data
  })
}

//新增投诉
export const addComplaintOne = (data) => {
  return request({
    url: "/loan/feedback/add",
    method: "post",
    data
  })
}

//投诉列表
export const getComplaintList = (data) => {
  return request({
    url: "/loan/feedback/list",
    method: 'get',
    params: data
  })
}


//投诉详情
export const getFeedbackDetail = (data) => {
  return request({
    url: "/loan/feedback/detail",
    method: "get",
    params: data
  })
}

//初次分配
export const addDistributionOne = (data) => {
  return request({
    url: "/loan/feedback/distribution",
    method: "post",
    data
  })
}


//用户反馈未解决
export const changenotSolved = (data) => {
  return request({
    url: '/loan/feedback/notSolved',
    method: 'post',
    data
  })
}


//用户反馈已解决
export const changetSolved = (data) => {
  return request({
    url: "/loan/feedback/solved",
    method: "post",
    data
  })
}

//取消用户投诉
export const delFeedback = (data) => {
  return request({
    url: '/loan/feedback/close',
    method: 'post',
    data
  })
}
