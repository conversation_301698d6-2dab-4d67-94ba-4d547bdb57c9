<template>
  <div class="loan-quota-content">
    <!-- 贷款额度标题 -->
    <div class="loan-quota-title">可用额度(元)</div>

    <!-- 贷款额度金额 -->
    <div class="loan-quota-amount">
      {{ configData.loanAmount || '2,094.01' }}
    </div>

    <!-- 总计额度说明 -->
    <div class="loan-quota-total">
      <span class="loan-quota-total-text">总计额度</span>
      <span class="loan-quota-total-amount">{{ configData.loanAmount || '2,094.01' }}</span>
      <img src="https://jst.oss-utos.hmctec.cn/common/path/eb4874d6736c47cdb0f195b46a931e4c.png"
        class="loan-quota-total-icon" />
    </div>

    <!-- 备用金区域 -->
    <div class="loan-quota-reserve">
      <div class="reserve-item-label">
        备用金
      </div>
      <div class="reserve-item-value">
        <div class="reserve-item-value-label">{{ configData.reserveDesc || '已获得额外专享额度' }}</div>
        <img src="https://jst.oss-utos.hmctec.cn/common/path/64c4ef16432145088e5bbd424f7fdd2c.png"
          class="icon-arrow-right" alt="">
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoanQuotaContent',
  props: {
    configData: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped lang="scss">
.loan-quota-content {
  margin: 38px auto 20px;
  padding-top: 52px;
  width: 695px;
  background: #FFFFFF;
  border-radius: 20px 20px 20px 20px;

  .loan-quota-title {
    font-family: "SourceHanSansSC-Regular";
    font-weight: 700;
    font-size: 32px;
    color: #333333;
    line-height: 46px;
    text-align: center;
  }

  .loan-quota-amount {
    margin: 6px 0 21px;
    font-family: "DIN";
    font-weight: 500;
    font-size: 62px;
    color: #333333;
    line-height: 75px;
    text-align: center;
  }

  .loan-quota-total {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .loan-quota-total-text {
      font-family: "SourceHanSansSC-Regular";
      font-weight: 400;
      font-size: 28px;
      color: #999999;
      line-height: 41px;
    }

    .loan-quota-total-amount {
      font-family: DIN;
      font-weight: 500;
      font-size: 34px;
      color: #999999;
      line-height: 41px;
    }

    .loan-quota-total-icon {
      width: 25px;
      height: 25px;
      transform: translateY(3px);
    }
  }

  .loan-quota-reserve {
    margin-top: 50px;
    padding: 0 20px 33px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;

    .reserve-item-label {
      font-weight: 400;
      font-size: 28px;
      color: #333333;
      line-height: 41px;
    }

    .reserve-item-value {
      display: flex;
      align-items: center;
      gap: 8px;

      .reserve-item-value-label {
        font-weight: 400;
        font-size: 28px;
        color: #2A83FF;
        line-height: 41px;
      }

      .icon-arrow-right {
        width: 15px;
        height: 23px;
        transform: translateY(3px);
      }
    }
  }
}
</style> 