<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"

          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="产品" prop="channelIds">
          <el-select clearable v-model="channelIds" filterable collapse-tags multiple placeholder="请选择产品">
            <el-option
              v-for="item in channelList"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="list">
      <el-table-column label="名称" prop="name" align="center" />
      <el-table-column label="总数" prop="total" align="center" />
      <el-table-column label="反馈状态">
        <template slot-scope="{ row }">
          <div>
            已接通:{{ row.connectedPercentage * 100 }}%
            <br />
            未接通:{{ row.unConnectedPercentage * 100 }}%
            <br />
            拒接多次:{{ row.refuseTimesPercentage * 100 }}%
            <br />
            空号:{{ row.vacantNumberPercentage * 100 }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column label="反馈质量">
        <template slot-scope="{ row }">
          <div>
            异地:{{ row.placeOtherPercentage * 100 }}%
            <br />
            有资质:{{ row.talentedPercentage * 100 }}%
            <br />
            无意向:{{ row.unintentionalPercentage * 100 }}%
            <br />
            加微信:{{ row.addWechatPercentage * 100 }}%
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getProductChannel,getFeedbackProductList} from "@/api/statisticalManage";

export default {
  name: "channelQuality",
  data() {
    return {
      dateRange: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      queryParams: {
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
        productIds:"",
      },
      channelIds:[],
      list: [],
      channelList:[]
    };
  },
  methods: {
    handleQuery() {
      if (this.dateRange && this.dateRange.length) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }

      this.getList();
    },
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    getList() {
      this.queryParams.productIds=this.channelIds.join(",")
      getProductChannel(this.queryParams).then((res) => {
        this.list = res.data;
      });
    },
  },
  mounted() {
    this.getList();
    getFeedbackProductList().then(res=>{

      this.channelList=res.data;
    })
  },
};
</script>

<style lang="scss" scoped></style>
