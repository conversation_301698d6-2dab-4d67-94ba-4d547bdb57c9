<template>
  <el-drawer title="款项管理" :visible.sync="drawer_" size="665px" :wrapper-closable="false"  :direction="direction" @close="handCloseDrawer">
    <div class="drawer-wrap">
      <div class="drawer-tabs flex">
        <div :class="['drawer-tabs-item c-p', type == 1 ? 'active' : '']" @click="handleToggle(1)"
          v-if="hasBool('loan:partya:recharge')">入账金额</div>
        <div :class="['drawer-tabs-item c-p', type == 2 ? 'active' : '']" @click="handleToggle(2)"
          v-if="hasBool('loan:partya:refund')">商户退款</div>
      </div>
      <template v-if="type == 1">
        <el-form ref="formChargeRef" :model="formChargeData" :rules="rulesSbu" :key="type" label-width="80px" label-position="left">
          <el-form-item label="入账金额" prop="price">
            <el-input oninput="value=value.replace(/[^0-9.]/g,'')" :disabled="formChargeData.deposit"
              v-model="formChargeData.price" placeholder="请输入入账金额" />
          </el-form-item>
          <!-- <el-form-item label="打款方" prop="payerName">
          <el-input
            v-model="formChargeData.payerName"
            placeholder="请输入打款方"
          />
        </el-form-item> -->
          <el-form-item label="收款主体" prop="payeeSubjectId">
            <el-select v-model="formChargeData.payeeSubjectId" placeholder="请选择收款主体" style="width:100%">
              <el-option v-for="item in subjectlist" :key="item.id" :value="item.id" :label="item.subjectName">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="入账时间" prop="rechargeDate">
            <el-date-picker :picker-options="pickerOptions" v-model="formChargeData.rechargeDate" type="datetime"
              align="right" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择入账时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="入账凭证" prop="file">
            <el-upload class="avatar-uploader" action="" :show-file-list="false" :auto-upload="false"
              :on-change="changeUpImg">
              <img v-if="imageUrl" style="vertical-align: middle; max-height: 100px" :src="imageUrl" />
              <div class="avatar-uploader-tips" v-else>
                <i class="el-icon-plus"></i>
                <span>点击上传凭证</span>
              </div>
              <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过2M</div>
            </el-upload>
          </el-form-item>
          <el-row :gutter="24">
            <el-col :span="12"> <el-form-item label="是否需要发票" label-width="100px" prop="invoicenStatus">
                <el-radio v-model="formChargeData.invoicenStatus" label="0">是</el-radio>
                <el-radio v-model="formChargeData.invoicenStatus" label="1">否</el-radio>
              </el-form-item></el-col>
            <el-col :span="12"> <el-form-item label="是否需要缴纳保证金" label-width="140px" prop="deposit">
                <el-radio v-model="formChargeData.deposit" :label="true">是</el-radio>
                <el-radio v-model="formChargeData.deposit" :label="false">否</el-radio>
              </el-form-item></el-col>
          </el-row>


          <el-form-item label="保证金金额" prop="depositAmount" label-width="120px"  v-if="formChargeData.deposit">
            <el-input oninput="value=value.replace(/[^0-9.]/g,'')"
              @blur="formChargeData.depositAmount = $event.target.value" v-model="formChargeData.depositAmount"
              placeholder="请输入保证金" />
          </el-form-item>
          <el-form-item label="广告充值金额" prop="chargeMoeny" label-width="120px"  v-if="formChargeData.deposit">
            <el-input oninput="value=value.replace(/[^0-9.]/g,'')"
              @blur="formChargeData.chargeMoeny = $event.target.value" v-model="formChargeData.chargeMoeny"
              placeholder="请输入广告充值金额" />
          </el-form-item>
        </el-form>
      </template>
      <template v-if="type == 2">
        <el-form ref="formRfoundRef" :model="formRfoundData" :rules="ruleRfound" :key="type" label-width="110px" label-position="left">
          <el-form-item label="商户名称" prop="name">
            <el-input disabled v-model="formRfoundData1.name" placeholder="请输入商户名" />
          </el-form-item>
          <el-form-item label="商户类型" prop="type">
            <el-input disabled v-model="formRfoundData1.type" placeholder="请输入商户类型" />
          </el-form-item>
          <el-form-item label="收款主体名称" prop="subjectName">
            <el-input v-model="formRfoundData.subjectName" placeholder="请输入收款主体名称" />
          </el-form-item>
          <el-form-item label="收款银行卡号" prop="bankCardNo">
            <el-input :change="
              (formRfoundData.bankCardNo = formRfoundData.bankCardNo.replace(
                /[^\d.]/g,
                ''
              ))
            " :maxlength="30" v-model="formRfoundData.bankCardNo" placeholder="请输入收款银行卡号" />
          </el-form-item>
          <el-form-item label="收款银行" prop="bankName">
            <el-input v-model="formRfoundData.bankName" placeholder="请输入收款银行" />
          </el-form-item>
          <el-form-item label="退款金额" prop="price">
            <el-input oninput="value=value.replace(/[^0-9.]/g,'')" v-model="formRfoundData.price" placeholder="请输入退款金额" />
          </el-form-item>
          <el-form-item label="我方收款主体" prop="loanSubjectId">
            <el-select v-model="formRfoundData.loanSubjectId" placeholder="请选择我方收款主体" style="width:100%">
              <el-option v-for="item in subjectlist" :key="item.id" :value="item.id" :label="item.subjectName">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否退保证金" prop="deposit">
            <el-radio v-model="formRfoundData.deposit" @change="handleDeposit" :label="true">是</el-radio>
            <el-radio v-model="formRfoundData.deposit" @change="handleDeposit" :label="false">否</el-radio>
          </el-form-item>
          <el-form-item label="可退保证金余额" v-if="formRfoundData.deposit">
            <el-input type="number" disabled v-model="useAmount" />
          </el-form-item>
          <el-form-item label="退款原因" prop="reason">
            <el-input type="textarea" v-model="formRfoundData.reason" placeholder="请输入退款原因" />
          </el-form-item>
        </el-form>
      </template>
    </div>
    <div class="drawer__footer flex align-items-c">
      <el-button type="primary" size="small" @click="handleSubmit"> 确定</el-button>
      <el-button size="small" @click="handCloseDrawer">取 消</el-button>
    </div>
  </el-drawer>
</template>

<script>
import {

  PartyArecharg,
  getSubjectAll,
  partyRefund,

} from "@/api/partyA";
import { hasBool } from "@/directive/permission/hasBool"
export default {
  data() {

    var imgRule1 = (rule, value, callback) => {
      if (this.formChargeData.file == "" && this.imageUrl == "") {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#F56C6C";
        callback(new Error("请上传凭证"));
      } else if (this.formChargeData.file || this.imageUrl) {
        callback();
      }
    };
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      type: 1,
      imageUrl: "",
      useAmount: "",
      subjectlist: [],//主体列表
      typeJson: {
        0: "门店商户",
        1: "个人商户",
        2: "线上商户",
        // 3: "线上个人"
      },
      formRfoundData: {
        partyFirstId: "",
        subjectName: "",
        bankCardNo: "",
        bankName: "",
        price: null,
        reason: "",
        deposit: false,
        loanSubjectId: null,
      },
      formRfoundData1: {
        name: "",
        type: "",
      },
      formChargeData: {
        price: "",
        payerName: "",
        payeeSubjectId: "",
        rechargeDate: "",
        file: "",
        invoicenStatus: "1",
        depositAmount: 0,
        deposit: false,
        chargeMoeny: 0,
      },
      rulesSbu: {
        price: [
          {
            required: true,
            message: "请输入入账金额",
            trigger: "blur",
          },
        ],
        depositAmount: [
          {
            required: true,
            message: "请输入保证金金额",
            trigger: "blur",
          },
        ],
        chargeMoeny: [
          {
            required: true,
            message: "请输入广告后台充值金额",
            trigger: "blur",
          },
        ],
        payeeSubjectId: [
          {
            required: true,
            message: "请选择收款主体",
            trigger: "blur",
          },
        ],
        file: [
          {
            required: true,
            message: "请上传凭证",
            validator: imgRule1,
          },
        ],

        rechargeDate: [
          {
            required: true,
            message: "请选择入账时间",
            trigger: "blur",
          },
        ],
      },
      ruleRfound: {
        subjectName: [
          {
            required: true,
            message: "请输入收款主体名称",
            trigger: "blur",
          },
        ],
        bankCardNo: [
          {
            required: true,
            message: "请输入收款银行卡号",
            trigger: "blur",
          },
        ],
        bankName: [
          {
            required: true,
            message: "请输入收款银行名称",
            trigger: "blur",
          },
        ],
        price: [
          {
            required: true,
            message: "请输入退款金额",
            trigger: "blur",
          },
        ],
        loanSubjectId: [
          {
            required: true,
            message: "请选择我收款主体",
            trigger: "blur",
          },
        ],
      },


    }
  },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    partybId: {
      type: [String, Number],
      default: ''
    },
    row: {
      type: Object,
      default: () => { }
    },
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer
      },
      set(v) {
        this.$emit("update:drawer", false)
      }
    }
  },
  watch: {
    drawer(val) {

      if (val) {
        if (!hasBool('loan:partya:recharge')) {
          this.type = 2
          this.handleToggle(this.type)
        }
        this.formChargeData.partyFirstId = this.row.partyFirstId;
        getSubjectAll().then((res) => {
          this.subjectlist = res.data;
        });
      }


    }
  },
  methods: {
    handleToggle(type) {
      this.type = type
      if (type == 2) {
        this.formRfoundData.partyFirstId = this.row.partyFirstId;
        this.formRfoundData.subjectName = this.row.subjectName;
        this.formRfoundData.bankCardNo = this.row.bankCardNo;
        this.formRfoundData.bankName = this.row.bankName;
        this.formRfoundData1.name = this.row.name;
        this.useAmount =
          this.row.availableAmount < 0
            ? this.row.availableAmount + this.row.depositAmount
            : this.row.depositAmount;
        this.formRfoundData1.type = this.typeJson[this.row.type];
      }

    },

    handCloseDrawer() {
      this.imageUrl = ''
      this.formChargeData = {
        price: "",
        payerName: "",
        payeeSubjectId: "",
        rechargeDate: "",
        file: "",
        invoicenStatus: "1",
        depositAmount: 0,
        deposit: false,
        chargeMoeny: 0,
      }
      if (document.getElementsByClassName("el-upload").length > 0) {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#8c939d";
      }
      this.formRfoundData = {
        partyFirstId: "",
        subjectName: "",
        bankCardNo: "",
        bankName: "",
        price: null,
        reason: "",
        deposit: false,
        loanSubjectId: null,
      }
      this.formRfoundData1 = {
        name: "",
        type: "",
      }
      this.useAmount = ""
      this.$refs["formChargeRef"] && this.$refs["formChargeRef"].resetFields();
      this.$refs["formRfoundRef"] && this.$refs["formRfoundRef"].resetFields();
      this.type = 1
      this.drawer_ = false
    },
    handleSubmit() {
      if (this.type == 1) {
        this.$refs.formChargeRef.validate((valid) => {
          if (valid) {
            let formData = new FormData();
            for (let i in this.formChargeData) {
              formData.append(i, this.formChargeData[i]);
            }
            PartyArecharg(formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("入账成功");
                this.drawer_ = false
                this.$emit("getList")
              }
            });
          }
        });
      } else {
        this.$refs.formRfoundRef.validate((valid) => {
          if (valid) {
            this.formRfoundData.price = this.formRfoundData.price * 1;
            partyRefund(this.formRfoundData)
              .then((res) => {
                if (res.code == 200) {
                  this.$message.success("退款成功");
                  this.drawer_ = false
                  this.$emit("getList")
                }
              })
              .catch((err) => { });
          }
        });
      }

    },
    //处理上传凭证
    changeUpImg(e) {
      const isJPG =
        e.raw.type === "image/jpeg" ||
        e.raw.type === "image/jpg" ||
        e.raw.type === "image/png";
      const isLt2M = e.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG/JPEG/PNG 格式!");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
        return;
      }
      this.imageUrl = URL.createObjectURL(e.raw);
      this.formChargeData.file = e.raw;
      if (document.getElementsByClassName("el-upload").length > 0) {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#8c939d";
        this.$refs.formChargeRef.clearValidate('file')
      }
    },
    handleDeposit() {
      if (this.useAmount <= 0) {
        this.$message.error("当前可退保证金为0")
        this.formRfoundData.deposit = false
      }
    },

  },
  mounted() {
  }
}
</script>

<style lang="scss" scoped>
.drawer-wrap {
  height: calc(100vh - 100px);
  overflow: auto;
  padding: 20px 20px;
}

.drawer-tabs {
  margin-bottom: 30px;

  &-item {
    margin-right: 50px;
    font-size: 18px;
    color: rgba(0, 0, 0, 0.6);

    &.active {
      color: #E37318;
      position: relative;

      &::after {
        content: "";
        display: block;
        position: absolute;
        width: 60%;
        height: 4px;
        border-radius: 2px;
        background: #E37318;
        left: 50%;
        top: 26px;
        transform: translateX(-50%);
      }
    }
  }
}

.drawer__footer {
  position: absolute;
  bottom: 0;
  border-top: 1px solid #DCDFE6;
  width: 100%;
  height: 50px;
  padding-left: 10px;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #EEEEEE;
  background: #DCDCDC;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-tips {
  color: #8c939d;
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  font-size: 12px;
  justify-content: center;
  align-items: center;

  i {
    font-size: 24px;
  }

}

::v-deep .el-upload__tip {
  color: rgba(0, 0, 0, 0.4);
  margin-top: -10px;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
