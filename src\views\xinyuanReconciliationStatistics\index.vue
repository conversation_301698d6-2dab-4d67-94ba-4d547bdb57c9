<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" size="small">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="queryParams.date"
          value-format="yyyy-MM-dd"
          type="date"
          placeholder="选择日期"
          @change="handleDateChange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道名称">
        <el-input
          v-model="queryParams.channelName"
          placeholder="请输入渠道名称"
          @keyup.enter.native="fetchData"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道id">
        <el-input
          v-model="queryParams.channelId"
          placeholder="请输入渠道id"
          @keyup.enter.native="fetchData"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道类型">
        <el-select v-model="channelType" placeholder="请选择渠道类型" clearable>
          <el-option label="惠逸花" :value="1"></el-option>
          <el-option label="贷悦通" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="ROI控制">
        <el-input
          v-model="queryParams.roiControl"
          type="number"
          step="0.01"
          min="0"
          placeholder="请输入ROI控制值"
          @keyup.enter.native="fetchData"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="fetchData">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 未录入数据产品警告提示 -->
    <div @click="showNotEnterProductDialog" class="not-enter-product-alert">
      <el-alert
        v-if="notEnterProducts.length > 0"
        type="warning"
        :closable="false"
        center
      >
        <template #title>
          <span class="title">
            存在
            <span class="highlight">{{ notEnterProducts.length }}</span>
            个未录入数据的产品，点击查看详情
          </span>
        </template>
      </el-alert>
    </div>

    <!-- 表格骨架屏 -->
    <div v-if="loading">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 数据表格 -->
    <el-table v-else :data="tableData" style="width: 100%" border>
      <el-table-column
        prop="channelId"
        label="渠道ID"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.isSummary">{{ scope.row.channelId }}</span>
          <span v-else>{{ scope.row.channelId }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="channelName"
        label="渠道名称"
        width="250"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="!scope.row.isSummary">{{ scope.row.channelName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="渠道类型" width="120" align="center">
        <template slot-scope="scope">
          <span v-if="!scope.row.isSummary">
            {{ scope.row.channelType == 1 ? '惠逸花' : scope.row.channelType == 2 ? '贷悦通' : '' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="profit"
        label="总收益"
        align="center"
      ></el-table-column>

      <el-table-column label="薪源收益" align="center">
        <template slot-scope="scope">
          <el-popover placement="top" trigger="hover">
            <el-table :data="[scope.row.xyProfit]" border>
              <el-table-column
                prop="offlineProfit"
                label="线下收益"
                width="90"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="halfProfit"
                label="半流程收益"
                width="90"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="qwProfit"
                label="企微收益"
                width="90"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="dcProfit"
                label="贷超收益"
                width="90"
                align="center"
              ></el-table-column>
            </el-table>
            <span slot="reference" class="pointer">{{
              scope.row.xyProfit.profit
            }}</span>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="惠逸花收益" align="center">
        <template slot-scope="scope">
          <el-popover placement="top" trigger="hover">
            <el-table :data="[scope.row.hyhProfit]" border>
              <el-table-column
                prop="offlineProfit"
                label="线下收益"
                width="90"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="halfProfit"
                label="半流程收益"
                width="90"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="qwProfit"
                label="企微收益"
                width="90"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="dcProfit"
                label="贷超收益"
                width="90"
                align="center"
              ></el-table-column>
            </el-table>
            <span slot="reference" class="pointer">{{
              scope.row.hyhProfit.profit
            }}</span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        prop="dcDiffProfit"
        label="贷超收益差值"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="channelCost"
        label="渠道成本"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="realCost"
        label="实际收益"
        align="center"
      ></el-table-column>
      <el-table-column prop="xyRatio" label="薪源收益占比" align="center">
        <template slot-scope="scope">
          <span v-if="!scope.row.isSummary">{{ scope.row.xyRatio }}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="hyhRatio" label="惠逸花收益占比" align="center">
        <template slot-scope="scope">
          <span v-if="!scope.row.isSummary">{{ scope.row.hyhRatio }}%</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 未录入数据产品弹窗 -->
    <el-dialog
      title="未录入数据的产品列表"
      :visible.sync="dialogVisible"
      width="600px"
    >
      <el-table :data="notEnterProducts" style="width: 100%" border max-height="500">
        <el-table-column
          prop="id"
          label="产品ID"
          align="center"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="产品名称"
          align="center"
        ></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import dayjs from "dayjs";
import {
  getXinyuanReconciliationStatistics,
  getNotEnterProductList,
} from "@/api/xinyuanReconciliationStatistics";
import { sum } from "@/utils/calculate";

export default {
  name: "XinyuanReconciliationStatisticsIndex",
  data() {
    return {
      queryParams: {
        channelName: "",
        channelId: "",
        date: "",
        roiControl: null,
      },
      tableData: [], // 表格数据
      notEnterProducts: [], // 未录入数据的产品列表
      dialogVisible: false, // 弹窗显示状态
      // 渠道类型
      channelType: "",
      // 表格加载状态
      loading: false
    };
  },
  created() {
    this.setDefaultDate();
    this.fetchData(); // 初始化时获取列表数据
  },

  methods: {
    setDefaultDate() {
      this.queryParams.date = dayjs().format("YYYY-MM-DD");
    },

    getParams() {
      return {
        queryDate: this.queryParams.date,
        channelName: this.queryParams.channelName,
        channelId: this.queryParams.channelId,
        roiControl: this.queryParams.roiControl,
      };
    },

    handleDateChange() {
      this.fetchData();
    },

    /** 重置查询参数 */
    resetQuery() {
      this.queryParams.channelName = "";
      this.queryParams.channelId = "";
      this.queryParams.roiControl = null;
      this.channelType = "";
      this.setDefaultDate();
      this.fetchData();
    },

    /** 创建合计行 */
    createSummaryRow(data) {
      if (!data || data.length === 0) {
        return null;
      }

      const summaryRow = {
        channelId: "合计",
        channelName: "合计",
        profit: sum(data.map((item) => Number(item.profit || 0))),
        channelCost: sum(data.map((item) => Number(item.channelCost || 0))),
        realCost: sum(data.map((item) => Number(item.realCost || 0))),
        dcDiffProfit: sum(data.map((item) => Number(item.dcDiffProfit || 0))), // 新增贷超收益差值合计
        isSummary: true,
        // 薪源收益合计
        xyProfit: {
          profit: sum(data.map((item) => Number(item.xyProfit?.profit || 0))),
          offlineProfit: sum(
            data.map((item) => Number(item.xyProfit?.offlineProfit || 0))
          ),
          halfProfit: sum(
            data.map((item) => Number(item.xyProfit?.halfProfit || 0))
          ),
          qwProfit: sum(
            data.map((item) => Number(item.xyProfit?.qwProfit || 0))
          ),
          dcProfit: sum(
            data.map((item) => Number(item.xyProfit?.dcProfit || 0))
          ),
        },
        // 惠逸花收益合计
        hyhProfit: {
          profit: sum(data.map((item) => Number(item.hyhProfit?.profit || 0))),
          offlineProfit: sum(
            data.map((item) => Number(item.hyhProfit?.offlineProfit || 0))
          ),
          halfProfit: sum(
            data.map((item) => Number(item.hyhProfit?.halfProfit || 0))
          ),
          qwProfit: sum(
            data.map((item) => Number(item.hyhProfit?.qwProfit || 0))
          ),
          dcProfit: sum(
            data.map((item) => Number(item.hyhProfit?.dcProfit || 0))
          ),
        },
      };

      return summaryRow;
    },

    // 从数组中筛选出指定渠道类型的数据
    filterDataByChannelType(data) {
      if (!data || data.length === 0) {
        return [];
      }

      if (!this.channelType) {
        return data;
      }

      return data.filter((item) => item.channelType == this.channelType);
    },

    /** 获取列表数据 */
    fetchData() {
      this.loading = true; // 开始加载，显示骨架屏
      getXinyuanReconciliationStatistics(this.getParams()).then((response) => {
        let data = response.data || [];

        data = this.filterDataByChannelType(data);

        // 添加合计行到数据的第一行
        if (data && data.length > 0) {
          const summaryRow = this.createSummaryRow(data);
          if (summaryRow) {
            this.tableData = [summaryRow, ...data];
          } else {
            this.tableData = data;
          }
        } else {
          this.tableData = data;
        }
      }).finally(() => {
        this.loading = false; // 加载出错时也要隐藏骨架屏
      });

      // 获取未录入数据的产品列表
      this.fetchNotEnterProducts();
    },

    /** 获取未录入数据的产品列表 */
    fetchNotEnterProducts() {
      getNotEnterProductList({
        queryDate: this.queryParams.date,
      }).then((response) => {
        this.notEnterProducts = response.data || [];
      });
    },

    /** 显示未录入数据产品弹窗 */
    showNotEnterProductDialog() {
      this.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.not-enter-product-alert {
  margin-bottom: 15px;
  // width: 500px;
  font-size: px;
  cursor: pointer;

  .title {
    font-size: 16px;
  }

  .highlight {
    font-size: 20px;
    font-weight: 600;
    color: #f56c6c;
  } 
}
</style>
