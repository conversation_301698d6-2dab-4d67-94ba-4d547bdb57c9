<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="今日:" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="昨日:" prop="time">
        <el-date-picker
          v-model="value2"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="筛选渠道">
        <el-select
          size="small"
          v-model="queryParams.channelId"
          filterable
          collapse-tags
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in optionsList"
            :key="item.id"
            :label="`id:${item.id}--${item.channelName}`"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <div class="content-box">
      <!-- <div class="chart">
        <div class="chart-box" ref="echartUv"></div>
      </div> -->
      <el-table
        :data="dataList"
        style="width: 100%"
        row-key="rowKey"
        default-expand-all
        :tree-props="{
          children: 'platformStats',
        }"
        @sort-change="handleSortChange"
      >
        <el-table-column label="渠道ID" prop="channelId" align="center" />
        <el-table-column prop="channelName" label="渠道名称">
          <template slot-scope="{ row }">
            <template v-if="row.name">
              <el-tag size="small" type="info" v-if="row.isInner">{{
                  row.name
                }}</el-tag>
              <el-tag size="small" v-else>{{ row.name }}</el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          label="今日匹配数"
          prop="todayMatchNum"
          align="center"
        />
        <el-table-column label="昨日匹配数" prop="yesMatchNum" align="center" />
        <el-table-column label="今日收益" prop="todayProfit" align="center">
          <template slot-scope="{ row }">
            <div
              :style="{
                color: `${row.todayProfit > row.yesProfit ? 'red' : 'blue'}`,
                'font-size': '16px',
              }"
            >
              {{ row.todayProfit }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="昨日收益" prop="yesProfit" align="center" />
      </el-table>
    </div>
  </div>
</template>

<script>
import { getChannelCompareList } from "@/api/distributeStatistics/compareChannelData";
import { getChannelList } from "@/api/productManage/product";
import { v4 as uuidv4 } from "uuid";
import { sum } from '@/utils/calculate'
export default {
  data() {
    return {
      checkValue: "",

      optionsList: [],

      value1: [this.getTodayTime() + " 00:00:00", this.getTodayTime(true)],
      value2: [this.getYesTime() + " 00:00:00", this.getYesTime(true)],

      dataList: [],
      queryParams: {
        channelName: "",
        channelId: "",
        startTime: `${this.getTodayTime()} 00:00:00`,
        endTime: this.getTodayTime(true),
      },
      queryParams1: {
        channelName: "",
        channelId: "",
        startTime: `${this.getYesTime()} 00:00:00`,
        endTime: `${this.getYesTime(true)}`,
      },
    };
  },
  methods: {
    handleQuery() {
      if (!(this.value1 && this.value2))
        return this.$message.error("请选择时间");
      if (this.value1 !== null) {
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      if (this.value2 !== null) {
        this.queryParams1.startTime = this.value2[0];
        this.queryParams1.endTime = this.value2[1];
      } else {
        this.queryParams1.startTime = "";
        this.queryParams1.endTime = "";
      }
      this.getList();
    },

    getTodayTime(flag) {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      let hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
      let minute =
        date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      let second =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      if (!flag) {
        return YY + "-" + MM + "-" + DD;
      } else {
        return `${YY}-${MM}-${DD} ${hour}:${minute}:${second}`;
      }
    },

    getYesTime(flag) {
      var date = new Date(new Date().valueOf() - 86400 * 1000);

      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      let hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
      let minute =
        date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      let second =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      if (!flag) {
        return YY + "-" + MM + "-" + DD;
      } else {
        return `${YY}-${MM}-${DD} ${hour}:${minute}:${second}`;
      }
    },
    //前端排序
    handleSortChange(column) {
      this.proptype = column.prop;
      let sortingType = column.order;

      if (sortingType == "ascending") {
        //正序
        this.dataList = this.dataList.sort(
          (a, b) => b[this.proptype] - a[this.proptype]
        );
      }
      if (sortingType == "descending") {
        // 倒序
        this.dataList = this.dataList.sort(
          (a, b) => a[this.proptype] - b[this.proptype]
        );
      }
    },
    getSubList(arr1, arr2) {
      let data = [];
      arr1.map((item1) => {
        arr2.map((item2) => {
          if (item1.name && item2.name) {
            let name = item1.name.split("_")[1];
            let name1 = item2.name.split("_")[1];
            if (name == name1) {
              data.push({
                name,
                channelId: null,
                todayMatchNum: item1.matchNum,
                yesMatchNum: item2.matchNum,
                todayProfit: item1.profit,
                yesProfit: item2.profit,
                rowKey: uuidv4(),
                isInner: true,
              });
            }
          }
        });
      });
      let data1 = data.map((item) => item.name);
      arr1.map((item) => {
        if (item.name && data1.indexOf(item.name.split("_")[1]) == -1) {
          data.push({
            name: item.name.split("_")[1],
            channelId: null,
            todayMatchNum: item.matchNum,
            yesMatchNum: 0,
            todayProfit: item.profit,
            yesProfit: 0,
            rowKey: uuidv4(),
            isInner: true,
          });
        }
      });
      arr2.map((item) => {
        if (item.name && data1.indexOf(item.name.split("_")[1]) == -1) {
          data.push({
            name: item.name.split("_")[1],
            channelId: null,
            todayMatchNum: 0,
            yesMatchNum: item.matchNum,
            todayProfit: 0,
            yesProfit: item.profit,
            rowKey: uuidv4(),
            isInner: true,
          });
        }
      });
      return data;
    },
    getEmptyList(arr) {
      return arr.map((item) => {
        return {
          name: item.name,
          channelId: item.channelId,
          matchNum: 0,
          profit: 0,
          platformStats: null,
        };
      });
    },
    getEmptySubList(arr, isToay) {
      return arr.map((item) => {
        let name = item.name&&item.name.split("_")[1];
        return {
          name,
          channelId: null,
          todayMatchNum: isToay ? item.matchNum : 0,
          yesMatchNum: isToay ? 0 : item.matchNum,
          todayProfit: isToay ? item.profit : 0,
          yesProfit: isToay ? 0 : item.profit,
          rowKey: uuidv4(),
          isInner: true,
        };
      }).filter(item => item.name);
    },
    getList() {
      this.dataList = [];
      this.queryParams1.channelId = this.queryParams.channelId;

      Promise.all([
        getChannelCompareList(this.queryParams),
        getChannelCompareList(this.queryParams1),
      ]).then(([res1, res2]) => {
        if (res1.data.length == 0) {
          res1.data = res2.data.map((item) => {
            return {
              name: item.name,
              channelId: item.channelId,
              matchNum: 0,
              profit: 0,
              platformStats: this.getEmptyList(item.platformStats),
            };
          });
        }

        if (res2.data.length == 0) {
          res2.data = res1.data.map((item) => {
            return {
              name: item.name,
              channelId: item.channelId,
              matchNum: 0,
              profit: 0,
              platformStats: this.getEmptyList(item.platformStats),
            };
          });
        }
        res1.data.map((item1) => {
          res2.data.map((item2) => {
            if (item1.channelId == item2.channelId) {
              this.dataList.push({
                name: item1.name,
                channelId: item1.channelId,
                todayMatchNum: item1.matchNum,
                yesMatchNum: item2.matchNum,
                todayProfit: item1.profit,
                yesProfit: item2.profit,
                rowKey: uuidv4(),

                platformStats: this.getSubList(
                  item1.platformStats,
                  item2.platformStats
                ),
              });
            }
          });
        });
        let dataId = this.dataList.map((item) => item.channelId);

        res1.data.map((item1) => {
          if (dataId.indexOf(item1.channelId) == -1) {
            this.dataList.push({
              name: item1.name,
              channelId: item1.channelId,
              todayMatchNum: item1.matchNum,
              yesMatchNum: 0,
              todayProfit: item1.profit,
              yesProfit: 0,
              rowKey: uuidv4(),
              platformStats: this.getEmptySubList(item1.platformStats, true),
            });
          }
        });
        let dataId2 = this.dataList.map((item) => item.channelId);
        res2.data.map((item1) => {
          if (dataId2.indexOf(item1.channelId) == -1) {
            this.dataList.push({
              name: item1.name,
              channelId: item1.channelId,
              todayMatchNum: 0,
              yesMatchNum: item1.matchNum,
              todayProfit: 0,
              yesProfit: item1.profit,
              rowKey: uuidv4(),
              platformStats: this.getEmptySubList(item1.platformStats, false),
            });
          }
        });

        if (this.dataList.length) {
          this.dataList.unshift(this.createSummaryRow(this.dataList));
        }
      });
    },

    createSummaryRow(data) {
      const summaryRow = {
        channelId: "合计",
        todayMatchNum: 0,
        todayProfit: 0,
        yesMatchNum: 0,
        yesProfit: 0,
        isSummary: true,
      };

      summaryRow.todayMatchNum = sum(data.map((item) => item.todayMatchNum));
      summaryRow.todayProfit = sum(data.map((item) => item.todayProfit));
      summaryRow.yesMatchNum = sum(data.map((item) => item.yesMatchNum));
      summaryRow.yesProfit = sum(data.map((item) => item.yesProfit));

      return summaryRow;
    },
  },

  mounted() {
    this.getList();
    getChannelList().then((res) => {
      this.optionsList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped></style>
