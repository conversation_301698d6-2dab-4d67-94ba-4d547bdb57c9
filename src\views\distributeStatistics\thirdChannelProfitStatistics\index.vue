<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams">
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道ID" prop="channelId">
        <el-input
          v-model.number="queryParams.channelId"
          placeholder="请输入渠道ID"
          clearable
          @keyup.enter.native="fetchTableData"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道名称" prop="channelName">
        <el-input
          v-model="queryParams.channelName"
          placeholder="请输入渠道名称"
          clearable
          @keyup.enter.native="fetchTableData"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="fetchTableData">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      border
    >
      <el-table-column prop="channelId" label="渠道ID" align="center" />
      <el-table-column prop="channelName" label="渠道名称" align="center" />
      <el-table-column prop="uv" label="UV数量" align="center" />
      <el-table-column prop="register" label="注册数量" align="center" />
      <el-table-column prop="form" label="表单数量" align="center" />
      <el-table-column prop="profit" label="收益金额(元)" align="center">
        <template slot-scope="scope">
          {{ scope.row.profit || '0.00' }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getThirdChannelProfitStatistics } from '@/api/distributeStatistics/thirdChannelProfitStatistics'
import dayjs from 'dayjs'

export default {
  name: 'ThirdChannelProfitStatistics',
  data() {
    const today = dayjs().format('YYYY-MM-DD')
    
    return {
      // 表格数据
      tableData: [],
      // 遮罩层
      loading: false,
      // 日期范围
      dateRange: [today, today],
      // 查询参数
      queryParams: {
        startDate: today,
        endDate: today,
        channelId: undefined,
        channelName: undefined
      }
    }
  },
  created() {
    this.fetchTableData()
  },
  methods: {
    // 获取表格数据
    fetchTableData() {
      this.loading = true
      getThirdChannelProfitStatistics(this.queryParams)
        .then(res => {
          if (res.code === 200) {
            this.tableData = res.data || []
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 日期变化
    handleDateChange(dateRange) {
      if (dateRange) {
        this.queryParams.startDate = dateRange[0]
        this.queryParams.endDate = dateRange[1]
      } else {
        const today = dayjs().format('YYYY-MM-DD')
        this.queryParams.startDate = today
        this.queryParams.endDate = today
      }
    },
    // 重置查询
    resetQuery() {
      const today = dayjs().format('YYYY-MM-DD')
      this.dateRange = [today, today]
      this.queryParams = {
        startDate: today,
        endDate: today,
        channelId: undefined,
        channelName: undefined
      }
      this.fetchTableData()
    }
  }
}
</script>

<style scoped>
.pagination-container {
  margin-top: 15px;
}
</style> 