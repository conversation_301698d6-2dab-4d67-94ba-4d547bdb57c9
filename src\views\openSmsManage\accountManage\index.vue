<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryForm" class="search-form">
      <el-form-item label="短信账户">
        <el-select 
          v-model="queryForm.smsConfigTemplateId" 
          placeholder="请选择短信账户" 
          clearable
          filterable>
          <el-option
            v-for="item in smsAccountOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账户名称">
        <el-input v-model="queryForm.accountName" placeholder="请输入账户名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleAdd">新增账户</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column
        prop="channelId"
        label="渠道ID">
      </el-table-column>
      <el-table-column
        prop="channelName"
        label="渠道名称">
      </el-table-column>
      <el-table-column
        prop="accountName"
        label="账户名称">
      </el-table-column>
      <el-table-column
        prop="accountPassword"
        label="账户密码">
      </el-table-column>
      <el-table-column
        prop="smsConfigTemplateName"
        label="短信账户">
      </el-table-column>
      <el-table-column
        prop="accountStatusDesc"
        label="状态"
        align="center">
        <template slot-scope="{row}">
          <el-switch
            v-model="row.accountStatus"
            :active-value="1"
            :inactive-value="2"
            active-text="启用"
            inactive-text="禁用"
            @change="handleStatusChange(row)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间" width="160">
      </el-table-column>
      <el-table-column
        prop="createBy"
        label="创建人">
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="更新时间" width="160">
      </el-table-column>
      <el-table-column
        prop="updateBy"
        label="更新人">
      </el-table-column>
      <el-table-column
        label="操作"
        width="150"
        align="center">
        <template slot-scope="{row}">
          <el-button
            size="mini"
            type="text"
            @click="handleEdit(row)"
          >编辑</el-button>
          <el-button
            size="mini"
            type="text"
            @click="handleDetail(row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            @click="handleTestSend(row)"
          >测试发送</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryForm.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryForm.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination">
    </el-pagination>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      destroy-on-close
      :close-on-click-modal="false"
      @close="handleDialogClose">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px">
        <el-form-item label="账户名称" prop="accountName">
          <el-input v-model="form.accountName" placeholder="请输入账户名称"></el-input>
        </el-form-item>
        <el-form-item label="账户密码" prop="accountPassword">
          <div style="display: flex;">
            <el-input v-model="form.accountPassword" placeholder="请输入账户密码" show-password maxlength="12"></el-input>
            <el-button type="primary" style="margin-left: 10px;" @click="generateRandomPassword">随机生成</el-button>
          </div>
        </el-form-item>
        <el-form-item label="短信签名" prop="smsSign">
          <el-input v-model.trim="form.smsSign" placeholder="请输入短信签名"></el-input>
        </el-form-item>
        <el-form-item label="短信签名盐值" v-if="form.accountId">
          <el-input v-model="form.accountSignSalt" disabled></el-input>
        </el-form-item>
        <el-form-item label="IP地址">
          <div v-for="(ip, index) in form.ipList" :key="index" class="ip-input-item">
            <el-form-item 
              :prop="'ipList.' + index"
              :rules="ipRules.ip"
              class="ip-form-item">
              <el-input 
                v-model="form.ipList[index]" 
                placeholder="请输入IP地址，如：***********">
              </el-input>
            </el-form-item>
            <el-button type="text" icon="el-icon-delete" @click="removeIp(index)" style="margin-left: 10px;"></el-button>
          </div>
          <el-button type="text" icon="el-icon-plus" @click="addIp">添加IP地址</el-button>
        </el-form-item>
        <el-form-item label="账号状态" prop="accountStatus">
          <el-radio-group v-model="form.accountStatus">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="短信账户" prop="smsConfigTemplateId">
          <el-select v-model="form.smsConfigTemplateId" placeholder="请选择短信账户" filterable>
            <el-option
              v-for="item in smsAccountOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="渠道" prop="channelId">
          <el-select v-model="form.channelId" placeholder="请选择渠道" filterable :disabled="!!form.accountId">
            <el-option
              v-for="item in channelList"
              :key="item.id"
              :label="`${item.id}-${item.channelName}`"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">保存账户</el-button>
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      title="账户详情"
      :visible.sync="detailVisible"
      width="1000px"
      :close-on-click-modal="false">
      <el-descriptions
        :column="2"
        border
       >
        <el-descriptions-item label="账户名称" :span="2">{{ detailForm.accountName }}</el-descriptions-item>
        <el-descriptions-item label="账户密码" :span="2">{{ detailForm.accountPassword }}</el-descriptions-item>
        <el-descriptions-item label="短信签名" :span="2">{{ detailForm.smsSign }}</el-descriptions-item>
        <el-descriptions-item label="短信签名盐值" :span="2">{{ detailForm.accountSignSalt }}</el-descriptions-item>
        <el-descriptions-item label="IP地址" :span="2">
          <template v-if="detailForm.ipAddress">
            <el-tag v-for="(ip, index) in detailForm.ipAddress.split(',')" :key="index" style="margin-right: 10px">
              {{ ip }}
            </el-tag>
          </template>
        </el-descriptions-item>
        <el-descriptions-item label="短信账户" :span="2">{{ detailForm.smsConfigTemplateName }}</el-descriptions-item>
        <el-descriptions-item label="渠道" :span="2">{{ detailForm.channelId }}-{{ detailForm.channelName }}</el-descriptions-item>
        <el-descriptions-item label="状态" :span="2">
          <el-tag :type="detailForm.accountStatus == 1 ? 'success' : 'danger'">
            {{ detailForm.accountStatus == 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailForm.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailForm.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新人">{{ detailForm.updateBy }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ detailForm.updateTime }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 测试发送对话框 -->
    <el-dialog
      title="测试短信发送"
      :visible.sync="testSendVisible"
      width="500px"
      destroy-on-close
      :close-on-click-modal="false"
      @close="handleTestSendClose">
      <el-form
        ref="testSendForm"
        :model="testSendForm"
        :rules="testSendRules"
        label-width="100px">
        <el-form-item label="短信内容" prop="content">
          <el-input
            v-model.trim="testSendForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入短信内容">
          </el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <div v-for="(mobile, index) in testSendForm.mobileList" :key="index" class="mobile-input-item">
            <el-form-item 
              :prop="'mobileList.' + index"
              :rules="mobileRules"
              class="mobile-form-item">
              <el-input 
                v-model.trim="testSendForm.mobileList[index]"
                maxlength="11"
                placeholder="请输入手机号">
              </el-input>
            </el-form-item>
            <el-button type="text" icon="el-icon-delete" @click="removeMobile(index)" style="margin-left: 10px;"></el-button>
          </div>
          <el-button type="text" icon="el-icon-plus" @click="addMobile">添加手机号</el-button>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="testSendVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitTestSend">发 送</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSmsConfigurationTemplate } from "@/api/threeParty/note"
import { getAccountList, saveAccount, testSendSms } from '@/api/openSmsManage/accountManage'
import { getChannelList } from '@/api/productManage/product'
import { generateRandomPassword } from '@/utils/password'
import test from '@/utils/test'

export default {
  name: 'AccountManage',
  data() {
    // IP地址格式校验
    const validateIp = (rule, value, callback) => {
      if (!value || !value.trim()) {
        callback()
        return
      }
      
      const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/
      const isValidIp = (ip) => {
        if (!ipRegex.test(ip)) return false
        return ip.split('.').every(num => {
          const n = parseInt(num)
          return n >= 0 && n <= 255
        })
      }

      if (!isValidIp(value.trim())) {
        callback(new Error('请输入正确的IP地址格式，如：***********'))
        return
      }

      // 检查IP是否重复
      const currentIndex = parseInt(rule.field.split('.')[1])
      const otherIps = this.form.ipList.filter((_, index) => index != currentIndex)
      if (otherIps.includes(value.trim())) {
        callback(new Error('IP地址不能重复'))
        return
      }

      callback()
    }

    return {
      queryForm: {
        smsConfigTemplateId: '',
        accountName: '',
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      tableData: [],
      dialogVisible: false,
      dialogTitle: '新增账户',
      form: {
        accountId: undefined,
        accountName: '',
        accountPassword: '',
        accountStatus: 1,
        smsConfigTemplateId: '',
        ipList: [''],
        accountSignSalt: '',
        channelId: '',
        smsSign: ''
      },
      rules: {
        accountName: [
          { required: true, message: '请输入账户名称', trigger: 'blur' }
        ],
        accountPassword: [
          { required: true, message: '请输入账户密码', trigger: 'blur' }
        ],
        accountStatus: [
          { required: true, message: '请选择账号状态', trigger: 'change' }
        ],
        smsConfigTemplateId: [
          { required: true, message: '请选择短信账户', trigger: 'change' }
        ],
        channelId: [
          { required: true, message: '请选择渠道', trigger: 'change' }
        ],
        smsSign: [
          { required: false, trigger: 'blur' }
        ]
      },
      ipRules: {
        ip: [
          { validator: validateIp, trigger: 'blur' }
        ]
      },
      smsAccountOptions: [],
      detailVisible: false,
      detailForm: {
        accountId: undefined,
        accountName: '',
        accountPassword: '',
        accountStatus: 1,
        smsConfigTemplateId: '',
        smsConfigTemplateName: '',
        ipAddress: '',
        accountSignSalt: '',
        createTime: '',
        createBy: '',
        updateTime: '',
        updateBy: '',
        smsSign: ''
      },
      channelList: [],
      testSendVisible: false,
      testSendForm: {
        smsAccountId: '',
        content: '',
        mobileList: ['']
      },
      testSendRules: {
        content: [
          { required: true, message: '请输入短信内容', trigger: 'blur' }
        ]
      },
      mobileRules: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { 
          validator: (rule, value, callback) => {
            if (!value || test.mobile(value)) {
              callback()
            } else {
              callback(new Error('请输入正确的手机号'))
            }
          }, 
          trigger: 'blur' 
        }
      ],
    }
  },
  created() {
    this.getTableData()
    this.getSmsAccounts()
    this.getChannels()
  },
  methods: {
    handleSearch() {
      this.queryForm.pageNum = 1
      this.getTableData()
    },
    handleReset() {
      this.queryForm.smsConfigTemplateId = ''
      this.queryForm.accountName = ''
      this.queryForm.pageNum = 1
      this.getTableData()
    },
    getTableData() {
      getAccountList(this.queryForm).then(res => {
        this.tableData = res.rows || []
        this.total = res.total || 0
      })
    },
    handleAdd() {
      this.dialogTitle = '新增账户'
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '编辑账户'
      this.form = {
        accountId: row.accountId,
        accountName: row.accountName,
        accountPassword: row.accountPassword,
        accountStatus: row.accountStatus,
        smsConfigTemplateId: row.smsConfigTemplateId,
        ipList: row.ipAddress ? row.ipAddress.split(',') : [''],
        accountSignSalt: row.accountSignSalt,
        channelId: row.channelId ? Number(row.channelId) : '',
        smsSign: row.smsSign ? row.smsSign.replace(/^【|】$/g, '') : ''
      }
      this.dialogVisible = true
    },
    handleDialogClose() {
      this.$refs.form && this.$refs.form.resetFields()
      this.form = {
        accountId: undefined,
        accountName: '',
        accountPassword: '',
        accountStatus: 1,
        smsConfigTemplateId: '',
        ipList: [''],
        accountSignSalt: '',
        channelId: '',
        smsSign: ''
      }
    },
    addIp() {
      this.form.ipList.push('')
    },
    removeIp(index) {
      if (this.form.ipList.length > 1) {
        this.form.ipList.splice(index, 1)
      }
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid && this.form.ipList.length > 0) {
          const params = {
            ...this.form,
            ipAddress: this.form.ipList.filter(ip => ip).join(','),
            smsSign: this.form.smsSign ? `【${this.form.smsSign}】` : ''
          }
          delete params.ipList
          saveAccount(params).then(() => {
            this.dialogVisible = false
            this.getTableData()
            this.$message.success(`${this.form.accountId ? '编辑' : '新增'}成功`)
          })
        }
      })
    },
    getSmsAccounts() {
      getSmsConfigurationTemplate().then(res => {
        this.smsAccountOptions = res.data
      })
    },
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.queryForm.pageNum = val
      this.getTableData()
    },
    handleStatusChange(row) {
      const statusText = row.accountStatus == 1 ? '启用' : '禁用'
      this.$confirm(`确认${statusText}账户"${row.accountName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          accountId: row.accountId,
          accountName: row.accountName,
          accountPassword: row.accountPassword,
          accountStatus: row.accountStatus,
          smsConfigTemplateId: row.smsConfigTemplateId,
          ipAddress: row.ipAddress,
          accountSignSalt: row.accountSignSalt,
          channelId: row.channelId
        }
        saveAccount(params).then(() => {
          this.$message.success(`${statusText}成功`)
          this.getTableData()
        })
      }).catch(() => {
        row.accountStatus = row.accountStatus == 1 ? 2 : 1
      })
    },
    handleDetail(row) {
      this.detailForm = { ...row }
      this.detailVisible = true
    },
    getChannels() {
      getChannelList().then(res => {
        this.channelList = res.data || []
      })
    },
    generateRandomPassword() {
      this.form.accountPassword = generateRandomPassword()
    },
    handleTestSend(row) {
      this.testSendForm.smsAccountId = row.accountId
      this.testSendForm.content = ''
      this.testSendForm.mobileList = ['']
      this.testSendVisible = true
    },
    addMobile() {
      this.testSendForm.mobileList.push('')
    },
    removeMobile(index) {
      if (this.testSendForm.mobileList.length > 1) {
        this.testSendForm.mobileList.splice(index, 1)
      }
    },
    handleTestSendClose() {
      this.$refs.testSendForm && this.$refs.testSendForm.resetFields()
      this.testSendForm = {
        smsAccountId: '',
        content: '',
        mobileList: ['']
      }
    },
    submitTestSend() {
      this.$refs.testSendForm.validate(valid => {
        if (valid) {
          const params = {
            smsAccountId: this.testSendForm.smsAccountId,
            content: this.testSendForm.content,
            mobileList: this.testSendForm.mobileList.filter(mobile => mobile)
          }
          testSendSms(params).then(res => {
            if (res.data == false) {
              this.$message.error('发送失败')
              return
            }
            this.$message.success('发送成功')
          })
        }
      })
    },
  }
}
</script>

<style scoped>
.ip-input-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}
.ip-form-item {
  margin-bottom: 0;
  flex: 1;
}
.pagination {
  margin-top: 20px;
  text-align: right;
}
.mobile-input-item {
  display: flex;
  align-items: center;
  margin-bottom: 22px;
}
.mobile-input-item:last-child {
  margin-bottom: 10px;
}
.mobile-form-item {
  margin-bottom: 0;
  flex: 1;
}
</style>