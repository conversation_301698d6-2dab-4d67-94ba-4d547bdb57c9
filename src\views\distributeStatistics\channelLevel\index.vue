<script>
import {
  getStarRatingStat,
  getUserStarListDetail,
  exportStarData
} from "@/api/distributeStatistics/userData";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
import { exportChannelUserStatus } from '@/api/nameManage'

export default {
  name: "channelUser",

  data() {
    return {
      total: 0,
      dialogVisible: false,
      detailList: [],
      currentQueryParams: null,
      // 搜索条件 渠道名称 渠道id 开始时间 结束时间
      queryParams: {
        channelName: "",
        channelId: "",
        pageSize: 10,
        pageNum: 1,
        pushDate: "",
      },
      tableData: [],
    };
  },

  mounted() {
    this.setDefaultDate();
    this.fetchTableData();
  },

  methods: {
    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format("YYYY-MM-DD");
      this.queryParams.pushDate = startTime;
    },

    getParams() {
      return {
        pushDate: this.queryParams.pushDate,
        pageSize: this.queryParams.pageSize,
        pageNum: this.queryParams.pageNum,
        channelName: this.queryParams.channelName,
        channelId: this.queryParams.channelId,
      };
    },

    handleTableData(arr) {
      arr.forEach((row) => {
        row.rowKey = uuidv4();

        if (row.subList && row.subList.length) {
          row.subList.forEach((innerRow) => {
            innerRow.rowKey = uuidv4();

            innerRow.isInner = true;
          });
        } else {
          row.subList = null;
        }
      });
    },

    async fetchTableData() {
      const params = this.getParams();
      const res = await getStarRatingStat(params);
      this.currentQueryParams = { ...this.queryParams };
      this.handleTableData(res.rows);
      this.total = res.total;
      this.tableData = res.rows;
    },
    handleDetail(row) {
      console.log(row);
      this.detailList = [];
      getUserStarListDetail({
        channelId: row.channelId,
        pushDate: this.currentQueryParams.pushDate,
        platTypes: row.plateTypes,
      }).then((res) => {
        this.detailList = res.data;
        this.dialogVisible = true;
      });
    },
    async handleExport(row) {
      const res = await exportStarData({
        channelId: row.channelId,
        starRating: 0,
        pushDate: this.queryParams.pushDate,
      })
      const fileName = `渠道[${row.channelId}]-0星MD5导出_${this.queryParams.pushDate}`
      this.createXlsx(fileName, res);

    },
    createXlsx(title, res) {
      let a = document.createElement("a");
      let blob = new Blob([res], { type: "application/vnd.ms-excel" });
      let objectUrl = URL.createObjectURL(blob);
      a.setAttribute("href", objectUrl);
      a.setAttribute("download", `${title}.xlsx`);
      a.click();
      this.$message.success("导出成功");
    }
  },
};
</script>

<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" size="small">
      <el-form-item label="日期" prop="pushDate">
        <el-date-picker
          v-model="queryParams.pushDate"
          type="date"
          size="small"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        >
        </el-date-picker
      ></el-form-item>
      <el-form-item label="渠道名称">
        <el-input
          v-model="queryParams.channelName"
          placeholder="请输入渠道名称"
          @keyup.enter.native="fetchTableData"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道id">
        <el-input
          v-model="queryParams.channelId"
          placeholder="请输入渠道id"
          @keyup.enter.native="fetchTableData"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="fetchTableData">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      style="width: 100%"
      row-key="rowKey"
      default-expand-all
      :tree-props="{
        children: 'subList',
      }"
    >
      <!-- <el-table-column prop="channelId" label="渠道id"></el-table-column>
      <el-table-column prop="channelName" label="渠道">

      </el-table-column>
      <el-table-column prop="successNum" label="申请数"></el-table-column>
      <el-table-column prop="successPrice" label="总金额"></el-table-column> -->
      <el-table-column label="渠道ID" align="center" prop="channelId">
        <template slot-scope="{ row }">
          <span v-if="!row.isInner">
            {{ row.channelId }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="channelName" label="渠道名称">
        <template slot-scope="{ row }">
          <el-tag size="small" type="info" v-if="row.isInner">{{
            row.channelName
          }}</el-tag>
          <el-tag size="small" v-else>{{ row.channelName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="0星数量"
        align="center"
        prop="zeroStarNum"
      ></el-table-column>
      <el-table-column
        label="1星数量"
        align="center"
        prop="oneStarNum"
      ></el-table-column>
      <el-table-column
        label="2星数量"
        align="center"
        prop="twoStarNum"
      ></el-table-column>

      <el-table-column
        label="3星数量"
        align="center"
        prop="threeStarNum"
      ></el-table-column>
      <el-table-column
        label="4星数量"
        align="center"
        prop="fourStarNum"
      ></el-table-column>
      <el-table-column
        label="5星数量"
        align="center"
        prop="fiveStarNum"
      ></el-table-column>
      <el-table-column
        label="合计"
        align="center"
        prop="total"
      ></el-table-column>
      <el-table-column label="操作" align="center" prop="total">
        <template slot-scope="{ row }">
          <div>
            <el-button v-if="row.isInner" @click="handleDetail(row)" type="text"
            >查看详情</el-button>
              <el-button v-else @click="handleExport(row)" type="text"
              >导出0星数据</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="fetchTableData"
    />
    <el-dialog
      :visible.sync="dialogVisible"
      title="详情"
      width="80%"
      append-to-body
    >
      <el-table :data="detailList" border>
        <el-table-column
          label="产品ID"
          align="center"
          prop="id"
        ></el-table-column>
        <el-table-column
          label="产品名称"
          align="center"
          prop="productName"
        ></el-table-column>
        <el-table-column
          label="推送时间"
          align="center"
          prop="pushDay"
        ></el-table-column>
        <el-table-column
          label="0星数量"
          align="center"
          prop="zeroStarNum"
        ></el-table-column>
        <el-table-column
          label="1星数量"
          align="center"
          prop="oneStarNum"
        ></el-table-column>
        <el-table-column
          label="2星数量"
          align="center"
          prop="twoStarNum"
        ></el-table-column>

        <el-table-column
          label="3星数量"
          align="center"
          prop="threeStarNum"
        ></el-table-column>
        <el-table-column
          label="4星数量"
          align="center"
          prop="fourStarNum"
        ></el-table-column>
        <el-table-column
          label="5星数量"
          align="center"
          prop="fiveStarNum"
        ></el-table-column>
        <el-table-column
          label="合计"
          align="center"
          prop="totalNum"
        ></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss"></style>
