<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="标签名称" prop="label">
        <el-input v-model.number="queryParams.label" placeholder="请输入标签名称" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标签状态" prop="labelStatus">

        <el-select v-model="queryParams.labelStatus" clearable size="small">

          <el-option :value="0" label="禁用"></el-option>
          <el-option :value="1" label="启用"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableList" border>
      <el-table-column label="标签ID" align="center" prop="labelId" />
      <el-table-column label="标签名称" align="center" prop="label" />
      <el-table-column label="状态" align="center" prop="labelStatus">
        <template  slot-scope="{row}">
          <div>
            <el-switch v-model="row.labelStatus"  :active-value="1"
              :inactive-value="0" active-text="启用" inactive-text="禁用" @change="changeStatus(row)"></el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" prop="label">
        <template  slot-scope="{row}">
          <div>
            <el-button type="text" icon="el-icon-edit" @click="handleEdit(row)">修改</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog title="新增标签" :visible.sync="showTag" width="600px" append-to-body @close="cancel">
      <el-form label-width="100px">
        <el-form-item label="标签名称">
          <el-input v-model="label" maxlength="10" placeholder="请输入标签名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改标签" :visible.sync="showEditTag" width="600px" append-to-body>
      <el-form label-width="100px">
        <el-form-item label="标签名称">
          <el-input v-model="editForm.label" maxlength="10" placeholder="请输入标签名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="showEditTag = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import { getTagList, addTagOne, changeTagStatus, editTagOne } from "@/api/saas/tag"
export default {
  data() {
    return {
      tableList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        label: "",
        labelStatus: ""
      },
      label: "",
      editForm: {
        labelId: "",
        label: ""
      },
      showEditTag: false,
      showTag: false
    }
  },
  methods: {
    getList() {
      getTagList(this.queryParams).then(res => {
        this.tableList = res.rows
        this.total = res.total
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleAdd() {
      this.showTag = true
    },
    changeStatus(row) {
      this.$confirm("确定要修改状态吗？", {
        type: 'warning'
      }).then(res => {
        changeTagStatus({ labelId: row.labelId }).then(res => {
          this.$message.success("状态修改成功")
        })
      }).catch(err => {
        row.labelStatus = row.labelStatus == 0 ? 1 : 0
      })

    },
    submitForm() {
      if (!this.label) return this.$message.error("标签名称不能为空")
      addTagOne({
        label: this.label
      }).then(res => {
        this.$message.success('新增成功')
        this.getList()
        this.cancel()
      })
    },
    handleEdit(row) {
      this.showEditTag = true
      this.editForm.labelId = row.labelId
      this.editForm.label = row.label
    },
    submitEditForm() {
      if (!this.editForm.label) return this.$message.error("标签名称不能为空")
      editTagOne(this.editForm).then(res => {
        this.showEditTag = false
        this.getList()
        this.$message.success('修改成功')
      })
    },
    cancel() {
      this.showTag = false
      this.label = ""
    }
  },
  mounted() {
    this.getList()
  }
}
</script>

<style lang="scss" scoped>
</style>
