<template>
  <div class="app-container">
    <div class="status" v-if="show">
      合同状态: <span :style="color[status]">{{ statusType[status] }}</span>
      <span v-if="status == 3" style="margin-left: 10px">失败理由：{{ contractCheckRemark }}</span>
    </div>
    <el-form label-position="top" :model="formData" ref="formData" :rules="rules">
      <div>
        <div class="account">账户认证</div>
        <div class="info">
          <el-row type="flex" justify="space-between">
            <el-col :span="6">
              <el-form-item label="合同编号" prop="">
                <el-input maxlength="50" :disabled="status == 1" size="mini" v-model="formData.contractNo"
                  placeholder="请输入合同编号" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="认证类型" prop="authType">
                <el-select :disabled="status == 1" v-model="formData.authType" clearable size="mini" style="width: 100%"
                  placeholder="请选择认证类型">
                  <el-option label="公司" :value="1"></el-option>
                  <el-option label="个人" :value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公司全称：">
                <el-input maxlength="40" :disabled="status == 1" size="mini" v-model="formData.companyName"
                  placeholder="请输入公司全称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" justify="space-between">
            <el-col :span="6">
              <el-form-item label="公司统一社会信用码:">
                <el-input maxlength="30" :disabled="status == 1" size="mini" v-model="formData.companyCreditCode"
                  placeholder="请输入公司统一社会信用码" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="授权代表姓名:" prop="authName">
                <el-input :disabled="status == 1" size="mini" maxlength="6" v-model="formData.authName"
                  placeholder="请输入授权代表姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="授权代表身份证号：" prop="authIdCard">
                <el-input :disabled="status == 1" size="mini" placeholder="请输入授权代表身份证号"
                  v-model.trim="formData.authIdCard" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" justify="space-between">
            <el-col :span="6">
              <el-form-item label="联系人：" prop="contactsName">
                <el-input :disabled="status == 1" size="mini" maxlength="6" placeholder="请输入联系人"
                  v-model="formData.contactsName" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="联系人电话:" prop="contactsPhone">
                <el-input :disabled="status == 1" size="mini" maxlength="11" v-model.trim="formData.contactsPhone"
                  placeholder="请输入联系人电话 ：" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="联系人邮箱:" prop="contactsEmail">
                <el-input :disabled="status == 1" size="mini" maxlength="50" v-model.trim="formData.contactsEmail"
                  placeholder="请输入联系人邮箱" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="联系人地址:" prop="contactsAddress">
                <el-input maxlength="50" :disabled="status == 1" size="mini" v-model="formData.contactsAddress"
                  placeholder="请输入联系人地址" />
              </el-form-item>
            </el-col>
            <el-col :span="6" :offset="3">
              <el-form-item label="备注" prop="remark">
                <el-input :disabled="status == 1" size="mini" v-model="formData.remark" placeholder="请输入备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
      <div>
        <div class="account">办公地址</div>
        <div class="info">
          <el-row type="flex" justify="space-between">
            <el-col :span="6">
              <el-form-item label="所在省份" prop="provinceCode">
                <el-select :disabled="status == 1" v-model="formData.provinceCode" clearable size="mini"
                  style="width: 100%" @change="getProvinceCode">
                  <el-option :label="i.name" :value="i.code" v-for="i in cityList" :key="i.code"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在市区" prop="cityCode">
                <el-select :disabled="status == 1" v-model="formData.cityCode" clearable size="mini" style="width: 100%"
                  @change="getAreaCodeCode">
                  <el-option :label="i.name" :value="i.code" v-for="i in areaList" :key="i.code"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="所在地区" prop="areaCode">
                <el-select :disabled="status == 1" v-model="formData.areaCode" clearable size="mini" style="width: 100%">
                  <el-option :label="i.name" :value="i.code" v-for="i in countrysList" :key="i.code"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="6">
              <el-form-item label="详细地址" prop="address">
                <el-input maxlength="50" :disabled="status == 1" size="mini" placeholder="请输入详细地址"
                  v-model="formData.address" />
              </el-form-item>
            </el-col>
            <el-col :span="6" :offset="3">
              <el-form-item label="门牌号" prop="doorplate">
                <el-input maxlength="50" :disabled="status == 1" size="mini" v-model="formData.doorplate"
                  placeholder="请输入门牌号" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="account">资质信息</div>
      <div class="images">
        <el-row type="flex" justify="space-between" :gutter="20">
          <el-col :span="12">
            <el-row type="flex" :gutter="12">
              <el-col :span="6">
                <el-form-item label="营业执照" prop="businessLicenseFilename">
                  <el-upload class="avatar-uploader" ref="businessLicenseFilename" :show-file-list="false"
                    :on-change="(e) => changeUpfile(e, { type: 1, name: 'businessLicenseFilename', path: 'businessLicenseFilepath' })"
                    action="#" :auto-upload="false">
                    <img v-if="imageUrl" :src="imageUrl" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="营业执照注册时间" prop="businessLicenseRegisterDate">
                  <el-date-picker v-model="formData.businessLicenseRegisterDate" @change="hanldeDate"
                    value-format="yyyy-MM-dd" type="date" size="small" placeholder="选择日期">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="营业执照有效期" prop="businessLicenseValidityDate" :rules="{
                  required: !formData.isBusinessLicenseForever,
                  message: '请选择营业执照有效期',
                  trigger: 'change',
                }">
                  <el-date-picker v-model="formData.businessLicenseValidityDate" type="date"
                    :disabled="formData.isBusinessLicenseForever" size="small" value-format="yyyy-MM-dd"
                    placeholder="选择日期" style="margin-right:5px">
                  </el-date-picker>
                  <el-checkbox v-model="formData.isBusinessLicenseForever" @change="hanldeValidityDate">长期</el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="12">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="身份证正面" prop="frontIdCardFilename">
                  <el-upload class="avatar-uploader" :auto-upload="false" action="#" :show-file-list="false"
                    ref="frontIdCardFilename"
                    :on-change="(e) => changeUpfile(e, { type: 1, name: 'frontIdCardFilename', path: 'frontIdCardFilepath' })">
                    <img v-if="imageUrl1" :src="imageUrl1" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="反面" prop="reverseIdCardFilename">
                  <el-upload class="avatar-uploader" :auto-upload="false" action="#" ref="reverseIdCardFilename"
                    :on-change="(e) => changeUpfile(e, { type: 1, name: 'reverseIdCardFilename', path: 'reverseIdCardFilepath' })"
                    :show-file-list="false">
                    <img v-if="imageUrl2" :src="imageUrl2" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="8">

                <el-form-item prop="idGrantType" label="授权类型">
                  <div class="f_c">
                    <el-radio v-model="formData.idGrantType" :label="1">法人</el-radio>
                    <el-radio v-model="formData.idGrantType" :label="2">授权人</el-radio>
                  </div>
                </el-form-item>

              </el-col>
            </el-row>

          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-row :gutter="20">
              <el-col :span="5" v-if="!dateStatus">
                <el-form-item label="租赁协议（营业执照注册时间未满半年）" prop="leaseAgreementFilename">
                  <el-upload class="upload-demo" action="#" :auto-upload="false" ref="leaseAgreementFilename"
                    :show-file-list="false"
                    :on-change="(e) => changeUpfile(e, { type: 3, name: 'leaseAgreementFilename', path: 'leaseAgreementFilepath' })">
                    <el-button size="mini" type="primary">点击上传</el-button>
                    <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
                  </el-upload>
                </el-form-item>
                <el-tag type="info" size="small" v-if="formData.leaseAgreementFilename"
                  @close="handleRemoveFile({ name: 'leaseAgreementFilename', path: 'leaseAgreementFilepath' })" closable>
                  <span class="tag">{{ formData.leaseAgreementFilename }}</span>
                </el-tag>
              </el-col>
              <el-col :span="10" v-if="!dateStatus">
                <el-form-item prop="leaseType" label="承租类型">
                  <div class="f_c">
                    <el-radio v-model="formData.leaseType" :label="1">承租人为法人或公司</el-radio>
                    <el-radio v-model="formData.leaseType" :label="2">承租人为个人</el-radio>
                  </div>
                </el-form-item>


              </el-col>
              <el-col :span="5" v-if="!dateStatus && formData.leaseType == '2'">
                <el-form-item label="租赁授权协议" prop="leaseAgreementGrantFilename">
                  <el-upload class="upload-demo" action="#" :auto-upload="false" ref="leaseAgreementGrantFilename"
                    :show-file-list="false"
                    :on-change="(e) => changeUpfile(e, { type: 3, name: 'leaseAgreementGrantFilename', path: 'leaseAgreementGrantFilepath' })">
                    <el-button size="mini" type="primary">点击上传</el-button>
                    <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
                  </el-upload>

                </el-form-item>
                <el-tag type="info" size="small" v-if="formData.leaseAgreementGrantFilename"
                  @close="handleRemoveFile({ name: 'leaseAgreementGrantFilename', path: 'leaseAgreementGrantFilepath' })"
                  closable>
                  <span class="tag">{{ formData.leaseAgreementGrantFilename }}</span>
                </el-tag>
              </el-col>

            </el-row>
          </el-col>
          <el-col :span="12">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="办公场所视频" prop="officeVideoFilename">
                  <el-upload class="upload-demo" action="#" :auto-upload="false" ref="officeVideoFilename"
                    :show-file-list="false"
                    :on-change="(e) => changeUpfile(e, { type: 2, name: 'officeVideoFilename', path: 'officeVideoFilepath' })">
                    <el-button size="mini" type="primary">点击上传</el-button>
                    <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
                  </el-upload>
                </el-form-item>
                <el-tag type="info" size="small" v-if="formData.officeVideoFilename"
                  @close="handleRemoveFile({ name: 'officeVideoFilename', path: 'officeVideoFilepath' })" closable>
                  <span class="tag">{{ formData.officeVideoFilename }}</span>
                </el-tag>
              </el-col>
              <el-col :span="8">
                <el-form-item label="合同文件" prop="contractFilename">
                  <el-upload class="upload-demo" action="#" :auto-upload="false" :show-file-list="false"
                    :on-change="(e) => changeUpfile(e, { type: 3, name: 'contractFilename', path: 'contractFilepath' })">
                    <el-button size="mini" type="primary">点击上传</el-button>
                    <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
                  </el-upload>
                </el-form-item>
                <el-tag type="info" size="small" v-if="formData.contractFilename"
                  @close="handleRemoveFile({ name: 'contractFilename', path: 'contractFilepath' })" closable>
                  <span class="tag">{{ formData.contractFilename }}</span>
                </el-tag>
              </el-col>
              <el-col :span="8" v-if="isLoanType">
                <el-form-item label="员工在职证明" prop="certificateEmploymentFilename">
                  <el-upload class="upload-demo" action="#" :auto-upload="false" ref="certificateEmploymentFilename"
                    :show-file-list="false"
                    :on-change="(e) => changeUpfile(e, { type: 3, name: 'certificateEmploymentFilename', path: 'certificateEmploymentFilepath' })">
                    <el-button size="mini" type="primary">点击上传</el-button>
                    <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
                  </el-upload>
                </el-form-item>
                <el-tag type="info" size="small" v-if="formData.certificateEmploymentFilename"
                  @close="handleRemoveFile({ name: 'certificateEmploymentFilename', path: 'certificateEmploymentFilepath' })"
                  closable>
                  <span class="tag">{{ formData.certificateEmploymentFilename }}</span>
                </el-tag>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-row :gutter="20">
              <el-col :span="5">
                <el-form-item label="其他文件">
                  <el-upload class="upload-demo" action="#" :auto-upload="false" :limit="10" :fileList="fileList"
                    :on-change="(e, fileList) => handleOtherFile(e, fileList)"
                    :on-remove="(e, fileList) => handleOtherRemove(e, fileList)">
                    <el-button size="mini" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">单个文件不能超过2MB；软著、放款资质、ICP备案或其他资料请上传至补充资料</div>
                  </el-upload>
                </el-form-item>
              </el-col>

            </el-row>
          </el-col>

        </el-row>
        <!-- 流程 -->
        <div v-if="processList.length">
          <div class="check-title">审核信息</div>
          <div class="check-info">
            <el-timeline>
              <el-timeline-item v-for="(activity, index) in processList" :key="index" :color="processColor(activity)">
                <div :class="[activity.status == 2 ? 'c_red' : '']">
                  <div> {{ activity.name }}</div>
                  <div>时间:{{ activity.checkTime }}</div>
                  <div v-if="activity.status != -2">状态：{{ statusJson[activity.status] }}</div>
                  <div v-if="activity.status != -2"> 备注：{{ activity.checkRemark || "-" }}</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>

    </el-form>
    <div class="center">
      <el-button v-if="status == 0" type="primary" class="submit" @click="submitform">提交</el-button>
      <el-button type="primary" v-if="status == 2 || status == 3" class="submit"
        v-hasPermi="['partyaAdmin:contract:update']" @click="submitupDataform">修改</el-button>
    </div>
  </div>
</template>

<script>
import {
  getFindAllArea,
  contractUploadFile,
  AddPartyaAdminContract,
  updataContractInfo,
} from "@/api/partyManage";
import { getContractInfo } from "@/api/partyA";
export default {
  props: ["id", "isLoanType"],
  data() {
    var validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("手机号不能为空"));
      } else if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
        callback(new Error("手机号格式错误"));
      } else {
        callback();
      }
    };
    var validateEamil = (rule, value, callback) => {
      if (!value) {
        callback(new Error("邮箱不能为空"));
      } else if (
        !/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(
          value
        )
      ) {
        callback(new Error("邮箱格式错误"));
      } else {
        callback();
      }
    };
    var validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("名字不能为空"));
      } else if (!/^[\u4e00-\u9fa5]{2,6}$/.test(value)) {
        callback(new Error("名字格式错误"));
      } else {
        callback();
      }
    };


    var validateID = (rule, value, callback) => {
      if (!value) {
        callback(new Error("授权代表身份证号不能为空"));
      } else if (
        !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/.test(
          value
        ) ||
        !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/.test(
          value
        )
      ) {
        callback(new Error("格式错误"));
      } else {
        callback();
      }
    };
    return {
      imageUrl: "",
      imageUrl1: "",
      imageUrl2: "",
      cityList: [],
      provinceList: [],
      areaList: [],
      countrysList: [],
      fileList: [],
      processList: [],
      contractCheckRemark: "",
      show: false,
      color: {
        0: "color:red",
        1: "color:#DAA520",
        2: "color:#32CD32",
        3: "color:red",
      },
      statusJson: {
        0: "发起",
        1: "通过",
        2: "驳回",
        3: "结束",
      },
      statusType: {
        // @ApiModelProperty(value = "合同审核状态(0未上传/1待审核/2审核成功/3审核失败)")
        0: "未上传",
        1: "待审核",
        2: "审核成功",
        3: "审核失败",
      },
      dateStatus: true,
      status: 0,
      formData: {
        authType: null,
        partyFirstId: null,
        companyName: null,
        companyCreditCode: null,
        authName: null,
        authIdCard: null,
        contactsName: null,
        contactsPhone: null,
        contactsEmail: null,
        contactsAddress: null,
        doorplate: null,
        areaCode: null,
        provinceCode: null,
        cityCode: null,
        businessLicenseFilename: null,
        businessLicenseFilepath: null,
        frontIdCardFilename: null,
        frontIdCardFilepath: null,
        reverseIdCardFilename: null,
        reverseIdCardFilepath: null,
        officeVideoFilename: null,
        officeVideoFilepath: null,
        leaseAgreementFilepath: null,
        leaseAgreementFilename: null,
        leaseAgreementGrantFilename: null,
        leaseAgreementGrantFilepath: null,
        contractFilename: null,
        contractFilepath: null,
        certificateEmploymentFilename: null,
        certificateEmploymentFilepath: null,
        otherInfoFiles: [],
        address: null,
        contractNo: null,
        remark: null,
        businessLicenseRegisterDate: null,
        businessLicenseValidityDate: null,
        isBusinessLicenseForever: false,
        idGrantType: "",
        leaseType: ""
      },
      rules: {
        authType: [
          { required: true, message: "请选择认证类型", trigger: "blur" },
        ],
        frontIdCardFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        businessLicenseFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        certificateEmploymentFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        reverseIdCardFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        leaseAgreementFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        officeVideoFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        contractFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        leaseAgreementGrantFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        idGrantType: [
          { required: true, message: "请选择授权类型", trigger: "change" },
        ],
        leaseType: [
          { required: true, message: "请选择承租类型", trigger: "change" },
        ],
        businessLicenseRegisterDate: [
          { required: true, message: "请选择注册时间", trigger: "change" },
        ],
        companyName: [
          { required: true, message: "请输入公司全称", trigger: "blur" },
        ],
        companyCreditCode: [
          {
            required: true,
            message: "请输入公司统一社会信用码",
            trigger: "blur",
          },
        ],
        authName: [
          { required: true, validator: validateName, trigger: "blur" },
        ],
        authIdCard: [
          {
            required: true,
            validator: validateID,
            trigger: "blur",
          },
        ],
        contactsName: [
          { required: true, validator: validateName, trigger: "blur" },
        ],
        contactsPhone: [
          { required: true, validator: validatePhone, trigger: "blur" },
        ],
        contactsEmail: [
          { required: true, validator: validateEamil, trigger: "blur" },
        ],
        contactsAddress: [
          { required: true, message: "请输入联系人地址", trigger: "blur" },
        ],
        areaCode: [{ required: true, message: "请选择区", trigger: "blur" }],
        cityCode: [{ required: true, message: "请选择市", trigger: "blur" }],
        provinceCode: [
          { required: true, message: "请选择省", trigger: "blur" },
        ],
        doorplate: [
          { required: true, message: "请输入门牌号", trigger: "blur" },
        ],
        address: [{ required: true, message: "请输入地址", trigger: "blur" }],
        // contractNo: [
        //   { required: true, message: "请输入合同编号", trigger: "blur" },
        // ],
      },
    };
  },
  methods: {
    // @ApiModelProperty(value = "合同审核状态(0未上传/1待审核/2审核成功/3审核失败)")
    //验证营业执照是否是长期
    hanldeValidityDate(e) {
      if (e) {
        this.formData.businessLicenseValidityDate = null
      }
    },
    //操作选择的时间
    hanldeDate(e) {

      if (!e) {
        this.dateStatus = true
        return
      }
      let date = +new Date()
      let lastDate = 86400 * 182 * 1000
      let selectDate = new Date(e).valueOf()
      this.dateStatus = (date - selectDate > lastDate)
    },
    //上传文件
    changeUpfile(e, dataInfo) {

      if (dataInfo.type == 1) {
        const isJPG =
          e.raw.type === "image/jpg" ||
          e.raw.type === "image/jpeg" ||
          e.raw.type === "image/png";
        const isLt2M = e.size / 1024 / 1024 < 10;
        if (!isJPG) {

          this.$message.error("上传图片只能是 JPG/PNG/jpeg 格式!");
          return;
        }
        if (!isLt2M) {

          this.$message.error("上传图片大小不能超过 10MB!");
          return;
        }
      }
      if (dataInfo.type == 3) {
        const isLt2M = e.size / 1024 / 1024 < 10;
        if (!isLt2M) {
          this.$message.error("上传文件大小不能超过 10MB!");
          return
        }
      }
      if (dataInfo.type == 2) {
        if (!e.raw.type.includes("video")) {

          this.$message.error("请上传视频");
          return;
        }
        const isLt2M = e.size / 1024 / 1024 < 20;
        if (!isLt2M) {

          this.$message.error("上传文件大小不能超过 20MB!");
          return;
        }
      }

      this.$refs.formData.clearValidate(dataInfo.name)
      let data = new FormData();
      data.append("fileNum", dataInfo.type);
      data.append("file", e.raw);
      if (dataInfo.name == "businessLicenseFilename") {
        this.imageUrl = URL.createObjectURL(e.raw)
      }
      contractUploadFile(data, this.id).then((res) => {
        this.formData[dataInfo.name] = res.filename
        this.formData[dataInfo.path] = res.filepath
        if (dataInfo.name == "businessLicenseFilename") {
          this.imageUrl = URL.createObjectURL(e.raw)
        }
        if (dataInfo.name == "frontIdCardFilename") {
          this.imageUrl1 = URL.createObjectURL(e.raw)
        }
        if (dataInfo.name == "reverseIdCardFilename") {
          this.imageUrl2 = URL.createObjectURL(e.raw)
        }
      });
    },
    //删除文件
    handleRemoveFile(e) {
      this.formData[e.name] = ""
      this.formData[e.path] = ""
    },
    //上传其他文件
    handleOtherFile(e, fileList) {
      const isLt2M = e.size / 1024 / 1024 < 10;
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 10MB!");
        return
      }
      if (fileList.length > 10) {
        this.$message.error("不能超过10个文件")
        return
      }
      let data = new FormData();
      data.append("fileNum", 3);
      data.append("file", e.raw);
      contractUploadFile(data, this.id).then(res => {
        this.fileList.push({ name: res.filename, url: res.filepath })
        this.formData.otherInfoFiles = this.fileList.map(item => {
          return {
            filename: item.name,
            filepath: item.url
          }
        })
      })
    },
    //删除其他文件
    handleOtherRemove(e, fileList) {
      this.fileList = fileList
      this.formData.otherInfoFiles = fileList.map(item => {
        return {
          filename: item.name,
          filepath: item.url
        }
      })
    },
    submitform() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.partyFirstId = this.id;
          if (this.formData.leaseType == 1) {
            this.formData.leaseAgreementGrantFilename = ""
            this.formData.leaseAgreementGrantFilepath = ""
          }
          if (this.dateStatus) {
            this.formData.leaseAgreementFilepath = ""
            this.formData.leaseAgreementFilename = ""
            this.formData.leaseType = ""
            this.formData.leaseAgreementGrantFilename = ""
            this.formData.leaseAgreementGrantFilepath = ""

          }

          AddPartyaAdminContract(this.formData).then((res) => {
            if (res.code == 200) {
              this.getInfo();
              this.$parent.$parent.contactAvisible = false;
              this.$parent.$parent.getList();
              this.$message.success("上传成功");
            }
          });
        }
      });
    },

    getProvinceCode(e) {
      this.formData.areaCode = "";
      this.formData.cityCode = "";
      this.areaList = this.cityList.filter((item) => item.code == e)[0]?.citys;
    },
    getAreaCodeCode(e) {
      this.formData.areaCode = "";
      this.countrysList = this.areaList.filter(
        (item) => item.code == e
      )[0]?.countrys;
    },
    submitupDataform() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.partyFirstId = this.id;
          if (this.formData.leaseType == 1) {
            this.formData.leaseAgreementGrantFilename = ""
            this.formData.leaseAgreementGrantFilepath = ""
          }
          if (this.dateStatus) {
            this.formData.leaseAgreementFilepath = ""
            this.formData.leaseAgreementFilename = ""
            this.formData.leaseType = ""
            this.formData.leaseAgreementGrantFilename = ""
            this.formData.leaseAgreementGrantFilepath = ""

          }
          updataContractInfo(this.formData).then((res) => {
            this.$message.success("修改成功");
            this.$parent.$parent.contactAvisible = false;
            this.$parent.$parent.getList();
            this.getInfo();
          });
        }
      });
    },
    getInfo() {
      getContractInfo(this.id).then((res) => {
        const data = res.data;
        this.show = true;
        if (res.data.provinceCode) {
          this.getProvinceCode(res.data.provinceCode);
          this.getAreaCodeCode(res.data.cityCode);
        }
        setTimeout(() => {
          this.$refs.formData.clearValidate('businessLicenseValidityDate')
        }, 100)
        if (res.data.leaseAgreementFilename) {
          this.dateStatus = false
        }
        this.status = res.data.contractCheckStatus;
        this.contractCheckRemark = res.data.contractCheckRemark;
        this.imageUrl = res.data.businessLicenseFilenameUrl
        this.imageUrl1 = res.data.frontIdCardFilenameUrl
        this.imageUrl2 = res.data.reverseIdCardFilenameUrl
        this.processList = res.data.processList
        this.hanldeDate(res.data.businessLicenseRegisterDate)
        let list = res.data.fileOrderList || []
        this.fileList = list.map(item => {
          return {
            name: item.filename,
            url: item.filepath
          }
        })
        this.formData.otherInfoFiles = list.map(item => {
          return {
            filename: item.filename,
            filepath: item.filepath
          }
        })
        // 还原数据
        for (let key in data) {
          // 接口请求出来的
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = data[key];
          }
        }
      });
    },
  },
  computed: {
    processColor: () => {
      return (item) => {
        if (item.status == -1) {
          return ''
        } else if (item.status == 2) {
          return "#ff0000"
        } else {
          return "#00a607"
        }
      };
    }
  },
  mounted() {

    getFindAllArea().then((res) => {
      this.cityList = res.data;
      this.getInfo();
    });
  },
};
</script>

<style lang="scss" scoped>
.app-container {

  padding: 0 0;
}

.avatar {
  max-height: 120px;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 120px;
  height: 120px;
  line-height: 120px;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.account {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

::v-deep .info .el-form-item__label {
  line-height: 0px !important;
  font-size: 12px;
}

.iamge-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 270px;
  border: 1px solid #9999;

  &:hover {
    border: 1px solid #1890ff;
  }

  &-title {
    margin-top: 20px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #666;
  }
}



::v-deep .el-upload-list__item-name {
  width: 120px;
  overflow: hidden;
}

.submit {
  margin-top: 20px;
  width: 150px;
}

.center {
  display: flex;
  justify-content: center;
}

.status {
  margin-bottom: 20px;
  font-size: 16px;
  color: #333;
}

.remove {
  margin-top: -16px;
  width: 100%;
  display: flex;
  justify-content: center;

  button {
    padding: 0;
    width: 40px;
    height: 20px;
    line-height: 0;
  }
}

.file-remove {
  display: flex;
  width: 100%;
  height: 30px;
  justify-content: center;
  margin-top: 2px;

  button {
    padding: 0;
    width: 40px;
    height: 20px;
    line-height: 0;
  }
}

.f_c {
  height: 80px;
  line-height: 80px;
}

.tag {
  width: 100px;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::v-deep .el-tag.el-tag--info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 150px;
}

::v-deep .el-dialog__body {
  max-height: calc(100vh - 120px);
  overflow: auto;
}


::v-deep .el-upload-list__item.is-ready {
  display: none;
}
</style>
