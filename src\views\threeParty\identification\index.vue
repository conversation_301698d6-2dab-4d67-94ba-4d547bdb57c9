<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item label="key名称">
        <el-input size="mini" v-model="queryParams.name" clearable></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleAdd"
          >添加</el-button
        >
      </el-form-item>
    </el-form>

    <el-table border :data="list">
      <el-table-column label="key名称" prop="keyName" align="center" />
      <el-table-column label="key标识" prop="smsTemplateKey" align="center" />
      <el-table-column label="短信签名" prop="signName" align="center" />
      <el-table-column label="状态" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.status == 1 ? "有效" : "无效" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" align="center" />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="handEdit(row)"
              >修改信息</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="isadd ? '新增key' : '修改key'"
      :visible.sync="keyAvisible"
      width="500px"
      append-to-body
      center
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="key名称" prop="keyName">
          <el-input
            v-model.trim="formData.keyName"
            placeholder="请输入key名称"
          />
        </el-form-item>
        <el-form-item label="key标识" prop="smsTemplateKey">
          <el-input
          :disabled="!isadd"
            v-model.trim="formData.smsTemplateKey"
            placeholder="请输入key标识"
          />
        </el-form-item>
        <el-form-item label="短信签名" prop="signName">
          <el-input
            v-model.trim="formData.signName"
            placeholder="请输入短信签名"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio v-model="formData.status" :label="1">有效</el-radio>
          <el-radio v-model="formData.status" :label="2">无效</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSmsTemplateList,
  addSmsTemplateOne,
  editSmsTemplateOne,
} from "@/api/threeParty/note";
export default {
  data() {
    return {
      list: [],
      isadd: true,
      formData: {
        keyName: "",
        smsTemplateKey: "",
        signName: "",
        status: 1,
      },
      keyAvisible: false,
      queryParams: {
        name: "",
      },
      rules: {
        keyName: [
          { required: true, message: "请输入key名称", trigger: "blur" },
        ],
        smsTemplateKey: [
          { required: true, message: "请输入key", trigger: "blur" },
        ],
        status: [{ required: true, message: "请输选择状态", trigger: "blur" }],
      },
    };
  },
  methods: {
    handleQuery() {
        this.getList()
    },
    getList() {
      getSmsTemplateList(this.queryParams).then((res) => {
        this.list = res.data;
      });
    },
    handEdit(row) {
      this.keyAvisible = true;
      this.isadd = false;
      this.formData.status = row.status;
      this.formData.keyName = row.keyName;
      this.formData.smsTemplateKey = row.smsTemplateKey;
      this.formData.signName = row.signName || "";
    },
    handleAdd() {
      this.keyAvisible = true;
      this.isadd = true;
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isadd) {
            addSmsTemplateOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.getList();
                this.cancel();
              }
            });
          } else {
            editSmsTemplateOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.getList();
                this.cancel();
              }
            });
          }
        }
      });
    },
    cancel() {
      this.keyAvisible = false;
      this.formData = {
        keyName: "",
        smsTemplateKey: "",
        signName: "",
        status:1
      };
      this.$refs.formData.resetFields();
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
