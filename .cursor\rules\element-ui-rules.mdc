---
description: 
globs: 
alwaysApply: true
---
## loading

页面和组件中，在网络请求时不需要设置loading，因为全局请求拦截器已经设置过了。

## 页面顶部筛选表单项

页面中的筛选条件，要使用el-form组件，每个控件都要使用单独的el-form-item组件，el-form组件不需要再使用div包裹。

## size 属性

Element UI的组件不需要设置size属性。

## 分页功能

页面中如果需要分页功能，使用src\components\Pagination\index.vue组件。

## 减少元素嵌套

避免不必要的元素嵌套，默认禁止使用自定义的css。

## 状态显示

展示不同的状态数据（如已通过、已解决），使用不同类型的el-tag组件。

## 模态框（弹窗）

宽度：根据实际情况设置为400px到1200px之间。

模态框中的表单，关闭模态框时需要重置表单字段和校验。

## 表单

表单组件el-form-item都需要设置prop属性