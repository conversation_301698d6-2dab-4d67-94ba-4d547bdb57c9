<template>
  <div class="platform-type-setting">
    <el-form-item label="平台类型">
      <el-radio-group v-model="deviceOs" @change="handlePlatformChange">
        <el-radio label="ios">iOS</el-radio>
        <el-radio label="android">Android</el-radio>
      </el-radio-group>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'PlatformTypeSetting',
  props: {
    deviceConfig: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      deviceOs: this.deviceConfig.os
    }
  },
  watch: {
    'deviceConfig.os': {
      handler(newVal) {
        this.deviceOs = newVal;
      }
    }
  },
  methods: {
    handlePlatformChange() {
      this.$emit('update:platform', {
        ...this.deviceConfig,
        os: this.deviceOs
      });
    }
  }
}
</script>

<style scoped lang="scss">
.platform-type-setting {
  margin-bottom: 20px;
}
</style> 