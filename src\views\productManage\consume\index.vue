<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="推广名称" prop="name">
        <el-date-picker
          v-model="times"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>

      <el-form-item v-if="isCps">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-table border :data="consumesList">
      <el-table-column label="日期" prop="profitDate" align="center" />
      <el-table-column label="合作价格" prop="cooperationCost" align="center" />
      <el-table-column label="合作方式" prop="cooperationCost" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ type[row.cooperationType] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="申请量/推送量" prop="putinPush" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.putinPush ? row.putinPush : "-" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="放款额" prop="loanAmount" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.loanAmount ? row.loanAmount : "-" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="收益" prop="profit" align="center" />
      <el-table-column label="返点金额" prop="rebateAmount" align="center" />
      <el-table-column label="操作" align="center" v-if="isCps">
        <template  slot-scope="{row}">
          <div>
            <el-button type="text" icon="el-icon-edit" @click="handleEdit(row)">
              修改</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="isadd ? '新增消耗' : '修改消耗'"
      :visible.sync="cpsAvisible"
      @close="cancle"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="收益日期" prop="profitDate">
          <el-date-picker
            :disabled="!isadd"
            v-model="formData.profitDate"
            type="date"
            :picker-options="pickerOptions"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            @input="changeDate"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="申请量/推送量"
          prop="putinPush"
          :rules="{
            required:
              (loanType == 1 || loanType == 3) && formData.profitDate
                ? true
                : false,
            message: '请输入申请量/推送量',
            trigger: 'blur',
          }"
        >
          <el-input
            @input="getprofit"
            :disabled="!formData.profitDate"
            size="small"
            v-model.number="formData.putinPush"
            type="number"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="放款额"
          prop="loanAmount"
          :rules="{
            required: loanType == 2 && formData.profitDate ? true : false,
            message: '请输入申请量/推送量',
            trigger: 'blur',
          }"
        >
          <el-input
            @input="getprofit"
            :disabled="!formData.profitDate"
            size="small"
            v-model.number="formData.loanAmount"
            type="number"
          ></el-input>
        </el-form-item>
        <el-form-item label="收益" prop="profit">
          <el-input
            :disabled="!formData.profitDate"
            size="small"
            v-model.number="formData.profit"
            type="number"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancle">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  productEarningsList,
  checkProductIsCps,
  addproductProfitOne,
  editproductProfitOne,
  getProductProfit,
} from "@/api/productManage/product";
export default {
  data() {
    return {
      isCps: false,
      cpsAvisible: false,
      isadd: true,
      total: 0,
      type: {
        1: "CPA",
        2: "CPS",
        3: "CPC",
      },

      loanType: null,
      cooperationCost: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productId: "",
        endTime: "",
        startTime: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },

      rules: {
        profitDate: [
          { required: true, message: "请选择收益日期", trigger: "blur" },
        ],
        profit: [{ required: true, message: "请输入收益", trigger: "blur" }],
        // loanAmount: [
        //   { required: true, message: "请输入放款额度", trigger: "blur" },
        // ],
        // putinPush: [
        //   { required: true, message: "请输入推送量", trigger: "blur" },
        // ],
      },
      formData: {
        loanAmount: 0,
        productId: null,
        profitDate: null,
        putinPush: 0,
        profit: 0,
      },
      times: [],
      consumesList: [],
    };
  },

  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.times !== null) {
        this.queryParams.startTime = this.times[0];
        this.queryParams.endTime = this.times[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    handleAdd() {
      this.isadd = true;
      this.cpsAvisible = true;
    },

    getprofit() {
      if (this.loanType == 2) {
        this.formData.profit = this.formData.loanAmount * this.cooperationCost;
      }
      if (this.loanType == 1 || this.loanType == 3) {
        this.formData.profit = this.formData.putinPush * this.cooperationCost;
      }
    },
    handleEdit(row) {
      this.isadd = false;
      this.cpsAvisible = true;
      this.loanType = row.cooperationType;
      this.formData.loanAmount = row.loanAmount;
      this.formData.profitDate = row.profitDate;
      this.formData.putinPush = row.putinPush;
      this.formData.profit = row.profit;
      this.cooperationCost = row.cooperationCost;
    },
    changeDate() {
      getProductProfit({
        profitDate: this.formData.profitDate,
        productId: this.$route.query.id,
      }).then((res) => {
        this.formData.putinPush = res.data.putinPush;
        this.formData.loanAmount = res.data.loanAmount;
        this.formData.profit = res.data.profit;
        this.loanType = res.data.cooperationType;
        this.cooperationCost = res.data.cooperationCost;
      });
      if (!this.formData.profitDate) {
        this.formData = {
          loanAmount: 0,
          productId: null,
          profitDate: null,
          putinPush: 0,
          profit: 0,
        };
        this.loanType = "";
      }
    },
    submitForm() {
      this.formData.productId = this.$route.query.id;
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isadd) {
            addproductProfitOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.getList();
                this.$message.success("新增成功");
                this.cancle();
              }
            });
          } else {
            editproductProfitOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.getList();
                this.cancle();
              }
            });
          }
        }
      });
    },
    cancle() {
      this.formData = {
        loanAmount: null,
        productId: null,
        profitDate: null,
        putinPush: null,
        profit: null,
      };
      this.cpsAvisible = false;
      this.$refs.formData.resetFields();
    },
    getList() {
      this.queryParams.productId = this.$route.query.id;
      productEarningsList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.consumesList = res.rows;
          this.total = res.total;
        }
      });
    },
  },
  mounted() {
    this.getList();
    checkProductIsCps(this.$route.query.id).then((res) => {
      this.isCps = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
</style>
