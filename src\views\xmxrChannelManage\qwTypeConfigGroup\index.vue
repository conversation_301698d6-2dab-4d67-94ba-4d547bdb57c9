<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="组名称" prop="groupName">
        <el-input
          v-model="queryParams.groupName"
          placeholder="请输入组名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="有效" value="1" />
          <el-option label="无效" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- Tab标签页 -->
    <el-tabs
      v-model="activeTab"
      type="card"
      @tab-click="handleTabClick"
      v-loading="loading"
      class="group-tabs"
    >
      <el-tab-pane
        v-for="group in filteredGroupedData"
        :key="group.groupName"
        :label="group.groupName"
        :name="group.groupName"
      >
        <!-- 分组信息卡片 -->
        <el-card class="group-info-card" shadow="never">
          <div slot="header" class="clearfix">
            <span class="group-title">{{ group.groupName }}</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              @click="handleUpdate(group.groupData)"
            >
              <i class="el-icon-edit"></i> 修改配置
            </el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="info-label">A组申请数：</span>
                <span class="info-value">{{ group.groupData.maxNum }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="info-label">B组申请数：</span>
                <span class="info-value">{{ group.groupData.maxLowNum }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="info-label">时间限制（分钟）：</span>
                <span class="info-value">{{ group.groupData.timeLimit }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item status-item">
                <span class="info-label">状态：</span>
                <el-switch
                  v-model="group.groupData.status"
                  :active-value="1"
                  :inactive-value="2"
                  @change="handleStatusChange(group.groupData)"
                ></el-switch>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 渠道列表 -->
        <el-card class="channel-list-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>渠道列表 ({{ group.channels.length }}个)</span>
          </div>
          <div class="channel-grid">
            <el-tag
              v-for="channel in group.channels"
              :key="channel.id"
              type="primary"
              closable
              class="channel-tag"
              @close="handleRemoveChannel(group.groupData, channel.id)"
            >
              {{ channel.id }}-{{ channel.channelName }}
            </el-tag>
          </div>
          <div v-if="group.channels.length === 0" class="empty-channels">
            <i class="el-icon-info"></i>
            <span>该分组暂无渠道</span>
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 空状态 -->
      <div v-if="filteredGroupedData.length === 0" class="empty-state">
        <i class="el-icon-folder-opened"></i>
        <p>暂无分组数据</p>
      </div>
    </el-tabs>

    <!-- 添加或修改配置组对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="组名称" prop="groupName">
              <el-input v-model="form.groupName" placeholder="请输入组名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="渠道列表" prop="channelIds">
              <el-select
                v-model="form.channelIds"
                multiple
                filterable
                placeholder="请选择渠道"
                style="width: 100%"
              >
                <el-option
                  v-for="channel in availableChannelOptions"
                  :key="channel.id"
                  :label="`${channel.id}-${channel.channelName}`"
                  :value="channel.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="A组申请数" prop="maxNum">
              <el-input-number
                v-model="form.maxNum"
                :min="1"
                :max="999"
                :controls="false"
                placeholder="请输入A组申请数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="B组申请数" prop="maxLowNum">
              <el-input-number
                v-model="form.maxLowNum"
                :min="1"
                :max="999"
                :controls="false"
                placeholder="请输入B组申请数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="时间限制（分钟）" prop="timeLimit">
              <el-input-number
                v-model="form.timeLimit"
                :min="1"
                :max="1440"
                :controls="false"
                placeholder="请输入时间限制（分钟）"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">有效</el-radio>
                <el-radio :label="2">无效</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getQwTypeConfigGroupList,
  addQwTypeConfigGroup,
  updateQwTypeConfigGroup,
  updateQwTypeConfigGroupStatus
} from '@/api/xmxrChannelManage/qwTypeConfigGroup'
import { getAllChannelList } from '@/api/channeManage/channelList'

export default {
  name: 'QwTypeConfigGroup',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 配置组表格数据
      configGroupList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 当前激活的Tab
      activeTab: '',
      // 查询参数（移除分页相关参数）
      queryParams: {
        groupName: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        groupName: [
          { required: true, message: '组名称不能为空', trigger: 'blur' }
        ],
        channelIds: [
          { required: true, message: '渠道列表不能为空', trigger: 'change' }
        ],
        maxNum: [
          { required: true, message: 'A组申请数不能为空', trigger: 'blur' }
        ],
        maxLowNum: [
          { required: true, message: 'B组申请数不能为空', trigger: 'blur' }
        ],
        timeLimit: [
          { required: true, message: '时间限制（分钟）不能为空', trigger: 'blur' }
        ]
      },
      // 渠道选项
      channelOptions: []
    }
  },
  computed: {
    // 分组数据
    groupedData() {
      if (!this.configGroupList.length) return []

      return this.configGroupList.map(group => {
        // 获取该分组的渠道信息
        const channelIds = group.channelIds ? group.channelIds.split(',') : []
        const channels = channelIds.map(id => {
          const channel = this.channelOptions.find(item => item.id == id)
          return channel || { id, channelName: '未知渠道' }
        })

        return {
          groupName: group.groupName,
          groupData: group,
          channels: channels
        }
      })
    },
    // 根据搜索条件过滤的分组数据
    filteredGroupedData() {
      let filtered = this.groupedData

      // 按组名称过滤
      if (this.queryParams.groupName) {
        filtered = filtered.filter(group =>
          group.groupName.toLowerCase().includes(this.queryParams.groupName.toLowerCase())
        )
      }

      // 按状态过滤
      if (this.queryParams.status) {
        filtered = filtered.filter(group =>
          group.groupData.status == this.queryParams.status
        )
      }

      return filtered
    },
    // 可用的渠道选项（排除已被其他分组使用的渠道）
    availableChannelOptions() {
      if (!this.channelOptions.length || !this.configGroupList.length) {
        return this.channelOptions
      }

      // 获取所有已被使用的渠道ID（排除当前正在编辑的分组）
      const usedChannelIds = new Set()

      // 获取当前正在编辑的分组的原始渠道ID（编辑模式下这些渠道都应该可选）
      const currentGroupOriginalChannelIds = new Set()

      this.configGroupList.forEach(group => {
        if (this.form.id && group.id === this.form.id) {
          // 记录当前编辑分组的原始渠道ID
          if (group.channelIds) {
            const channelIds = group.channelIds.split(',')
            channelIds.forEach(id => {
              if (id.trim()) {
                currentGroupOriginalChannelIds.add(parseInt(id.trim()))
              }
            })
          }
          return
        }

        // 收集其他分组使用的渠道ID
        if (group.channelIds) {
          const channelIds = group.channelIds.split(',')
          channelIds.forEach(id => {
            if (id.trim()) {
              usedChannelIds.add(parseInt(id.trim()))
            }
          })
        }
      })

      // 过滤出可用的渠道：
      // 1. 未被其他分组使用的渠道
      // 2. 当前编辑分组的原始渠道（无论是否还在表单中选中）
      return this.channelOptions.filter(channel =>
        !usedChannelIds.has(channel.id) || currentGroupOriginalChannelIds.has(channel.id)
      )
    }
  },
  created() {
    this.getList()
    this.getChannelOptions()
  },
  watch: {
    // 监听分组数据变化，设置默认激活的Tab
    filteredGroupedData: {
      handler(newVal) {
        if (newVal.length > 0 && !this.activeTab) {
          // 只在初始化时设置默认Tab
          this.activeTab = newVal[0].groupName
        } else if (newVal.length === 0) {
          this.activeTab = ''
        } else if (this.activeTab && !newVal.find(group => group.groupName === this.activeTab)) {
          // 只有当前激活的Tab在过滤后的数据中不存在时，才设置为第一个
          this.activeTab = newVal.length > 0 ? newVal[0].groupName : ''
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 查询配置组列表 */
    getList() {
      this.loading = true
      // 移除分页参数，因为接口不需要分页
      const params = {
        groupName: this.queryParams.groupName,
        status: this.queryParams.status
      }
      return getQwTypeConfigGroupList(params).then(response => {
        // 根据后端返回的数据结构调整
        if (response.data && Array.isArray(response.data)) {
          this.configGroupList = response.data
        } else if (response.rows) {
          // 兼容分页数据结构
          this.configGroupList = response.rows || []
        } else {
          this.configGroupList = []
        }
        this.loading = false
        return response
      }).catch(error => {
        this.loading = false
        throw error
      })
    },
    /** 获取渠道选项 */
    getChannelOptions() {
      getAllChannelList().then(response => {
        this.channelOptions = response.data || []
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        groupName: null,
        channelIds: [],
        maxNum: 1,
        maxLowNum: 1,
        timeLimit: 10,
        status: 1
      }
      this.resetForm('form')
    },
    /** Tab点击事件 */
    handleTabClick(tab) {
      this.activeTab = tab.name
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 搜索时重新获取数据，Tab会自动更新
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加企微类型配置组'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.open = true
      this.title = '修改企微类型配置组'
      this.form = {
        ...row,
        channelIds: row.channelIds ? row.channelIds.split(',').map(Number) : []
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const formData = {
            ...this.form,
            channelIds: this.form.channelIds.join(',')
          }
          // 保存当前激活的Tab，用于修改后恢复
          const currentActiveTab = this.activeTab

          if (this.form.id != null) {
            updateQwTypeConfigGroup(formData).then(() => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList().then(() => {
                // 修改成功后，恢复到原来的Tab（如果该分组名称没有改变）
                this.$nextTick(() => {
                  const targetGroup = this.filteredGroupedData.find(group =>
                    group.groupData.id === this.form.id
                  )
                  if (targetGroup) {
                    this.activeTab = targetGroup.groupName
                  } else if (currentActiveTab && this.filteredGroupedData.find(group =>
                    group.groupName === currentActiveTab
                  )) {
                    this.activeTab = currentActiveTab
                  }
                })
              })
            })
          } else {
            addQwTypeConfigGroup(formData).then(() => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList().then(() => {
                // 新增成功后，切换到新创建的分组Tab
                this.$nextTick(() => {
                  const newGroup = this.filteredGroupedData.find(group =>
                    group.groupName === formData.groupName
                  )
                  if (newGroup) {
                    this.activeTab = newGroup.groupName
                  }
                })
              })
            })
          }
        }
      })
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === 1 ? '启用' : '停用'
      // 保存当前激活的Tab
      const currentActiveTab = this.activeTab

      this.$modal.confirm('确认要"' + text + '""' + row.groupName + '"配置组吗？').then(function() {
        return updateQwTypeConfigGroupStatus({
          id: row.id,
          status: row.status
        })
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
        // 状态修改成功后，保持当前Tab
        this.getList().then(() => {
          this.$nextTick(() => {
            if (currentActiveTab && this.filteredGroupedData.find(group =>
              group.groupName === currentActiveTab
            )) {
              this.activeTab = currentActiveTab
            }
          })
        })
      }).catch(() => {
        row.status = row.status === 1 ? 2 : 1
        // 状态修改失败后，也要保持当前Tab
        this.getList().then(() => {
          this.$nextTick(() => {
            if (currentActiveTab && this.filteredGroupedData.find(group =>
              group.groupName === currentActiveTab
            )) {
              this.activeTab = currentActiveTab
            }
          })
        })
      })
    },
    /** 格式化渠道ID显示 */
    formatChannelIds(channelIds) {
      if (!channelIds) return '-'
      const ids = channelIds.split(',')
      const names = ids.map(id => {
        const channel = this.channelOptions.find(item => item.id == id)
        return channel ? `${channel.id}-${channel.channelName}` : id
      })
      return names.join(', ')
    },
    /** 移除渠道 */
    handleRemoveChannel(groupData, channelId) {
      this.$modal.confirm('确认要从该分组中移除此渠道吗？').then(() => {
        // 获取当前渠道ID列表
        const currentChannelIds = groupData.channelIds ? groupData.channelIds.split(',') : []
        // 移除指定的渠道ID
        const newChannelIds = currentChannelIds.filter(id => id != channelId)

        // 构造更新数据
        const updateData = {
          ...groupData,
          channelIds: newChannelIds.join(',')
        }

        // 保存当前激活的Tab
        const currentActiveTab = this.activeTab

        // 调用更新接口
        updateQwTypeConfigGroup(updateData).then(() => {
          this.$modal.msgSuccess('移除渠道成功')
          // 刷新列表数据
          this.getList().then(() => {
            this.$nextTick(() => {
              // 恢复当前Tab
              if (currentActiveTab && this.filteredGroupedData.find(group =>
                group.groupName === currentActiveTab
              )) {
                this.activeTab = currentActiveTab
              }
            })
          })
        })
      }).catch(() => {
        // 用户取消操作，不做任何处理
      })
    }
  }
}
</script>

<style scoped>
.group-tabs {
  margin-top: 20px;
}

.group-tabs .el-tabs__content {
  padding: 20px 0;
}

.group-info-card {
  margin-bottom: 20px;
}

.group-info-card .el-card__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.info-label {
  color: #606266;
  font-size: 14px;
  margin-right: 8px;
  min-width: 120px;
}

.info-value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

/* 状态开关样式调整 */
.status-item {
  display: flex;
  align-items: center;
}

.status-item .info-label {
  margin-right: 8px;
  flex-shrink: 0;
}

.status-item .el-switch {
  margin-left: 0;
}

.channel-list-card {
  margin-top: 20px;
}

.channel-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 40px;
}

.channel-tag {
  margin: 0;
}

.empty-channels {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  font-size: 14px;
}

.empty-channels i {
  margin-right: 8px;
  font-size: 18px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

/* Tab标签页样式优化 */
.group-tabs .el-tabs__header {
  margin-bottom: 0;
}

.group-tabs .el-tabs__nav-wrap {
  padding: 0 20px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.group-tabs .el-tabs__item {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  border: 1px solid transparent;
  border-bottom: none;
  margin-right: 2px;
}

.group-tabs .el-tabs__item.is-active {
  background-color: #fff;
  border-color: #e4e7ed;
  border-bottom-color: #fff;
  position: relative;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-label {
    margin-bottom: 4px;
    min-width: auto;
  }

  .channel-grid {
    gap: 6px;
  }


}
</style>
