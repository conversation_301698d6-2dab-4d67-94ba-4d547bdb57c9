<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="渠道类型" prop="channelIds">
        <el-select v-model="queryParams.channelIds" placeholder="渠道类型" clearable filterable multiple collapse-tags
          size="small">
          <el-option :value="item.id" :label="item.id+'----'+item.channelName" v-for="(item,index) in channelIdList "
            :key="index">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据类型" prop="dataType">
        <el-select v-model="queryParams.dataType" placeholder="数据类型" @change="getList" size="small">
          <el-option :value="1" label="切片数据"></el-option>
          <el-option :value="2" label="漏斗数据"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期类型" prop="dateType">
        <el-select v-model="queryParams.dateType" placeholder="日期类型" :disabled="!!dateRange&&dateRange.length>0"
          clearable size="small">
          <el-option value="1" label="今天"></el-option>
          <el-option value="2" label="昨天"></el-option>
          <el-option value="3" label="最近7天"></el-option>
          <el-option value="4" label="最近30天"></el-option>
          <el-option value="5" label="当月"></el-option>
          <el-option value="6" label="上月"></el-option>
          <el-option value="7" label="近半年"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd" :disabled="!!queryParams.dateType"
          type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="客户端" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="客户端" clearable size="small">
          <el-option :value="1" label="苹果"></el-option>
          <el-option :value="2" label="安卓"></el-option>

        </el-select>
      </el-form-item>
      <el-form-item label="产品类型" prop="productType">
        <el-select v-model="queryParams.productType" placeholder="产品类型" clearable size="small">
          <el-option value="1" label="表单产品"></el-option>
          <el-option value="2" label="接口产品"></el-option>
          <el-option value="3" label="线上产品"></el-option>
        </el-select>

      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>
    <el-table border :data="dataList">
      <el-table-column label="日期" prop="queryDate" align="center" width="150" />
      <el-table-column label="预估ROI" prop="predictProfitRate" align="center">
        <template slot-scope="{row}">
          <div>
            {{row.predictProfitRate}}%
          </div>
        </template>

      </el-table-column>
      <el-table-column label="H5" align="center">
        <el-table-column label="H5落地页UV" prop="uvNum" align="center" />
        <el-table-column label="注册成功用户数" prop="h5NewRegisterNum" align="center" />
        <!-- 切片 -->
        <el-table-column label="老登录用户数" prop="h5OldRegisterNum" align="center" v-if="queryParams.dataType==1" />
        <el-table-column label="注册成功率" prop="h5RegisterSuccessRate" align="center">
          <template slot-scope="{row}">
            <div>
              {{row.h5RegisterSuccessRate}}%
            </div>
          </template>
        </el-table-column>
        <el-table-column label="进件成功用户数" prop="h5AsstesNum" align="center" />
        <el-table-column label="进件率" prop="h5AsstesRate" align="center">
          <template slot-scope="{row}">
            <div>
              {{row.h5AsstesRate}}%
            </div>
          </template>
        </el-table-column>
        <el-table-column label="曝光产品用户数" prop="seeProductUserNum" align="center" />
        <el-table-column label="曝光1款产品用户数量" prop="seeProduct1UserNum" align="center" />
        <el-table-column label="曝光2款产品用户数量" prop="seeProduct2UserNum" align="center" />
        <el-table-column label="曝光3款产品用户数量" prop="seeProduct3UserNum" align="center" />
        <el-table-column label="点击用户数" prop="seeClickUserNum" align="center" />
        <el-table-column label="点击家数" prop="seeClickProductNum" align="center" />
        <el-table-column label="用户点击率" prop="seeUserClickRate" align="center">
          <template slot-scope="{row}">
            <div>
              {{row.seeUserClickRate}}%
            </div>
          </template>
        </el-table-column>
        <el-table-column label="平均点击数量" prop="seeAvgClickRate" align="center">
          <template slot-scope="{row}">
            <div>
              {{row.seeAvgClickRate}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="预估收益" prop="h5Profit" align="center" />
      </el-table-column>
      <el-table-column label="APP" align="center">
        <el-table-column label="APP下载页UV" prop="downloadUv" align="center" />
        <el-table-column label="APP下载按钮点击用户数" prop="downloadUserNum" align="center" />
        <el-table-column label="APP下载率" prop="downloadRate" align="center">
          <template slot-scope="{row}">
            <div>
              {{row.downloadRate}}%
            </div>
          </template>
        </el-table-column>
        <el-table-column label="APP登录用户数" prop="appLoginNum" align="center" />
        <el-table-column label="APP登录率" prop="appLoginRate" align="center">
          <template slot-scope="{row}">
            <div>
              {{row.appLoginRate}}%
            </div>
          </template>
        </el-table-column>
        <el-table-column label="点击产品用户数" prop="appClickUserNum" align="center" />
        <el-table-column label="点击产品数量" prop="appProductSuccessNum" align="center" />
        <el-table-column label="申请成功用户数" prop="appSuccessUserNum" align="center" />
        <el-table-column label="用户点击率" prop="appUserApplyRate" align="center">
          <template slot-scope="{row}">
            <div>
              {{row.appUserApplyRate}}%
            </div>
          </template>
        </el-table-column>
        <el-table-column label="平均点击数量" prop="appAvgUserRate" align="center">
          <template slot-scope="{row}">
            <div>
              {{row.appAvgUserRate}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="预估收益" prop="appProfit" align="center" />
      </el-table-column>


    </el-table>
  </div>
</template>

<script>
import { getBusinessDataList, getBusinessChannelList } from "@/api/statisticalManage";
export default {
  data() {
    return {
      dateRange: [],
      dataList: [],
      channelIdList: [],
      queryParams: {
        endDate: "",
        startDate: "",
        channelIds: [],
        dataType: 2,
        deviceType: '',
        dateType: '1',
        productType: ""
      }
    }
  },
  methods: {
    handleQuery() {
      if (this.dateRange && this.dataList.length > 0) {
        this.queryParams.startDate = this.dateRange[0]
        this.queryParams.endDate = this.dateRange[1]
      } else {
        this.queryParams.startDate = ""
        this.queryParams.endDate = ""
      }
      this.getList()
    },
    getList() {
      getBusinessDataList(this.queryParams).then(res => {
        this.dataList = res.data
      })
    }
  },
  mounted() {
    this.getList()
    getBusinessChannelList().then(res => {

      this.channelIdList = res.data
    })
  }
}
</script>

<style lang="scss" scoped>

</style>
