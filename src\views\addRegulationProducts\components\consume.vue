<template>
  <el-drawer
    title="消耗明细"
    :visible.sync="visible"
    append-to-body
    :size="width"
    @close="handleCloseDrawer"
  >
    <main class="container-consume">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="推广时间" prop="name">
          <el-date-picker
            v-model="times"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >
            筛选
          </el-button>
        </el-form-item>

        <el-form-item v-if="isCps">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >
            新增
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-download"
            size="mini"
            @click="aa"
            v-hasPermi="['loan:productProfit:export']"
          >
            导出
          </el-button>
        </el-form-item>
      </el-form>
      <el-table border :data="consumesList">
        <el-table-column label="日期" prop="profitDate" align="center" />
        <el-table-column
          label="合作价格"
          prop="cooperationCost"
          align="center"
        />
        <el-table-column label="合作方式" prop="cooperationCost" align="center">
          <template slot-scope="{ row }">
            <div>
              {{ type[row.cooperationType] }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请量/推送量" prop="putinPush" align="center">
          <template slot-scope="{ row }">
            <div>
              {{ row.putinPush ? row.putinPush : "-" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="放款额" prop="loanAmount" align="center">
          <template slot-scope="{ row }">
            <div>
              {{ row.loanAmount ? row.loanAmount : "-" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="收益" prop="profit" align="center" />
        <el-table-column label="返点金额" prop="rebateAmount" align="center" />
        <el-table-column label="操作" align="center" v-if="isCps">
          <template slot-scope="{ row }">
            <div>
              <el-button
                type="text"
                icon="el-icon-edit"
                @click="handleEdit(row)"
              >
                修改</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="init"
      />
      <el-dialog
        :title="isadd ? '新增消耗' : '修改消耗'"
        :visible.sync="cpsAvisible"
        @close="cancle"
        width="600px"
        append-to-body
        center
        :close-on-click-modal="false"
      >
        <el-form
          ref="formData"
          :model="formData"
          :rules="rules"
          label-width="120px"
        >
          <el-form-item label="收益日期" prop="profitDate">
            <el-date-picker
              :disabled="!isadd"
              v-model="formData.profitDate"
              type="date"
              :picker-options="pickerOptions"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              @input="changeDate"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            label="申请量/推送量"
            prop="putinPush"
            :rules="{
              required:
                (loanType == 1 || loanType == 3) && formData.profitDate
                  ? true
                  : false,
              message: '请输入申请量/推送量',
              trigger: 'blur',
            }"
          >
            <el-input
              @input="getprofit"
              :disabled="!formData.profitDate"
              size="small"
              v-model.number="formData.putinPush"
              type="number"
            />
          </el-form-item>
          <el-form-item
            label="放款额"
            prop="loanAmount"
            :rules="{
              required: loanType == 2 && formData.profitDate ? true : false,
              message: '请输入申请量/推送量',
              trigger: 'blur',
            }"
          >
            <el-input
              @input="getprofit"
              :disabled="!formData.profitDate"
              size="small"
              v-model.number="formData.loanAmount"
              type="number"
            />
          </el-form-item>
          <el-form-item label="收益" prop="profit">
            <el-input
              :disabled="!formData.profitDate"
              size="small"
              v-model.number="formData.profit"
              type="number"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancle">取 消</el-button>
        </div>
      </el-dialog>
    </main>
  </el-drawer>
</template>

<script>
import {
  productEarningsList,
  checkProductIsCps,
  addproductProfitOne,
  editproductProfitOne,
  getProductProfit,
  exportaa,
} from "@/api/productManage/product";
export default {
  name: "consume",
  data() {
    return {
      visible: false,
      isCps: true,
      times: [],
      isadd: false,
      cpsAvisible: false,
      loanType: "",
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productId: "",
        endTime: "",
        startTime: "",
      },
      type: {
        1: "CPA",
        2: "CPS",
        3: "CPC",
      },
      rules: {
        profitDate: [
          { required: true, message: "请选择收益日期", trigger: "blur" },
        ],
        profit: [{ required: true, message: "请输入收益", trigger: "blur" }],
        // loanAmount: [
        //   { required: true, message: "请输入放款额度", trigger: "blur" },
        // ],
        // putinPush: [
        //   { required: true, message: "请输入推送量", trigger: "blur" },
        // ],
      },
      formData: {
        loanAmount: 0,
        productId: null,
        profitDate: null,
        putinPush: 0,
        profit: 0,
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      consumesList: [],
    };
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: "80%",
    },
    id: {
      type: [Number, String],
      default: "",
    },
    product: {
      type: Object,
      default: () => ({})
    }
  },

  watch: {
    value: {
      handler() {
        this.visible = this.value;
        if (this.visible && this.id) {
          this.init();
          checkProductIsCps(this.id).then((res) => {
            this.isCps = res.data;
          });
        }
      },
      deep: true,
    },
  },
  methods: {
    /**
     *  @param { Function } init 初始化 - 获取控量设置列表
     */
    init() {
      this.queryParams.productId = this.id;
      productEarningsList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.consumesList = res.rows;
          this.total = res.total;
        }
      });
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.times !== null) {
        this.queryParams.startTime = this.times[0];
        this.queryParams.endTime = this.times[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.init();
    },

    handleAdd() {
      this.isadd = true;
      this.cpsAvisible = true;
    },

    handleEdit(row) {
      this.isadd = false;
      this.cpsAvisible = true;
      this.loanType = row.cooperationType;
      this.formData.loanAmount = row.loanAmount;
      this.formData.profitDate = row.profitDate;
      this.formData.putinPush = row.putinPush;
      this.formData.profit = row.profit;
      this.cooperationCost = row.cooperationCost;
    },

    /**
     *  @param { Function } handleCloseDrawer 关闭弹窗
     */
    handleCloseDrawer() {
      this.$emit("close", false);
      this.$emit("update");
    },

    changeDate() {
      getProductProfit({
        profitDate: this.formData.profitDate,
        productId: this.id,
      }).then((res) => {
        this.formData.putinPush = res.data.putinPush;
        this.formData.loanAmount = res.data.loanAmount;
        this.formData.profit = res.data.profit;
        this.loanType = res.data.cooperationType;
        this.cooperationCost = res.data.cooperationCost;
      });
      if (!this.formData.profitDate) {
        this.formData = {
          loanAmount: 0,
          productId: null,
          profitDate: null,
          putinPush: 0,
          profit: 0,
        };
        this.loanType = "";
      }
    },

    getprofit() {
      if (this.loanType == 2) {
        this.formData.profit = this.formData.loanAmount * this.cooperationCost;
      }
      if (this.loanType == 1 || this.loanType == 3) {
        this.formData.profit = this.formData.putinPush * this.cooperationCost;
      }
    },
    submitForm() {
      this.formData.productId = this.id;
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isadd) {
            addproductProfitOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.init();
                this.$message.success("新增成功");
                this.cancle();
              }
            });
          } else {
            editproductProfitOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.init();
                this.cancle();
              }
            });
          }
        }
      });
    },
    cancle() {
      this.formData = {
        loanAmount: null,
        productId: null,
        profitDate: null,
        putinPush: null,
        profit: null,
      };
      this.cpsAvisible = false;
      this.$refs.formData.resetFields();
    },
    aa() {
      if (!this.times || !this.times.length)
        return this.$message.error("请选择时间");
      exportaa({
        startTime: this.times[0],
        endTime: this.times[1],
        productId: this.id,
      }).then(res=>{
        console.log(res);
        let a = document.createElement("a");
          let blob = new Blob([res], { type: "application/vnd.ms-excel" });
          let objectUrl = URL.createObjectURL(blob);
          a.setAttribute("href", objectUrl);
          a.setAttribute("download", `产品统计.xlsx`);
          a.click();
          this.$message.success("导出成功");
      });

    },
  },
};
</script>

<style lang="scss" scoped>
.container-consume {
  padding: 24px 24px 16px;
}
</style>
