<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dataRange"
          :clearable="false"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="商务">
        <el-select
          v-model="queryParams.userId"
          size="small"
          placeholder="请选择商务"
          clearable
        >
          <el-option
            v-for="(item, index) in userList"
            :key="index"
            :label="item.nickName"
            :value="item.userId"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="dataList">
      <el-table-column label="商务" prop="nickName" align="center" />
      <el-table-column label="银行机构消耗" prop="bankProfit" align="center" />
      <el-table-column
        label="银行机构返点"
        prop="bankProfitBack"
        align="center"
      />
      <el-table-column
        label="银行机构内部返点"
        prop="interiorBankProfitBack"
        align="center"
      />
      <el-table-column
        label="银行机构提成"
        prop="shareBankProfit"
        align="center"
      />

      <el-table-column
        label="全国机构消耗"
        prop="nationwideProfit"
        align="center"
      />
      <el-table-column
        label="全国机构返点"
        prop="nationwideProfitBack"
        align="center"
      />
      <el-table-column
        label="全国机构内部返点"
        prop="interiorNationwideProfitBack"
        align="center"
      />
      <el-table-column
        label="全国机构提成"
        prop="shareNationwideProfit"
        align="center"
      />

      <el-table-column
        label="贷超机构消耗"
        prop="onlineProfit"
        align="center"
      />
      <el-table-column
        label="贷超机构返点"
        prop="onlineProfitBack"
        align="center"
      />
      <el-table-column
        label="贷超机构内部返点"
        prop="interiorOnlineProfitBack"
        align="center"
      />
      <el-table-column
        label="贷超机构提成"
        prop="shareOnlineProfit"
        align="center"
      />
      <el-table-column label="合计提成" prop="total" align="center" />
    </el-table>
  </div>
</template>

<script>
import {
  getpartyFirstUser,
  getUserProfitStats,
  getConsumeTotal,
} from "@/api/crmReport";
import * as xlsx from "xlsx";
export default {
  data() {
    return {
      dataRange: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      dataList: [],
      userList: [],
      total: 0,
      queryParams: {
        userId: "",
        page: 1,
        size: 1500,
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
      },
    };
  },
  methods: {
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    //搜索
    handleQuery() {
      if (this.dataRange !== null) {
        this.queryParams.page = 1;
        this.queryParams.startTime = this.dataRange[0];
        this.queryParams.endTime = this.dataRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },

    getList() {
      getConsumeTotal(this.queryParams).then((res) => {
        this.dataList = res.data;
      });
    },
    handleExport() {
      let arrlist = [];
      this.dataList.forEach((item) => {
        arrlist.push({
          商务: item.nickName,
          银行机构消耗: item.bankProfit,
          银行机构返点: item.bankProfitBack,
          银行机构内部返点: item.interiorBankProfitBack,
          银行机构提成: item.shareBankProfit,
          全国机构消耗: item.nationwideProfit,
          全国机构返点: item.nationwideProfitBack,
          全国机构内部返点: item.interiorNationwideProfitBack,
          全国机构提成: item.shareNationwideProfit,
          贷超机构消耗: item.onlineProfit,
          贷超机构返点: item.onlineProfitBack,
          贷超机构内部返点: item.interiorOnlineProfitBack,
          贷超机构提成: item.shareOnlineProfit,
          合计提成: item.total,
        });
      });

      let sheet = xlsx.utils.json_to_sheet(arrlist);
      let book = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(book, sheet, "sheet1");
      xlsx.writeFile(book, `提成报表.xls`);
    },
  },
  mounted() {
    this.getList();
    getpartyFirstUser().then((res) => {
      this.userList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped></style>
