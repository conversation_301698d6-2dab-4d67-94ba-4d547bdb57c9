<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" size="small">
      <el-form-item label="渠道名称" prop="channelName">
        <el-input
          clearable
          placeholder="请输入渠道名称"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道ID" prop="channelId">
        <el-input
          clearable
          placeholder="请输入渠道ID"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
          v-model="queryParams.channelId"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="channelList">
      <el-table-column label="渠道ID" prop="id" align="center" />
      <el-table-column label="渠道名称" prop="channelName" align="center" />
      <el-table-column label="渠道类型" prop="type" align="center">
        <template slot-scope="{ row }">
          <span>{{ getChannelType(row.type) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="城市撞库开关" prop="cityCheck" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.cityCheck"
              active-text="开启"
              inactive-text="关闭"
              :active-value="2"
              :inactive-value="1"
              @change="changeCityCheck($event, row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="匹配产品数量" prop="matchCount" align="center" width="150">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.matchCount"
            type="number"
            :min="0"
            style="width: 130px"
            @change="handleChangeMatchCount(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="银行机构数量" prop="bankCount" align="center" width="150">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.bankCount"
            type="number"
            :min="0"
            style="width: 130px"
            @change="handleChangeBankCount(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="全国机构数量" prop="countryCount" align="center" width="150">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.countryCount"
            type="number"
            :min="0"
            style="width: 130px"
            @change="handleChangeCountryCount(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="产品总价格" prop="priceSum" align="center">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.priceSum"
            type="number"
            :min="0"
            style="width: 130px"
            @change="handleChangePriceSum(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="银行最低单价" prop="maxPrice" align="center">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.maxPrice"
            type="number"
            :min="0"
            style="width: 130px"
            @change="handleChangeMaxPrice(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="企微最低单价" prop="weChatMaxPrice" align="center">
        <template slot-scope="scope">
          <el-input
            v-model.number="scope.row.weChatMaxPrice"
            type="number"
            :min="0"
            style="width: 130px"
            @change="handleChangeWeChatMaxPrice(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getCityLoginControlList,
  updateCityLoginControl
} from "@/api/channeManage/cityLoginControl";

export default {
  name: "CityLoginControl",
  data() {
    return {
      total: 0,
      channelList: [],
      queryParams: {
        channelName: "",
        channelId: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    getList() {
      getCityLoginControlList(this.queryParams).then((res) => {
        // 处理cityCheck字段：
        // 后端定义：cityCheck=1表示关闭状态，但没有具体指定表示开启的值（可能是2、null等）
        // 而前端el-switch组件需要具体的值（这里用2）来表示开启状态
        // 所以这里统一处理：只要不是1，就都转为2，确保开关组件正确显示
        this.channelList = res.rows.map(item => ({
          ...item,
          cityCheck: item.cityCheck == 1 ? 1 : 2
        }));
        this.total = res.total;
      });
    },
    //查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 获取渠道类型名称
    getChannelType(type) {
      const typeMap = {
        1: '联登',
        2: '半流程',
        3: 'api',
        4: '全流程UV',
        5: '信息流'
      }
      return typeMap[type] || '-'
    },
    changeCityCheck(e, row) {
      this.$confirm(
        `${row.channelName}(ID:${row.id}) ${e == 1 ? "关闭" : "开启"}城市撞库？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          const params = {
            ...row,
            cityCheck: e
          }
          updateCityLoginControl(params)
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch(() => {
              this.getList();
            });
        })
        .catch(() => {
          this.getList();
        });
    },
    handleChangeMatchCount(row) {
      this.handleCommonChange(row, 'matchCount', '匹配产品数量');
    },
    handleChangePriceSum(row) {
      this.handleCommonChange(row, 'priceSum', '产品总价格');
    },
    handleChangeMaxPrice(row) {
      this.handleCommonChange(row, 'maxPrice', '银行最低单价');
    },
    handleChangeWeChatMaxPrice(row) {
      this.handleCommonChange(row, 'weChatMaxPrice', '企微最低单价');
    },
    handleChangeBankCount(row) {
      this.handleCommonChange(row, 'bankCount', '银行机构数量');
    },
    handleChangeCountryCount(row) {
      this.handleCommonChange(row, 'countryCount', '全国机构数量');
    },
    handleCommonChange(row, field, fieldName) {

      if (row[field]) {
        row[field] = parseInt(row[field])
      }

      this.$confirm(
        `${row.channelName}(ID:${row.id}) ${fieldName}改为 ${row[field]}？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          updateCityLoginControl(row)
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch(() => {
              this.getList();
            });
        })
        .catch(() => {
          this.getList();
        });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.el-switch__label.el-switch__label--left.is-active {
  color: #333;
}

::v-deep .el-input {
  input[type="number"] {
    padding-right: 0px;
    appearance: textfield;
    // -moz-appearance: textfield;
    // -webkit-appearance: textfield;
    // 解决el-input设置类型为number时，中文输入法光标上移问题
    line-height: 1px !important;
  }
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    appearance: none;
    // -webkit-appearance: none;
    margin: 0;
  }
}
</style> 