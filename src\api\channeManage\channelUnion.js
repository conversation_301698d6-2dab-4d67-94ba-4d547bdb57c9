import request from '@/utils/request'
//获取渠道列表
export const getUnionList = (params) => {
  return request({
    url: "/loan/PartyB/loginChannelControl",
    method: "get",
    params
  })
}
//修改控量状态
export const editChangeAutoStatus = (data) => {
  return request({
    url: "/loan/PartyB/channel/autoControlStatus",
    method: "post",
    data
  })
}
//修改uv价格
export const editChangeUvPrice = (data) => {
  return request({
    url: "loan/PartyB/channel/updateUvPrice",
    method: "post",
    data
  })
}
export const editInputRatio = (data) => {
  return request({
    url: "loan/PartyB/channel/updateInputRatio ",
    method: "post",
    data
  })
}
