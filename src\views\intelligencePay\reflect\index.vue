<template>
  <div class="app-container">
    <el-form inline>
      <el-form-item label="分账商户号">
        <el-input v-model="queryParams.subMerchantCode" placeholder="请输入分账商户号" clearable size="small"></el-input>
      </el-form-item>
      <el-form-item label="智付提现编号">
        <el-input v-model="queryParams.transferNo" placeholder="请输入智付提现编号" clearable size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" @click="handleQuery">搜索</el-button>
        <el-button size="small" type="primary" @click="hanldeBalance" v-hasPermi="['loan:din_pay_transfer:add']">查询余额信息
        </el-button>
        <el-button size="small" type="primary" @click="hanldeWithdraw"
          v-hasPermi="['loan:din_pay_transfer:query_balance']">提现</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableList">

      <el-table-column label="提现ID" align="center" prop="id" />
      <el-table-column label="商家提现编号" align="center" prop="merTransferNo" width="300" />
      <el-table-column label="分账商户号" align="center" prop="subMerchantCode" width="200" />
      <el-table-column label="提现金额" align="center" prop="tranAmount" />
      <el-table-column label="提现方式" align="center" prop="tranType">
        <template  slot-scope="{row}">
          <div>
            {{ row.tranType == 1 ? "加急" : "普通" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="扣除手续费方式" align="center" prop="tranFeeType" width="200">
        <template  slot-scope="{row}">
          <div>
            {{ row.tranFeeType == 0 ? "从提现金额中扣除" : "" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="交易码" align="center" prop="recvCode" width="100">
        <template  slot-scope="{row}">
          <div>
            {{ codeType[row.recvCode] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="提现信息" align="center" prop="recvInfo" width="200" />
      <el-table-column label="智付提现编号" align="center" prop="transferNo" width="200" />
      <el-table-column label="申请提现金额" align="center" prop="tranApplyAmount" width="150" />
      <el-table-column label="提现时间" align="center" prop="tranDate" width="160" />
      <el-table-column label="备注" align="center" prop="remark" width="200" />
      <el-table-column label="创建者" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="200" />
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div v-if="row.recvCode == '0001'">
            <el-button @click="handleStatus(row)" type="text" v-hasPermi="['loan:din_pay_transfer:query']">查询
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog :visible.sync="showBalance" width="600px" title="查询余额" append-to-body center
      :close-on-click-modal="false" @close="formData.subMerchantCode = ''">
      <el-form label-position="left" label-width="60px">
        <el-form-item label="商户号">
          <el-input v-model="formData.subMerchantCode" placeholder="请输入商户号" size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSeachAccount">查 询</el-button>
        </el-form-item>
        <div class="account-info" v-if="acountInfo.accountAmount">
          <div>
            总金额:{{ acountInfo.accountAmount }}
          </div>
          <div>
            可用余额:{{ acountInfo.accountAvailableAmount }}
          </div>
          <div>
            账户币种（CNY）:{{ acountInfo.accountCurrency }}
          </div>
          <div>
            账户状态:{{ accountStatusType[acountInfo.accountStatus] }}
          </div>
        </div>
      </el-form>
    </el-dialog>

    <el-dialog :visible.sync="showWithdraw" width="600px" title="余额提现" append-to-body center
      :close-on-click-modal="false" @close="cancelWithDraw">
      <el-form label-position="left" :rules="rules" ref="withdrawRef" :model="withdrawFormData" label-width="100px">
        <el-form-item label="分账商户号" prop="subMerchantCode">
          <el-input v-model.trim="withdrawFormData.subMerchantCode" placeholder="请输入分账商户号" size="small"></el-input>
        </el-form-item>
        <el-form-item label="提现金额" prop="tranAmount">
          <el-input v-model.trim="withdrawFormData.tranAmount" type="number" placeholder="请输入提现金额" size="small">
          </el-input>
        </el-form-item>
        <el-form-item label="提现方式" prop="tranType">
          <el-radio v-model="withdrawFormData.tranType" label="0">普通</el-radio>
          <el-radio v-model="withdrawFormData.tranType" label="1">加急</el-radio>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="withdrawFormData.remark" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitWithdrawFormData">确认提现</el-button>
          <el-button @click="cancelWithDraw">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getStbQueryBalance,
  addStbTransfer,
  getStbTransferQuery,
  getTransferList
} from "@/api/intelligencePay"

export default {
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        subMerchantCode: "",
        transferNo: ""
      },
      codeType: {
        "0000": "提现成功", "0001": "提现处理中", "0002": "提现失败", "0003": "提现取消", "0004": "余额查询成功",
        "1003": "查无此交易", "1004": "提现超限", "1005": "非法参数", "1006": "验签失败", "1007": "商家无直联提现权限",
        "1008": "账户余额不足", "1009": "商家账户不存在", "2000": "系统异常", "9000": "未知错误"
      },
      accountStatusType: {
        "N": "正常",
        "F": "冻结",
        "C": "销户"
      },
      acountInfo: {},
      withdrawFormData: {
        "remark": "",
        "subMerchantCode": "",
        "tranAmount": "",
        "tranType": "0"
      },
      formData: {
        subMerchantCode: ""
      },
      tableList: [],
      total: 0,
      showBalance: false,
      showWithdraw: false,
      rules: {
        remark: [
          {
            required: true,
            message: "请输入备注",
            trigger: "blur",
          },
        ],
        subMerchantCode: [
          {
            required: true,
            message: "请输入分账商户号",
            trigger: "blur",
          },
        ],
        tranAmount: [
          {
            required: true,
            message: "请输入提现金额",
            trigger: "blur",
          },
        ],

      }
    }
  },
  methods: {
    getList() {

      getTransferList(this.queryParams).then(res => {
        this.tableList = res.rows
        this.total = res.total
      })
    },
    //查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    //查询余额弹窗
    hanldeBalance() {
      this.showBalance = true
      this.acountInfo = {}
    },
    handleSeachAccount() {
      this.acountInfo = {}
      if (!this.formData.subMerchantCode) return this.$message.error("商户号不能为空")
      getStbQueryBalance(this.formData.subMerchantCode).then(res => {
        this.acountInfo = res.data
      })
    },
    //提现
    hanldeWithdraw() {
      this.showWithdraw = true
    },
    cancelWithDraw() {
      this.showWithdraw = false
      this.withdrawFormData = {
        "remark": "",
        "subMerchantCode": "",
        "tranAmount": "",
        "tranType": "1"
      }
      this.$refs.withdrawRef.resetFields()
    },
    submitWithdrawFormData() {
      this.$refs.withdrawRef.validate((valid) => {
        if (valid) {
          addStbTransfer(this.withdrawFormData).then(res => {
            this.$message.success("提现成功")
            this.getList()
            this.cancelWithDraw()
          })
        }
      })
    },
    handleStatus(row) {
      getStbTransferQuery(row.id).then(res => {
        this.$message.success(res.msg)
        this.getList()
      })
    }
  },
  mounted() {
    this.getList()
  }
}
</script>

<style lang="scss" scoped>
.account-info {
  font-size: 16px;
  line-height: 30px;
}
</style>

