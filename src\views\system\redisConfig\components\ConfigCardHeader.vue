<template>
  <div class="config-card-header">
    <div class="config-info">
      <div class="config-title-line">
        <div class="config-title-container">
          <h4 class="config-title">{{ config.redisTitle }}</h4>
          <div class="config-key-container">
            <span class="config-key">{{ config.redisKey }}</span>
            <el-tooltip content="复制键名" placement="top">
              <i class="fas fa-copy copy-icon" @click="handleCopy"></i>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConfigCardHeader',
  props: {
    config: {
      type: Object,
      required: true,
      default: () => ({
        redisTitle: '',
        redisKey: '',
        redisValueType: 1
      })
    }
  },
  
  methods: {
    handleCopy() {
      this.$emit('copy', this.config.redisKey)
    }
  }
}
</script>

<style lang="scss" scoped>
.config-card-header {
  padding: 12px 16px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);

  .config-info {
    .config-title-line {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .config-title-container {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;

        .config-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--text-primary);
        }

        .config-key-container {
          display: flex;
          align-items: center;
          gap: 4px;

          .config-key {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            color: var(--text-tertiary);
            background: var(--bg-tertiary);
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px solid var(--border-light);
          }

          .copy-icon {
            color: var(--text-tertiary);
            padding: 2px 4px;
            cursor: pointer;
            border-radius: 3px;
            transition: all 0.3s ease;

            &:hover {
              color: var(--redis-secondary-color);
              background: rgba(33, 150, 243, 0.1);
            }
          }
        }
      }
    }
  }
}


</style>