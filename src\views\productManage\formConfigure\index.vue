<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="字段名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入字段名称" clearable @keyup.enter.native="handleQuery"
          size="small" />
      </el-form-item>
      <el-form-item>
        <el-select v-model="queryParams.fieldGroup" placeholder="请选择字段类型" @change="handleQuery" filterable clearable>
          <el-option label="基础信息" :value="1" />
          <el-option label="联系人信息" :value="2" />
          <el-option label="公司信息" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">
          新增</el-button>

      </el-form-item>
    </el-form>
    <el-table :data="tableList" border>
      <el-table-column label="字段ID" prop="id" align="center" />
      <el-table-column label="字段名称" prop="fieldName" align="center" />
      <el-table-column label="字段参数" prop="fieldKey" align="center" />
      <el-table-column label="字段归属" prop="fieldGroup" align="center">
        <template slot-scope="{row}">
          <div>
            {{typeJson[row.fieldGroup]}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="字段类型" prop="fieldType" align="center">
        <template slot-scope="{row}">
          <div>
            {{typeOptionJson[row.fieldType]}}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center">
        <template slot-scope="{row}">
          <div>
            <el-button type="text" @click="handleEdit(row)">修改</el-button>
            <el-button type="text" @click="handleDel(row)">删除</el-button>
            <el-button type="text" @click="handleOptions(row)" v-if="row.fieldType=='SELECT'">编辑选项</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :title="isAdd?'新增字段':'修改字段'" :visible.sync="showAdd" width="1000px" append-to-body center
      :close-on-click-modal="false" @close="cancal">
      <el-form label-width="100px" :rules="rules" :model="formData" ref="form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="字段名称" prop="fieldName">
              <el-input v-model="formData.fieldName" placeholder="请输入字段名称" />
            </el-form-item>
            <el-form-item label="字段参数" prop="fieldKey">
              <el-input v-model="formData.fieldKey" :disabled="!isAdd" placeholder="请输入字段参数" />
            </el-form-item>
            <el-form-item label="字段排序">
              <el-input v-model.number="formData.fieldSort" placeholder="请输入字段排序" />
            </el-form-item>
            <el-form-item label="字段归属" prop="fieldGroup">
              <el-select v-model="formData.fieldGroup" class="m-2" placeholder="请选择字段类型" filterable clearable>
                <el-option label="基础信息" :value="1" />
                <el-option label="联系人信息" :value="2" />
                <el-option label="公司信息" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="字段类型" prop="fieldType">
              <el-select v-model="formData.fieldType" class="m-2" placeholder="请选择字段类型" filterable clearable>
                <el-option label="文本" value="STRING" />
                <!-- <el-option label="数字" value="NUMBER" /> -->
                <el-option label="日期" value="DATE" />
                <el-option label="下拉框" value="SELECT" />
                <el-option label="城市" value="CITY" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <div class="search-input">
              <el-input placeholder="请输入关键字" v-model="keywords" clearable>
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
              </el-input>
              <el-button size="mini" style="margin-left:20px" v-if="!isAdd" @click="handleShowPord">添加产品</el-button>
              <el-checkbox v-model="checked" style="margin-left:20px" @change="hanldeRequire">选中必传</el-checkbox>
            </div>
            <el-table :data="list" border ref="prodRef" :max-height="400" :row-key="(row)=>row.productId"
              @selection-change="hanldeSelection" @select="selectionRow">
              <el-table-column type="selection" width="55" align="center" :reserve-selection="true" />
              <el-table-column label="产品名称" prop="productName" align="center" />
              <el-table-column label="是否必传" prop="required" align="center">
                <template slot-scope="{row}">
                  <div>

                    <el-switch :active-value="true" :disabled="!row.selected" :inactive-value="false" active-text="是"
                      inactive-text="否" v-model="row.required"></el-switch>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" prop="productName" align="center" v-if="!isAdd">
                <template slot-scope="{row}">
                  <div>
                    <el-button type="text" @click="hanldeDelProduct(row)">删除</el-button>
                  </div>
                </template>
              </el-table-column>

            </el-table>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormData">确 定</el-button>
        <el-button @click="cancal">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑选项" :visible.sync="showOption" width="1150px" append-to-body center>
      <div class="option" v-for="(item,index) in fieldOptionList" :key="index">
        <div class="option-item">
          <span>名称:</span>
          <el-input size="small" class="option-input" v-model="item.label"></el-input>
        </div>
        <div class="option-item"><span>值:</span>
          <el-input size="small" class="option-input" v-model.number="item.value"></el-input>
        </div>
        <div class="option-item"><span>排序:</span>
          <el-input size="small" class="option-input" v-model.number="item.sort"></el-input>
        </div>

        <div class="option-item"><span>是否禁用:</span>
          <el-switch v-model="item.disabled" :active-value="true" :inactive-value="false" active-text="是"
            inactive-text="否"></el-switch>
        </div>
        <div class="option-item">
          <el-button size="mini" type="danger" @click="hanldeDelOption(index)">删除</el-button>
          <el-button size="mini" type="primary" @click="hanldeAddOption" v-if="fieldOptionList.length==(index+1)">新增
          </el-button>

        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitOption">保存</el-button>
        <el-button type="primary" @click="hanldeAddOption" v-if="fieldOptionList.length==0">新增</el-button>

      </div>
    </el-dialog>
    <el-dialog title="添加产品" width="600px" :visible.sync="showProduct" center>
      <el-form label-width="100px">
        <el-form-item label="选择产品" prop="fieldName">
          <el-select v-model="productIds" multiple placeholder="请选择字段类型" filterable clearable>
            <el-option :label="item.productName" :value="item.productId" v-for="item in prodAddOptions"
              :key="item.productId" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAddProd">确 定</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getFormConfigure,
  getProductList,
  handleConfigOne,
  delFormConfigOne,
  saveOptions,
  delProductOne,
  getProductAddList,
  addProductList
} from "@/api/productManage/formConfigure"
export default {
  data() {
    return {
      showAdd: false,//新增
      showOption: false,
      isAdd: true,
      checked: false,
      showProduct: false,//新增产品
      keywords: "",
      optionId: '',
      queryParams: {
        name: ""
      },
      tableList: [],
      fieldOptionList: [],
      prouctList: [],
      productIds: [],
      prodAddOptions: [],
      formData: {
        fieldGroup: "",
        fieldKey: "",
        fieldName: "",
        fieldRemark: "",
        fieldType: "",
        fieldSort: 0,
        productFieldConfigs: []
      },
      typeOptionJson: {
        STRING: "文本",
        DATE: "日期",
        SELECT: "下拉框",
        CITY: "城市"
      },
      typeJson: {
        1: "基础信息", 2: "联系人信息", 3: "公司信息"
      },
      rules: {
        fieldGroup: [{ required: true, message: "请选择字段归属", trigger: "change" }],
        fieldKey: [{ required: true, message: "请输入字段参数", trigger: "blur" }],
        fieldName: [{ required: true, message: "请输入字段名称", trigger: "blur" }],
        fieldSort: [{ required: true, message: "请输入字段必填", trigger: "blur" }],
        fieldType: [{ required: true, message: "请选择字段类型", trigger: "change" }],
      }

    }
  },
  methods: {
    //新增弹窗
    handleAdd() {
      getProductList().then(res => {
        this.showAdd = true
        this.isAdd = true
        if (this.formData.id) delete this.formData.id
        this.prouctList = res.data
      })

    },

    handleQuery() {
      this.getList()
    },
    getList() {
      getFormConfigure(this.queryParams).then(res => {
        this.tableList = res.rows
      })
    },
    //提交
    submitFormData() {
      this.formData.productFieldConfigs = this.$refs.prodRef.selection.map(item => {
        return {
          productId: item.productId,
          required: item.required
        }
      })
      this.$refs.form.validate((valid) => {
        if (valid) {
          handleConfigOne(this.formData).then(res => {
            this.getList()
            this.$message.success("操作成功")

            this.cancal()
          })

        }
      })

    },
    //修改
    handleEdit(row) {
      getProductList({ fieldId: row.id }).then(res => {
        this.showAdd = true
        this.isAdd = false
        this.formData.id = row.id
        this.formData.fieldGroup = row.fieldGroup
        this.formData.fieldKey = row.fieldKey
        this.formData.fieldName = row.fieldName
        this.formData.fieldType = row.fieldType
        this.formData.fieldSort = row.fieldSort || 0

        if (this.$refs.prodRef) {
          this.$refs.prodRef.clearSelection();
        }
        this.prouctList = res.data || []
        setTimeout(() => {
          this.prouctList.forEach(row => {
            this.$refs.prodRef.toggleRowSelection(
              row,
              row.selected
            );
          })
        }, 0);

      })
    },
    //删除
    handleDel(row) {
      this.$confirm("确认删除吗", "温馨提示", { type: "warning" }).then(res => {
        delFormConfigOne({ id: row.id }).then(res => {
          this.$message.success("操作成功")
          this.getList()
        })
      }).catch(err => { })
    },
    //取消添加
    cancal() {
      this.formData = {
        fieldGroup: "",
        fieldKey: "",
        fieldName: "",
        fieldRemark: "",
        fieldType: "",
        fieldSort: 0,
        productFieldConfigs: []
      }
      this.keywords = ""
      this.showAdd = false
      this.prouctList.forEach(row => {
        this.$refs.prodRef.toggleRowSelection(
          row,
          false
        );
      })
      this.prouctList = []

      this.$refs.form.resetFields()
    },
    //操作配置项
    handleOptions(row) {
      this.showOption = true
      this.fieldOptionList = JSON.parse(JSON.stringify(row.fieldOptionList || '[]'))
      this.optionId = row.id
    },
    //删除配置项
    hanldeDelOption(index) {
      this.fieldOptionList.splice(index, 1)
    },
    //添加配置项
    hanldeAddOption() {
      let isFinish = this.fieldOptionList.every(item => {
        return !!item.label && (!!(item.value || item.value === 0)) && (!!(item.sort || item.sort === 0))
      })
      if (!isFinish) {
        this.$message.error("请先完成信息")
        return
      }
      this.fieldOptionList.push({
        sort: 0,
        value: '',
        label: "",
        disabled: false
      })
    },
    //删除配置项
    submitOption() {
      let isFinish = this.fieldOptionList.every(item => {
        return !!item.label && (!!(item.value || item.value === 0)) && (!!(item.sort || item.sort === 0))
      })
      if (!isFinish) {
        this.$message.error("请先完成信息")
        return
      }
      saveOptions({
        id: this.optionId,
        fieldOptionList: this.fieldOptionList
      }).then(res => {
        this.optionId = ''
        this.fieldOptionList = []
        this.showOption = false
        this.$message.success("操作成功")
        this.getList()
      })
    },
    hanldeRequire() {
      this.$refs.prodRef.selection.forEach(item => {
        if (this.checked) {
          item.required = true
        } else {
          item.required = false
        }
      })
    },
    hanldeSelection(data) {
      data.forEach(item => {
        if (this.checked) {
          item.required = true
        }
      })
      data.forEach(item => {
        item.selected = true
      })
      if (data.length == 0) {
        this.prouctList.forEach(item => {
          item.selected = false
        })
      }
    },
    selectionRow(selection, row) {
      row.selected = !row.selected
    },
    hanldeDelProduct(row) {
      this.$confirm('确认删除吗', '温馨提示', { type: "warning" }).then(res => {
        delProductOne({ fieldId: this.formData.id, productId: row.productId }).then(() => {
          let data = JSON.parse(JSON.stringify(this.prouctList))
          this.prouctList = data.filter(item => {
            return row.productId !== item.productId
          })

          this.$message.success("操作成功")
        })
      }).catch(err => { })
    },
    handleShowPord() {
      this.showProduct = true
      this.productIds = []
      getProductAddList({
        fieldId: this.formData.id
      }).then(res => {
        this.prodAddOptions = res.data
      })
    },
    submitAddProd() {
      addProductList({
        fieldId: this.formData.id,
        "productIds": this.productIds
      }).then(res => {
        let data = this.prodAddOptions.filter(item => {
          return this.productIds.indexOf(item.productId) != -1
        })
        let dataItem = data.map(item => {
          return {
            ...item,
            selected: false,
            required: false
          }
        })

        this.showProduct = false
        dataItem.forEach(item => {
          this.prouctList.unshift(item)
        })
        this.$message.success("操作成功")
      })

    }



  },
  computed: {
    list: function () {

      return this.prouctList.filter(item => item.productName.includes(this.keywords))

    }
  },
  mounted() {
    this.getList()

  },

}
</script>

<style lang="scss" scoped>
.allCheck {
  height: 30px;
  display: flex;
  align-items: center;
  color: #333;
  font-size: 14px;
  padding-left: 20px;
}

.search-input {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.option {
  display: flex;
  margin-bottom: 10px;

  &-item {
    display: flex;
    align-items: center;
    margin-right: 30px;

    span {
      color: #333;
      font-weight: 600;
      margin-right: 10px;
    }

  }

  &-input {
    width: 170px;
  }
}
</style>
