<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="提交时间">
        <el-date-picker size="small" v-model="dateRange" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" @change="handleQuery">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="收款主体">
        <el-input size="small" clearable v-model="queryParams.subjectKeywords" placeholder="请输入收款主体"
          @change="handleQuery" @keyup.enter.native="handleQuery"></el-input>
      </el-form-item>
      <el-form-item label="入账商务">
        <el-select v-model="queryParams.confirm" size="small" filterable clearable @change="handleQuery">
          <el-option v-for="i in affairsList" :key="i.userId" :label="i.nickName" :value="i.userId"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" border :data="refoundList" :row-key="(row) => row.id">
      <el-table-column label="申请时间" prop="refundDate" align="center" />
      <el-table-column label="商户名称" prop="partyFirstName" align="center" />
      <el-table-column label="退款前余额" prop="availableAmount" align="center" />
      <el-table-column label="退款前保证金余额" prop="depositAmount" align="center" />
      <el-table-column label="是否退保证金" prop="deposit" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ row.deposit ? "是" : "否" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="退款金额" prop="price" align="center" />
      <el-table-column label="收款主体" prop="subjectName" align="center" />
      <el-table-column label="开户行" prop="bankName" align="center" />
      <el-table-column label="收款账号" prop="bankCardNo" align="center" />
      <el-table-column label="申请人" prop="refundUser" align="center" />

      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button v-if="row.status == 0" type="text" @click="confirm(row)"
              v-hasPermi="['loan:partya:refund:execute']">{{ row.isAction ? "审核" : "查看" }}</el-button>
            <el-button v-if="row.status == 1" type="text" @click="confirm(row)"
              v-hasPermi="['loan:partya:refund:detail']"> 已退款 </el-button>
            <el-button style="color: red" v-if="row.status == 2" type="text" @click="confirm(row)"
              v-hasPermi="['loan:partya:refund:detail']">
              已驳回
            </el-button>
            <!-- <el-button v-if="row.status == 4" type="text" @click="confirm(row)"> 已确认 </el-button> -->
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog title="商户退款审核" :visible.sync="show" @close="cancel" width="900px" append-to-body center
      :close-on-click-modal="false">
      <el-row :gutter="20">
        <el-col :span="13">
          <el-form label-position="left" label-width="140px" ref="refoundData">
            <el-form-item label="商户名称" prop="" label-width="90px">
              <div class="flex">
                <el-input disabled v-model="formData.partyFirstName"></el-input>
                <el-button type="primary" size="mini" class="m-l10" @click="handleToPartyA">查看商户详情</el-button>
              </div>

            </el-form-item>
            <el-form-item label="商户类型" prop="">
              <el-input disabled v-model="formData.type"></el-input>
            </el-form-item>
            <el-form-item label="退款收款主体名" prop="">
              <el-input disabled v-model="formData.subjectName"></el-input>
            </el-form-item>
            <el-form-item label="退款收款开户行" prop="">
              <el-input disabled v-model="formData.bankName"></el-input>
            </el-form-item>
            <el-form-item label="退款收款银行卡号" prop="">
              <el-input disabled v-model="formData.bankCardNo"></el-input>
            </el-form-item>
            <el-form-item label="退款金额" prop="">
              <el-input disabled v-model="formData.price"></el-input>
            </el-form-item>
            <el-form-item label="我方收款主体" prop="">
              <el-input disabled v-model="formData.loanSubject"></el-input>
            </el-form-item>
            <el-form-item label="是否退保证金" prop="">
              <el-radio v-model="formData.deposit" disabled :label="true">是</el-radio>
              <el-radio v-model="formData.deposit" disabled :label="false">否</el-radio>
            </el-form-item>
            <el-form-item label="保证金" prop="" v-if="formData.deposit">
              <el-input disabled v-model="formData.refundDepositPrice"></el-input>
            </el-form-item>
            <el-form-item label="凭证上传" v-if="formData.mustCertificate">
              <el-upload class="avatar-uploader" action="" :show-file-list="false" :auto-upload="false"
                :on-change="changeUpImg" drag>
                <img v-if="imageUrl" style="vertical-align: middle; max-height: 100px" :src="imageUrl" />
                <template v-else>
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </template>
<!--                <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
                <div class="el-upload__tip" slot="tip">通过须上传凭证</div>
              </el-upload>
            </el-form-item>
            <el-form-item label="审批意见" prop="" v-if="isShowHandle">
              <el-input type="textarea" placeholder="请输入审批意见,驳回必填" :disabled="!isShowHandle" v-model="remark" :rows="2"
                maxlength="20" show-word-limit></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="11">
          <div>
            <div class="check-title">审核流程</div>
            <div class="check-info1" v-if="processList.length">
              <el-timeline>
                <el-timeline-item v-for="(item, index) in processList" :key="index" :color="processColor(item)">
                  <div :class="[item.status == 2 ? 'c_red' : '']">
                    <div> {{ item.userRemark }}</div>
                    <div>时间:{{ item.checkTime || '-' }}</div>
                    <div v-if="item.status != 3">状态：{{ item.checkResult || '-' }}</div>
                    <div v-if="item.status != 3"> 备注：{{ item.checkRemark || "-" }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
            <div class="check-center" v-else>暂无数据</div>
          </div>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer" v-if="isShowHandle">
        <el-button type="primary" @click="submitForm" v-hasPermi="['loan:partya:refund:execute']">通过</el-button>
        <el-button @click="refuseRefund" v-hasPermi="['loan:partya:refund:execute']">驳回</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getAffairsList } from "@/api/partyB";
import {
  getPartyaRefundList,
  rejectPartyaRefundOne,
  passPartyaRefundOne,
  getRefundProcess
} from "@/api/auditFinance/refundPartya"
export default {
  name: "RefundPartya",
  data() {
    return {
      show: false,
      remark: "",
      imageUrl: "",
      file: "",
      processList: [],
      typeJson: {
        0: "门店商户",
        1: "个人商户",
        2: "线上商户",
        // 3: "线上个人"
      },
      formData: {
        type: "",
        price: "",
        partyFirstName: "",
        subjectName: "",
        bankName: "",
        bankCardNo: "",
        loanSubject: "",
        deposit: false,
        mustCertificate: false,
      },
      isShowHandle: false,
      refoundList: [],
      dateRange: [],
      affairsList: [],
      total: 0,
      loading: false,
      id: "",
      queryParams: {
        confirm: "",
        stopTime: "",
        startTime: "",
        subjectKeywords: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  computed: {
    processColor: () => {
      return (item) => {
        if (item.status == -1) {
          return ''
        } else if (item.status == 2) {
          return "#ff0000"
        } else {
          return "#00a607"
        }
      };
    }

  },
  methods: {
    //查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.dateRange != null) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.stopTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.stopTime = "";
      }
      this.getList();
    },
    // 取消
    cancel() {
      this.formData = {
        type: "",
        price: "",
        partyFirstName: "",
        subjectName: "",
        bankName: "",
        bankCardNo: "",
        filename: "",
        loanSubject: "",
        deposit: false,
        mustCertificate: false,
      };
      this.id = "";
      this.remark = ""
      this.file = ""
      this.imageUrl=""
      this.show = false;
      this.$refs.refoundData.resetFields();
    },
    //上传凭证
    changeUpImg(e) {
      const isJPG =
        e.raw.type === "image/jpeg" ||
        e.raw.type === "image/jpg" ||
        e.raw.type === "image/png";
      const isLt2M = e.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传图片只能是 JPG/JPEG/PNG 格式!");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
        return;
      }
      this.imageUrl = URL.createObjectURL(e.raw);
      this.file = e.raw;
    },
    //确认退款
    submitForm() {
      if (this.formData.mustCertificate && !this.file) {
        this.$message.error("请上传凭证")
        return
      }
      let obj = {
        id: this.id, remark: this.remark, file: this.file
      }

      let data = new FormData()
      for (let key in obj) {
        data.append(key, obj[key])
      }
      passPartyaRefundOne(data).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.getList();
          this.cancel();
        }
      });
    },
    //驳回
    refuseRefund() {
      if (!this.remark) return this.$message.error("审批意见不能为空")
      let obj = {
        id: this.id, remark: this.remark, file: this.file
      }
      let data = new FormData()
      for (let key in obj) {
        data.append(key, obj[key])
      }
      rejectPartyaRefundOne(data).then(res => {
        this.$message.success("操作成功");
        this.getList();
        this.cancel();
      })
    },

    getList() {
      this.loading = true;
      getPartyaRefundList(this.queryParams).then((res) => {
        this.refoundList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    //确认弹窗
    confirm(row) {
      if (row.status == 0 && row.isAction) {
        this.isShowHandle = true
      } else {
        this.isShowHandle = false
      }
      this.formData = JSON.parse(JSON.stringify(row));
      this.formData.type = this.typeJson[row.type];
      this.id = row.id;
      getRefundProcess({
        refundId: row.id
      }).then(res => {
        this.show = true;
        this.processList = res.data
      })
    },
    handleToPartyA() {
      let url = this.$router.resolve('/partyA/list?name=' + this.formData.partyFirstName)
      window.open(url.href, '_blank')
    },
  },
  mounted() {
    this.getList();
    //查询商务
    getAffairsList().then((res) => {
      this.affairsList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
.priview {
  width: 300px;

  img {
    width: 85px;
    height: 50px;
    padding: 0 10px;
  }
}

.replayImage {
  border: 0;
  max-height: 100vh;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.check-info1 {
  margin-top: 10px;
  height: 500px;
  overflow: auto;
}

.m-l10 {
  margin-left: 10px;
}

.flex {
  display: flex;
}
</style>
