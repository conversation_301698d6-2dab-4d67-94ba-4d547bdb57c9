import request from '@/utils/request'

// 个人请款列表
export const getFinances = (params = {}) => {
  return request({
    url: '/system/personalCashOut/personalCashOut/list',
    method: 'get',
    params
  })
}

//外部返点产品查询
export const getFinanceProducts = ({ productName = '' }) => {
  return request({
    // url: '/system/personalCashOut/product/' + productName,
    url: '/system/personalCashOut/product/list/' + productName,
    method: 'get',
    // params: { id :productName}
  })
}

//
export const getRebates = (data = {}) => {
  return request({
    url: '/system/personalCashOut/product/rebateList',
    method: 'post',
    data
  })
}

// 外部返点申请
export const rebateApply = (data = {}) => {
  return request({
    url: '/system/personalCashOut/rebates/add',
    method: 'post',
    data
  })
}

// 个人报销申请
//
export const expenseApply = (data = {}) => {
  return request({
    url: '/system/personalCashOut/reimbursement/add',
    method: 'post',
    data
  })
}

// 重新提交
export const resetApply = (data = {}) => {
  return request({
    url: '/system/personalCashOut/restart/process',
    method: 'post',
    data
  })
}

//修改返点
export const editApply = (data = {}) => {
  return request({
    url: "/system/personalCashOut/rebates/update",
    method: "post",
    data
  })
}
