import Cookies from 'js-cookie'

const Token<PERSON>ey = 'Admin-Token'
const YxqbTokenKey = 'Yxqb-Token'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token, { expires: 1 })
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getYxqbToken() {
  return Cookies.get(YxqbTokenKey)
}

export function setYxqbToken(token) {
  return Cookies.set(YxqbTokenKey, token)
}

export function removeYxqbToken() {
  return Cookies.remove(YxqbTokenKey)
}
