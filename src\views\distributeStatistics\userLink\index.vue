<script>
import { fetchUserLinkList } from "@/api/distributeStatistics/userLink";
import dayjs from "dayjs";
import { getCityList } from "@/api/statisticalManage";

export default {
  name: "userLink",

  data() {
    return {
      cityList: [],
      queryParams: {
        // 后台唯一标识
        adminKey: "",
        // 申请状态: 1 成功 2 失败 3 未申请
        applyStatus: "",
        // 城市
        city: [],
        // md5手机号
        md5Phone: "",
        // 渠道号
        channelId: "",
        // 页码
        pageNum: 1,
        // 每页显示记录数
        pageSize: 20,
        dateRange: [],
      },
      total: 0,
      tableData: [],
    };
  },

  mounted() {
    this.fetchCityData();
    this.setDefaultDate();
    this.fetchTableData();
  },

  methods: {
    async fetchCityData() {
      const res = await getCityList();
      this.cityList = res.data.map((item) => {
        if (item.citys) {
          {
            return {
              value: item.name,
              label: item.name,
              disabled: false,
              children: [
                ...item.citys.map((citem) => {
                  return {
                    value: citem.name,
                    label: citem.name,
                    disabled: false,
                  };
                }),
              ],
            };
          }
        } else {
          return {
            value: item.name,
            label: item.name,
            disabled: false,
          };
        }
      });
    },

    getParams() {
      const [startTime = "", endTime = ""] = this.queryParams.dateRange || [];

      return {
        startTime,
        endTime,
        adminKey: this.queryParams.adminKey,
        applyStatus: this.queryParams.applyStatus,
        city: this.queryParams.city.at(-1),
        md5Phone: this.queryParams.md5Phone,
        channelId: this.queryParams.channelId,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
      };
    },

    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format("YYYY-MM-DD 00:00:00");
      const endTime = today.format("YYYY-MM-DD 23:59:59");

      this.queryParams.dateRange = [startTime, endTime];
    },

    handleTableData(arr) {
      arr.forEach((item) => {
        item.matchProduct = item.matchProduct
          ? JSON.parse(item.matchProduct)
          : [];
        item.showProduct = item.showProduct ? JSON.parse(item.showProduct) : [];
        item.applyProduct = item.applyProduct
          ? JSON.parse(item.applyProduct)
          : [];
      });
    },

    async fetchTableData() {
      const params = this.getParams();
      const res = await fetchUserLinkList(params);

      this.handleTableData(res.rows);

      this.tableData = res.rows;
      this.total = res.total;
    },

    handleSearch() {
      this.queryParams.pageNum = 1;
      this.fetchTableData();
    },

    joinProduct(product) {
      return Object.values(product).join("--");
    },
    handleProduct(item) {
      return `${item.price}--${item.productName}`;
    },
    handleApplyResult(item) {
      if (!item) return "";
      if (item.includes('applyResult')) {
        return JSON.parse(item).map((i) => {
          return `${i.productName}--${i.applyResult||'正在申请中'}`;
        });
      } else {
        return item;
      }
    },
  },

  filters: {
    typeFilter(type) {
      const value = String(type);
      switch (value) {
        case "1":
          return "api";
        case "2":
          return "H5";
        default:
          return "";
      }
    },
  },
};
</script>

<template>
  <div class="app-container">
    <el-form inline :model="queryParams" size="small">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="queryParams.dateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleSearch"
          :clearable="false"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="城市">
        <el-cascader
          v-model="queryParams.city"
          :options="cityList"
          @change="handleSearch"
          clearable
          filterable
        >
        </el-cascader>
      </el-form-item>
      <el-form-item label="渠道号">
        <el-input v-model="queryParams.channelId"></el-input>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="queryParams.md5Phone"></el-input>
      </el-form-item>
      <el-form-item label="申请状态">
        <el-select v-model="queryParams.applyStatus" @change="handleSearch">
          <el-option label="全部" value=""></el-option>
          <el-option label="成功" value="1"></el-option>
          <el-option label="失败" value="2"></el-option>
          <el-option label="未申请" value="3"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border style="width: 100%" size="small">
      <el-table-column
        prop="channelId"
        label="渠道号"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="city"
        label="城市"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="md5Phone"
        label="md5手机号"
        width="250"
        align="center"
      ></el-table-column>
      <el-table-column prop="type" label="类型" width="60" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.type | typeFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="matchTime"
        label="撞库时间"
        width="160"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="matchProduct"
        label="所有匹配到的产品"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <div style="display: flex; flex-wrap: wrap; gap: 10px">
            <el-tag
              v-for="(item, index) in scope.row.matchProduct"
              :key="index"
            >
              {{ joinProduct(item) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="matchReqTime"
        label="撞库耗时(毫秒)"
        align="center"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="showProduct"
        label="最终展示的产品"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <div style="display: flex; flex-wrap: wrap; gap: 10px">
            <el-tag v-for="(item, index) in scope.row.showProduct" :key="index">
              {{ joinProduct(item) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="applyProduct"
        label="申请的产品"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <div style="display: flex; flex-wrap: wrap; gap: 10px">
            <el-tag
              v-for="(item, index) in scope.row.applyProduct"
              :key="index"
            >
              {{ handleProduct(item) }}

              <span v-if="item.lastPushDate">  {{ item.lastPushDate.slice(11) }}</span>
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="applyTime"
        label="申请时间"
        align="center"
        width="160"
      ></el-table-column>
      <el-table-column
        prop="applyResult"
        label="申请结果"
        align="center"
        width="180"
      >
        <template slot-scope="{ row }">
          <div v-if="handleApplyResult(row.applyResult) instanceof Array">
            <div style="display: flex; flex-wrap: wrap; gap: 10px">
              <el-tag
                v-for="(item, index) in handleApplyResult(row.applyResult)"
                :key="index"
              >
                {{ item }}
              </el-tag>
            </div>
          </div>
          <div v-else>{{ handleApplyResult(row.applyResult) }}</div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="fetchTableData"
    />
  </div>
</template>

<style scoped lang="scss">
.el-tag {
  height: auto !important;
  white-space: normal !important;
}
</style>
