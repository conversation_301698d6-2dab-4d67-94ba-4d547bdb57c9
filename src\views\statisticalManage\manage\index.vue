<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table border :data="list" @sort-change="handleSortChange">
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="日期"
        prop="countTime"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="渠道总成本"
        prop="sumCost"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="总推广收益"
        prop="earnings"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="返点金额"
        prop="backPrice"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="单申请成本"
        prop="sqCost"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="单申请收益"
        prop="sqProfit"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="付费收益"
        prop="payCost"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="毛利"
        prop="grossProfit"
        align="center"
      />
      <el-table-column
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        label="毛利率"
        prop="grossMargin"
        align="center"
      >
        <template  slot-scope="{row}">
          <div>{{ row.grossMargin }}%</div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getManagementList } from "@/api/statisticalManage";
export default {
  data() {
    return {
      value1: [this.getWeekTime(), this.getTime()],
      list: [],
      queryParams: {
        endTime: this.getTime(),
        startTime: this.getWeekTime(),
        orderByColumn: "",
        isAsc: "",
      },
    };
  },
  methods: {
    handleQuery() {
      if (this.value1 !== null) {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    getWeekTime() {
      let wekDay = Date.now() - 86400 * 7 * 1000;
      var date = new Date(parseInt(wekDay));
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    getList() {
      getManagementList(this.queryParams).then((res) => {
        this.list = res.data;
        this.list.push({
          countTime: "合计",
          sumCost: this.getTotal(this.list, "sumCost").toFixed(2),
          earnings: this.getTotal(this.list, "earnings").toFixed(2),
          backPrice: this.getTotal(this.list, "backPrice").toFixed(2),
          sqCost: this.getTotal(this.list, "sqCost").toFixed(2),
          sqProfit: this.getTotal(this.list, "sqProfit").toFixed(2),
          payCost: this.getTotal(this.list, "payCost").toFixed(2),
          grossProfit: this.getTotal(this.list, "grossProfit").toFixed(2),
          grossMargin:
            this.getTotal(this.list, "grossProfit") &&
            this.getTotal(this.list, "sumCost")
              ? (
                  (this.getTotal(this.list, "grossProfit") /
                    this.getTotal(this.list, "sumCost")) *
                  100
                ).toFixed(2)
              : 0,
        });
      });
    },
    getTotal(arr, type) {
      return arr.reduce((pre, data) => {
        return pre + data[type];
      }, 0);
    },
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
  },

  mounted() {
    this.getList();
    console.log(this.getWeekTime());
  },
};
</script>

<style lang="scss" scoped>
</style>
