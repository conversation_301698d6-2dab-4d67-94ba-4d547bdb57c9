//读文件的方法
export function readFile(file) {
    return new Promise(resolve => {
        let reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = ev => {
            resolve(ev.target.result)
        }
    })
}

//将execl表里面的数据表头参数替换成数据库对应的参数
export function handleFileChar(dataArr, charReplace) {
    let newDataArr = []
    if (!charReplace) {
        return dataArr
    } else {

        dataArr.forEach((item) => {
            let obj = {};
            for (let key in charReplace) {
                if (!charReplace.hasOwnProperty(key)) break;
                let v = charReplace[key];
                let text = v.text;
                let type = v.type;
                v = item[text] || "";
                type === "string" ? (v = String(v)) : null;
                type === "number" ? (v = Number(v)) : null;
                obj[key] = v;

            }
            newDataArr.push(obj);
        });
        return newDataArr
    }

}

//execl第二个表替换的字段
export let sheetChar = {
    cost: {
        text: "成本",
        type: "number"
    },
    ms: {
        text: "日期",
        type: "string"
    },
    uv: {
        text: "UV",
        type: "string"
    }

}