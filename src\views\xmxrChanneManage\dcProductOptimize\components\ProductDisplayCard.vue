<template>
  <el-card class="platform-group" size="mini" shadow="never">
    <div slot="header" class="clearfix">
      <span>{{ platformData.platformName }}</span>
    </div>
    <div class="product-tags-container" v-if="platformData.products && platformData.products.length > 0">
      <el-tag
        v-for="product in platformData.products"
        :key="product.productId"
        closable
        @close="$emit('delete-product', platformId, product)"
        class="product-tag"
      >
        {{ product.productName }} (ID: {{ product.productId }})
      </el-tag>
    </div>
    <div v-else class="empty-products-text">
      暂无已配置的贷超优化产品
    </div>
  </el-card>
</template>

<script>
export default {
  name: "ProductDisplayCard",
  props: {
    platformId: {
      type: [String, Number],
      required: true,
    },
    platformData: {
      type: Object,
      required: true,
      default: () => ({ platformName: "", products: [] }),
    },
  },
  // No specific methods needed here for now as delete is emitted directly
  // mounted() {
  //   console.log('ProductDisplayCard mounted with:', this.platformId, this.platformData);
  // }
};
</script>

<style scoped>
.platform-group {
  margin-bottom: 20px;
}

.platform-group:last-child {
  margin-bottom: 0;
}

.platform-group .el-card__header .clearfix span {
  font-size: 17px;
  color: #333;
}

.product-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* Consider if this is supported or if margin is better */
}

.empty-products-text {
  color: #909399;
  font-size: 14px;
}

.product-tag {
  margin-right: 5px; /* Replaces gap for broader compatibility if needed */
  margin-bottom: 5px;
}
</style>
