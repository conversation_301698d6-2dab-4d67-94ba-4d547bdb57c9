import request from '@/utils/request'

// 查询配置列表
export function getConfigList() {
  return request({
    url: '/loan/xm/redis/config/manage/list',
    method: 'get'
  })
}

// 根据key查询配置详情
export function getConfigByKey(params) {
  return request({
    url: '/loan/xm/redis/config/manage/getByKey',
    method: 'get',
    params
  })
}

// 更新Redis值
export function updateRedisValue(data) {
  return request({
    url: '/loan/xm/redis/config/manage/updateRedisValue',
    method: 'post',
    data
  })
}
