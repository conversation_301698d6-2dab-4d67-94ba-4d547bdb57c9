<template>
  <div class="screen-container">
    <!-- 状态栏 iOS/Android -->
    <component
      :is="configData.device && configData.device.os == 'android' ? 'AndroidStatusBar' : 'IOSStatusBar'"
      :time="configData.statusBar.time"
      :show-signal="configData.statusBar.showSignal"
      :show-wifi="configData.statusBar.showWifi"
      :show-battery="configData.statusBar.showBattery"
      :battery-level="configData.statusBar.batteryLevel"
      :mode="configData.statusBar.mode"
    />

    <!-- iOS/Android 导航栏 -->
    <component :is="configData.device && configData.device.os == 'android' ? 'AndroidNavBar' : 'IOSNavBar'" />

    <div class="screen-container-inner">
      <!-- Background Image -->
      <img class="background-img" src="https://jst.oss-utos.hmctec.cn/common/path/9388ae1bbfea43faa5bc4ac0ab672ac4.png"
        alt="Background">

      <!-- 仪表盘区域 -->
      <Dashboard 
        :user-name="configData.userName"
        :score="configData.score"
        :arc-settings="configData.arcSettings"
      />

      <!-- 服务区域 -->
      <ServiceItems :services="configData.services" />

      <!-- 消息助手 -->
      <MessageAssistant 
        :message-count="configData.messageCount"
        :messages="configData.messages"
      />

      <!-- 广告图 -->
      <AdImage :ad-image-url="configData.adImage.url" />
    </div>

    <!-- tabbar图 -->
    <TabBar />
  </div>
</template>
<script>
// Added import for html2canvas
import html2canvas from 'html2canvas';
import IOSStatusBar from '../../components/iOSStatusBar.vue';
import AndroidStatusBar from '../../components/AndroidStatusBar.vue';
import IOSNavBar from './preview/iOSNavBar.vue';
import AndroidNavBar from './preview/AndroidNavBar.vue';
import Dashboard from './preview/Dashboard.vue';
import ServiceItems from './preview/ServiceItems.vue';
import MessageAssistant from './preview/MessageAssistant.vue';
import AdImage from './preview/AdImage.vue';
import TabBar from './preview/TabBar.vue';

export default {
  name: 'SesameScorePreview',
  components: {
    IOSStatusBar,
    AndroidStatusBar,
    IOSNavBar,
    AndroidNavBar,
    Dashboard,
    ServiceItems,
    MessageAssistant,
    AdImage,
    TabBar
  },
  props: {
    configData: {
      type: Object,
      required: true
    }
  },
  methods: {
    // Added method to capture screenshot
    async captureScreenshot() {
      const targetElement = this.$el;
      if (!targetElement) {
        console.error('Target element .screen-container not found (this.$el is null?).');
        return null;
      }

      try {
        const canvas = await html2canvas(targetElement, {
          useCORS: true,
          scale: 1.5,
          backgroundColor: null,
          // 打印日志
          logging: false,
          width: targetElement.scrollWidth,
          height: targetElement.scrollHeight
        });
        
        // 根据平台类型决定图片格式
        const imageType = this.configData.device.os === 'android' ? 'image/jpeg' : 'image/png';
        const dataUrl = canvas.toDataURL(imageType);
        return dataUrl;

      } catch (error) {
        console.error('Error capturing screenshot:', error);
        return null;
      }
    }
  }
}
</script>
<style scoped lang="scss">
// 隐藏所有滚动条
::-webkit-scrollbar {
  display: none;
}

.screen-container {
  position: relative;
  width: 750px;
  // height: 1334px;
  height: 1624px;
  font-family: 'SourceHanSansSC-Regular';
  display: flex;
  flex-direction: column;

  .screen-container-inner {
    flex: 1;
    overflow-y: hidden;
    background-color: #F0F3F7;
  }
}

.background-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 750px;
  height: 741px;
  object-fit: cover;
  z-index: 0;
}
</style>