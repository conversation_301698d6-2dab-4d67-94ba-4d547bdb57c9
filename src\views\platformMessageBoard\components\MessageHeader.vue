<template>
  <div class="message-header">
    <div class="header-row">
      <div class="field-group">
        <span class="field-label">标题：</span>
        <span class="field-value">{{ messageData.title }}</span>
      </div>
      <div class="field-group">
        <span class="field-label">时间：</span>
        <span class="field-value">{{ formatDate(messageData.created) }}</span>
      </div>
    </div>
    <div class="header-row">
      <div class="field-group">
        <span class="field-label">内容：</span>
        <span class="field-value">{{ messageData.content }}</span>
      </div>
      <div class="field-group">
        <span class="field-label">创建人：</span>
        <span class="field-value">{{ messageData.createBy }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'

export default {
  name: 'MessageHeader',
  props: {
    messageData: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  methods: {
    /** 格式化日期 */
    formatDate(date) {
      if (!date) return ''
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>

<style scoped>
.message-header {
  padding: 12px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
  margin-bottom: 12px;
}

.header-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 6px;
  font-size: 13px;
  line-height: 1.4;
}

.header-row:last-child {
  margin-bottom: 0;
}

.field-group {
  display: flex;
  align-items: flex-start;
  flex: 1;
  margin-right: 20px;
}

.field-group:last-child {
  margin-right: 0;
}

.field-label {
  color: #909399;
  font-weight: 500;
  width: 60px;
  flex-shrink: 0;
  text-align: right;
  margin-right: 8px;
}

.field-value {
  color: #303133;
  flex: 1;
  word-break: break-word;
}
</style>