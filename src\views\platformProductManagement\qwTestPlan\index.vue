<template>
  <div class="app-container">

    <!-- 表格工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="getList"
        >刷新</el-button>
      </el-col>
    </el-row>

    <!-- 表格区域 -->
    <el-table
      :data="tableList"
      border
    >
      <el-table-column label="计划名称" align="center" prop="planName" />
      <el-table-column label="匹配价格" align="center" prop="matchPrice" width="80"/>
      <el-table-column label="企微链接" align="center" prop="weChatLink" show-overflow-tooltip width="400"/>
      <el-table-column label="时间点" align="center" width="160">
        <template slot-scope="scope">
          {{ scope.row.startPoint }} - {{ scope.row.endPoint }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="150">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="2"
            active-text="启用"
            inactive-text="禁用"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="控量" align="center" prop="applyControl" width="100">
        <template slot-scope="scope">
          <el-input-number
            style="width: 100%;"
            v-model="scope.row.applyControl"
            :min="0"
            :precision="0"
            :controls="false"
            size="mini"
            @focus="handleControlFocus(scope.row)"
            @blur="handleControlBlur(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="申请成功数量" align="center" prop="applyCount" />
      <el-table-column label="接入数量" align="center" prop="accessCount" width="100">
        <template slot-scope="scope">
          <el-input-number
            style="width: 100%;"
            v-model="scope.row.accessCount"
            :min="0"
            :precision="0"
            :controls="false"
            size="mini"
            @focus="handleAccessFocus(scope.row)"
            @blur="handleAccessBlur(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="接入率" align="center" prop="accessRate" width="80">
        <template slot-scope="scope">
          {{ scope.row.accessRate }}
        </template>
      </el-table-column>
      <el-table-column label="指定测试渠道" align="center" prop="channelId" width="100"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 表单弹窗 -->
    <form-dialog ref="formDialog" @success="getList" />
  </div>
</template>

<script>
import { getList, updateStatus, updateApplyControl, enterAccessNum } from '@/api/platformProductManagement/qwTestPlan'
import FormDialog from './components/form-dialog'

export default {
  name: 'QwTestPlan',
  components: {
    FormDialog
  },
  data() {
    return {
      // 总条数
      total: 0,
      // 表格数据
      tableList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 临时存储控量原值
      tempApplyControl: null,
      // 临时存储接入数原值
      tempAccessCount: null
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      getList(this.queryParams).then(res => {
        this.tableList = res.rows || []
        this.total = res.total
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.formDialog.openAdd()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$refs.formDialog.openEdit(row)
    },
    /** 开关状态修改 */
    handleStatusChange(row) {
      const action = row.status == 1 ? '启用' : '禁用'
      this.$confirm(`确认要${action}该计划吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateStatus({
          id: row.id,
          status: row.status
        }).then(() => {
          this.$message.success('状态修改成功')
          this.getList()
        })
      }).catch(() => {
        row.status = row.status == 1 ? 2 : 1 // 取消时恢复开关状态
      })
    },
    /** 控量输入框聚焦 */
    handleControlFocus(row) {
      this.tempApplyControl = row.applyControl
    },
    /** 控量输入框失焦 */
    handleControlBlur(row) {
      if (row.applyControl === this.tempApplyControl) {
        return
      }
      this.$confirm('确认要修改控量吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handleControlChange(row)
      }).catch(() => {
        this.getList() // 取消时刷新列表恢复原值
      })
    },
    /** 控量修改 */
    handleControlChange(row) {
      updateApplyControl({
        id: row.id,
        num: row.applyControl
      }).then(() => {
        this.$message.success('控量修改成功')
        this.getList()
      })
    },
    /** 接入数输入框聚焦 */
    handleAccessFocus(row) {
      this.tempAccessCount = row.accessCount
    },
    /** 接入数输入框失焦 */
    handleAccessBlur(row) {
      if (row.accessCount === this.tempAccessCount) {
        return
      }
      this.$confirm('确认要修改接入数吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handleAccessChange(row)
      }).catch(() => {
        this.getList() // 取消时刷新列表恢复原值
      })
    },
    /** 接入数修改 */
    handleAccessChange(row) {
      enterAccessNum({
        id: row.id,
        num: row.accessCount
      }).then(() => {
        this.$message.success('接入数修改成功')
        this.getList()
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 8px;
}
.el-range-separator {
  padding: 0 5px;
}
</style>