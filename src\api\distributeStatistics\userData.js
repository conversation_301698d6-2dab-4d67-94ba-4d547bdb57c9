import request from '@/utils/request'
//获取用户状态统计
export const getStarRatingStat = (data) => {
  return request({
    // url: "/loan/partya/push/starRatingStat",
    url: "/loan/xm/stat/platform/user/star",
    method: "get",
    params: data
  })
}


export const getPushStatistics = (data) => {
  return request({
    // url: "/loan/partya/push/statistics",
    url: "/loan/xm/stat/platform/user/status",
    method: "get",
    params: data
  })
}

export const getUserStarListDetail = (data) => {
  return request({

    url: "/loan/xm/stat/platform/user/star/details",
    method: "get",
    params: data
  })
}
export const getStatusListDetail = (data) => {
  return request({
    url: "/loan/xm/stat/platform/user/status/details",
    method: "get",
    params: data
  })
}
export const getChannelDetail = (data) => {
  return request({
    url: "/loan/xm/stat/channel/detail",
    method: "get",
    params: data
  })
}

export const getChannelDetail2 = (data) => {
  return request({
    url: "/loan/xm/stat/channel/v2/detail",
    method: "get",
    params: data
  })
}

//用户星级导出
export const exportStarData = (data) => {
  return request({
    url: "/loan/xm/stat/platform/user/star/xlsx",
    method: "get",
    responseType: "arraybuffer",
    params: data,
    timeout: 180000
  })
}
//用户质量导出
export const exportStatusData = (data) => {
  return request({
    url: "/loan/xm/stat/platform/user/status/xlsx",
    method: "get",
    responseType: "arraybuffer",
    params: data,
    timeout: 180000
  })
}

export const channelAgentDetail = (data) => {
  return request({
    url: '/external/channelAgent/detail',
    method: "get",
    params: data
  })
}
