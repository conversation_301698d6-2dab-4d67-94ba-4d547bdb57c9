<template>
  <div class="app-container">
    <el-form
      ref="configForm"
      :model="configForm"
      :rules="rules"
      label-width="120px"
      style="max-width: 600px;"
    >
        <el-form-item label="价格大于" prop="price">
          <el-input-number
            v-model="configForm.price"
            :precision="2"
            :step="0.01"
            :min="0"
            placeholder="请输入价格"
            style="width: 100%;"
          />
          <div class="form-tip">单位：元</div>
        </el-form-item>

        <el-form-item label="时间" prop="time">
          <el-input-number
            v-model="configForm.time"
            :min="0"
            placeholder="请输入时间"
            style="width: 100%;"
          />
          <div class="form-tip">单位：秒</div>
        </el-form-item>

        <el-form-item label="最大申请数" prop="maxApplyNum">
          <el-input-number
            v-model="configForm.maxApplyNum"
            :min="0"
            placeholder="请输入最大申请数"
            style="width: 100%;"
          />
          <div class="form-tip">单位：个</div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="handleSave"
            :loading="saveLoading"
          >
            保存配置
          </el-button>
        </el-form-item>
      </el-form>
  </div>
</template>

<script>
import { getProductControlConfig, setProductControlConfig } from '@/api/xmxrChannelManage/qwProductControlConfig'

export default {
  name: 'QwProductControlConfig',
  data() {
    return {
      configForm: {
        price: null,
        time: null,
        maxApplyNum: null
      },
      rules: {
        price: [
          { required: true, message: '请输入价格', trigger: 'blur' },
          { type: 'number', min: 0, message: '价格必须大于等于0', trigger: 'blur' }
        ],
        time: [
          { required: true, message: '请输入时间', trigger: 'blur' },
          { type: 'number', min: 0, message: '时间必须大于等于0', trigger: 'blur' }
        ],
        maxApplyNum: [
          { required: true, message: '请输入最大申请数', trigger: 'blur' },
          { type: 'number', min: 0, message: '最大申请数必须大于等于0', trigger: 'blur' }
        ]
      },
      saveLoading: false
    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    // 获取配置
    getConfig() {
      getProductControlConfig().then(response => {
        if (response.code === 200 && response.data) {
          this.configForm = {
            price: response.data.price || null,
            time: response.data.time || null,
            maxApplyNum: response.data.maxApplyNum || null
          }
        }
      }).catch(error => {
        console.error('获取配置失败:', error)
        this.$message.error('获取配置失败')
      })
    },

    // 保存配置
    handleSave() {
      this.$refs.configForm.validate((valid) => {
        if (valid) {
          this.saveLoading = true
          setProductControlConfig(this.configForm).then(response => {
            if (response.code === 200) {
              this.$message.success('保存成功')
            } else {
              this.$message.error(response.msg || '保存失败')
            }
          }).catch(error => {
            console.error('保存配置失败:', error)
            this.$message.error('保存失败')
          }).finally(() => {
            this.saveLoading = false
          })
        } else {
          this.$message.warning('请检查表单输入')
        }
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
