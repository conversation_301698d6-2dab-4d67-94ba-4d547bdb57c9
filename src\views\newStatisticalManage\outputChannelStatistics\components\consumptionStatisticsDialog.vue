<script>
import { getOutputConsumptionStatistics } from "@/api/statisticalManage";

export default {
  name: "consumptionStatistics",

  data() {
    return {
      tableData: [],
      visible: false,
      queryParams: {},
    };
  },

  methods: {
    open({ params }) {
      this.visible = true;
      this.queryParams = params;
      this.handleQuery();
    },

    async handleQuery() {
      const res = await getOutputConsumptionStatistics(this.queryParams);
      this.tableData = res.rows || [];
      if (this.tableData.length) {
        this.tableData.unshift(this.createSummaryRow(this.tableData));
      }
    },

    createSummaryRow(data) {
      const summaryRow = {
        channelId: "合计",
        applyNum: 0,
      };

      data.forEach((item) => {
        summaryRow.applyNum += item.applyNum;
      });

      return summaryRow;
    },

    sortChange({ prop, order }) {
      if (!this.tableData.length) {
        return;
      }

      const summaryRow = this.tableData[0];
      const sortRows = this.tableData.slice(1);

      switch (order) {
        //正序
        case "ascending":
          sortRows.sort((a, b) => a[prop] - b[prop]);
          break;
        //倒序
        case "descending":
          sortRows.sort((a, b) => b[prop] - a[prop]);
          break;
      }

      sortRows.unshift(summaryRow);
      this.tableData = sortRows;
    },
  },
};
</script>

<template>
  <el-dialog
    :visible.sync="visible"
    title="出量消耗统计"
    width="1000px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
  >
    <el-table :data="tableData" border @sort-change="sortChange" max-height="500">
      <el-table-column label="渠道id" align="center" prop="channelId" />
      <el-table-column label="渠道名称" align="center" prop="channelName" />
      <el-table-column
        label="提交数量"
        align="center"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="applyNum"
      />
    </el-table>
  </el-dialog>
</template>

<style scoped lang="scss"></style>
