// 房产类型
export const HOUSE_TYPES = {
  132: '全款商品房',
  133: '按揭商品房',
  134: '自建房',
  135: '外地有房',
  131: '无房'
};

// 车辆类型
export const VEHICLE_TYPES = {
  139: '全款汽车',
  140: '按揭汽车',
  138: '无车'
};

// 公积金类型
export const PROVIDENT_FUND_TYPES = {
  153: '六个月以上',
  152: '六个月以下',
  150: '无公积金'
};

// 芝麻信用分类型
export const SESAME_CREDIT_TYPES = {
  115: '700分以上',
  113: '650-700分',
  112: '600-650分',
  110: '600分以下'
};

// 社保类型
export const SOCIAL_SECURITY_TYPES = {
  159: '缴纳6个月以上',
  158: '缴纳6个月以下',
  156: '无'
};

// 信用状态类型（逾期）
export const CREDIT_STATUS_TYPES = {
  122: '信用良好，无逾期',
  123: '无信用卡或贷款',
  124: '近1年无逾期',
  125: '1年内逾期少于3次且少于90天',
  126: '1年内逾期超过3次或者90天'
};

// 职业类型（营业执照）
export const VOCATION_TYPES = {
  128: '企业主 有营业执照',
  127: '上班族 无营业执照'
};

// 保险类型
export const INSURANCE_TYPES = {
  145: '没有',
  147: '有'
};

// 性别类型
export const SEX_TYPES = {
  0: '男',
  1: '女'
};
