<script>
import dayjs from "dayjs";
import { getOutputConsumptionStatistics } from "@/api/statisticalManage";

export default {
  name: "consumptionStatistics",

  data() {
    return {
      queryParams: {
        dateRange: [],
        channelName: "",
      },
      tableData: [],
    };
  },

  mounted() {
    this.setDefaultDate();
    this.handleQuery();
  },

  methods: {
    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format("YYYY-MM-DD 00:00:00");
      const endTime = today.format("YYYY-MM-DD 23:59:59");

      this.queryParams.dateRange = [startTime, endTime];
    },

    getParams() {
      const [startTime = "", endTime = ""] = this.queryParams?.dateRange || [];
      const channelName = this.queryParams.channelName;

      return {
        startTime,
        endTime,
        channelName,
      };
    },

    async handleQuery() {
      const res = await getOutputConsumptionStatistics(this.getParams());
      this.tableData = res.rows || [];
      if (this.tableData.length) {
        this.tableData.unshift(this.createSummaryRow(this.tableData));
      }
    },

    createSummaryRow(data) {
      const summaryRow = {
        channelId: "合计",
        applyNum: 0,
        priceTotal: 0,
      };

      data.forEach((item) => {
        summaryRow.applyNum += item.applyNum;
        summaryRow.priceTotal += item.priceTotal;
      });

      return summaryRow;
    },

    sortChange({ prop, order }) {
      if (!this.tableData.length) {
        return;
      }

      const summaryRow = this.tableData[0];
      const sortRows = this.tableData.slice(1);

      switch (order) {
        //正序
        case "ascending":
          sortRows.sort((a, b) => a[prop] - b[prop]);
          break;
        //倒序
        case "descending":
          sortRows.sort((a, b) => b[prop] - a[prop]);
          break;
      }

      sortRows.unshift(summaryRow);
      this.tableData = sortRows;
    },
  },
};
</script>

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" inline size="small">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="queryParams.dateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道名称">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入渠道名称"
          clearable
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border @sort-change="sortChange">
      <el-table-column label="渠道id" align="center" prop="channelId" fixed />
      <el-table-column label="渠道名称" align="center" prop="channelName" />
      <el-table-column
        label="提交数量"
        align="center"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="applyNum"
      />
      <el-table-column
        label="预估收益"
        align="center"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="priceTotal"
      />
    </el-table>
  </div>
</template>

<style scoped lang="scss"></style>
