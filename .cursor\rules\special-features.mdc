---
description: 
globs: 
alwaysApply: true
---
# 特定功能和依赖包

项目中某些特定功能应使用专门的依赖包，以保持代码一致性和可维护性。

## 拖拽功能
```js
// 拖拽功能使用 vuedraggable
import draggable from 'vuedraggable'

// 使用示例
<draggable
  v-model="list"
  handle=".drag-handle"
  :animation="200"
  @start="drag=true"
  @end="drag=false">
  <div v-for="item in list" :key="item.id">
    {{ item.name }}
    <i class="drag-handle el-icon-rank"></i>
  </div>
</draggable>
```

## 日期时间处理
```js
// 日期时间处理使用 dayjs
import dayjs from 'dayjs'

// 使用示例
const now = dayjs()
const formattedDate = now.format('YYYY-MM-DD')
const lastWeek = now.subtract(7, 'day')
```

## 生成唯一标识
```js
// 生成唯一标识使用 uuid
import { v4 as uuidv4 } from 'uuid'

// 使用示例
const id = uuidv4() // 生成类似 '1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed' 的唯一标识
```

## 二维码生成
```js
// 二维码生成使用 qrcode.vue
import QrcodeVue from 'qrcode.vue'

// 使用示例
<qrcode-vue value="https://example.com" :size="200" level="H" />
```

## 富文本编辑器
```js
// 富文本编辑器使用 wangeditor
// 已封装为组件，路径: @/components/Editor/index.vue
import Editor from "@/components/Editor"

// 使用示例
<editor v-model="form.content" :min-height="192"/>
```

## 工具函数库
```js
// 工具函数使用 lodash
import _ from 'lodash'

// 深拷贝
const cloned = _.cloneDeep(obj)

// 防抖
const debouncedFn = _.debounce(fn, 300)

// 节流
const throttledFn = _.throttle(fn, 300)
```

## 文件操作
```js
// 文件操作使用 file-saver
import { saveAs } from 'file-saver'

// 使用示例
saveAs(blob, "filename.pdf")
```

## Excel 处理
```js
// Excel 处理使用 xlsx
import * as XLSX from 'xlsx'

// 使用示例
const workbook = XLSX.read(data, { type: 'array' })
const firstSheet = workbook.Sheets[workbook.SheetNames[0]]
const jsonData = XLSX.utils.sheet_to_json(firstSheet)
```

## 页面筛选表单的日期时间选择器
```vue
<el-date-picker
  v-model="dateRange"
  type="daterange"
  value-format="yyyy-MM-dd HH:mm:ss"
  :default-time="['00:00:00', '23:59:59']"
  range-separator="至"
  start-placeholder="开始日期"
  end-placeholder="结束日期"
/>

<script>
export default {
  data() {
    return {
      dateRange: [] // 日期范围
    }
  },
  created() {
    // 默认选中当天
    const today = new Date()
    const start = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
    const end = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)
    this.dateRange = [
      this.parseTime(start, '{y}-{m}-{d} {h}:{i}:{s}'),
      this.parseTime(end, '{y}-{m}-{d} {h}:{i}:{s}')
    ]
  }
}
</script>

