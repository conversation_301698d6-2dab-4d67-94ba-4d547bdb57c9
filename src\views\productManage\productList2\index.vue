<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="推广名称" prop="name">
        <el-input
          v-model.trim="queryParams.name"
          placeholder="请输入推广名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商户名称" prop="name">
        <el-input
          v-model="queryParams.partyaName"
          placeholder="请输入商户名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <template v-if="!isMobile">
        <el-form-item label="商务名称" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入商务名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="商户类型" prop="mid">
          <el-select
            clearable
            @change="getList"
            v-model="queryParams.mid"
            placeholder="请选择商户类型"
            size="small"
          >
            <el-option label="银行机构" :value="1"> </el-option>
            <el-option label="线上-贷超" :value="2"> </el-option>
            <el-option label="线上持牌机构" :value="3"> </el-option>
            <el-option label="一级机构" :value="4"> </el-option>
            <el-option label="二级机构" :value="5"> </el-option>
            <el-option label="三级机构" :value="6"> </el-option>
            <el-option label="四级机构" :value="7"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态" prop="auditStatus">
          <el-select
            clearable
            @change="getList"
            v-model="queryParams.auditStatus"
            placeholder="请选择推广类型"
            size="small"
          >
            <el-option label="全部" value="-1"> </el-option>
            <el-option label="审核中" value="0"> </el-option>
            <el-option label="审核通过" value="1"> </el-option>
            <el-option label="审核失败" value="2"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推广状态" prop="status" key="status">
          <el-select
            clearable
            @change="getList"
            v-model="queryParams.status"
            placeholder="请选择推广状态"
            size="small"
            style="width: 160px"
          >
            <el-option label="全部" value="-1"> </el-option>
            <el-option label="在线" value="1"> </el-option>
            <el-option label="下线" value="2"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="投放状态" prop="status1" key="status1">
          <el-select
            clearable
            @change="getList"
            v-model="queryParams.status1"
            placeholder="请选择投放状态"
            size="small"
            style="width: 160px"
          >
            <el-option label="全部" value="-1"> </el-option>
            <el-option label="在线" value="1"> </el-option>
            <el-option label="下线" value="2"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="活跃投放产品"
          prop="activeStatus"
          v-if="hasBool('loan:product:query_a2')"
        >
          <el-select
            v-model="queryParams.activeStatus"
            style="width: 160px"
            size="small"
            @change="handleQuery"
          >
            <el-option :value="0" label="全部"></el-option>
            <el-option :value="1" label="近一天有效活跃"></el-option>
            <el-option :value="2" label="近三天有效活跃"></el-option>
            <el-option :value="3" label="连续三天无效活跃"></el-option>
            <el-option :value="4" label="近五无效活跃"></el-option>
            <el-option :value="5" label="静默投放产品"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="城市">
          <el-cascader
            v-model="queryParams.cityCodes"
            :options="cityList"
            collapse-tags
            @change="handleQuery"
            clearable
            filterable
            :props="{ multiple: true, emitPath: false }"
            size="mini"
          >
          </el-cascader>
        </el-form-item>
      </template>
      <template v-else>
        <el-collapse-transition name="el-zoom-in-center">
          <div v-show="queryVisible">
            <el-form-item label="商务名称" prop="userName" key="userName">
              <el-input
                v-model="queryParams.userName"
                placeholder="请输入商务名称"
                clearable
                size="small"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="商户类型" prop="mid" key="mid">
              <el-select
                clearable
                @change="getList"
                v-model="queryParams.mid"
                placeholder="请选择商户类型"
                size="small"
                style="width: 100%"
              >
                <el-option label="银行机构" :value="1"> </el-option>
                <el-option label="线上-贷超" :value="2"> </el-option>
                <el-option label="线上持牌机构" :value="3"> </el-option>
                <el-option label="一级机构" :value="4"> </el-option>
                <el-option label="二级机构" :value="5"> </el-option>
                <el-option label="三级机构" :value="6"> </el-option>
                <el-option label="四级机构" :value="7"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="审核状态" prop="auditStatus" key="auditStatus">
              <el-select
                clearable
                @change="getList"
                v-model="queryParams.auditStatus"
                placeholder="请选择推审核状态"
                size="small"
                style="width: 100%"
              >
                <el-option label="全部" value="-1"> </el-option>
                <el-option label="审核中" value="0"> </el-option>
                <el-option label="审核通过" value="1"> </el-option>
                <el-option label="审核失败" value="2"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="推广状态" prop="status" key="status">
              <el-select
                clearable
                @change="getList"
                v-model="queryParams.status"
                placeholder="请选择推广状态"
                size="small"
                style="width: 100%"
              >
                <el-option label="全部" value="-1"> </el-option>
                <el-option label="在线" value="1"> </el-option>
                <el-option label="下线" value="2"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="投放状态" prop="status1" key="status1">
              <el-select
                clearable
                @change="getList"
                v-model="queryParams.status1"
                placeholder="请选择投放状态"
                size="small"
                style="width: 100%"
              >
                <el-option label="全部" value="-1"> </el-option>
                <el-option label="在线" value="1"> </el-option>
                <el-option label="下线" value="2"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="活跃投放产品"
              prop="activeStatus"
              v-if="hasBool('loan:product:query_a2')"
            >
              <el-select
                v-model="queryParams.activeStatus"
                size="small"
                @change="handleQuery"
              >
                <el-option :value="0" label="全部"></el-option>
                <el-option :value="1" label="近一天有效活跃"></el-option>
                <el-option :value="2" label="近三天有效活跃"></el-option>
                <el-option :value="3" label="连续三天无效活跃"></el-option>
                <el-option :value="4" label="近五无效活跃"></el-option>
                <el-option :value="5" label="静默投放产品"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="城市">
              <el-cascader
                v-model="queryParams.cityCodes"
                :options="cityList"
                @change="handleQuery"
                clearable
                filterable
                collapse-tags
                :props="{ multiple: true, emitPath: false }"
                size="mini"
              >
              </el-cascader>
            </el-form-item>
          </div>
        </el-collapse-transition>
        <div class="mobile-query-open" @click="queryVisible = !queryVisible">
          <span>{{ queryVisible ? "收起" : "展开" }}</span>
          <i
            :class="[queryVisible ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          ></i>
        </div>
      </template>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
<!--      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          v-hasPermi="['productManage:product:add2']"
          @click="handleChooseDropdownClick('add', {})"
          >新增投放产品</el-button
        >
      </el-form-item>-->
      <el-form-item>
        <el-button
          type="primary"
          plain
          icon="el-icon-download"
          size="mini"
          v-hasPermi="['loan:product:exportProductDistribution2']"
          @click="exportData"
          >投放城市分布导出</el-button
        >
      </el-form-item>
      <el-form-item
        label="开屏页开关"
        v-hasPermi="['productManage:product:conductSwitch2']"
      >
        <el-switch
          v-model="switchStatus"
          @change="handleSwitchStatus"
          inactive-text="关"
          active-text="开"
          inactive-value="0"
          active-value="1"
        ></el-switch>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="productList"
      @sort-change="handleSortChange"
    >
      <el-table-column label="推广ID" prop="id" align="center" />
      <el-table-column label="推广名称" prop="name" width="100" />
      <el-table-column
        label="推广状态"
        prop="status"
        align="center"
        width="120"
      >
        <template slot-scope="{ row }">
          <div v-if="row.status">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="2"
              :active-text="row.status == 1 ? '在线' : ''"
              :inactive-text="row.status != 1 ? '下线' : ''"
              :disabled="
                (row.isFlowPackage && row.isFlowPackageValid) ||
                (!hasBool('op_all:product:status2') && !row.isCreate)
              "
              @change="changeProdStatus($event, row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="投放状态"
        prop="status1"
        width="140"
        align="center"
      >
        <template slot-scope="{ row }">
          <div>
            <el-switch
              disabled
              v-model="row.status1"
              :active-value="1"
              :inactive-value="2"
              :active-text="row.status1 == 1 ? '在线' : ''"
              :inactive-text="row.status1 != 1 ? '下线' : ''"
              @change="changeLoanStautus($event, row.id)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="hasBool('productManage:product:conductSwitch2')"
        label="开屏页广告"
        prop="status1"
        width="140"
        align="center"
      >
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.advertisingFlag"
              :active-value="1"
              :inactive-value="0"
              :active-text="row.advertisingFlag == 1 ? '在线' : ''"
              :inactive-text="row.advertisingFlag != 1 ? '下线' : ''"
              :disabled="row.mid != 2"
              @change="handleProductStatus(row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="hasBool('productManage:product:productQiWeiSwitch2')"
        label="企微表单"
        width="140"
        align="center"
      >
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.isQiWei"
              :active-value="true"
              :inactive-value="false"
              active-text="启用"
              inactive-text="禁用"
              @change="handleQiweiStatus(row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="商户类型"
        prop="midName"
        align="center"
        width="120"
      />
      <el-table-column
        label="排序"
        prop="sort"
        align="center"
        width="110"
        v-if="isShowMore"
      >
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.sort"
            controls-position="right"
            :min="0"
            :ref="'valueinput' + scope.$index"
            @change="handleChangeSort($event, scope.row)"
            :style="{ width: '90px' }"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="商户"
        prop="partyaName"
        :sort-orders="['descending', 'ascending']"
        sortable="custom"
      />
      <el-table-column label="商务" prop="userName" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-tag v-if="row.isTransfer == 1">转</el-tag> {{ row.userName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        width="100"
        label="推广价格"
        prop="cooperationCost"
        align="center"
        :sort-orders="['descending', 'ascending']"
        sortable="custom"
      />
      <!-- <el-table-column label="申请量/推送量" prop="putin_push" align="center" /> -->
      <!-- <el-table-column label="放款额" prop="lendingquotas" align="center" /> -->
      <!-- <el-table-column label="产品来源" prop="" align="center">
        <template slot-scope="{ row }">
            <div v-if="row.source">
            {{ row.source == 1 ? "我方后台" : "商户后台" }}
            </div>
        </template>
        </el-table-column> -->
      <el-table-column label="审核状态" prop="" align="center" width="120">
        <template slot-scope="{ row }">
          <div>
            <el-button
              type="primary"
              plain
              size="mini"
              disabled
              v-if="row.auditStatus == 0"
            >
              待审核
            </el-button>
            <el-button
              type="primary"
              size="mini"
              disabled
              v-if="row.auditStatus == -1"
            >
              待提交
            </el-button>
            <el-button
              type="success"
              size="mini"
              plain
              disabled
              v-if="row.auditStatus == 1"
            >
              审核成功
            </el-button>
            <el-popover
              placement="top-start"
              title="不通过理由"
              width="300"
              trigger="click"
              :content="row.auditRemarks"
            >
              <el-button
                slot="reference"
                v-if="row.auditStatus == 2"
                type="danger"
                size="mini"
              >
                审核不通过
              </el-button>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="消耗" align="center">
        <template slot-scope="{ row }">
          <div
            style="color: #0256ff"
            class="earnings"
            @click="handleChooseDropdownClick('consume', row)"
          >
            {{ row.earnings }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="可用余额" prop="availableAmount" align="center" />
      <!-- <el-table-column label="返点金额" prop="rebates" align="center" /> -->
      <el-table-column label="上架时间" prop="createTime" />
      <el-table-column label="查看渠道分布">
        <template slot-scope="{ row }">
          <div>
            <el-button type="text" @click="handleDetail(row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="200"
        :fixed="screenWidth ? 'right' : false"
      >
        <template slot-scope="{ row }">
          <div>
            <el-button
              size="mini"
              :disabled="row.auditStatus == 0"
              type="text"
              style="color: #0256ff"
              @click="hanlSetting(row)"
            >
              推广设置
            </el-button>
            <el-button
              size="mini"
              type="text"
              style="color: #0256ff"
              @click="controlSetting(row)"
              :disabled="
                row.auditStatus == 0 ||
                row.auditStatus == -1 ||
                row.auditStatus == 2 ||
                row.isFlowPackage
              "
            >
              控量设置
            </el-button>
            <el-dropdown
              @command="handleChooseDropdownClick($event, row)"
              v-hasPermi="[
                'productManage:product:rebate2',
                'productManage:product:edit2',
              ]"
            >
              <span class="el-dropdown-link" style="color: #0256ff">
                更多
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  :disabled="
                    row.auditStatus == 0 ||
                    row.auditStatus == -1 ||
                    row.auditStatus == 2
                  "
                  v-hasPermi="['productManage:product:edit2']"
                  command="copy"
                  :style="{
                    color:
                      row.auditStatus == 0 ||
                      row.auditStatus == -1 ||
                      row.auditStatus == 2
                        ? '#999'
                        : '#0256FF',
                  }"
                >
                  复制
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="row.auditStatus == 0 || row.auditStatus == -1"
                  v-hasPermi="['productManage:product:edit2']"
                  command="edit"
                  :style="{
                    color:
                      row.auditStatus == 0 || row.auditStatus == -1
                        ? '#999'
                        : '#0256FF',
                  }"
                >
                  <span>修改</span>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="
                    row.auditStatus == 0 ||
                    row.auditStatus == -1 ||
                    row.auditStatus == 2
                  "
                  v-hasPermi="['loan:productManage:rebate2']"
                  command="apply"
                  :style="{
                    color:
                      row.auditStatus == 0 ||
                      row.auditStatus == -1 ||
                      row.auditStatus == 2
                        ? '#999'
                        : '#0256FF',
                  }"
                >
                  <div>返点申请</div>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="
                    row.auditStatus == 0 ||
                    row.auditStatus == -1 ||
                    row.auditStatus == 2
                  "
                  v-hasPermi="['loan:productRule:updateChannel2']"
                  command="limit"
                  :style="{
                    color:
                      row.auditStatus == 0 ||
                      row.auditStatus == -1 ||
                      row.auditStatus == 2
                        ? '#999'
                        : '#0256FF',
                  }"
                >
                  <div>渠道限制</div>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="
                    row.auditStatus == 0 ||
                    row.auditStatus == -1 ||
                    row.auditStatus == 2
                  "
                  v-hasPermi="['loan:productChannelProportion:export2']"
                  command="export"
                  :style="{
                    color:
                      row.auditStatus == 0 ||
                      row.auditStatus == -1 ||
                      row.auditStatus == 2
                        ? '#999'
                        : '#0256FF',
                  }"
                >
                  <div>导出</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 返点申请 -->
    <el-dialog
      title="返点申请"
      :visible.sync="rebatesAvisible"
      @close="rebatesRancel"
      width="1000px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="rebatesFormData"
        :model="rebatesFormData"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="返点时间" prop="rebateDate">
          <el-date-picker
            :picker-options="pickerOptions"
            v-model="rebatesFormData.rebateDate"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-row
          :gutter="24"
          v-for="(item, index) in rebatesFormData.list"
          :key="index + 'list'"
        >
          <el-col :span="8">
            <el-form-item
              label="返点金额"
              :prop="'list.' + index + '.rebateAmount'"
              :rules="{
                required: true,
                message: '请输入金额',
                trigger: 'blur',
              }"
            >
              <div class="align-items-c">
                <div
                  class="apply-minus align-items-c justify-content-c"
                  @click="handleChangeRebatesPrice(-1, index)"
                >
                  <span class="el-icon-minus"></span>
                </div>
                <el-input
                  type="number"
                  :min="0"
                  v-model="item.rebateAmount"
                  placeholder="请输入金额"
                ></el-input>
                <div
                  class="apply-add align-items-c justify-content-c"
                  @click="handleChangeRebatesPrice(1, index)"
                >
                  <span class="el-icon-plus"></span>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :prop="'list.' + index + '.reason'"
              label="申请原因"
              :rules="{
                required: true,
                message: '请输入金额',
                trigger: 'change',
              }"
            >
              <el-select v-model="item.reason">
                <el-option label="空号" value="空号"></el-option>
                <el-option label="异地" value="异地"></el-option>
                <el-option label="挂机" value="挂机"></el-option>
                <el-option label="其他原因" value="其他原因"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              :prop="'list.' + index + '.channelId'"
              label="渠道ID"
              :rules="{
                required: true,
                message: '请输入渠道ID',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model.number="item.channelId"
                type="number"
                placeholder="请输入渠道ID"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button
              size="mini"
              type="primary"
              v-if="rebatesFormData.list.length - 1 == index"
              @click="handleAddItem"
              >添加</el-button
            >
            <el-button
              size="mini"
              type="danger"
              v-if="rebatesFormData.list.length > 1"
              @click="hanldeDelItem(index)"
              >删除</el-button
            >
          </el-col>
        </el-row>
        <!-- <el-form-item label="返点金额" prop="price">
          <div class="align-items-c">
            <div
              class="apply-minus align-items-c justify-content-c"
              @click="handleChangeRebatesPrice(-1)"
            >
              <span class="el-icon-minus"></span>
            </div>
            <el-input
              type="number"
              :min="0"
              v-model="rebatesFormData.price"
              placeholder="请输入返点金额"
            ></el-input>
            <div
              class="apply-add align-items-c justify-content-c"
              @click="handleChangeRebatesPrice(1)"
            >
              <span class="el-icon-plus"></span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="申请原因" prop="reason">
          <el-input
            type="textarea"
            v-model="rebatesFormData.reason"
            placeholder="请输入申请原因"
          ></el-input>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRebatesForm">确 定</el-button>
        <el-button @click="rebatesRancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="导出产品分布"
      :visible.sync="exportVisible"
      width="600px"
      append-to-body
      center
      @close="exportCancel"
      :close-on-click-modal="false"
    >
      <el-form>
        <el-form-item label="时间:" prop="time">
          <el-date-picker
            v-model="dateRange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
          >
          </el-date-picker>
        </el-form-item>

        <div class="d-c">
          <el-button type="primary" @click="hanldeExportProduct"
            >导出</el-button
          >
        </div>
      </el-form>
    </el-dialog>
    <controlAmountVue
      v-model="controlAvisible"
      :width="screenWidth ? '80%' : '95%'"
      :id="productId"
      :title="productName"
      @close="controlAvisible = false"
      @update="getList"
    />

    <release
      v-model="releaseAvisible"
      :width="screenWidth ? '50%' : '95%'"
      :id="productId"
      @close="releaseAvisible = false"
      @update="getList"
    />
    <edit
      v-model="dialogAvisible"
      :id="productId"
      :type="editType"
      :isQiWei="isQiWei"
      :width="screenWidth ? '814px' : '95%'"
      @close="dialogAvisible = false"
      @update="getList"
    />
    <consume
      v-model="consumeVisible"
      :id="productId"
      :product="currentProduct"
      :width="screenWidth ? '80%' : '95%'"
      @close="consumeVisible = false"
      @update="getList"
    />
    <limit
      v-model="limitVisible"
      :id="productId"
      :width="screenWidth ? '50%' : '95%'"
      @close="limitVisible = false"
      @update="getList"
    />
    <el-dialog
      :visible.sync="dialogVisible"
      title="详情"
      width="80%"
      append-to-body
    >
      <el-table :data="detailList" border>
        <el-table-column
          label="渠道ID"
          align="center"
          prop="channelId"
        ></el-table-column>
        <!-- <el-table-column
          label="渠道名称"
          align="center"
          prop="channelName"
        ></el-table-column> -->
        <el-table-column
          label="产品名称"
          align="center"
          prop="productName"
        ></el-table-column>
        <el-table-column
          label="总数"
          align="center"
          prop="total"
        ></el-table-column>
        <el-table-column
          label="占比"
          align="center"
          prop="proportion"
        ></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  getloanModeList,
  productCooperations,
  productCondition,
  productCompileSort,
  productRebate,
  changeVipProduct,
  exportProductDistribution,
  getConductSwitchStatus,
  editConductSwitchStatus,
  editProductConductSwitch,
  editProductQiWeiSwitch,
} from "@/api/productManage/product";

import {
  getCityList,
  exportChannelProduct,
  exportChannelProductList,
} from "@/api/statisticalManage";
import { debounce } from "@/utils";
import controlAmountVue from "./components/controlAmount.vue";
import release from "./components/release.vue";
import edit from "./components/edit.vue";
import consume from "./components/consume.vue";
import limit from "./components/limit.vue";
import { hasBool } from "@/directive/permission/hasBool";
export default {
  name: "ProductList",
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      hasBool: hasBool,
      showUsers: ["shenxun", "yutong", "sunyang","xupengcheng"],
      switchStatus: "0",
      dateRange: [],
      screenWidth: true,
      rebatesAvisible: false,
      controlAvisible: false,
      releaseAvisible: false,
      dialogAvisible: false,
      consumeVisible: false,
      queryVisible: false,
      limitVisible: false,
      exportVisible: false,
      isQiWei: false,
      editType: "add",
      loading: false,
      exportName: "",
      dialogVisible: false,
      detailList: [],
      rules: {
        price: [{ required: true, message: "请输入返点金额", trigger: "blur" }],
        rebateDate: [
          { required: true, message: "请选择返点日期", trigger: "change" },
        ],
      },
      row: {},
      cityList: [],
      currentProduct: {},
      productId: "",
      productName: "",
      rebatesFormData: {
        partyFirstId: "",

        productId: "",

        list: [
          {
            channelId: "",
            reason: "",
            rebateAmount: "",
          },
        ],
      },
      prodStatus: [
        {
          value: 1,
          lable: "在线",
        },
        {
          value: 2,
          lable: "下线",
        },
        // {
        //   value: 3,
        //   lable: "限量下线",
        // },
        // {
        //   value: 4,
        //   lable: "限量上线",
        // },
      ],
      total: 0,
      productList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        name: "",
        userName: "",
        partyaName: "",
        mid: "",
        auditStatus: "-1",
        status: "-1",
        status1: "-1",
        activeStatus: 0,
        cityCodes: [],
        mappingFlag: 1,
      },
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  components: {
    controlAmountVue,
    release,
    edit,
    consume,
    limit,
  },
  computed: {
    isMobile: () => {
      return document.body.clientWidth < 768;
    },
    isShowMore() {
      return this.showUsers.includes(this.$store.getters.userInfo.userName);
    },
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    //导出产品分布信息
    exportData() {
      exportProductDistribution().then((res) => {
        let a = document.createElement("a");
        let blob = new Blob([res], { type: "application/vnd.ms-excel" });
        let objectUrl = URL.createObjectURL(blob);
        a.setAttribute("href", objectUrl);
        a.setAttribute("download", "投放城市分布导出.xlsx");
        a.click();
        this.$message.success("导出成功");
      });
    },
    // 返点取消
    rebatesRancel() {
      (this.rebatesFormData = {
        partyFirstId: "",
        productId: "",
        list: [
          {
            channelId: "",
            reason: "",
            rebateAmount: "",
          },
        ],
      }),
        (this.rebatesAvisible = false);
      this.$refs.rebatesFormData.resetFields();
    },
    submitRebatesForm() {
      this.$refs.rebatesFormData.validate((valid) => {
        if (valid) {
          console.log(this.rebatesFormData);
          productRebate(this.rebatesFormData).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              this.rebatesRancel();
              this.getList();
            }
          });
        }
      });
    },

    /**
     *  @param { String } command 点击选项  copy:复制 edit:修改 apply：申请返点
     * @param row 当前行数据
     */
    handleChooseDropdownClick(command = "", row = {}) {
      const {
        id = "",
        partyaId = "",
        isQiWei = false,
        name = "",
        qwFlag = false,
      } = row;
      switch (command) {
        case "add":
          this.dialogAvisible = true;
          this.editType = command;
          this.isQiWei = false;
          break;
        case "copy":
          this.dialogAvisible = true;
          this.editType = command;
          this.productId = id;
          break;
        case "edit":
          this.dialogAvisible = true;
          this.editType = command;
          this.productId = id;
          this.isQiWei = isQiWei;

          break;
        case "apply":
          this.rebatesAvisible = true;
          this.rebatesFormData.productId = id;
          this.rebatesFormData.partyFirstId = partyaId;
          break;
        case "consume":
          this.consumeVisible = true;
          this.productId = id;
          this.currentProduct = row;
          break;
        case "limit":
          this.limitVisible = true;
          this.productId = id;
          break;
        case "export":
          this.exportVisible = true;
          this.productId = id;
          this.exportName = name;
          break;
        default:
          break;
      }
    },
    /**
     *  @param { Function } handleChangeRebatesPrice 修改返点申请金额
     *  @param { Number } price 修改的金额
     */
    handleChangeRebatesPrice(price, index) {
      if (this.rebatesFormData.list[index].rebateAmount <= 0 && price == -1)
        return;
      price = this.rebatesFormData.list[index].rebateAmount / 1 + price;
      this.rebatesFormData.list[index].rebateAmount = price;
    },

    //控量设置
    controlSetting(row) {
      this.controlAvisible = true;
      this.productId = row.id;
      this.productName = row.name;
    },

    /**
     *  @parma { Function } handleChangeSort 变更表格排序
     */
    handleChangeSort: debounce(function (sort, { id }) {
      productCompileSort({ id, sort }).then((res) => {
        if (res.code == 200) {
          this.$message.success("排序成功");
          this.getList();
        }
      });
    }, 1000),

    changeLoanStautus(e, id) {
      this.$confirm("是否修改投放状态?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          productCooperations({ status1: e, id: id }).then((res) => {
            if (res.code == 200) {
              this.$message.success("状态跟新成功");
              this.getList();
            }
          });
        })
        .catch(() => {
          this.getList();
        });
    },
    //修改产品状态
    changeProdStatus(e, row) {
      this.$confirm(
        `是否修改${row.isFlowPackage ? "CPD投放展位" : "推广产品"}状态?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          productCondition({ status: e, id: row.id })
            .then((res) => {
              if (res.code == 200) {
                this.$message.success("状态跟新成功");
                this.getList();
              }
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch(() => {
          this.getList();
        });
    },
    //修改产品是否vip
    changeVipProductStatus(e) {
      this.$confirm("是否修改产品vip状态?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          changeVipProduct({ id: e }).then((res) => {
            if (res.code == 200) {
              this.$message.success("状态跟新成功");
              this.getList();
            }
          });
        })
        .catch(() => {
          this.getList();
        });
    },
    getList() {
      this.loading = true;
      getloanModeList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.productList = res.rows;
          this.productList.forEach((item) => {
            item.isEdit = true;
          });
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    hanlSetting({ id }) {
      this.releaseAvisible = true;
      this.productId = id;
      // this.$router.push(`/productManage/setting?id=${row.id}`);
    },
    handleSwitchStatus() {
      this.$confirm("是否修改状态?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((res) => {
          editConductSwitchStatus().then((res) => {
            this.$message.success("状态跟新成功");
          });
        })
        .catch((err) => {
          this.switchStatus = this.switchStatus == "0" ? "1" : "0";
        });
    },
    handleProductStatus(row) {
      this.$confirm("确定修改吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((res) => {
          editProductConductSwitch({ productId: row.id }).then((res) => {
            this.$message.success("操作成功");
          });
        })
        .catch((err) => {
          row.advertisingFlag = row.advertisingFlag == 1 ? 0 : 1;
        });
    },

    handleQiweiStatus(row) {
      this.$confirm("确定修改吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((res) => {
          editProductQiWeiSwitch({ productId: row.id }).then((res) => {
            this.$message.success("操作成功");
          });
        })
        .catch((err) => {
          row.isQiWei = row.isQiWei == true ? false : true;
        });
    },
    handleAddItem() {
      this.rebatesFormData.list.push({
        channelId: "",
        reason: "",
        rebateAmount: "",
      });
    },
    hanldeDelItem(index) {
      this.rebatesFormData.list.splice(index, 1);
    },
    //前端排序
    handleSortChange(column) {
      this.proptype = column.prop;
      let sortingType = column.order;
      if (this.proptype == "partyaName") {
        if (sortingType == "ascending") {
          //正序
          this.productList = this.productList.sort((a, b) =>
            a.partyaName.localeCompare(b.partyaName, "zh-Hans-CN", {
              sensitivity: "accent",
            })
          );
        }
        if (sortingType == "descending") {
          // 倒序
          this.productList = this.productList.sort((a, b) =>
            b.partyaName.localeCompare(a.partyaName, "zh-Hans-CN", {
              sensitivity: "accent",
            })
          );
        }

        return;
      }
      if (sortingType == "ascending") {
        //正序
        this.productList = this.productList.sort(
          (a, b) => b[this.proptype] - a[this.proptype]
        );
      }
      if (sortingType == "descending") {
        // 倒序
        this.productList = this.productList.sort(
          (a, b) => a[this.proptype] - b[this.proptype]
        );
      }
    },
    hanldeExportProduct() {
      if (this.dateRange == null || this.dateRange.length == 0)
        return this.$message.error("请选择时间");
      exportChannelProduct({
        productId: this.productId,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1],
      }).then((res) => {
        let a = document.createElement("a");
        let blob = new Blob([res], { type: "application/vnd.ms-excel" });
        let objectUrl = URL.createObjectURL(blob);
        a.setAttribute("href", objectUrl);
        a.setAttribute("download", `${this.exportName}.xlsx`);
        a.click();
        this.exportVisible = false;
        this.dateRange = [];
        this.$message.success("导出成功");
      });
    },
    exportCancel() {
      this.exportVisible = false;
      this.dateRange = [];
    },
    handleDetail(row) {
      let date = new Date();
      let year = date.getFullYear();
      let month = (date.getMonth() + 1 + "").padStart(2, "0");
      let day = (date.getDate() + "").padStart(2, "0");
      this.detailList = [];
      exportChannelProductList({
        productId: row.id,
        startTime: year + "-" + month + "-" + day + " 00:00:00",
        endTime: year + "-" + month + "-" + day + " 23:59:59",
      }).then((res) => {
        console.log(res);
        this.detailList = res.data;
        this.dialogVisible = true;
      });
    },
  },
  mounted() {
    getConductSwitchStatus().then((res) => {
      this.switchStatus = res.data;
    });
    this.getList();
    this.screenWidth = document.body.clientWidth > 768 ? true : false;
    //获取城市格式化
    getCityList().then((res) => {
      let data = null;
      data = res.data.map((item) => {
        if (item.citys) {
          {
            return {
              value: item.code,
              label: item.name,
              disabled: false,
              children: [
                ...item.citys.map((citem) => {
                  return {
                    value: citem.code,
                    label: citem.name,
                    disabled: false,
                  };
                }),
              ],
            };
          }
        } else {
          return {
            value: item.code,
            label: item.name,
            disabled: false,
          };
        }
      });
      this.cityList = JSON.parse(JSON.stringify(data));
    });
  },
};
</script>

<style lang="scss" scoped>
.earnings {
  text-decoration: underline;
  cursor: pointer;
}

.apply-minus,
.apply-add {
  width: 40px;
  height: 34px;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
}

.apply-minus {
  margin-right: 8px;
}

.apply-add {
  margin-left: 8px;
}

.mobile-query-open {
  text-align: right;
  padding-bottom: 20px;
  color: #666;
  font-size: 14px;
  line-height: 20px;

  i {
    font-size: 16px;
    margin-left: 3px;
  }
}

::v-deep .el-switch__label.el-switch__label--left.is-active {
  color: #999 !important;
}
.d-c {
  display: flex;
  justify-content: center;
}
</style>
