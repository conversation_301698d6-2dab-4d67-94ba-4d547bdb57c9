import request from '@/utils/request'
import { exportType } from './system/dict/type'

// 登录方法
export function login(username, password, code, uuid, lat, lng) {
  const data = {
    username,
    password,
    code,
    uuid, lat, lng
  }
  return request({
    url: '/login',
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}


// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    method: 'get',
    timeout: 20000
  })
}
export function getUserType() {
  return request({
    url: '/getUserType',
    method: "get"
  })
}
export function getCode(data) {
  return request({
    url: "/login/sendV2",
    // url: "/login/send",
    method: "post",
    data
  })
}
//修改密码
export function editRestPassword(data) {
  return request({
    url: "/updateRestPassword",
    method: "post",
    data
  })
}

//重新发送验证码
export function resetSend(data) {
  return request({
    url: "/login/reset/send",
    method: "post",
    data
  })
}
