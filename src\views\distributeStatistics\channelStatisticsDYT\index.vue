<script>
import { getChannelDetail2 as getChannelDetail } from "@/api/distributeStatistics/userData";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
import { sum, divide } from "@/utils/calculate";
export default {
  name: "channelUser",

  data() {
    return {
      total: 0,
      dialogVisible: false,
      showData: true,
      refreshTable: true,
      timeSlots: [
        { label: "9:00~18:00", value: "1" },
        { label: "12:00~13:30", value: "2" },
        { label: "10:00~18:00", value: "3" }
      ],
      // 搜索条件 渠道名称 渠道id 开始时间 结束时间
      queryParams: {
        channelName: "",
        channelId: "",
        dateRange: [],
        timeSlot: ""
      },
      detailList: [],
      tableData: [],
    };
  },

  mounted() {
    this.setDefaultDate();
    this.fetchTableData();
  },

  methods: {
    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format("YYYY-MM-DD 00:00:00");
      const endTime = today.format("YYYY-MM-DD 23:59:59");

      this.queryParams.dateRange = [startTime, endTime];
    },

    getParams() {
      const [startTime = "", endTime = ""] = this.queryParams.dateRange || [];

      return {
        startTime,
        endTime,
        channelName: this.queryParams.channelName,
        channelId: this.queryParams.channelId,
        projectId: 4
      };
    },

    handleTableData(arr) {
      arr.forEach((row) => {
        row.rowKey = uuidv4();
        row.outputValue = divide(row.profit, (row.uvNum || row.registerNum), 1);

        if (row.platformStats && row.platformStats.length) {
          row.platformStats.forEach((innerRow) => {
            innerRow.rowKey = uuidv4();
            innerRow.channelId = "";
            innerRow.isInner = true;
            innerRow.channelName = innerRow.channelName
              ? innerRow.channelName.split("_")[1]
              : "";
          });
        } else {
          row.subList = null;
        }
      });
    },
    createSummaryRow(data) {
      const summaryRow = {
        channelId: "合计",

        channelName: null,
        uvNum: 0,
        registerNum: 0,
        formNum: 0,
        offlineNum: 0,
        offlineProfit: 0,
        outputNum: 0,
        outputProfit: 0,
        halfNum: 0,
        halfProfit: 0,
        qwNum: 0,
        qwProfit: 0,
        dcNum: 0,
        dcProfit: 0,
        profit: 0,
        isSummary: true,
      };

      summaryRow.uvNum = sum(data.map((item) => item.uvNum));
      summaryRow.registerNum = sum(data.map((item) => item.registerNum));
      summaryRow.formNum = sum(data.map((item) => item.formNum));
      summaryRow.offlineNum = sum(data.map((item) => item.offlineNum));
      summaryRow.offlineProfit = sum(data.map((item) => item.offlineProfit));
      summaryRow.halfNum = sum(data.map((item) => item.halfNum));
      summaryRow.halfProfit = sum(data.map((item) => item.halfProfit));
      summaryRow.qwNum = sum(data.map((item) => item.qwNum));
      summaryRow.qwProfit = sum(data.map((item) => item.qwProfit));
      summaryRow.dcProfit = sum(data.map((item) => item.dcProfit));
      summaryRow.dcNum = sum(data.map((item) => item.dcNum));
      summaryRow.profit = sum(data.map((item) => item.profit));
      summaryRow.outputNum = sum(data.map((item) => item.outputNum));
      summaryRow.outputProfit = sum(data.map((item) => item.outputProfit));
      summaryRow.outputValue = divide(summaryRow.profit, (summaryRow.uvNum || summaryRow.registerNum), 1);

      return summaryRow;
    },
    async fetchTableData() {
      const params = this.getParams();
      const res = await getChannelDetail(params);

      this.handleTableData(res.data);

      this.tableData = res.data;
      if (this.tableData.length) {
        this.tableData.unshift(this.createSummaryRow(this.tableData));
      }
    },
    handleShowData() {
      this.refreshTable = false;
      this.showData = !this.showData;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },

    handleTimeSlotClear() {
      const [startTime, endTime] = this.queryParams.dateRange || [];

      const startDay = dayjs(startTime).format("YYYY-MM-DD");
      const endDay = dayjs(endTime).format("YYYY-MM-DD");

      const resultStart = `${startDay} 00:00:00`;
      const resultEnd = `${endDay} 23:59:59`;
      
      this.queryParams.dateRange = [resultStart, resultEnd];
      this.fetchTableData();
    },

    handleDateChange() {
      this.queryParams.timeSlot = ""; // 清空时间段选择器
      this.fetchTableData(); // 调用原有的查询方法
    },

    handleTimeSlotChange(value) {
      const [startTime, endTime] = this.queryParams.dateRange || [];
      const startDay = dayjs(startTime).format("YYYY-MM-DD");
      const endDay = dayjs(endTime).format("YYYY-MM-DD");
      let resultStart = "";
      let resultEnd = "";
      
      switch(value) {
        case "1":
          resultStart = `${startDay} 09:00:00`;
          resultEnd = `${endDay} 18:00:00`;
          break;
        case "2":
          resultStart = `${startDay} 12:00:00`;
          resultEnd = `${endDay} 13:30:00`;
          break;
        case "3":
          resultStart = `${startDay} 10:00:00`;
          resultEnd = `${endDay} 18:00:00`;
          break;
        default:
          resultStart = `${startDay} 00:00:00`;
          resultEnd = `${endDay} 23:59:59`;
          return;
      }
      
      this.queryParams.dateRange = [resultStart, resultEnd];
      this.fetchTableData();
    },
  },
  computed: {
    showHandle() {
      return this.$store.getters.userInfo.userName == "beta";
    },
  },
};
</script>

<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" size="small">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="queryParams.dateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="时间段">
        <el-select v-model="queryParams.timeSlot" placeholder="请选择时间段" clearable @change="handleTimeSlotChange" @clear="handleTimeSlotClear">
          <el-option
            v-for="item in timeSlots"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="渠道名称">
        <el-input
          v-model="queryParams.channelName"
          placeholder="请输入渠道名称"
          @keyup.enter.native="fetchTableData"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道id">
        <el-input
          v-model="queryParams.channelId"
          placeholder="请输入渠道id"
          @keyup.enter.native="fetchTableData"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="fetchTableData">查询</el-button>
      </el-form-item>
      <el-form-item v-if="showHandle">
        <el-button type="primary" @click="handleShowData">显示/隐藏</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      v-if="refreshTable"
      style="width: 100%"
      row-key="rowKey"
      :tree-props="{
        children: 'platformStats',
      }"
    >
      <el-table-column
        label="渠道ID"
        align="center"
        prop="channelId"
      ></el-table-column>
      <el-table-column prop="channelName" label="渠道名称" width="150px">
        <template slot-scope="{ row }">
          <div v-if="!row.isSummary">
            <el-tag size="small" type="info" v-if="row.isInner">{{
              row.channelName
            }}</el-tag>
            <el-tag size="small" v-else>{{ row.channelName }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="UV" align="center" prop="uvNum">
        <template slot-scope="{ row }">
          <span size="small" type="info" v-if="row.isInner">-</span>
          <span v-else>{{ row.uvNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="注册数" align="center" prop="registerNum" v-if="showData">
        <template slot-scope="{ row }">
          <span size="small" type="info" v-if="row.isInner">-</span>
          <span v-else>{{ row.registerNum }}</span>
        </template>
      </el-table-column>
      <template v-if="showData">
        <el-table-column label="表单数" align="center" prop="formNum">
          <template slot-scope="{ row }">
            <span size="small" type="info" v-if="row.isInner">-</span>
            <span v-else>{{ row.formNum }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="线下申请数"
          align="center"
          prop="offlineNum"
        ></el-table-column>
        <el-table-column
          label="线下收益"
          align="center"
          prop="offlineProfit"
        ></el-table-column>

        <el-table-column
          label="出量申请数"
          align="center"
          prop="outputNum"
        ></el-table-column>

        <el-table-column
          label="出量收益"
          align="center"
          prop="outputProfit"
        ></el-table-column>

        <el-table-column
          label="半流程申请数"
          align="center"
          prop="halfNum"
        ></el-table-column>

        <el-table-column
          label="半流程收益"
          align="center"
          prop="halfProfit"
        ></el-table-column>

        <el-table-column
          label="企微申请数"
          align="center"
          prop="qwNum"
        ></el-table-column>

        <el-table-column
          label="企微收益"
          align="center"
          prop="qwProfit"
        ></el-table-column>

        <el-table-column
          label="贷超申请数"
          align="center"
          prop="dcNum"
        ></el-table-column>

        <el-table-column
          label="贷超收益"
          align="center"
          prop="dcProfit"
        ></el-table-column>
      </template>

      <el-table-column
        label="总收益"
        align="center"
        prop="profit"
      ></el-table-column>

      <el-table-column label="产值" align="center" prop="outputValue">
        <template slot-scope="{ row }">
          {{ row.outputValue || '-' }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped lang="scss"></style>
