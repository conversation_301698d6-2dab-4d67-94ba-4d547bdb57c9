<template>
  <div class="app-container">
    <el-form ref="queryForm" :inline="true">
      <el-form-item label="分组" prop="auditStatus">
        <el-radio-group @change="handleRadio" size="small" v-model="radio">
          <el-radio
            border
            :label="item.groupName"
            :key="item.id"
            v-for="item in groupArr"
          ></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" prop="auditStatus">
        <el-button
          type="primary"
          size="mini"
          v-hasPermi="['loan:groupProduct:update']"
          @click="handleAdd"
          >修改</el-button
        >
        <el-button
          type="danger"
          v-hasPermi="['loan:groupProduct:update']"
          size="mini"
          @click="handleSave"
          >保存</el-button
        >
      </el-form-item>
    </el-form>
    <div v-if="type" class="type">
      {{
        type == 1
          ? "配置产品只对配置的渠道开放，渠道能匹配到其他产品"
          : "配置产品只对配置渠道开放，并且配置渠道只能对配置产品进量"
      }}
    </div>
    <div class="wrap">
      <div class="wrap-item">
        <div
          :class="['wrap-item-tag']"
          v-for="item in productList"
          :key="item.value"
          @click="hanldeTagitem(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="wrap-item">
        <div
          :class="['wrap-item-tag']"
          v-for="item in channelList"
          :key="item.value"
          @click="hanldeTagitem(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <el-dialog
      title="修改"
      :visible.sync="visible"
      width="80%"
      center
      :close-on-click-modal="false"
      @close="handleclose"
    >
      <el-form ref="form" :model="addForm" label-width="80px">
        <el-form-item label="产品">
          <el-select
            clearable
            filterable
            multiple
            v-model="addForm.productId"
            placeholder="可多选"
            style="width: 99%"
          >
            <el-option
              v-for="item in optionProductList"
              :key="item.id"
              :value="item.id"
              :label="`ID:${item.id}--` + item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="渠道">
          <el-select
            clearable
            filterable
            multiple
            v-model="addForm.channelId"
            placeholder="可多选"
            style="width: 100%"
          >
            <el-option
              v-for="item in optionChannelList"
              :key="item.id"
              :value="item.id"
              :label="`ID:${item.id}--` + item.channelName"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleclose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getGroupProductList,
  updateGroupProductList,
} from "@/api/productManage/product";
import {
  getFeedbackChannelList,
  getFeedbackProductList,
} from "@/api/statisticalManage";
export default {
  name: "correlation",
  data() {
    return {
      radio: "",
      id: "",
      type: 1,
      visible: false,
      productArr: [],
      channelArr: [],
      groupArr: [],
      channelList: [],
      productList: [],
      optionProductList: [],
      optionChannelList: [],
      addForm: {
        productId: [],
        channelId: [],
      },
    };
  },
  methods: {
    handleAdd() {
      this.visible = true;
      this.addForm.productId = this.productList.map((item) => item.value);
      this.addForm.channelId = this.channelList.map((item) => item.value);
    },
    handleSubmit() {
      let productData = [];
      let channelData = [];
      this.optionProductList.forEach((item) => {
        this.addForm.productId.forEach((item1) => {
          if (item.id == item1) {
            productData.push({
              label: item.id + "-" + item.name,
              value: item.id,
            });
          }
        });
      });
      this.optionChannelList.forEach((item) => {
        this.addForm.channelId.forEach((item1) => {
          if (item.id == item1) {
            channelData.push({
              label: item.id + "-" + item.channelName,
              value: item.id,
            });
          }
        });
      });
      this.channelList = channelData;
      this.productList = productData;
      this.visible = false;
    },
    handleclose() {
      this.visible = false;
      this.addForm = {
        productId: [],
        channelId: [],
      };
      this.$refs.form.resetFields();
    },
    handleSave() {
      this.$confirm("确定保存吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let productIds = this.productList.map((item) => item.value).join(",");
          let channelIds = this.channelList.map((item) => item.value).join(",");
          let data = {
            id: this.id,
            groupName: this.radio,
            channelIds: channelIds,
            productIds: productIds,
          };
          updateGroupProductList(data).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              // let data = this.groupArr.filter(
              //   (item) => item.groupName == this.radio
              // );
              // data[0].channelList = this.channelList;
              // data[0].productList = this.productList;
              this.groupArr.forEach((item) => {
                if (item.groupName == this.radio) {
                  item.channelList = this.channelList;
                  item.productList = this.productList;
                }
              });
            }
          });
        })
        .catch(() => {});
    },

    handleRadio() {
      let data = this.groupArr.filter((item) => item.groupName == this.radio);
      this.channelList = data[0].channelList || [];
      this.productList = data[0].productList || [];
      this.id = data[0].id || "";
      this.type = data[0].type || "";
    },

    getInit() {
      getGroupProductList().then((res) => {
        this.groupArr = res.data;
        this.radio = res.data[0].groupName || "";
        this.id = res.data[0].id || "";
        this.type = res.data[0].type || "";
        this.channelList = res.data[0].channelList || [];
        this.productList = res.data[0].productList || [];
      });
    },
  },
  mounted() {
    this.getInit();
    getFeedbackChannelList().then((res) => {
      this.optionChannelList = res.data;
    });
    getFeedbackProductList().then((res) => {
      this.optionProductList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  display: flex;
  width: 100%;
  .wrap-item {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    flex: 1;
    padding: 10px;
    min-height: 200px;
    border-radius: 10px;
    border: 1px solid #004DAB;
    align-content: flex-start;

    &:nth-child(1) {
      margin-right: 50px;
    }
    &-tag {
      height: 40px;
      margin-right: 10px;
      border: 1px solid #004DAB;
      margin-top: 10px;
      cursor: pointer;
      line-height: 40px;
      text-align: center;
      padding: 0px 5px;
      color: #444;
      font-size: 15px;
      border-radius: 5px;
    }
    .active {
      background: #004DAB;
      color: #fff;
    }
  }
}
.type {
  margin-bottom: 20px;
  font-weight: 600;
}
::v-deep .el-select.el-select--medium {
  overflow-y: scroll;
  overflow-x: hidden;
  max-height: 300px;
}
::-webkit-scrollbar {
  width: 0px;
}
</style>
