import request from '@/utils/request'

// 获取乙方的列表
export const getPartyBlilst = (data) => {
  return request({
    url: '/loan/PartyB/lsit',
    method: 'get',
    params: data
  })
}
//获取id乙方信息
export const getselectOdd = (data) => {
  return request({
    url: '/loan/PartyB/selectOdd',
    method: 'get',
    params: data
  })
}
//获取商户商务id
export const getAffairsList = (data) => {
  return request({
    url: "/loan/PartyB/likeA",
    method: "post",
    data
  })
}
//新增乙方
export const addPartyBuser = (data) => {
  return request({
    url: "/loan/PartyB/add",
    method: "post",
    data
  })
}
//修改乙方信息
export const ApppartyEdit = (data) => {
  return request({
    url: "/loan/PartyB/edit",
    method: "post",
    data
  })
}
//申请退款
export const ApplyForRefund = (data) => {
  return request({
    url: "/loan/PartyB/refund",
    method: "post",
    data
  })
}
//状态管理
export const getIib = (data) => {
  return request({
    url: '/loan/PartyB/iib',
    method: 'get',
    params: data
  })
}
//请款记录
export const PostCashOut = (data) => {
  return request({
    url: '/loan/PartyB/cashOut',
    method: 'post',
    data
  })
}
//查询所有主体
export const getSubjectAll = (data) => {
  return request({
    url: '/loan/PartyB/subjectAll',
    method: 'get',
    params: data
  })
}
//收款主体
export const getSubject = (data) => {
  return request({
    url: '/loan/subject/' + data,
    method: 'get'
  })
}

//请款列表
export const getCashOutList = (data) => {
  return request({
    url: '/loan/PartyB/cashOutList',
    method: 'get',
    params: data
  })
}
//乙方财务审核列表
export const partybeexamineList = (data) => {
  return request({
    url: '/loan/partybeexamine/getlist',
    method: 'get',
    params: data
  })
}
//请款审核
export const partybeexamineCashout = (data) => {
  return request({
    url: "/loan/partybeexamine/cashout",
    method: 'post',
    data
  })
}
//退款审核
export const partybeexamineRefund = (data) => {
  return request({
    url: '/loan/partybeexamine/refund',
    method: 'POST',
    data
  })
}
// 请款详情
export const financeDetail = (data) => {
  return request({
    url: '/loan/finance/detail',
    method: 'get',
    params: data
  })
}
//退款详情/loan/partybeexamine/refundinfo

export const partybeexamineRefundinfo = (data) => {
  return request({
    url: '/loan/partybeexamine/refundinfo',
    method: 'get',
    params: data
  })
}
//财务请款详情/loan/finance/financedetail
export const financeDetailfs = (data) => {
  return request({
    url: '/loan/finance/financedetail',
    method: 'get',
    params: data
  })
}
//财务审核详情 /loan/partybeexamine/getFinanceList
export const getFinanceList = (data) => {
  return request({
    url: '/loan/partybeexamine/getFinanceList',
    method: 'get',
    params: data
  })
}
//财务退款详情/loan/partybeexamine/financerefundinfo
export const financerefundinfo = (data) => {
  return request({
    url: '/loan/partybeexamine/financerefundinfo',
    method: 'get',
    params: data
  })
}
//财务退款审核 /loan/partybeexamine/financerefund
export const financerefund = (data) => {
  return request({
    url: '/loan/partybeexamine/financerefund',
    method: 'POST',
    data
  })
}
//财务请款审核/loan/partybeexamine/financecashout
export const financecashout = (data) => {
  return request({
    url: "/loan/partybeexamine/financecashout",
    method: 'post',
    data
  })
}
//获取乙方退款申请列表
export function getRefundList(data) {
  return request({
    url: "/loan/finance/refundList",
    method: 'get',
    params: data
  })
}
