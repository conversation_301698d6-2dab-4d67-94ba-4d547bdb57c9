<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="搜索关键词" prop="searchText">
        <el-input
          v-model="queryParams.searchText"
          placeholder="请输入名称/描述/内容"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item label="所属平台" prop="platformIds">
        <el-select 
          v-model="queryParams.platformIds" 
          placeholder="请选择平台"
          multiple
          clearable
          filterable
          style="width: 240px"
        >
          <el-option
            v-for="item in platformList"
            :key="item.id"
            :label="`${item.id}-${item.name}`"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="advertisingList">
      <el-table-column label="序号" type="index" width="50" />
      <el-table-column label="广告名称" prop="name" />
      <el-table-column label="所属平台" prop="platformId">
        <template slot-scope="scope">
          {{ getPlatformName(scope.row.platformId) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" width="150">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" />
      <el-table-column label="创建时间" prop="created" width="180">
        <template slot-scope="scope">
          {{ formatDate(scope.row.created) }}
        </template>
      </el-table-column>
      <el-table-column label="更新时间" prop="updated" width="180">
        <template slot-scope="scope">
          {{ formatDate(scope.row.updated) }}
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" show-overflow-tooltip />
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加平台广告抽屉 -->
    <el-drawer
      :title="form.id ? '修改平台广告' : '新增平台广告'"
      :visible.sync="open"
      size="70%"
      direction="rtl"
      :before-close="handleClose"
    >
      <div style="padding: 20px;">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <!-- 基本信息 -->
          <el-card class="form-card" shadow="never">
            <div slot="header" class="card-header">
              <span>基本信息</span>
            </div>
            <!-- 广告名称和所属平台在一行显示 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="广告名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入广告名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属平台" prop="platformId">
                  <el-select
                    v-model="form.platformId"
                    placeholder="请选择平台"
                    clearable
                    filterable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in platformList"
                      :key="item.id"
                      :label="`${item.id}-${item.name}`"
                      :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="描述" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入描述" :rows="3" />
            </el-form-item>
          </el-card>

          <!-- 广告内容配置 -->
          <el-card class="form-card" shadow="never" style="margin-top: 20px;" v-if="form.contentConfig">
            <div slot="header" class="card-header">
              <span>广告内容配置</span>
            </div>

            <!-- 广告标题 -->
            <el-form-item label="广告标题" prop="contentConfig.title">
              <el-input
                v-model="form.contentConfig.title"
                placeholder="请输入广告标题"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>

            <!-- Banner图片 -->
            <el-form-item label="Banner图片" prop="contentConfig.bannerImage">
              <el-upload
                ref="bannerUpload"
                action=""
                :http-request="handleBannerUpload"
                :show-file-list="false"
                :before-upload="beforeBannerUpload"
                accept=".png,.jpg,.jpeg"
              >
                <el-button size="small" type="primary">选择图片</el-button>
              </el-upload>
              <div v-if="form.contentConfig.bannerImage" style="margin-top: 10px;">
                <el-image
                  style="width: 100px; height: 100px;"
                  :src="form.contentConfig.bannerImage"
                  :preview-src-list="[form.contentConfig.bannerImage]"
                ></el-image>
                <el-button
                  type="text"
                  size="small"
                  @click="removeBannerImage"
                  style="margin-left: 10px;"
                >删除</el-button>
              </div>
            </el-form-item>

            <!-- 活动时间 -->
            <el-form-item label="活动时间" prop="contentConfig.activityTime">
              <el-date-picker
                v-model="form.contentConfig.activityTime"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="['00:00:00', '23:59:59']"
                style="width: 100%"
              />
            </el-form-item>

            <!-- 联系方式 -->
            <el-form-item label="联系方式">
              <span style="color: #909399; font-size: 12px;">至少填写一种联系方式</span>
            </el-form-item>

            <!-- 电话号码 -->
            <el-form-item label="电话号码">
              <el-input
                v-model="form.contentConfig.contactPhone"
                placeholder="请输入电话号码"
                maxlength="20"
              />
            </el-form-item>

            <!-- 微信号 -->
            <el-form-item label="微信号">
              <el-input
                v-model="form.contentConfig.contactWechat"
                placeholder="请输入微信号"
                maxlength="50"
              />
            </el-form-item>

            <!-- 微信二维码 -->
            <el-form-item label="微信二维码">
              <el-upload
                ref="qrcodeUpload"
                action=""
                :http-request="handleQrcodeUpload"
                :show-file-list="false"
                :before-upload="beforeQrcodeUpload"
                accept=".png,.jpg,.jpeg"
              >
                <el-button size="small" type="primary">选择二维码</el-button>
              </el-upload>
              <div v-if="form.contentConfig.contactQrcode" style="margin-top: 10px;">
                <el-image
                  style="width: 100px; height: 100px;"
                  :src="form.contentConfig.contactQrcode"
                  :preview-src-list="[form.contentConfig.contactQrcode]"
                ></el-image>
                <el-button
                  type="text"
                  size="small"
                  @click="removeQrcodeImage"
                  style="margin-left: 10px;"
                >删除</el-button>
              </div>
            </el-form-item>

            <!-- 活动概述 -->
            <el-form-item label="活动概述" prop="contentConfig.description">
              <el-input
                v-model="form.contentConfig.description"
                type="textarea"
                placeholder="请输入活动概述"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <!-- 活动权益 -->
            <el-form-item label="活动权益" prop="contentConfig.benefits">
              <div class="dynamic-list">
                <div
                  v-for="(_, index) in form.contentConfig.benefits"
                  :key="index"
                  class="dynamic-item"
                >
                  <el-input
                    v-model="form.contentConfig.benefits[index]"
                    placeholder="请输入权益描述"
                    maxlength="100"
                  />
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="small"
                    @click="removeBenefit(index)"
                    :disabled="form.contentConfig.benefits.length <= 1"
                  />
                </div>
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  size="small"
                  @click="addBenefit"
                  style="margin-top: 10px;"
                >
                  添加权益
                </el-button>
              </div>
            </el-form-item>

            <!-- 活动名额 -->
            <el-form-item label="活动名额" prop="contentConfig.quota">
              <el-input-number
                v-model="form.contentConfig.quota"
                :min="1"
                :max="9999"
                controls-position="right"
                style="width: 200px;"
              />
              <span style="margin-left: 10px; color: #909399;">人</span>
            </el-form-item>

            <!-- 活动流程 -->
            <el-form-item label="活动流程" prop="contentConfig.process">
              <div class="process-list">
                <draggable
                  v-model="form.contentConfig.process"
                  handle=".drag-handle"
                  @start="drag = true"
                  @end="drag = false"
                >
                  <div
                    v-for="(_, index) in form.contentConfig.process"
                    :key="index"
                    class="process-item"
                  >
                    <i class="el-icon-s-grid drag-handle"></i>
                    <span class="step-number">{{ index + 1 }}.</span>
                    <el-input
                      v-model="form.contentConfig.process[index]"
                      placeholder="请输入流程步骤"
                      maxlength="100"
                    />
                    <el-button
                      type="danger"
                      icon="el-icon-delete"
                      size="small"
                      @click="removeProcess(index)"
                      :disabled="form.contentConfig.process.length <= 1"
                    />
                  </div>
                </draggable>
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  size="small"
                  @click="addProcess"
                  style="margin-top: 10px;"
                >
                  添加步骤
                </el-button>
              </div>
            </el-form-item>
          </el-card>
        </el-form>
        <div class="drawer-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { searchPlatformAdvertising, addPlatformAdvertising, updatePlatformAdvertising, updatePlatformAdvertisingStatus, deletePlatformAdvertising } from '@/api/platformAdvertising'
import { getPlatformList } from '@/api/xmxrChannelManage/channelList'
import { uploadFileToOSS } from '@/api/file'
import Pagination from '@/components/Pagination'
import draggable from 'vuedraggable'
import dayjs from 'dayjs'

export default {
  name: 'PlatformAdvertising',
  components: {
    Pagination,
    draggable
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 平台广告表格数据
      advertisingList: [],
      // 平台列表
      platformList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 提交状态
      submitting: false,
      // 拖拽状态
      drag: false,
      // 表单参数
      form: {
        contentConfig: {
          title: '',
          bannerImage: '',
          activityTime: [
            dayjs().format('YYYY-MM-DD 00:00:00'),
            dayjs().format('YYYY-MM-DD 23:59:59')
          ],
          contactPhone: '',
          contactWechat: '',
          contactQrcode: '',
          description: '',
          benefits: [''],
          quota: 10,
          process: [
            '添加客服联系方式',
            '客服审核通过',
            '正式参与活动'
          ]
        }
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "广告名称不能为空", trigger: "blur" }
        ],
        platformId: [
          { required: true, message: "所属平台不能为空", trigger: "change" }
        ],
        remark: [
          { required: true, message: "描述不能为空", trigger: "blur" }
        ],
        'contentConfig.title': [
          { required: true, message: "广告标题不能为空", trigger: "blur" }
        ],
        'contentConfig.bannerImage': [
          { required: true, message: "Banner图片不能为空", trigger: "change" }
        ],
        'contentConfig.activityTime': [
          { required: true, message: "活动时间不能为空", trigger: "change" }
        ],
        'contentConfig.contactPhone': [
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ],
        'contentConfig.description': [
          { required: true, message: "活动概述不能为空", trigger: "blur" }
        ],
        'contentConfig.benefits': [
          { required: true, message: "活动权益不能为空", trigger: "change" }
        ],
        'contentConfig.quota': [
          { required: true, message: "活动名额不能为空", trigger: "change" }
        ],
        'contentConfig.process': [
          { required: true, message: "活动流程不能为空", trigger: "change" }
        ]
      },
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        searchText: '',
        platformIds: []
      }
    }
  },
  created() {
    this.getList()
    this.getPlatformList()
  },
  mounted() {
    // 添加自定义验证器
    this.rules['contentConfig.benefits'].push({ validator: this.validateBenefits, trigger: "change" })
    this.rules['contentConfig.process'].push({ validator: this.validateProcess, trigger: "change" })
  },
  methods: {
    /** 查询平台广告列表 */
    getList() {
      this.loading = true
      searchPlatformAdvertising(this.queryParams).then(response => {
        this.advertisingList = response.data.records
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 获取平台列表 */
    getPlatformList() {
      getPlatformList().then(response => {
        this.platformList = response.data || []
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 获取平台名称 */
    getPlatformName(platformId) {
      const platform = this.platformList.find(item => item.id === platformId)
      return platform ? `${platform.id}-${platform.name}` : platformId
    },
    /** 格式化日期 */
    formatDate(date) {
      if (!date) return ''
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加平台广告"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form.id = row.id
      this.form.name = row.name
      this.form.platformId = row.platformId
      this.form.remark = row.remark

      // 解析content字段
      if (row.content) {
        try {
          const contentConfig = JSON.parse(row.content)
          const defaultConfig = {
            title: '',
            bannerImage: '',
            activityTime: [
              dayjs().format('YYYY-MM-DD 00:00:00'),
              dayjs().format('YYYY-MM-DD 23:59:59')
            ],
            contactPhone: '',
            contactWechat: '',
            contactQrcode: '',
            description: '',
            benefits: [''],
            quota: 10,
            process: [
              '添加客服联系方式',
              '客服审核通过',
              '正式参与活动'
            ]
          }
          this.form.contentConfig = {
            ...defaultConfig,
            ...contentConfig
          }
        } catch (error) {
          console.error('解析广告内容配置失败:', error)
          this.$message.warning('广告内容配置格式错误，将使用默认配置')
          // 使用默认配置
          this.form.contentConfig = {
            title: '',
            bannerImage: '',
            activityTime: [
              dayjs().format('YYYY-MM-DD 00:00:00'),
              dayjs().format('YYYY-MM-DD 23:59:59')
            ],
            contactPhone: '',
            contactWechat: '',
            contactQrcode: '',
            description: '',
            benefits: [''],
            quota: 10,
            process: [
              '添加客服联系方式',
              '客服审核通过',
              '正式参与活动'
            ]
          }
        }
      }

      this.open = true
      this.title = "修改平台广告"
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        name: null,
        platformId: null,
        content: null,
        remark: null,
        contentConfig: {
          title: '',
          bannerImage: '',
          activityTime: [
            dayjs().format('YYYY-MM-DD 00:00:00'),
            dayjs().format('YYYY-MM-DD 23:59:59')
          ],
          contactPhone: '',
          contactWechat: '',
          contactQrcode: '',
          description: '',
          benefits: [''],
          quota: 10,
          process: [
            '添加客服联系方式',
            '客服审核通过',
            '正式参与活动'
          ]
        }
      }
      this.resetForm("form")
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 抽屉关闭前的回调 */
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.reset()
          done()
        })
        .catch(_ => {})
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid, fields) => {
        if (valid) {
          // 验证联系方式
          if (!this.validateContactInfo()) {
            return false
          }

          this.submitting = true

          try {
            // 序列化contentConfig为JSON字符串
            const content = JSON.stringify(this.form.contentConfig)

            if (this.form.id) {
              // 编辑时传递接口要求的字段：id、content、name、platformId、remark
              const { id, name, platformId, remark } = this.form
              const updateData = { id, content, name, platformId, remark }
              updatePlatformAdvertising(this.form.id, updateData).then(() => {
                this.$modal.msgSuccess("修改成功")
                this.open = false
                this.getList()
              }).catch(() => {
                this.$message.error("修改失败")
              }).finally(() => {
                this.submitting = false
              })
            } else {
              // 新增时不传递id参数
              const { id, contentConfig, ...formData } = this.form
              const submitData = { ...formData, content }
              addPlatformAdvertising(submitData).then(() => {
                this.$modal.msgSuccess("新增成功")
                this.open = false
                this.getList()
              }).catch(() => {
                this.$message.error("新增失败")
              }).finally(() => {
                this.submitting = false
              })
            }
          } catch (error) {
            console.error('序列化广告配置失败:', error)
            this.$message.error("广告配置数据格式错误")
            this.submitting = false
          }
        } else {
          // 校验不通过时，找到第一个错误并提示
          const firstError = Object.values(fields)[0][0].message
          this.$message.error(firstError)
          return false
        }
      })
    },

    /** 状态修改 */
    handleStatusChange(row) {
      const newStatus = row.status
      const statusText = newStatus == 1 ? '启用' : '禁用'
      const platformName = this.getPlatformName(row.platformId)
      
      const confirmText = `确认要${statusText}${platformName}的${row.name}吗？`
      
      this.$modal.confirm(confirmText).then(() => {
        return updatePlatformAdvertisingStatus(row.id, newStatus)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(`${statusText}成功`)
      }).catch(() => {
        // 如果用户取消或操作失败，恢复switch状态
        row.status = newStatus == 1 ? 0 : 1
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const platformName = this.getPlatformName(row.platformId)
      this.$modal.confirm(`确认要删除${platformName}的${row.name}吗？`).then(() => {
        return deletePlatformAdvertising(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },




    /** 验证联系方式信息 */
    validateContactInfo() {
      const { contactPhone, contactWechat, contactQrcode } = this.form.contentConfig

      // 检查是否至少填写了一种联系方式
      if (!contactPhone && !contactWechat && !contactQrcode) {
        this.$message.error('请至少填写一种联系方式')
        return false
      }

      // 如果填写了电话号码，验证格式
      if (contactPhone && !/^1[3-9]\d{9}$/.test(contactPhone)) {
        this.$message.error('请输入正确的手机号码')
        return false
      }

      return true
    },

    /** Banner图片上传 */
    async handleBannerUpload({ file }) {
      const formData = new FormData()
      formData.append("file", file)
      try {
        const { url } = await uploadFileToOSS(formData)
        this.form.contentConfig.bannerImage = url
        this.$message.success('Banner图片上传成功')
        return url
      } catch (error) {
        this.$message.error('Banner图片上传失败')
        throw error
      }
    },

    /** Banner图片上传前验证 */
    beforeBannerUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      return true
    },

    /** 删除Banner图片 */
    removeBannerImage() {
      this.form.contentConfig.bannerImage = ''
    },

    /** 二维码图片上传 */
    async handleQrcodeUpload({ file }) {
      const formData = new FormData()
      formData.append("file", file)
      try {
        const { url } = await uploadFileToOSS(formData)
        this.form.contentConfig.contactQrcode = url
        this.$message.success('二维码图片上传成功')
        return url
      } catch (error) {
        this.$message.error('二维码图片上传失败')
        throw error
      }
    },

    /** 二维码图片上传前验证 */
    beforeQrcodeUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },

    /** 删除二维码图片 */
    removeQrcodeImage() {
      this.form.contentConfig.contactQrcode = ''
    },

    /** 添加权益 */
    addBenefit() {
      this.form.contentConfig.benefits.push('')
    },

    /** 删除权益 */
    removeBenefit(index) {
      if (this.form.contentConfig.benefits.length > 1) {
        this.form.contentConfig.benefits.splice(index, 1)
      }
    },

    /** 添加流程步骤 */
    addProcess() {
      this.form.contentConfig.process.push('')
    },

    /** 删除流程步骤 */
    removeProcess(index) {
      if (this.form.contentConfig.process.length > 1) {
        this.form.contentConfig.process.splice(index, 1)
      }
    },

    /** 验证活动权益 */
    validateBenefits(_, value, callback) {
      if (!value || value.length === 0) {
        callback(new Error('请至少添加一条活动权益'))
        return
      }

      const hasEmptyBenefit = value.some(benefit => !benefit || !benefit.trim())
      if (hasEmptyBenefit) {
        callback(new Error('活动权益不能为空'))
        return
      }

      callback()
    },

    /** 验证活动流程 */
    validateProcess(_, value, callback) {
      if (!value || value.length === 0) {
        callback(new Error('请至少添加一个活动流程步骤'))
        return
      }

      const hasEmptyProcess = value.some(process => !process || !process.trim())
      if (hasEmptyProcess) {
        callback(new Error('活动流程步骤不能为空'))
        return
      }

      callback()
    }
  }
}
</script>

<style scoped>
.drawer-footer {
  position: sticky;
  bottom: 0;
  background: #fff;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
  text-align: right;
}

.drawer-footer .el-button {
  margin-left: 10px;
}

.form-card {
  margin-bottom: 20px;
}

.form-card .card-header {
  font-weight: 600;
  color: #303133;
}

.dynamic-list {
  width: 100%;
}

.dynamic-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.dynamic-item .el-input {
  flex: 1;
  margin-right: 10px;
}

.process-list {
  width: 100%;
}

.process-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.drag-handle {
  cursor: move;
  color: #909399;
  margin-right: 8px;
  font-size: 14px;
}

.drag-handle:hover {
  color: #409eff;
}

.step-number {
  font-weight: 600;
  color: #409eff;
  margin-right: 8px;
  min-width: 20px;
}

.process-item .el-input {
  flex: 1;
  margin-right: 10px;
}

/* 拖拽时的样式 */
.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  transform: scale(1.02);
}
</style>