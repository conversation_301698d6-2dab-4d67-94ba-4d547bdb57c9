<template>
  <div 
    class="config-card"
  >
    <!-- 配置卡片头部 -->
    <ConfigCardHeader
      :config="config"
      @copy="handleCopy"
    />
    
    <!-- 配置值编辑区域 -->
    <div class="config-card-content">
      <ConfigEditor
        v-model="config.redisValue"
        :type="config.redisValueType"
        :saving="saving && currentSavingKey == config.redisKey"
        @save="handleSave"
        @reset="handleReset"
      />
      
      <!-- 配置说明 -->
      <ConfigItemDescription
        :content="config.redisValueContent"
        :config-key="config.redisKey"
      />
    </div>
    

  </div>
</template>

<script>
import ConfigCardHeader from './ConfigCardHeader.vue'
import ConfigEditor from './ConfigEditor.vue'
import ConfigItemDescription from './ConfigItemDescription.vue'

export default {
  name: 'ConfigCard',
  components: {
    ConfigCardHeader,
    ConfigEditor,
    ConfigItemDescription
  },
  props: {
    config: {
      type: Object,
      required: true
    },
    platformId: {
      type: [String, Number],
      required: true
    },
    saving: {
      type: Boolean,
      default: false
    },
    currentSavingKey: {
      type: String,
      default: ''
    }
  },
  

  
  methods: {

    // 复制到剪贴板
    async handleCopy(text) {
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success('已复制到剪贴板')
      } catch (err) {
        // 降级处理
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('已复制到剪贴板')
      }
    },

    // 处理保存
    handleSave() {
      this.$emit('save', this.config)
    },

    // 处理重置
    handleReset() {
      this.$emit('reset', this.config)
    }
  }
}
</script>

<style lang="scss" scoped>
.config-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: 6px;
  margin-bottom: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px var(--shadow-light);

  &:hover {
    border-color: var(--redis-secondary-color);
    box-shadow: 0 4px 8px var(--shadow-medium);
  }



  // 卡片内容
  .config-card-content {
    padding: 16px;
  }
}
</style>