import request from '@/utils/request'
//查询配置列表
export function getConfigList(data) {
    return request({
        url: '/saas/sms/config/getList',
        method: "get",
        params: data
    })
}
//新增短信配置
export function addConfigOne(data) {
    return request({
        url: '/saas/sms/config/addSmsConfig',
        method: "post",
        data
    })
}
//修改短信配置
export function editConfigOne(data) {
    return request({
        url: '/saas/sms/config/updateSmsConfig',
        method: "post",
        data
    })
}