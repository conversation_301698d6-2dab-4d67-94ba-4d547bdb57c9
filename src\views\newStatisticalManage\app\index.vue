<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="dateValue"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道" v-if="isShowMore">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入渠道名称"
          size="small"
          clearable
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item label="筛选渠道" v-if="isShowMore">
        <el-select
          ref="select"
          size="small"
          v-model="channelValue"
          multiple
          clearable
          filterable
          collapse-tags
          placeholder="请选择"
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.channelId"
            :label="`id:${item.channelId}--${item.channelName}`"
            :value="item.channelId"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>

    <el-table :data="filterList" border @sort-change="handleSortChange">
      <el-table-column label="渠道ID" align="center" prop="channelId" fixed />
      <el-table-column
        label="渠道名称"
        align="center"
        prop="channelName"
        fixed
      />
      <el-table-column
        label="APP点击下载"
        align="center"
        prop="appDownloadNum"
      />
      <el-table-column label="APP登录数" align="center" prop="appLoginNum" />
      <el-table-column label="APP登录率" align="center" prop="registerRatio">
        <template slot-scope="{ row }">
          <div>
            {{
              row.appDownloadNum
                ? ((row.appLoginNum / row.appDownloadNum) * 100).toFixed(2)
                : 0
            }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column label="APP申请量" align="center" prop="appApplyNum" />
      <el-table-column
        label="APP申请人数"
        align="center"
        prop="appApplyPeopleNum"
      />

      <el-table-column
        label="App线上收益"
        align="center"
        width="100"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="onlineProfit"
      >
      </el-table-column>

      <el-table-column
        label="App线下收益"
        align="center"
        width="100"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="offlineProfit"
      >
      </el-table-column>

      <el-table-column
        label="预估收益"
        align="center"
        width="100"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="profit"
      >
        <template slot-scope="{ row }">
          <div>
            {{ parseInt(row.profit) }}
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {
  getChannelMatchInfoStats, getAppChannelStatisticsList
} from '@/api/statisticalManage'
export default {
  name: "ChannelDetail",
  data() {
    return {
      // 筛选渠道 value
      channelValue: [],
      // 筛选渠道 options
      channelOptions: [],
      // 日期
      dateValue: [
        this.getCurrentDate() + " 00:00:00",
        this.getCurrentDate() + " 23:59:59",
      ],
      total: 0,
      // 表格数据
      dataList: [],
      // 表格参数
      queryParams: {
        channelName: "",
        pageNum: 1,
        pageSize: 10,
        startTime: `${this.getCurrentDate()} 00:00:00`,
        endTime: `${this.getCurrentDate()} 23:59:59`,
      },
    };
  },
  methods: {
    // 查询
    handleQuery() {
      if (this.dateValue === null) {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      } else {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.dateValue[0];
        this.queryParams.endTime = this.dateValue[1];
      }
      this.getList();
    },

    /**
     * @description: 计算数组中对象的某个属性的总和
     * @param {Array} arr 数组
     * @param {String} type 属性
     * @return {Number} 总和
     */
    getTotal(arr, type) {
      return arr.reduce((pre, data) => {
        return pre + data[type];
      }, 0);
    },

    // 获取当前日期，格式为YYYY-MM-DD
    getCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    // 获取列表
    getList() {
      this.queryParams.channelName = this.isShowMore
        ? this.queryParams.channelName
        : "智优选";
      getAppChannelStatisticsList(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.channelOptions = JSON.parse(JSON.stringify(res.rows));
        this.total = res.total;
      });
    },

    // 前端排序
    handleSortChange(column) {
      const prop = column.prop;
      const sortingType = column.order;
      const sumData = this.dataList[0];

      //正序
      if (sortingType === "ascending") {
        this.dataList = this.dataList
          .splice(1)
          .sort((a, b) => b[prop] - a[prop]);

        this.dataList.unshift(sumData);
      }

      // 倒序
      if (sortingType === "descending") {
        this.dataList = this.dataList
          .splice(1)
          .sort((a, b) => a[prop] - b[prop]);
        this.dataList.unshift(sumData);
      }
    },
  },
  computed: {
    isShowMore() {
      return !["zr123456", "zr123123", "shenshenshen", "shengxun"].includes(
        this.$store.getters.userInfo.userName
      );
    },

    // 筛选渠道变化，筛选表格数据
    filterList() {
      let data = this.channelValue.length
        ? this.dataList.filter((item) =>
            this.channelValue.includes(item.channelId)
          )
        : this.dataList;

      if (data.length && data[0]["channelId"] !== "合计") {
        data.unshift({
          channelId: "合计",
          channelName: "",
          uvNum: this.getTotal(data, "uvNum"),
          uaNum: this.getTotal(data, "uaNum"),
          registerNum: this.getTotal(data, "registerNum"),
          fromNum: this.getTotal(data, "fromNum"),
          h5PushNum: this.getTotal(data, "h5PushNum"),
          appDownloadNum: this.getTotal(data, "appDownloadNum"),
          appLoginNum: this.getTotal(data, "appLoginNum"),
          appApplyPeopleNum: this.getTotal(data, "appApplyPeopleNum"),
          appApplyNum: this.getTotal(data, "appApplyNum"),
          payPeopleNum: this.getTotal(data, "payPeopleNum"),
          profit: this.getTotal(data, "profit"),
          onlineApplyNum: this.getTotal(data, "onlineApplyNum"),
          onlineProfit: this.getTotal(data, "onlineProfit"),
          offlineProfit: this.getTotal(data, "offlineProfit"),
          onlineWeChatProfit: this.getTotal(data, "onlineWeChatProfit"),
          onlineDaoChaoProfit: this.getTotal(data, "onlineDaoChaoProfit"),
        });
      }

      return data;
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.warning {
  color: red;
}
::v-deep .el-dialog__body {
  border-bottom: none;
}
</style>
