<template>
  <div class="app-container">
    <div class="top-operation">
      <el-button type="primary" icon="el-icon-refresh" @click="getProductList">刷新</el-button>
    </div>

    <el-table :data="deviceList" border style="width: 100%" row-key="deviceId" default-expand-all>
      <el-table-column type="expand">
        <template slot-scope="scope">
          <div style="padding: 0 10px;">
            <el-table :data="scope.row.products" border style="width: 100%" size="mini">
              <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
              <el-table-column prop="productName" label="产品名称" align="center"></el-table-column>
              <el-table-column prop="productId" label="产品ID" align="center"></el-table-column>
              <el-table-column label="平台类型" align="center">
                <template slot-scope="props">
                  <el-tag :style="{ backgroundColor: getPlatformTagColor(props.row.platformType), color: '#fff', border: 'none' }">
                    {{ getPlatformName(props.row.platformType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="applyTime" label="申请时间" align="center" width="180"></el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="deviceId" label="设备ID" align="center"></el-table-column>
      <el-table-column prop="count" label="产品数量" align="center" width="120"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getQwDeviceApplyProducts } from '@/api/autoTest/qwDeviceApplyProducts'
import { getPlatformList } from '@/api/xmxrChannelManage/channelList'
import { getPlatformTagColor } from '@/utils/platformTag'

export default {
  name: 'QwDeviceApplyProducts',
  data() {
    return {
      // 设备列表（每个设备包含其产品列表）
      deviceList: [],
      // 平台列表
      platformList: []
    }
  },
  created() {
    this.getPlatforms()
    this.getProductList()
  },
  methods: {
    /** 获取企微设备已申请的平台和产品列表 */
    async getProductList() {
      try {
        const response = await getQwDeviceApplyProducts()
        if (response.code == 200) {
          if (response.data && Object.keys(response.data).length > 0) {
            // 将设备ID作为主行，产品列表作为子行
            const devices = []
            for (const deviceId in response.data) {
              if (Array.isArray(response.data[deviceId])) {
                devices.push({
                  deviceId,
                  count: response.data[deviceId].length,
                  products: response.data[deviceId]
                })
              }
            }
            this.deviceList = devices
          } else {
            this.deviceList = []
          }
        } else {
          this.$message.error(response.msg || '获取数据失败')
          this.deviceList = []
        }
      } catch (error) {
        console.error('获取企微设备申请产品数据失败:', error)
        this.$message.error('获取数据失败')
        this.deviceList = []
      }
    },
    /** 获取所有平台列表 */
    async getPlatforms() {
      try {
        const response = await getPlatformList()
        if (response.code == 200 && response.data) {
          this.platformList = response.data
        }
      } catch (error) {
        console.error('获取平台列表失败:', error)
      }
    },
    /** 获取平台名称 */
    getPlatformName(platformType) {
      // 从接口获取的平台列表中查找
      if (this.platformList && this.platformList.length > 0) {
        const platform = this.platformList.find(item => item.id == platformType)
        if (platform) {
          return platform.name || '未知平台'
        }
      }
      // 如果接口数据中没有找到
      return '未知平台'
    },
    /** 获取平台标签颜色 */
    getPlatformTagColor(platformType) {
      return getPlatformTagColor(platformType)
    }
  }
}
</script>

<style scoped>
.top-operation {
  margin-bottom: 15px;
}
</style>