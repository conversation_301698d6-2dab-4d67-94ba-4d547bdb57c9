<template>
  <div class="app-container">
    <div class="custom-screenshot-container">
      <!-- 组件选择区域 -->
      <div class="component-selector">
        <el-select v-model="selectedComponent" placeholder="请选择组件">
          <el-option v-for="item in componentOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>

      <!-- 组件显示区域 -->
      <div class="component-display">
        <div class="display-wrapper">
          <!-- 这里后续会根据选择动态显示组件 -->
          <div class="display-content">
            <component v-if="selectedComponent" :is="selectedComponent"></component>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SesameScore from './lib/sesame-score/index.vue'
import SesameScoreNew from './lib/sesame-score-new/index.vue'
import LoanQuota from './lib/loan-quota/index.vue'
import JdFinance from './lib/jd-finance/index.vue'

export default {
  name: 'CustomScreenshot',
  components: {
    SesameScore,
    SesameScoreNew,
    LoanQuota,
    JdFinance
  },
  data() {
    return {
      selectedComponent: 'SesameScoreNew',
      componentOptions: [
        { label: '芝麻分', value: 'SesameScore' },
        { label: '芝麻信用', value: 'SesameScoreNew' },
        { label: '贷款额度', value: 'LoanQuota' },
        { label: '京东金融', value: 'JdFinance' }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-screenshot-container {
  background-color: #fff;
  padding: 20px;

  .component-selector {
    margin-bottom: 20px;

    .el-select {
      width: 300px;
    }
  }

  .component-display {
    height: calc(100% - 60px);

    .display-wrapper {
      height: 100%;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .display-content {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>