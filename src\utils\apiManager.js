/**
 * API地址动态管理器
 * 用于在开发环境下动态切换API地址而无需重启服务
 */

// 预定义的API地址列表
export const API_ENDPOINTS = {
  production: {
    name: '正式环境',
    url: 'https://api.whxmxr.cn/admin'
  },
  test: {
    name: '测试环境',
    url: 'http://test.admin.whxmxr.cn'
  },
  zly: {
    name: 'zly开发环境',
    url: 'http://192.168.0.44:9081'
  },
  fwl: {
    name: 'fwl开发环境',
    url: 'http://192.168.1.6:9081'
  },
  yqy: {
    name: 'yqy开发环境',
    url: 'http://192.168.0.20:8081'
  },
  lh: {
    name: 'lh开发环境',
    url: 'http://192.168.1.5:9081'
  },
  hj: {
    name: 'hj开发环境',
    url: 'http://192.168.8.6:9081'
  }
}

// 存储键名
const STORAGE_KEY = 'dev_api_endpoint'
const CUSTOM_STORAGE_KEY = 'dev_custom_api_endpoints'

class ApiManager {
  constructor() {
    this.currentEndpoint = this.getStoredEndpoint()
    this.customEndpoints = this.getCustomEndpoints()
    this.listeners = []

    // 确保当前端点存在于可用端点中
    this.validateCurrentEndpoint()

    // 开发环境下输出当前状态
    if (process.env.NODE_ENV === 'development') {
      console.log(`%c[API管理器] 初始化完成，当前端点: ${this.currentEndpoint}`, 'color: #409eff; font-weight: bold;')
      const endpoint = this.getCurrentEndpoint()
      if (endpoint) {
        console.log(`%c[API管理器] 当前API地址: ${endpoint.url}`, 'color: #409eff;')
      }
    }
  }

  /**
   * 获取当前API地址
   */
  getCurrentUrl() {
    if (process.env.NODE_ENV !== 'development') {
      return process.env.VUE_APP_BASE_API
    }

    const endpoint = this.getCurrentEndpoint()
    return endpoint ? endpoint.url : process.env.VUE_APP_BASE_API
  }

  /**
   * 获取当前API2地址 (如果有配置的话)
   */
  getCurrentUrl2() {
    if (process.env.NODE_ENV !== 'development') {
      return process.env.VUE_APP_BASE_API_2
    }

    const endpoint = this.getCurrentEndpoint()
    // 如果当前端点有api2配置，使用它，否则使用环境变量
    return (endpoint && endpoint.url2) ? endpoint.url2 : process.env.VUE_APP_BASE_API_2
  }

  /**
   * 获取当前端点信息
   */
  getCurrentEndpoint() {
    const allEndpoints = { ...API_ENDPOINTS, ...this.customEndpoints }
    return allEndpoints[this.currentEndpoint] || null
  }

  /**
   * 获取当前端点的key
   */
  getCurrentEndpointKey() {
    return this.currentEndpoint
  }

  /**
   * 切换API地址
   */
  switchEndpoint(key) {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('API切换功能仅在开发环境下可用')
      return false
    }

    const allEndpoints = { ...API_ENDPOINTS, ...this.customEndpoints }
    if (!allEndpoints[key]) {
      console.error(`%c[API管理器] 未找到端点: ${key}`, 'color: #f56c6c;')
      return false
    }

    const oldEndpoint = this.currentEndpoint
    this.currentEndpoint = key
    localStorage.setItem(STORAGE_KEY, key)

    console.log(`%c[API管理器] 切换端点: ${oldEndpoint} -> ${key} (${allEndpoints[key].name})`, 'color: #67c23a; font-weight: bold;')

    // 通知所有监听器
    this.notifyListeners(allEndpoints[key])

    return true
  }

  /**
   * 添加自定义端点
   */
  addCustomEndpoint(key, config) {
    if (!config.name || !config.url) {
      throw new Error('自定义端点必须包含name和url')
    }

    this.customEndpoints[key] = {
      name: config.name,
      url: config.url,
      custom: true
    }

    this.saveCustomEndpoints()
    return true
  }

  /**
   * 删除自定义端点
   */
  removeCustomEndpoint(key) {
    if (this.customEndpoints[key]) {
      delete this.customEndpoints[key]
      this.saveCustomEndpoints()

      // 如果删除的是当前使用的端点，切换到默认端点
      if (this.currentEndpoint === key) {
        this.switchEndpoint('zly')
      }

      return true
    }
    return false
  }

  /**
   * 获取所有端点
   */
  getAllEndpoints() {
    return { ...API_ENDPOINTS, ...this.customEndpoints }
  }

  /**
   * 添加变化监听器
   */
  addListener(callback) {
    this.listeners.push(callback)
  }

  /**
   * 移除监听器
   */
  removeListener(callback) {
    const index = this.listeners.indexOf(callback)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知所有监听器
   */
  notifyListeners(endpoint) {
    this.listeners.forEach(callback => {
      try {
        callback(endpoint)
      } catch (error) {
        console.error('API管理器监听器执行错误:', error)
      }
    })
  }

  /**
   * 从本地存储获取当前端点
   */
  getStoredEndpoint() {
    return localStorage.getItem(STORAGE_KEY) || 'test'
  }

  /**
   * 获取自定义端点
   */
  getCustomEndpoints() {
    try {
      const stored = localStorage.getItem(CUSTOM_STORAGE_KEY)
      return stored ? JSON.parse(stored) : {}
    } catch (error) {
      console.error('解析自定义端点失败:', error)
      return {}
    }
  }

  /**
   * 保存自定义端点
   */
  saveCustomEndpoints() {
    localStorage.setItem(CUSTOM_STORAGE_KEY, JSON.stringify(this.customEndpoints))
  }

  /**
   * 验证当前端点是否有效
   */
  validateCurrentEndpoint() {
    const allEndpoints = { ...API_ENDPOINTS, ...this.customEndpoints }

    // 如果当前端点不存在，设置为默认端点
    if (!allEndpoints[this.currentEndpoint]) {
      console.warn(`%c[API管理器] 当前端点 "${this.currentEndpoint}" 不存在，切换到默认端点`, 'color: #e6a23c;')
      this.currentEndpoint = 'test'
      localStorage.setItem(STORAGE_KEY, this.currentEndpoint)
    }
  }

  /**
   * 重置到默认配置
   */
  reset() {
    localStorage.removeItem(STORAGE_KEY)
    localStorage.removeItem(CUSTOM_STORAGE_KEY)
    this.currentEndpoint = 'test'
    this.customEndpoints = {}
  }
}

// 创建全局实例
export const apiManager = new ApiManager()

// 开发环境下挂载到window对象，方便调试
if (process.env.NODE_ENV === 'development') {
  window.apiManager = apiManager
}

export default apiManager
