<template>
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="80%" append-to-body
        :close-on-click-modal="false">
        <div class="app-container">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
                <el-form-item label="使用时间">
                    <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
                        :default-time="['00:00:00', '23:59:59']"></el-date-picker>
                </el-form-item>
                <el-form-item label="状态" prop="state">
                    <el-select v-model="queryParams.state" placeholder="请选择状态" clearable>
                        <el-option v-for="dict in stateOptions" :key="dict.value" :label="dict.label"
                            :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="使用人" prop="usageUser">
                    <el-input v-model="queryParams.usageUser" placeholder="请输入使用人" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-table v-loading="loading" :data="recordList" max-height="500">
                <el-table-column label="凭证ID" align="center" prop="orderId" min-width="300">
                    <template slot-scope="scope">
                        <div class="id-with-copy">
                            <span>{{ scope.row.orderId }}</span>
                            <el-button
                                type="text"
                                icon="el-icon-document-copy"
                                @click="copyOrderId(scope.row.orderId)"
                                title="复制凭证ID"
                                class="copy-btn"
                            ></el-button>
                        </div>
                    </template>
                </el-table-column>
                
                <el-table-column label="流量包名称" align="center" prop="flowPacketName"  min-width="100"/>
                <el-table-column label="状态" align="center" prop="state">
                    <template slot-scope="scope">
                        <el-tag :type="getStateTagType(scope.row.state)">
                            {{ formatState(scope.row.state) }}
                        </el-tag>
                    </template>
                </el-table-column>
                
                <el-table-column label="产品名称" align="center" prop="productName" />
                <el-table-column label="平台类型" align="center" prop="platformType">
                    <template slot-scope="scope">
                        {{ formatPlatformType(scope.row.platformType) }}
                    </template>
                </el-table-column>
                <el-table-column label="使用时间" align="center" prop="usageTime" width="160">
                    <template slot-scope="scope">
                        <span>{{ scope.row.usageTime || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="结束时间" align="center" prop="endTime" width="160">
                    <template slot-scope="scope">
                        <span>{{ scope.row.endTime || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="使用人" align="center" prop="usageUser" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="160">
                    <template slot-scope="scope">
                        <span>{{ scope.row.createTime }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="创建人" align="center" prop="createUser" />
            </el-table>

            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
    </el-dialog>
</template>

<script>
import { getFlowPacketUsageRecord } from "@/api/platformProductManagement/flowPacketConfig";

export default {
    name: "UsageRecordDialog",
    props: {
        // 是否显示弹窗
        visible: {
            type: Boolean,
            default: false
        },
        // 流量包ID
        flowPacketId: {
            type: [Number, String],
            default: ""
        },
        // 流量包名称，用于显示标题，可选
        flowPacketName: {
            type: String,
            default: ""
        }
    },
    data() {
        return {
            // 遮罩层
            loading: false,
            // 总条数
            total: 0,
            // 记录列表
            recordList: [],
            // 日期范围
            dateRange: [],
            // 状态选项
            stateOptions: [
                {
                    value: "1",
                    label: "待使用"
                },
                {
                    value: "2",
                    label: "使用中"
                },
                {
                    value: "3",
                    label: "结束"
                }
            ],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                state: undefined,
                usageUser: undefined,
                flowPacketId: undefined
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit("update:visible", val);
            }
        },
        dialogTitle() {
            return this.flowPacketName
                ? `流量包「${this.flowPacketName}」发放记录`
                : "流量包发放记录";
        }
    },
    watch: {
        visible(val) {
            if (val) {
                this.queryParams.flowPacketId = this.flowPacketId;
                this.getList();
            }
        },
        flowPacketId(val) {
            if (this.visible && val) {
                this.queryParams.flowPacketId = val;
                this.getList();
            }
        },
            dateRange: {
        handler(val) {
            if (val && val.length === 2) {
                this.queryParams.startTime = val[0];
                this.queryParams.endTime = val[1];
            } else {
                this.queryParams.startTime = undefined;
                this.queryParams.endTime = undefined;
            }
        },
        immediate: true
    }
    },
    methods: {
        // 获取列表数据
        getList() {
            this.loading = true;
            // 打印请求参数，便于调试
            console.log('发送请求参数:', JSON.stringify(this.queryParams));
            getFlowPacketUsageRecord(this.queryParams)
                .then(response => {
                    this.recordList = response.rows;
                    this.total = response.total;
                    this.loading = false;
                })
                .catch(() => {
                    this.recordList = [];
                    this.total = 0;
                    this.loading = false;
                });
        },
        // 搜索按钮操作
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        // 重置按钮操作
        resetQuery() {
            this.dateRange = [];
            // 清空日期相关查询参数
            this.queryParams.startTime = undefined;
            this.queryParams.endTime = undefined;
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 格式化状态
        formatState(state) {
            const stateMap = {
                1: '待使用',
                2: '使用中',
                3: '结束'
            };
            return stateMap[state] || state;
        },
        // 获取状态标签类型
        getStateTagType(state) {
            const typeMap = {
                1: 'info',    // 待使用 - 灰色
                2: 'success', // 使用中 - 绿色
                3: 'danger'   // 结束 - 红色
            };
            return typeMap[state] || '';
        },
        // 格式化平台类型
        formatPlatformType(type) {
            const typeMap = {
                1: '惠逸花',
                2: '多多钱包',
                3: '星惠花'
            };
            return typeMap[type] || type;
        },
        // 复制凭证ID
        copyOrderId(orderId) {
            // 创建临时textarea元素
            const textarea = document.createElement('textarea');
            textarea.value = orderId;
            textarea.style.position = 'fixed'; // 避免滚动到底部
            document.body.appendChild(textarea);
            textarea.select();

            try {
                // 执行复制命令
                document.execCommand('copy');
                this.$message.success('凭证ID已复制到剪贴板');
            } catch (err) {
                console.error('复制失败:', err);
                this.$message.error('复制失败，请手动复制');
            }

            // 移除临时元素
            document.body.removeChild(textarea);
        }
    }
};
</script>

<style scoped>
.id-with-copy {
    display: flex;
    align-items: center;
    justify-content: center;
}

.id-with-copy .copy-btn {
    margin-left: 10px;
    padding: 2px;
    font-size: 14px;
}

.id-with-copy .copy-btn:hover {
    color: #409EFF;
}
</style>