import request from '@/utils/request'

// 查询还款方式列表
export function getRepaymentmethodList(data) {
    return request({
        url: '/saas/repaymentmethod/getlist',
        method: 'get',
        params: data
    })
}
// 修改状态
export function updatestatus(data) {
    return request({
        url: '/saas/repaymentmethod/updatestatus',
        method: 'post',
        data
    })
}
//新增
export function addRepaymentOne(data) {
    return request({
        url: '/saas/repaymentmethod/addone',
        method: 'post',
        data
    })
}