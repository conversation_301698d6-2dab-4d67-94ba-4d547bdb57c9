<template>
  <div class="platform-config">
    <ConfigCard
      v-for="(config, index) in platform.list" 
      :key="config.redisKey"
      :config="config"
      :platform-id="platformId"
      :saving="saving"
      :current-saving-key="currentSavingKey"
      @save="saveConfig"
      @reset="resetConfig"
    />
    
    <div v-if="!platform.list || platform.list.length == 0" class="empty-config">
      <i class="fas fa-box"></i>
      <p>该平台暂无配置项</p>
    </div>
  </div>
</template>

<script>
import { updateRedisValue } from '@/api/system/redisConfig'
import ConfigCard from './ConfigCard.vue'

export default {
  name: 'PlatformConfigPanel',
  components: {
    ConfigCard
  },
  props: {
    platform: {
      type: Object,
      required: true
    },
    platformId: {
      type: [String, Number],
      required: true
    }
  },
  
  data() {
    return {
      saving: false,
      currentSavingKey: '',
      originalConfigs: {}
    }
  },
  
  created() {
    this.backupOriginalConfigs()
  },
  
  watch: {
    platform: {
      handler(newPlatform, oldPlatform) {
        // 只有在平台切换或初始加载时才备份原始配置
        // 避免在用户修改配置值时重新备份
        if (!oldPlatform || newPlatform.platformType !== oldPlatform.platformType) {
          this.backupOriginalConfigs()
        }
      },
      deep: false // 改为浅监听，避免配置值变化时触发
    }
  },
  
  methods: {
    // 备份原始配置
    backupOriginalConfigs() {
      this.originalConfigs = {}
      if (this.platform.list) {
        this.platform.list.forEach(config => {
          this.originalConfigs[config.redisKey] = config.redisValue
        })
      }
    },
    
    // 保存配置
    async saveConfig(config) {
      // 验证JSON格式
      if (config.redisValueType == 1 && !this.isValidJsonString(config.redisValue)) {
        this.$message.error('JSON格式错误，请检查后重试')
        return
      }

      // 验证字符串配置
      if (config.redisValueType == 2 && !config.redisValue.trim()) {
        this.$message.error('配置值不能为空')
        return
      }

      try {
        this.saving = true
        this.currentSavingKey = config.redisKey

        await updateRedisValue({
          platformId: this.platformId,
          redisKey: config.redisKey,
          redisValue: config.redisValue
        })

        this.$message.success('配置保存成功')
        // 更新原始数据
        this.originalConfigs[config.redisKey] = config.redisValue
        this.$emit('update', { platformId: this.platformId, config })
      } catch (error) {
        this.$message.error('配置保存失败')
      } finally {
        this.saving = false
        this.currentSavingKey = ''
      }
    },
    
    // 重置配置
    resetConfig(config) {
      const originalValue = this.originalConfigs[config.redisKey]
      if (originalValue !== undefined) {
        config.redisValue = originalValue
        this.$message.success('配置已重置')
      }
    },
    
    // 验证JSON字符串
    isValidJsonString(str) {
      try {
        JSON.parse(str)
        return true
      } catch (e) {
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.platform-config {
  height: 100%;
  overflow-y: auto;
  padding: 4px;

  // 空状态
  .empty-config {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-tertiary);
    text-align: center;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    p {
      font-size: 14px;
      margin: 0;
    }
  }
}

// 滚动条样式
.platform-config::-webkit-scrollbar {
  width: 6px;
}

.platform-config::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

.platform-config::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
  
  &:hover {
    background: var(--text-tertiary);
  }
}
</style>