<template>
  <div class="app-container">
    <el-form
      :model="formData"
      :rules="ruels"
      ref="formData"
      label-position="left"
      label-width="190px"
    >
      <el-form-item label="网银服务器异步通知地址" prop="bankNotifyUrl">
        <el-input
          size="small"
          placeholder="请输入网银服务器异步通知地址"
          v-model="formData.bankNotifyUrl"
        ></el-input>
      </el-form-item>
      <el-form-item label="网银支付接口版本" prop="bankPayVersion">
        <el-input
          size="small"
          placeholder="请输入网银支付接口版本"
          v-model="formData.bankPayVersion"
        ></el-input>
      </el-form-item>
      <el-form-item label="网银服务器同步通知地址" prop="bankReturnUrl">
        <el-input
          size="small"
          placeholder="请输入网银服务器同步通知地址"
          v-model="formData.bankReturnUrl"
        ></el-input>
      </el-form-item>
      <el-form-item label="智付公钥-支付" prop="dinPayPublicKey">
        <el-input
          size="small"
          placeholder="请输入智付公钥-支付"
          v-model="formData.dinPayPublicKey"
        ></el-input>
      </el-form-item>
      <el-form-item label="平台商户号" prop="merchantCode">
        <el-input
          size="small"
          placeholder="请输入平台商户号"
          v-model="formData.merchantCode"
        ></el-input>
      </el-form-item>
      <el-form-item label="商家私钥-支付" prop="merchantPrivateKey">
        <el-input
          size="small"
          placeholder="请输入商家私钥-支付"
          v-model="formData.merchantPrivateKey"
        ></el-input>
      </el-form-item>
      <el-form-item label="商家公钥-支付" prop="merchantPublicKey">
        <el-input
          size="small"
          placeholder="商家公钥-支付"
          v-model="formData.merchantPublicKey"
        ></el-input>
      </el-form-item>
      <el-form-item label="服务器异步通知地址" prop="notifyUrl">
        <el-input
          size="small"
          placeholder="请输入服务器异步通知地址"
          v-model="formData.notifyUrl"
        ></el-input>
      </el-form-item>
      <el-form-item label="网银下单接口请求地址" prop="payBankUrl">
        <el-input
          size="small"
          placeholder="请输入网银下单接口请求地址"
          v-model="formData.payBankUrl"
        ></el-input>
      </el-form-item>
      <el-form-item label="下单接口请求地址" prop="payUrl">
        <el-input
          size="small"
          placeholder="请输入下单接口请求地址"
          v-model="formData.payUrl"
        ></el-input>
      </el-form-item>
      <el-form-item label="支付接口版本" prop="payVersion">
        <el-input
          size="small"
          placeholder="请输入支付接口版本"
          v-model="formData.payVersion"
        ></el-input>
      </el-form-item>
      <el-form-item label="查询订单请求地址" prop="queryOrderUrl">
        <el-input
          size="small"
          placeholder="请输入查询订单请求地址"
          v-model="formData.queryOrderUrl"
        ></el-input>
      </el-form-item>
      <el-form-item label="查询订单接口版本" prop="queryOrderVersion">
        <el-input
          size="small"
          placeholder="请输入查询订单接口版本"
          v-model="formData.queryOrderVersion"
        ></el-input>
      </el-form-item>
      <el-form-item label="查询退款请求地址" prop="queryRefundUrl">
        <el-input
          size="small"
          placeholder="请输入查询退款请求地址"
          v-model="formData.queryRefundUrl"
        ></el-input>
      </el-form-item>
      <el-form-item label="查询退款接口版本" prop="queryRefundVersion">
        <el-input
          size="small"
          placeholder="请输入查询退款接口版本"
          v-model="formData.queryRefundVersion"
        ></el-input>
      </el-form-item>
      <el-form-item label="退款请求地址" prop="refundUrl">
        <el-input
          size="small"
          placeholder="请输入退款请求地址"
          v-model="formData.refundUrl"
        ></el-input>
      </el-form-item>
      <el-form-item label="退款接口版本" prop="refundVersion">
        <el-input
          size="small"
          placeholder="请输入退款接口版本"
          v-model="formData.refundVersion"
        ></el-input>
      </el-form-item>
      <el-form-item label="交易商户商家号(子商户号)" prop="subMerchantCode">
        <el-input
          size="small"
          placeholder="请输入交易商户商家号(子商户号)"
          v-model="formData.subMerchantCode"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitFormData">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  getdinPayConfig,
  editdinPayConfig,
} from "@/api/threeParty/radersetting";
export default {
  name: "RadarSetting",
  data() {
    return {
      ruels: {
        bankNotifyUrl: [{ required: true, message: "请输入网银服务器异步通知地址", trigger: "blur" }],
        bankPayVersion: [{ required: true, message: "请输入网银支付接口版本", trigger: "blur" }],
        bankReturnUrl: [
          { required: true, message: "请输入网银服务器同步通知地址", trigger: "blur" },
        ],
        dinPayPublicKey: [
          { required: true, message: "请输入智付公钥-支付", trigger: "blur" },
        ],
        projectId: [
          { required: true, message: "请输入渠道ID", trigger: "blur" },
        ],
        merchantCode: [
          { required: true, message: "请输入平台商户号", trigger: "blur" },
        ],
        merchantPrivateKey: [
          { required: true, message: "请输入商家私钥-支付", trigger: "blur" },
        ],
        merchantPublicKey: [
          { required: true, message: "请输入商家公钥-支付", trigger: "blur" },
        ],
        notifyUrl: [{ required: true, message: "服务器异步通知地址", trigger: "blur" }],
        payBankUrl: [{ required: true, message: "请输入网银下单接口请求地址", trigger: "blur" }],
        payUrl: [{ required: true, message: "请输入下单接口请求地址", trigger: "blur" }],
        payVersion: [{ required: true, message: "请输入支付接口版本", trigger: "blur" }],
        queryOrderUrl: [{ required: true, message: "请输入查询订单请求地址", trigger: "blur" }],
        queryOrderVersion: [{ required: true, message: "请输入查询订单接口版本", trigger: "blur" }],
        queryRefundUrl: [{ required: true, message: "请输入查询退款请求地址", trigger: "blur" }],
        queryRefundVersion: [{ required: true, message: "请输入查询退款接口版本", trigger: "blur" }],
        refundUrl: [{ required: true, message: "请输入退款请求地址", trigger: "blur" }],
        refundVersion: [{ required: true, message: "请输入退款接口版本", trigger: "blur" }],
        subMerchantCode: [{ required: true, message: "请输入交易商户商家号(子商户号)", trigger: "blur" }],
      },
      formData: {
        bankNotifyUrl: "",
        bankPayVersion: "",
        bankReturnUrl: "",
      
        dinPayPublicKey: "",
        merchantCode: "",
        merchantPrivateKey: "",
        merchantPublicKey: "",
        notifyUrl: "",
        payBankUrl: "",
        payUrl: "",
        payVersion: "",
        queryOrderUrl: "",
        queryOrderVersion: "",
        queryRefundUrl: "",
        queryRefundVersion: "",
        refundUrl: "",
        refundVersion: "",
        subMerchantCode: "",
       
      },
    };
  },
  methods: {
    initPage() {
      getdinPayConfig().then((res) => {
        for (let key in res.data) {
          // 接口请求出来的
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = res.data[key];
          }
        }
      });
    },
    submitFormData() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          editdinPayConfig(this.formData).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.initPage();
            }
          });
        }
      });
    },
  },
  mounted() {
    this.initPage();
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input--small .el-input__inner {
  width: 500px;
}
</style>