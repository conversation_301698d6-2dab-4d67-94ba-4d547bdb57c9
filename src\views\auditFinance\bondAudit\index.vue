<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="商户名称" prop="partyFirstName">
        <el-input v-model="queryParams.partyFirstName" size="small" clearable placeholder="请输入商户名称"></el-input>
      </el-form-item>
      <el-form-item label="商务名称" prop="username">
        <el-input v-model="queryParams.username" size="small" clearable placeholder="请输入商务名称"></el-input>
      </el-form-item>

      <el-form-item label="审核状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择审核状态" @change="handleQuery" clearable size="small">
          <el-option :value="-1" label="全部"></el-option>
          <el-option :value="0" label="待确认"></el-option>
          <el-option :value="1" label="已确认"></el-option>

        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>
    <el-table border :data="tableList">
      <el-table-column label="提交时间" prop="date" align="center" />
      <el-table-column label="商户名称" prop="partyFirstName" align="center" />
      <el-table-column label="提交人" prop="username" align="center" />
      <el-table-column label="类型" prop="type" align="center" />
      <el-table-column label="缴纳金额" prop="price" align="center" />
      <el-table-column align="center" label="缴纳凭证">
        <template  slot-scope="{row}">
          <div v-if="row.file">
            <el-image style="width: 100px; height: 100px" :src="row.file" :preview-src-list="[row.file]">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center">
        <template  slot-scope="{row}">
          <div :style="colorType[row.status]">
            {{ statusJson[row.status] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="phone" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button type="text" v-hasPermi="['loan:partyadetail:deposit']" @click="handleAudit(row)">{{ row.isAction
                ? '审核' : '查看'
            }}</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <!-- 保证金审核 -->
    <el-dialog title="保证金额缴纳详情" :visible.sync="showBond" width="800px" append-to-body center
      :close-on-click-modal="false">
      <el-form label-width="100px">
        <el-form-item label="商户名称">
          <div class="flex">
            <el-input v-model="formData.partyFirstName" disabled />
            <el-button type="primary" size="mini" class="m-l10" @click="handleToPartyA">查看商户详情</el-button>
          </div>
        </el-form-item>
        <el-form-item label="金额">
          <el-input v-model="formData.price" disabled />
        </el-form-item>
        <el-form-item label="审批意见" v-if="isShowHandle">
          <el-input v-model="infoText" placeholder="请输入审批意见,驳回必填" maxlength="20" show-word-limit type="textarea" />
        </el-form-item>
      </el-form>
      <div>
        <div class="check-title">审核流程</div>
        <div class="check-info1" v-if="processList.length">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in processList" :key="index" :color="processColor(item)">
              <div :class="[item.status == 2 ? 'c_red' : '']">
                <div> {{ item.userRemark }}</div>
                <div>时间:{{ item.checkTime||'-' }}</div>
                <div v-if="item.status != 3">状态：{{ item.checkResult||'-' }}</div>
                <div v-if="item.status != 3"> 备注：{{ item.checkRemark || "-" }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
        <div class="check-center" v-else>暂无数据</div>
      </div>
      <div slot="footer" class="dialog-footer" v-if="isShowHandle">
        <el-button type="primary" @click="submitPass" v-hasPermi="['loan:partyadetail:execute_deposit']">通 过</el-button>
        <el-button @click="submitReject" v-hasPermi="['loan:partyadetail:execute_deposit']">驳 回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDepositList, getDepositProcess, depositDoPass, depositDoReject } from "@/api/auditFinance/bond"
export default {
  data() {
    return {
      total: 0,
      showBond: false,
      isShowHandle: true,
      infoText: "",
      statusJson: {
        0: '审核中', 1: '已通过', 2: '已拒绝'
      },
      colorType: {
        0: "color:#004DAB",
        1: "color:#008000",
        2: "color:red",
      },
      processList: [],
      formData: {},
      tableList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: 1
      }
    }
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    getList() {
      getDepositList(this.queryParams).then(res => {
        this.tableList = res.rows
        this.total = res.total
      })
    },
    handleAudit(row) {
      this.formData = row
      if (row.status == 0 && row.isAction) {
        this.isShowHandle = true
      } else {
        this.isShowHandle = false
      }
      this.infoText = ""
      getDepositProcess({ depositId: row.depositId }).then(res => {
        this.processList = res.data
      })
      this.showBond = true
    },
    submitPass() {
      depositDoPass({
        "depositId": this.formData.depositId,
        "remark": "",
      }).then(res => {
        this.getList()
        this.showBond = false

        this.$message.success("操作成功")
      })
    },
    submitReject() {
      if (!this.infoText) return this.$message.error("审批意见不能为空")
      depositDoReject({
        "depositId": this.formData.depositId,
        "remark": this.infoText,
      }).then(res => {
        this.getList()
        this.showBond = false

        this.$message.success("操作成功")
      })
    },
    handleToPartyA() {
      let url = this.$router.resolve('/partyA/list?name=' + this.formData.partyFirstName)
      window.open(url.href, '_blank')
    },

  },
  computed: {
    processColor: () => {
      return (item) => {
        if (item.status == -1) {
          return ''
        } else if (item.status == 2) {
          return "#ff0000"
        } else {
          return "#00a607"
        }
      };
    }
  },
  mounted() {
    this.getList()
  }

}
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
}

.m-l10 {
  margin-left: 10px;
}

.check-info1 {
  margin-top: 10px;
  max-height: 500px;
  overflow: auto;
}
</style>
