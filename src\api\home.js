import request from '@/utils/request'

//获取本月数据统计
export function getStatsMonth(){
  return request({
    url:"/home/<USER>/getStatsMonth",
    method:"get"
  })
}
//获取今日数据统计
export function getStatsDay(){
  return request({
    url:"/home/<USER>/getStatsDay",
    method:"get"
  })
}
//获取今日今日待办
export function getStatsDayPending(){
  return request({
    url:"/home/<USER>/getStatsDayPending",
    method:"get"
  })
}
//获取今日待办
export function getStatsRecentDays(){
  return request({
    url:"/home/<USER>/getStatsRecentDays",
    method:"get"
  })
}
//获取前五产品收益
export function getDayTopProfit(data){
  return request({
    url:"/home/<USER>/getDayTopProfit",
    method:"get",
    params:data
  })
}
//获取UV统计
export function getDaysUvList(data){
  return request({
    url:"/home/<USER>/getDaysUvList",
    method:"get",
    params:data
  })
}
