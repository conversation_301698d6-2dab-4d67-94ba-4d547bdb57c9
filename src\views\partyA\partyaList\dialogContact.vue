<template>
  <div>
    <el-drawer
      title="上传资料"
      :visible.sync="drawer_"
      size="800px"
      :wrapper-closable="false"
      :direction="direction"
      @close="hanldeClose"
    >
      <div class="drawer-container">
        <div class="drawer-tabs">
          <div class="drawer-tabs-item flex align-items-c">
            <i
              class="iconfont icon-a-paichu3 tab-active"
              v-if="tabIndex > 1"
            ></i>
            <span class="drawer-tabs-cicle" v-else>1</span>

            <span :class="[tabIndex == 1 ? 'tab-active' : 'tab-finish']"
              >账户认证</span
            >
          </div>
          <div class="drawer-tabs-line"></div>
          <div class="drawer-tabs-item flex align-items-c">
            <i
              :class="['iconfont icon-a-paichu3 tab-active']"
              v-if="tabIndex > 2"
            ></i>
            <span
              :class="['drawer-tabs-cicle', tabIndex == 2 ? '' : 'tab-todo']"
              v-else
              >2</span
            >
            <span
              :class="[
                tabIndex == 2 ? 'tab-active' : '',
                tabIndex > 2 ? 'tab-finish' : '',
              ]"
              >办公地址</span
            >
          </div>
          <div :class="[tabIndex == 3 ? 'drawer-tabs-line' : 'tab-line']"></div>
          <div class="drawer-tabs-item flex align-items-c">
            <span
              :class="['drawer-tabs-cicle', tabIndex == 3 ? '' : 'tab-todo']"
              >3</span
            >
            <span :class="[tabIndex == 3 ? 'tab-active' : '']">资质信息</span>
          </div>
        </div>
        <el-form
          :model="formData"
          ref="formData"
          label-position="top"
          :rules="rules"
          :key="tabIndex"
        >
          <template v-if="tabIndex == 1">
            <el-row :gutter="24">
              <el-col :span="12"
                ><el-form-item label="合同编号" prop="">
                  <el-input
                    maxlength="50"
                    :disabled="status == 1"
                    size="mini"
                    v-model="formData.contractNo"
                    placeholder="请输入合同编号"
                  /> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="认证类型" prop="authType">
                  <el-select
                    :disabled="status == 1"
                    v-model="formData.authType"
                    clearable
                    size="mini"
                    style="width: 100%"
                    placeholder="请选择认证类型"
                  >
                    <el-option label="公司" :value="1"></el-option>
                    <el-option label="个人" :value="2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="公司全称：">
                  <el-input
                    maxlength="40"
                    :disabled="status == 1"
                    size="mini"
                    v-model="formData.companyName"
                    placeholder="请输入公司全称"
                  /> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="公司统一社会信用码:">
                  <el-input
                    maxlength="30"
                    :disabled="status == 1"
                    size="mini"
                    v-model="formData.companyCreditCode"
                    placeholder="请输入公司统一社会信用码"
                  /> </el-form-item
              ></el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="授权代表姓名:" prop="authName">
                  <el-input
                    :disabled="status == 1"
                    size="mini"
                    maxlength="6"
                    v-model="formData.authName"
                    placeholder="请输入授权代表姓名"
                  /> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="授权代表身份证号：" prop="authIdCard">
                  <el-input
                    :disabled="status == 1"
                    size="mini"
                    placeholder="请输入授权代表身份证号"
                    v-model.trim="formData.authIdCard"
                  /> </el-form-item
              ></el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="联系人：" prop="contactsName">
                  <el-input
                    :disabled="status == 1"
                    size="mini"
                    maxlength="6"
                    placeholder="请输入联系人"
                    v-model="formData.contactsName"
                  /> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="联系人电话:" prop="contactsPhone">
                  <el-input
                    :disabled="status == 1"
                    size="mini"
                    maxlength="11"
                    v-model.trim="formData.contactsPhone"
                    placeholder="请输入联系人电话 ："
                  /> </el-form-item
              ></el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="联系人邮箱:" prop="contactsEmail">
                  <el-input
                    :disabled="status == 1"
                    size="mini"
                    maxlength="50"
                    v-model.trim="formData.contactsEmail"
                    placeholder="请输入联系人邮箱"
                  /> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="联系人地址:" prop="contactsAddress">
                  <el-input
                    maxlength="50"
                    :disabled="status == 1"
                    size="mini"
                    v-model="formData.contactsAddress"
                    placeholder="请输入联系人地址"
                  /> </el-form-item
              ></el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    :disabled="status == 1"
                    size="mini"
                    type="textarea"
                    v-model="formData.remark"
                    placeholder="请输入备注"
                  /> </el-form-item
              ></el-col>
            </el-row>

            <div class="drawer-title" v-if="processList && processList.length">
              审批流程
            </div>
            <div
              class="drawer-process"
              v-if="processList && processList.length"
            >
              <div v-for="(item, index) in processList" :key="index">
                <div class="flex align-items-c">
                  <span
                    :class="[
                      'iconfont f-suceess drawer-process-icon',
                      iconList[item.status],
                      colorList[item.status],
                    ]"
                  ></span>
                  <span class="drawer-process-user">{{ item.name }} </span>
                  <span
                    :class="['drawer-process-status', tagList[item.status]]"
                    v-if="item.status != 0 && item.status != -2"
                  >
                    <span
                      v-if="
                        processList[1] &&
                        processList[1].status == 2 &&
                        processList[0] &&
                        processList[0].status == -1 &&
                        index == 0
                      "
                    >
                      待修改
                    </span>
                    <span v-else>{{ statusJson[item.status] || "" }}</span>
                  </span>
                </div>
                <div
                  :class="[
                    'drawer-process-line',
                    processList.length - 1 == index ? 'boder-none' : '',
                  ]"
                >
                  <div class="drawer-process-time">
                    {{ item.checkTime || "-" }}
                  </div>
                  <div
                    :class="[
                      'drawer-process-remark',
                      item.status == 2 ? 'fail' : '',
                    ]"
                    v-if="item.checkRemark && item.status != -2"
                  >
                    {{ item.checkRemark || "-" }}
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-if="tabIndex == 2">
            <el-row :gutter="24">
              <el-col :span="12"
                ><el-form-item label="所在省份" prop="provinceCode">
                  <el-select
                    :disabled="status == 1"
                    v-model="formData.provinceCode"
                    clearable
                    size="mini"
                    style="width: 100%"
                    @change="getProvinceCode"
                  >
                    <el-option
                      :label="i.name"
                      :value="i.code"
                      v-for="i in cityList"
                      :key="i.code"
                    ></el-option>
                  </el-select> </el-form-item
              ></el-col>
              <el-col :span="12"
                ><el-form-item label="所在市区" prop="cityCode">
                  <el-select
                    :disabled="status == 1"
                    v-model="formData.cityCode"
                    clearable
                    size="mini"
                    style="width: 100%"
                    @change="getAreaCodeCode"
                  >
                    <el-option
                      :label="i.name"
                      :value="i.code"
                      v-for="i in areaList"
                      :key="i.code"
                    ></el-option>
                  </el-select> </el-form-item
              ></el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="所在地区" prop="areaCode">
                  <el-select
                    :disabled="status == 1"
                    v-model="formData.areaCode"
                    clearable
                    size="mini"
                    style="width: 100%"
                  >
                    <el-option
                      :label="i.name"
                      :value="i.code"
                      v-for="i in countrysList"
                      :key="i.code"
                    ></el-option>
                  </el-select> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="详细地址" prop="address">
                  <el-input
                    maxlength="50"
                    :disabled="status == 1"
                    size="mini"
                    placeholder="请输入详细地址"
                    v-model="formData.address"
                  /> </el-form-item
              ></el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="门牌号" prop="doorplate">
                  <el-input
                    maxlength="50"
                    :disabled="status == 1"
                    size="mini"
                    v-model="formData.doorplate"
                    placeholder="请输入门牌号"
                  /> </el-form-item
              ></el-col>
            </el-row>
          </template>
          <template v-if="tabIndex == 3">
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="营业执照" prop="businessLicenseFilename">
                  <el-upload
                    class="avatar-uploader"
                    ref="businessLicenseFilename"
                    :show-file-list="false"
                    :on-change="
                      (e) =>
                        changeUpfile(e, {
                          type: 1,
                          name: 'businessLicenseFilename',
                          path: 'businessLicenseFilepath',
                        })
                    "
                    action="#"
                    :auto-upload="false"
                  >
                    <img
                      v-if="imageUrl"
                      style="vertical-align: middle; max-height: 100px"
                      :src="imageUrl"
                    />
                    <div class="avatar-uploader-tips" v-else>
                      <i class="el-icon-plus"></i>
                      <span>点击上传凭证</span>
                    </div>
                    <!-- <img v-if="imageUrl" :src="imageUrl" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i> -->
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item
                  label="营业执照注册时间"
                  prop="businessLicenseRegisterDate"
                >
                  <el-date-picker
                    v-model="formData.businessLicenseRegisterDate"
                    @change="hanldeDate"
                    value-format="yyyy-MM-dd"
                    type="date"
                    size="small"
                    placeholder="选择日期"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="营业执照有效期"
                  prop="businessLicenseValidityDate"
                  :rules="{
                    required: !formData.isBusinessLicenseForever,
                    message: '请选择营业执照有效期',
                    trigger: 'change',
                  }"
                >
                  <el-date-picker
                    v-model="formData.businessLicenseValidityDate"
                    type="date"
                    :disabled="formData.isBusinessLicenseForever"
                    size="small"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                    style="margin-right: 5px"
                  >
                  </el-date-picker>
                  <el-checkbox
                    v-model="formData.isBusinessLicenseForever"
                    @change="hanldeValidityDate"
                    >长期</el-checkbox
                  >
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item
              prop="idGrantType"
              label="授权类型"
              label-width="100px"
            >
              <div class="f_c">
                <el-radio v-model="formData.idGrantType" :label="1"
                  >法人</el-radio
                >
                <el-radio v-model="formData.idGrantType" :label="2"
                  >授权人</el-radio
                >
              </div>
            </el-form-item>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="身份证正面" prop="frontIdCardFilename">
                  <el-upload
                    class="avatar-uploader"
                    :auto-upload="false"
                    action="#"
                    :show-file-list="false"
                    ref="frontIdCardFilename"
                    :on-change="
                      (e) =>
                        changeUpfile(e, {
                          type: 1,
                          name: 'frontIdCardFilename',
                          path: 'frontIdCardFilepath',
                        })
                    "
                  >
                    <!-- <img v-if="imageUrl1" :src="imageUrl1" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i> -->
                    <img
                      v-if="imageUrl1"
                      style="vertical-align: middle; max-height: 100px"
                      :src="imageUrl1"
                    />
                    <div class="avatar-uploader-tips" v-else>
                      <i class="el-icon-plus"></i>
                      <span>点击上传凭证</span>
                    </div>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="反面" prop="reverseIdCardFilename">
                  <el-upload
                    class="avatar-uploader"
                    :auto-upload="false"
                    action="#"
                    ref="reverseIdCardFilename"
                    :on-change="
                      (e) =>
                        changeUpfile(e, {
                          type: 1,
                          name: 'reverseIdCardFilename',
                          path: 'reverseIdCardFilepath',
                        })
                    "
                    :show-file-list="false"
                  >
                    <!-- <img v-if="imageUrl2" :src="imageUrl2" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i> -->
                    <img
                      v-if="imageUrl2"
                      style="vertical-align: middle; max-height: 100px"
                      :src="imageUrl2"
                    />
                    <div class="avatar-uploader-tips" v-else>
                      <i class="el-icon-plus"></i>
                      <span>点击上传凭证</span>
                    </div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12" v-if="!dateStatus">
                <el-form-item
                  label="租赁协议（营业执照注册时间未满半年）"
                  prop="leaseAgreementFilename"
                >
                  <el-upload
                    class="upload-demo"
                    action="#"
                    :auto-upload="false"
                    ref="leaseAgreementFilename"
                    :show-file-list="false"
                    :on-change="
                      (e) =>
                        changeUpfile(e, {
                          type: 3,
                          name: 'leaseAgreementFilename',
                          path: 'leaseAgreementFilepath',
                        })
                    "
                  >
                    <el-button size="mini" icon="el-icon-upload2"
                      >点击上传</el-button
                    >
                    <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
                  </el-upload>
                </el-form-item>
                <el-tag
                  type="info"
                  size="small"
                  v-if="formData.leaseAgreementFilename"
                  @close="
                    handleRemoveFile({
                      name: 'leaseAgreementFilename',
                      path: 'leaseAgreementFilepath',
                    })
                  "
                  closable
                >
                  <span class="tag">{{ formData.leaseAgreementFilename }}</span>
                </el-tag>
              </el-col>
              <el-col
                :span="12"
                v-if="!dateStatus && formData.leaseType == '2'"
              >
                <el-form-item
                  label="租赁授权协议"
                  prop="leaseAgreementGrantFilename"
                >
                  <el-upload
                    class="upload-demo"
                    action="#"
                    :auto-upload="false"
                    ref="leaseAgreementGrantFilename"
                    :show-file-list="false"
                    :on-change="
                      (e) =>
                        changeUpfile(e, {
                          type: 3,
                          name: 'leaseAgreementGrantFilename',
                          path: 'leaseAgreementGrantFilepath',
                        })
                    "
                  >
                    <el-button size="mini" icon="el-icon-upload2"
                      >点击上传</el-button
                    >
                    <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
                  </el-upload>
                </el-form-item>
                <el-tag
                  type="info"
                  size="small"
                  v-if="formData.leaseAgreementGrantFilename"
                  @close="
                    handleRemoveFile({
                      name: 'leaseAgreementGrantFilename',
                      path: 'leaseAgreementGrantFilepath',
                    })
                  "
                  closable
                >
                  <span class="tag">{{
                    formData.leaseAgreementGrantFilename
                  }}</span>
                </el-tag>
              </el-col>
            </el-row>
            <el-form-item prop="leaseType" label="承租类型" v-if="!dateStatus">
              <div class="f_c">
                <el-radio v-model="formData.leaseType" :label="1"
                  >承租人为法人或公司</el-radio
                >
                <el-radio v-model="formData.leaseType" :label="2"
                  >承租人为个人</el-radio
                >
              </div>
            </el-form-item>
            <el-row :gutter="24">
              <!-- <el-col :span="12">
                <el-form-item label="合同文件" prop="contractFilename">
                  <el-upload class="upload-demo" action="#" :auto-upload="false" :show-file-list="false"
                    :on-change="(e) => changeUpfile(e, { type: 3, name: 'contractFilename', path: 'contractFilepath' })">
                    <el-button size="mini" icon="el-icon-upload2">点击上传</el-button>

                  </el-upload>
                </el-form-item>
                <el-tag type="info" size="small" v-if="formData.contractFilename"
                  @close="handleRemoveFile({ name: 'contractFilename', path: 'contractFilepath' })" closable>
                  <span class="tag">{{ formData.contractFilename }}</span>
                </el-tag>
              </el-col> -->

              <el-col :span="12">
                <el-form-item label="办公场所视频" prop="officeVideoFilename">
                  <el-upload
                    class="upload-demo"
                    action="#"
                    :auto-upload="false"
                    ref="officeVideoFilename"
                    :show-file-list="false"
                    :on-change="
                      (e) =>
                        changeUpfile(e, {
                          type: 2,
                          name: 'officeVideoFilename',
                          path: 'officeVideoFilepath',
                        })
                    "
                  >
                    <el-button size="mini" icon="el-icon-upload2"
                      >点击上传</el-button
                    >
                    <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
                  </el-upload>
                </el-form-item>
                <el-tag
                  type="info"
                  size="small"
                  v-if="formData.officeVideoFilename"
                  @close="
                    handleRemoveFile({
                      name: 'officeVideoFilename',
                      path: 'officeVideoFilepath',
                    })
                  "
                  closable
                >
                  <span class="tag">{{ formData.officeVideoFilename }}</span>
                </el-tag>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <!-- <el-col :span="12" v-if="isLoanType">
                <el-form-item label="员工在职证明" prop="certificateEmploymentFilename">
                  <el-upload class="upload-demo" action="#" :auto-upload="false" ref="certificateEmploymentFilename"
                    :show-file-list="false"
                    :on-change="(e) => changeUpfile(e, { type: 3, name: 'certificateEmploymentFilename', path: 'certificateEmploymentFilepath' })">
                    <el-button size="mini" icon="el-icon-upload2">点击上传</el-button>

                  </el-upload>
                </el-form-item>
                <el-tag type="info" size="small" v-if="formData.certificateEmploymentFilename"
                  @close="handleRemoveFile({ name: 'certificateEmploymentFilename', path: 'certificateEmploymentFilepath' })"
                  closable>
                  <span class="tag">{{ formData.certificateEmploymentFilename }}</span>
                </el-tag>
              </el-col> -->
            </el-row>
            <!-- <el-row :span="24">
              <el-col :span="24">
                <el-form-item label="其他文件">
                  <el-upload class="upload-demo" action="#" :auto-upload="false" :limit="10" :fileList="fileList"
                    :on-change="(e, fileList) => handleOtherFile(e, fileList)"
                    :on-remove="(e, fileList) => handleOtherRemove(e, fileList)">
                    <el-button size="mini" icon="el-icon-upload2">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">单个文件不能超过2MB；软著、放款资质、ICP备案或其他资料请上传至补充资料</div>
                  </el-upload>
                </el-form-item>
              </el-col>

            </el-row> -->
          </template>
        </el-form>

      </div>
      <div class="drawer__footer flex align-items-c">
        <!-- <el-button type="primary" size="small" @click="handleSubmit"> 确定</el-button> -->
        <el-button size="small" @click="handlePreStep" v-if="tabIndex > 1"
          >上一步</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="handleNextStep"
          v-if="tabIndex < 3"
          >下一步</el-button
        >
        <el-button
          size="small"
          v-if="status == 0 && tabIndex == 3"
          type="primary"
          @click="submitform"
          >提交</el-button
        >
        <el-button
          type="primary"
          v-if="
            (status == 2 && tabIndex == 3) || (status == 3 && tabIndex == 3)||(status == 4 && tabIndex == 3)||(status == 5 && tabIndex == 3)
          "
          class="submit"
          v-hasPermi="['partyaAdmin:contract:update']"
          @click="submitupDataform"
          >修改</el-button
        >
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  getFindAllArea,
  contractUploadFile,
  AddPartyaAdminContract,
  updataContractInfo,
} from "@/api/partyManage";
import { getContractInfo } from "@/api/partyA";
export default {
  data() {
    var validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("手机号不能为空"));
      } else if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
        callback(new Error("手机号格式错误"));
      } else {
        callback();
      }
    };
    var validateEamil = (rule, value, callback) => {
      if (!value) {
        callback(new Error("邮箱不能为空"));
      } else if (
        !/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(
          value
        )
      ) {
        callback(new Error("邮箱格式错误"));
      } else {
        callback();
      }
    };
    var validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("名字不能为空"));
      } else if (!/^[\u4e00-\u9fa5]{2,6}$/.test(value)) {
        callback(new Error("名字格式错误"));
      } else {
        callback();
      }
    };


    var validateID = (rule, value, callback) => {
      if (!value) {
        callback(new Error("授权代表身份证号不能为空"));
      } else if (
        !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/.test(
          value
        ) ||
        !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/.test(
          value
        )
      ) {
        callback(new Error("格式错误"));
      } else {
        callback();
      }
    };
    return {
      tabIndex: 1,
      imageUrl: "",
      imageUrl1: "",
      imageUrl2: "",
      cityList: [],
      provinceList: [],
      areaList: [],
      countrysList: [],
      fileList: [],
      processList: [],
      contractCheckRemark: "",
      show: false,
      statusJson: {
        0: "发起",
        1: "已通过",
        2: "已驳回",
        3: "结束",
        "-1": '待审核'
      },
      iconList: {
        0: 'icon-a-chaji6',
        1: 'icon-a-paichu3',
        2: 'icon-a-paichu2',
        3: 'icon-a-paichu3',
        "-1": 'icon-a-paichu1',
        "-2": 'icon-a-paichu3',
      },

      colorList: {
        0: 'f-suceess',
        1: 'f-suceess',
        2: 'f-danger',
        3: 'f-suceess',
        "-1": 'f-info',
      },
      tagList: {
        0: 'success',
        1: 'success',
        2: 'danger',
        3: 'success',
        "-1": 'info',
      },
      statusType: {
        // @ApiModelProperty(value = "合同审核状态(0未上传/1待审核/2审核成功/3审核失败)")
        0: "未上传",
        1: "待审核",
        2: "审核成功",
        3: "审核失败",
      },
      dateStatus: true,
      status: 0,
      formData: {
        authType: null,
        partyFirstId: null,
        companyName: null,
        companyCreditCode: null,
        authName: null,
        authIdCard: null,
        contactsName: null,
        contactsPhone: null,
        contactsEmail: null,
        contactsAddress: null,
        doorplate: null,
        areaCode: null,
        provinceCode: null,
        cityCode: null,
        businessLicenseFilename: null,
        businessLicenseFilepath: null,
        frontIdCardFilename: null,
        frontIdCardFilepath: null,
        reverseIdCardFilename: null,
        reverseIdCardFilepath: null,
        officeVideoFilename: null,
        officeVideoFilepath: null,
        leaseAgreementFilepath: null,
        leaseAgreementFilename: null,
        leaseAgreementGrantFilename: null,
        leaseAgreementGrantFilepath: null,
        // contractFilename: null,
        // contractFilepath: null,
        // certificateEmploymentFilename: null,员工在职证明
        // certificateEmploymentFilepath: null,员工在职证明路径
        // otherInfoFiles: [],
        address: null,
        contractNo: null,
        remark: null,
        businessLicenseRegisterDate: null,
        businessLicenseValidityDate: null,
        isBusinessLicenseForever: false,
        idGrantType: "",
        leaseType: "",

      },
      rules: {
        authType: [
          { required: true, message: "请选择认证类型", trigger: "blur" },
        ],
        frontIdCardFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        businessLicenseFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        certificateEmploymentFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        reverseIdCardFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        leaseAgreementFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        officeVideoFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        contractFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        leaseAgreementGrantFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        idGrantType: [
          { required: true, message: "请选择授权类型", trigger: "change" },
        ],
        leaseType: [
          { required: true, message: "请选择承租类型", trigger: "change" },
        ],
        businessLicenseRegisterDate: [
          { required: true, message: "请选择注册时间", trigger: "change" },
        ],
        companyName: [
          { required: true, message: "请输入公司全称", trigger: "blur" },
        ],
        companyCreditCode: [
          {
            required: true,
            message: "请输入公司统一社会信用码",
            trigger: "blur",
          },
        ],
        authName: [
          { required: true, validator: validateName, trigger: "blur" },
        ],
        authIdCard: [
          {
            required: true,
            validator: validateID,
            trigger: "blur",
          },
        ],
        contactsName: [
          { required: true, validator: validateName, trigger: "blur" },
        ],
        contactsPhone: [
          { required: true, validator: validatePhone, trigger: "blur" },
        ],
        contactsEmail: [
          { required: true, validator: validateEamil, trigger: "blur" },
        ],
        contactsAddress: [
          { required: true, message: "请输入联系人地址", trigger: "blur" },
        ],
        areaCode: [{ required: true, message: "请选择区", trigger: "blur" }],
        cityCode: [{ required: true, message: "请选择市", trigger: "blur" }],
        provinceCode: [
          { required: true, message: "请选择省", trigger: "blur" },
        ],
        doorplate: [
          { required: true, message: "请输入门牌号", trigger: "blur" },
        ],
        address: [{ required: true, message: "请输入地址", trigger: "blur" }],
        // contractNo: [
        //   { required: true, message: "请输入合同编号", trigger: "blur" },
        // ],
      },
    };
  },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    partybId: {
      type: [String, Number],
      default: ''
    },
    row: {
      type: Object,
      default: () => { }
    },
    partyFirstId: {
      type: [Number, String],
      default: ''
    },
    isLoanType: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer
      },
      set(v) {
        this.$emit("update:drawer", false)
      }
    }
  },
  watch: {
    drawer(val) {
      if (val) {
        getFindAllArea().then((res) => {
          this.cityList = res.data;
          this.getInfo();
        });
      }

    }
  },
  methods: {
    handlePreStep() {
      this.tabIndex -= 1
    },
    handleNextStep() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.tabIndex += 1
        }
      })

    },
    hanldeClose() {
      this.tabIndex = 1
      this.$emit('update:drawer', false);
    },
    // @ApiModelProperty(value = "合同审核状态(0未上传/1待审核/2审核成功/3审核失败)")
    //验证营业执照是否是长期
    hanldeValidityDate(e) {
      if (e) {
        this.formData.businessLicenseValidityDate = null
      }
    },
    //操作选择的时间
    hanldeDate(e) {

      if (!e) {
        this.dateStatus = true
        return
      }
      let date = +new Date()
      let lastDate = 86400 * 182 * 1000
      let selectDate = new Date(e).valueOf()
      this.dateStatus = (date - selectDate > lastDate)
    },
    //上传文件
    changeUpfile(e, dataInfo) {

      if (dataInfo.type == 1) {
        const isJPG =
          e.raw.type === "image/jpg" ||
          e.raw.type === "image/jpeg" ||
          e.raw.type === "image/png";
        const isLt2M = e.size / 1024 / 1024 < 10;
        if (!isJPG) {

          this.$message.error("上传图片只能是 JPG/PNG/jpeg 格式!");
          return;
        }
        if (!isLt2M) {

          this.$message.error("上传图片大小不能超过 10MB!");
          return;
        }
      }
      if (dataInfo.type == 3) {
        const isLt2M = e.size / 1024 / 1024 < 10;
        if (!isLt2M) {
          this.$message.error("上传文件大小不能超过 10MB!");
          return
        }
      }
      if (dataInfo.type == 2) {
        if (!e.raw.type.includes("video")) {

          this.$message.error("请上传视频");
          return;
        }
        const isLt2M = e.size / 1024 / 1024 < 20;
        if (!isLt2M) {

          this.$message.error("上传文件大小不能超过 20MB!");
          return;
        }
      }

      this.$refs.formData.clearValidate(dataInfo.name)
      let data = new FormData();
      data.append("fileNum", dataInfo.type);
      data.append("file", e.raw);
      if (dataInfo.name == "businessLicenseFilename") {
        this.imageUrl = URL.createObjectURL(e.raw)
      }
      contractUploadFile(data, this.partyFirstId).then((res) => {
        this.formData[dataInfo.name] = res.filename
        this.formData[dataInfo.path] = res.filepath
        if (dataInfo.name == "businessLicenseFilename") {
          this.imageUrl = URL.createObjectURL(e.raw)
        }
        if (dataInfo.name == "frontIdCardFilename") {
          this.imageUrl1 = URL.createObjectURL(e.raw)
        }
        if (dataInfo.name == "reverseIdCardFilename") {
          this.imageUrl2 = URL.createObjectURL(e.raw)
        }
      });
    },
    //删除文件
    handleRemoveFile(e) {
      this.formData[e.name] = ""
      this.formData[e.path] = ""
    },
    //上传其他文件
    handleOtherFile(e, fileList) {
      const isLt2M = e.size / 1024 / 1024 < 10;
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 10MB!");
        return
      }
      if (fileList.length > 10) {
        this.$message.error("不能超过10个文件")
        return
      }
      let data = new FormData();
      data.append("fileNum", 3);
      data.append("file", e.raw);
      contractUploadFile(data, this.partyFirstId).then(res => {
        this.fileList.push({ name: res.filename, url: res.filepath })
        this.formData.otherInfoFiles = this.fileList.map(item => {
          return {
            filename: item.name,
            filepath: item.url
          }
        })
        this.fileList = this.formData.otherInfoFiles.map(item => {
          return {
            name: item.filename,
            url: item.filepath
          }
        }) || []
      })


    },
    //删除其他文件
    handleOtherRemove(e, fileList) {
      this.fileList = fileList
      this.formData.otherInfoFiles = fileList.map(item => {
        return {
          filename: item.name,
          filepath: item.url
        }
      })
    },
    submitform() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.partyFirstId = this.partyFirstId;
          if (this.formData.leaseType == 1) {
            this.formData.leaseAgreementGrantFilename = ""
            this.formData.leaseAgreementGrantFilepath = ""
          }
          if (this.dateStatus) {
            this.formData.leaseAgreementFilepath = ""
            this.formData.leaseAgreementFilename = ""
            this.formData.leaseType = ""
            this.formData.leaseAgreementGrantFilename = ""
            this.formData.leaseAgreementGrantFilepath = ""
          }

          AddPartyaAdminContract(this.formData).then((res) => {
            if (res.code == 200) {
              this.getInfo();
              this.$emit('getList')
              this.drawer_ = false
              this.$message.success("上传成功");
            }
          });
        }
      });
    },

    getProvinceCode(e) {
      this.formData.areaCode = "";
      this.formData.cityCode = "";
      this.areaList = this.cityList.filter((item) => item.code == e)[0]?.citys;
    },
    getAreaCodeCode(e) {
      this.formData.areaCode = "";
      this.countrysList = this.areaList.filter(
        (item) => item.code == e
      )[0]?.countrys;
    },
    submitupDataform() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.partyFirstId = this.partyFirstId;
          if (this.formData.leaseType == 1) {
            this.formData.leaseAgreementGrantFilename = ""
            this.formData.leaseAgreementGrantFilepath = ""
          }
          if (this.dateStatus) {
            this.formData.leaseAgreementFilepath = ""
            this.formData.leaseAgreementFilename = ""
            this.formData.leaseType = ""
            this.formData.leaseAgreementGrantFilename = ""
            this.formData.leaseAgreementGrantFilepath = ""
          }
          updataContractInfo(this.formData).then((res) => {
            this.$message.success("修改成功");
            this.$emit('getList')
            this.drawer_ = false
            this.getInfo();
          });
        }
      });
    },
    getInfo() {
      getContractInfo(this.partyFirstId).then((res) => {
        const data = res.data;
        this.show = true;
        if (res.data.provinceCode) {
          this.getProvinceCode(res.data.provinceCode);
          this.getAreaCodeCode(res.data.cityCode);
        }
        setTimeout(() => {
          this.$refs.formData.clearValidate('businessLicenseValidityDate')
        }, 100)
        if (res.data.leaseAgreementFilename) {
          this.dateStatus = false
        }
        this.status = res.data.contractCheckStatus;
        this.contractCheckRemark = res.data.contractCheckRemark;
        this.imageUrl = res.data.businessLicenseFilenameUrl
        this.imageUrl1 = res.data.frontIdCardFilenameUrl
        this.imageUrl2 = res.data.reverseIdCardFilenameUrl
        this.processList = res.data.processList
        this.hanldeDate(res.data.businessLicenseRegisterDate)
        // let list = res.data.fileOrderList || []
        // this.fileList = list.map(item => {
        //   return {
        //     name: item.filename,
        //     url: item.filepath
        //   }
        // })
        // this.formData.otherInfoFiles = list.map(item => {
        //   return {
        //     filename: item.filename,
        //     filepath: item.filepath
        //   }
        // })
        // 还原数据
        for (let key in data) {
          // 接口请求出来的
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = data[key];
          }
        }
      });
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #eeeeee;
  background: #dcdcdc;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-tips {
  color: #8c939d;
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  font-size: 12px;
  justify-content: center;
  align-items: center;

  i {
    font-size: 24px;
  }
}

::v-deep .el-upload__tip {
  color: rgba(0, 0, 0, 0.4);
  margin-top: -1px;
}

.drawer-container {
  height: calc(100vh - 100px);
  overflow: auto;
  padding: 20px;
}

.drawer-tabs {
  width: 702px;
  height: 76px;
  background: #f5f7fa;
  border-radius: 4px 4px 4px 4px;
  margin: 0 auto;
  display: flex;
  padding: 0px 20px;
  justify-content: space-between;
  align-items: center;
  color: rgba(0, 0, 0, 0.4);
  font-size: 16px;
  margin-bottom: 10px;

  &-cicle {
    width: 20px;
    height: 20px;
    background: #e37318;
    display: block;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    margin-right: 5px;
    font-size: 12px;
  }

  &-line {
    width: 160px;
    height: 4px;
    background: #e37318;
  }

  .tab-line {
    width: 160px;
    height: 4px;
    background: #dcdcdc;
  }

  .tab-active {
    color: #e37318;
  }

  .tab-finish {
    color: rgba(0, 0, 0, 0.9);
  }

  .tab-todo {
    border: 1px solid rgba(0, 0, 0, 0.4);
    color: rgba(0, 0, 0, 0.4);
    background: transparent;
  }
}

.drawer-title {
  font-size: 18px;
  font-weight: 400;
  color: #3d3d3d;
  position: relative;
  padding-left: 20px;
  margin-bottom: 20px;

  &::after {
    content: "";
    display: block;
    position: absolute;
    width: 4px;
    height: 20px;
    background: #e37318;
    top: 4px;
    left: 0;
  }
}

.drawer-process {
  padding: 20px;

  &-icon {
    margin-right: 10px;
  }

  &-user {
    font-size: 16px;
    color: #181716;
    width: 250px;
  }

  &-status {
    margin-left: 100px;
    font-size: 12px;
    padding: 2px 4px;
    background: #e5f9e9;
    border-radius: 2px;

    &.success {
      background: #e5f9e9;
      color: #3fa372;
      border: 1px solid #3fa372;
    }

    &.danger {
      color: #ff0000;
      background: #ffecec;
      border: 1px solid #f00;
    }

    &.info {
      color: #ff8f1f;
      background: #ffe8d1;
      border: 1px solid #ff8f1f;
    }
  }

  &-line {
    margin: 3px 0px 5px 8px;
    border-left: 1px dashed #d8d8d8;
    padding-left: 20px;

    padding-bottom: 10px;
  }

  &-time {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 10px;
  }

  &-remark {
    width: 100%;
    background: #fafafa;
    border-radius: 4px 4px 4px 4px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    padding: 20px;

    &.fail {
      background: #ffecec;
      color: #f00;
      border: 1px solid #f00;
    }
  }
}

.drawer__footer {
  position: absolute;
  bottom: 0;
  border-top: 1px solid #dcdfe6;
  width: 100%;
  height: 50px;
  padding-left: 10px;
}

.icon-a-paichu3 {
  margin-right: 8px;
}

::v-deep .el-form-item--medium .el-form-item__label {
  line-height: 0;
}

::v-deep .el-form-item {
  margin-bottom: 18px;
}

::v-deep .el-tag.el-tag--info {
  transform: translateY(-12px);
}

::v-deep .el-upload-list__item-name {
  width: 120px;
  overflow: hidden;
}

.boder-none {
  border: none;
}

.iconfont {
  margin-right: 5px;
}
</style>
