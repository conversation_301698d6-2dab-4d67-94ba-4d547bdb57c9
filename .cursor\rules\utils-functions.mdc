---
description: 
globs: 
alwaysApply: true
---
# 常用工具函数

项目中常用的工具函数位于 `src/utils` 目录下，按功能分类。在开发前应先检查该目录，优先使用现有工具函数，避免重复实现已有功能。

## 请求相关工具
```js
// 路径：@/utils/request.js
import request from '@/utils/request'
import { download } from '@/utils/request'

// request 使用示例
request({
  url: '/system/user/list',
  method: 'get',
  params: query
})

// download 使用示例
download('/system/user/export', {
  params
}, '用户数据.xlsx')
```

## 权限相关工具
```js
// 路径：@/utils/permission.js
import { hasPermi, hasRole } from '@/utils/permission'

// 检查用户是否拥有某个权限
hasPermi('system:user:list')

// 检查用户是否拥有某个角色
hasRole('admin')
```

## 日期时间处理函数
```js
// 路径：@/utils/index.js
import { parseTime, formatTime } from '@/utils'

// 日期格式化
parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}') // 2021-01-01 12:00:00

// 时间格式化
formatTime(Date.now()) // x天x小时x分钟前
```

## 数据处理工具
```js
// 路径：@/utils/index.js
import { deepClone } from '@/utils'

// 深拷贝对象
const obj2 = deepClone(obj1)
```

## 常用验证工具
```js
// 路径：@/utils/validate.js
import { isExternal, isEmail, isMobile, isIdCard } from '@/utils/validate'

// 是否为外部链接
isExternal('https://example.com') // true

// 是否为有效邮箱
isEmail('<EMAIL>') // true

// 是否为有效手机号
isMobile('13800138000') // true

// 是否为有效身份证号
isIdCard('11010119900101001X') // true
```

## token 处理工具
```js
// 路径：@/utils/auth.js
import { getToken, setToken, removeToken } from '@/utils/auth'

// 获取 token
const token = getToken()

// 设置 token
setToken('xxxx')

// 移除 token
removeToken()
```

## 树形数据处理工具
```js
// 路径：@/utils/index.js
import { handleTree } from '@/utils'

// 将线性数据转换为树形结构
const treeData = handleTree(data, 'id', 'parentId')
```

## 通用业务工具
```js
// 路径：@/utils/ruoyi.js
import { 
  resetForm,
  selectDictLabel,
  selectDictLabels,
  download,
  handleTree
} from '@/utils/ruoyi'

// 重置表单
resetForm('formRef')

// 获取字典标签
selectDictLabel(dicts, value)

// 获取字典多选标签
selectDictLabels(dicts, value, separator)

// 下载文件
download(fileName)

// 构建树结构
handleTree(data, id, parentId, children)
```

