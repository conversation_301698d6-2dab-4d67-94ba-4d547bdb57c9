/**
 * 生成随机密码
 * @param {number} length 密码长度，默认12位
 * @returns {string} 生成的随机密码
 */
export function generateRandomPassword(length = 12) {
  const charSets = {
    uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    lowercase: 'abcdefghijklmnopqrstuvwxyz',
    numbers: '0123456789',
    symbols: '!@#$%^&*',
    similar: 'Il1O0'
  }

  // 设置字符集
  let chars = ''
  chars += charSets.uppercase // 包含大写字母
  chars += charSets.lowercase // 包含小写字母
  chars += charSets.numbers  // 包含数字
  chars += charSets.symbols  // 包含特殊字符

  // 排除易混淆字符
  chars = chars.split('').filter(char => !charSets.similar.includes(char)).join('')

  // 生成密码
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }

  return result
} 