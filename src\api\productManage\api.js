import request from '@/utils/request'
//获取api列表
export const getProducApiList=(data)=>{
    return request({
        url:"/loan/productApi/list",
        params:data,
        method:"get"
    })
}

//新增产品api
export const addProducApiOne=(data)=>{
    return request({
        url:"/loan/productApi/add",
        data,
        method:"post"
    })
}

//编辑产品api
export const editProducApiOne=(data)=>{
    return request({
        url:"/loan/productApi/edit",
        data,
        method:"post"
    })
}
//删除产品api
export const delProducApiOne=(id)=>{

    return request({
        url:`/loan/productApi/del/${id}`,
        method:"post"
    })
}
//获取产品api详情
export const getProducApiOne=(id)=>{
    return request({
        url:`/loan/productApi/query/${id}`,
        data,
        method:"post"
    })
}