<template>
  <el-drawer
    title="账号管理"
    :visible.sync="drawer_"
    size="1400px"
    :wrapper-closable="false"
    :direction="direction"
    @close="hanldeClose"
  >
    <el-button
      type="primary"
      icon="el-icon-plus"
      size="mini"
      @click="addUser"
      style="margin: 20px"
      >添加</el-button
    >
    <el-table
      v-loading="loading"
      :data="userList"
      @expand-change="handleExpandChange"
      :row-key="(row) => row.userId"
    >
      <el-table-column type="expand">
        <template slot-scope="{ row }">
          <div style="padding: 0px 12px">
            <el-table
              :data="row.list"
              :row-key="(row) => row.userId"
              :key="row.userId"
            >
              <el-table-column label="姓名" align="center" prop="nickName">
              </el-table-column>
              <el-table-column label="账号" align="center" prop="userName" />
              <el-table-column
                label="手机号"
                align="center"
                prop="phonenumber"
              />
              <el-table-column
                label="接单状态"
                prop="refundAmount"
                align="center"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.pushStatus == "0" ? "启用" : "禁用" }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="账号状态"
                prop="refundAmount"
                align="center"
              >
                <template slot-scope="{ row }">
                  <div>
                    <el-switch
                      v-model="row.status"
                      style="display: block"
                      active-text="启用"
                      inactive-text="禁用"
                      active-value="0"
                      inactive-value="1"
                      @change="editStatus($event, row.userId)"
                    >
                    </el-switch>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="登录状态"
                prop="loginStatus"
                align="center"
              >
                <template slot-scope="{ row }">
                  <div>
                    <el-switch
                      v-model="row.loginStatus"
                      style="display: block"
                      active-text="正常"
                      inactive-text="锁定"
                      active-value="0"
                      inactive-value="1"
                      @change="editLoginStatus($event, row.userId)"
                    >
                    </el-switch>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" prop="loginStatus" align="center">
                <template slot-scope="{ row }">
                  <div>
                    <span
                      class="f-fail"
                      type="text"
                      @click="delSubUser(row)"
                      v-hasPermi="['loan:partya:deletesub']"
                      >删除</span
                    >
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="姓名" align="center" prop="nickName">
        <template slot-scope="{ row }">
          <div>
            <!-- <el-button size="mini" type="text" @click="getSonAdminInfo(row)">{{
              row.nickName
            }}</el-button> -->
            {{ row.nickName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center" prop="userName" />

      <el-table-column label="手机号" align="center" prop="phonenumber" />

      <el-table-column
        label="投放产品"
        align="center"
        show-overflow-tooltip
        width="120"
      >
        <template slot-scope="{ row }">
          <div>{{ row.products.map((item) => item.name).join(",") }}</div>
        </template>
      </el-table-column>
      <el-table-column label="接单状态" prop="refundAmount" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ row.pushStatus == "0" ? "启用" : "禁用" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="账号状态"
        prop="refundAmount"
        align="center"
        width="160"
      >
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.status"
              style="display: block"
              active-text="启用"
              inactive-text="禁用"
              active-value="0"
              inactive-value="1"
              @change="editStatus($event, row.userId)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="登录状态"
        prop="loginStatus"
        align="center"
        width="160"
      >
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.loginStatus"
              style="display: block"
              active-text="正常"
              inactive-text="锁定"
              active-value="0"
              inactive-value="1"
              @change="editLoginStatus($event, row.userId)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" align="center">
        <template slot-scope="{ row }">
          <div>
            <span
              style="margin-right: 8px"
              class="f_c005 c-p"
              @click="updataPassword(row)"
              >修改密码</span
            >
            <span
              style="margin-right: 8px"
              class="f_c005 c-p"
              @click="updateInfo(row)"
              >修改信息</span
            >
            <span
              style="margin-right: 8px"
              class="f_c005 c-p"
              @click="updateAdmin(row)"
              >修改权限</span
            >
            <span
              style="margin-right: 8px"
              class="f-fail c-p"
              @click="deladminUser(row)"
              v-hasPermi="['loan:partya:deleteprimary']"
              >删除</span
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 新增 -->
    <el-dialog
      title="新增账号"
      :visible.sync="userAvisible"
      @close="cancel"
      append-to-body
      center
      width="800px"
      :close-on-click-modal="false"
      @opened="setTreeAll"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <div class="dialog-title">基础资料</div>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input
                v-model.trim="formData.name"
                placeholder="请输入姓名"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="账号" prop="userName">
              <el-input
                v-model.trim="formData.userName"
                placeholder="请输入账号"
              /> </el-form-item
          ></el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <!-- <el-input
                v-model.trim="formData.password"
                placeholder="请输入密码"
              /> -->
              <el-input
                placeholder="请输入8-20位，字母/数字/标点符号至少两种"
                oninput="value=value.replace(/[(\u4e00-\u9fa5)|\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]/g,'')"
                @blur="formData.password = $event.target.value"
                v-model.trim="formData.password"
                maxlength="20"
                clearable
                type="password"
                show-password
              ></el-input> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio v-model="formData.status" label="0">启用</el-radio>
              <el-radio v-model="formData.status" label="1">禁用</el-radio>
            </el-form-item></el-col
          >
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="短信手机号" prop="phonenumber">
              <el-input
                v-model.trim="formData.phonenumber"
                placeholder="请输入短信手机号"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="投放产品">
              <el-select
                clearable
                v-model="formData.productIds"
                multiple
                placeholder="请选择投放产品"
                style="width: 100%"
              >
                <el-option
                  v-for="item in products"
                  :key="item.id"
                  :label="item.id + '--' + item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select> </el-form-item
          ></el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="城市白名单" prop="areaList">
              <el-cascader
                placeholder="请选择城市白名单"
                ref="cascader"
                style="width: 100%"
                v-model="select_options"
                :options="cityList"
                filterable
                clearable
                :props="{ multiple: true }"
              ></el-cascader> </el-form-item
          ></el-col>
        </el-row>
        <div class="dialog-title">菜单权限</div>
        <el-form-item prop="menuIds" label-width="0px">
          <el-tree
            :props="{
              children: 'children',
              label: 'label',
            }"
            :data="menuOptions"
            show-checkbox
            ref="menu"
            node-key="id"
            @check-change="setSelectCheckKey"
          ></el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPartyAForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 修改密码 -->
    <el-dialog
      title="修改密码"
      :visible.sync="updataAvisible"
      width="500px"
      append-to-body
      center
      @close="editCancel"
      :close-on-click-modal="false"
    >
      <el-form
        @submit.native.prevent
        ref="formEidtData"
        :model="editFormData"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="密码" prop="password">
          <el-input
            v-model.trim="editFormData.password"
            clearable
            type="password"
            show-password
            oninput="value=value.replace(/[(\u4e00-\u9fa5)|\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]/g,'')"
            placeholder="请输入密码"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="editCancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 修改账号信息 -->
    <el-dialog
      title="修改信息"
      :visible.sync="updataInfo"
      width="800px"
      append-to-body
      center
      @close="infoCancel"
      :close-on-click-modal="false"
    >
      <el-form
        @submit.native.prevent
        ref="formInfotData"
        :model="infoFormData"
        :rules="rules"
        label-width="100px"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input
                v-model.trim="infoFormData.name"
                placeholder="请输入姓名"
              />
            </el-form-item>

            <el-form-item label="投放产品">
              <el-select
                clearable
                style="width: 100%"
                v-model="infoFormData.productIds"
                multiple
                placeholder="请选择投放产品"
              >
                <el-option
                  v-for="item in updataProduct"
                  :key="item.id"
                  :label="item.id + '--' + item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="手机号">
              <el-input
                v-model.trim="infoFormData.phonenumber"
                placeholder="请输入手机号"
              />
              <div class="tips">Tips:为空则不修改手机号</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市白名单" prop="areaList">
              <el-cascader
                ref="cascader1"
                v-model="back_options"
                :options="cityList"
                filterable
                clearable
                placeholder="请选择城市白名单"
                :props="{ multiple: true }"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- <el-form-item label="菜单权限" prop="menuIds">
          <el-tree
            class="tree-border"
            :props="{
              children: 'children',
              label: 'label',
            }"
            default-expand-all
            :data="menuOptions"
            show-checkbox
            ref="menu"
            node-key="id"
            @check-change="editSetSelectCheckKey"
          ></el-tree>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitInfoForm">确 定</el-button>
        <el-button @click="infoCancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 修改权限 -->
    <el-dialog
      title="修改权限"
      :visible.sync="updataAdmin"
      width="800px"
      append-to-body
      center
      @close="adminCancel"
      @opened="setSelectKey"
      :close-on-click-modal="false"
    >
      <el-form
        @submit.native.prevent
        ref="adminForm"
        :model="adminData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="菜单权限" prop="menuIds" v-if="updataAdmin">
          <el-tree
            :props="{
              children: 'children',
              label: 'label',
            }"
            default-expand-all
            :data="menuOptions"
            show-checkbox
            ref="menu"
            node-key="id"
            @check-change="editSetSelectCheckKey"
          ></el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAdminForm">确 定</el-button>
        <el-button @click="adminCancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="子账号信息"
      :visible.sync="sonAdmin"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-table :data="sonList" border>
        <el-table-column label="姓名" align="center" prop="nickName">
        </el-table-column>
        <el-table-column label="账号" align="center" prop="userName" />
        <el-table-column label="手机号" align="center" prop="phonenumber" />
        <el-table-column label="接单状态" prop="refundAmount" align="center">
          <template slot-scope="{ row }">
            <div>
              {{ row.pushStatus == "0" ? "启用" : "禁用" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="账号状态" prop="refundAmount" align="center">
          <template slot-scope="{ row }">
            <div>
              <el-switch
                v-model="row.status"
                style="display: block"
                active-text="启用"
                inactive-text="禁用"
                active-value="0"
                inactive-value="1"
                @change="editStatus($event, row.userId)"
              >
              </el-switch>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="登录状态" prop="loginStatus" align="center">
          <template slot-scope="{ row }">
            <div>
              <el-switch
                v-model="row.loginStatus"
                style="display: block"
                active-text="正常"
                inactive-text="锁定"
                active-value="0"
                inactive-value="1"
                @change="editLoginStatus($event, row.userId)"
              >
              </el-switch>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="loginStatus" align="center">
          <template slot-scope="{ row }">
            <div>
              <el-button
                type="text"
                @click="delSubUser(row)"
                v-hasPermi="['loan:partya:deletesub']"
                >删除</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </el-drawer>
</template>

<script>
import {
  addPartyaUser,
  getPartyaUserList,
  aupdateUserStatus,
  updateUserPassword,
  updatePartyaUser,
  getAllProducts,
  getpartyUserContronl,
  getCityBlankList,
  updatePartyaUserRole,
  editLoginStatus,
  getSonAdminInfo,
  delPartyAUserAdmin,
  delPartySubAdmin,
} from "@/api/partyA";
export default {
  data() {
    var validatePhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("手机号不能为空"));
      } else if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
        callback(new Error("手机号格式错误"));
      } else {
        callback();
      }
    };
    var validatePwd = (rule, value, callback) => {
      if (!value) {
        callback(new Error("密码不能为空"));
      } else if (
        !/^(?![a-zA-Z]+$)(?!\d+$)(?![^\da-zA-Z\s]+$).{8,20}$/.test(value)
      ) {
        callback(new Error("新密码不符合规范，请按照要求修改!"));
      } else {
        callback();
      }
    };
    return {
      userAvisible: false,
      updataAvisible: false,
      loading: false,
      updataInfo: false,
      updataAdmin: false,
      sonAdmin: false,
      select_options: [],
      back_options: [],
      sonList: [], //子账号信息
      subId: "", //主账号id
      cityList: [],
      userList: [],
      menuOptions: [],
      formData: {
        userName: "",
        password: "",
        productIds: [],
        phonenumber: "",
        status: "0",
        name: "",
        partyFirstId: "",
        menuIds: [],
        areaList: [],
      },
      adminData: {
        menuIds: [],
      },
      infoFormData: {
        name: "",
        phonenumber: "",
        productIds: [],
        userId: "",
        areaList: [],
      },
      editFormData: {
        password: "",
        userId: "",
      },

      updataProduct: [],
      products: [],
      checkKey: [],
      rules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        userName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, validator: validatePwd, trigger: "blur" }],
        status: [{ required: true, message: "请选择状态", trigger: "blur" }],
        phonenumber: [
          { required: true, validator: validatePhone, trigger: "blur" },
        ],
        productIds: [
          { required: true, message: "请选择投放产品", trigger: "blur" },
        ],
        areaList: [
          { required: true, message: "请选择城市白名单", trigger: "blur" },
        ],

        menuIds: [
          {
            type: "array",
            required: true,
            message: "请选择菜单权限",
            trigger: "change",
          },
        ],
      },
      editRules: {
        password: [{ required: true, validator: validatePwd, trigger: "blur" }],
      },
    };
  },
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
    direction: {
      type: String,
      default: "rtl",
    },
    partybId: {
      type: [String, Number],
      default: "",
    },
    row: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    drawer(val) {
      if (val) {
        this.getList();
        getCityBlankList().then((res) => {
          let data = null;
          data = res.data.map((item) => {
            if (item.citys) {
              {
                return {
                  value: item.code,
                  label: item.name,
                  children: [
                    ...item.citys.map((citem) => {
                      return {
                        value: citem.code,
                        label: citem.name,
                      };
                    }),
                  ],
                };
              }
            } else {
              return {
                value: item.code,
                label: item.name,
              };
            }
          });

          this.cityList = JSON.parse(JSON.stringify(data));
        });
      }
    },
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer;
      },
      set(v) {
        this.$emit("update:drawer", false);
      },
    },
  },
  methods: {
    hanldeClose() {
      this.userList = [];
    },
    handleExpandChange(row, rows) {
      const isExpend = rows.some((r) => r.userId === row.userId);
      if (isExpend) {
        // this.subId = row.userId
        getSonAdminInfo(row.userId).then((res) => {
          this.userList = this.userList.map((item) => ({
            ...item,
            list: row.userId == item.userId ? res.userList : item.list || [],
            subId: row.userId == item.userId ? row.userId : item.subId || "",
          }));
        });
      }
    },
    //实现全选树形结构
    setTreeAll() {
      this.$refs.menu.setCheckedNodes(this.menuOptions);
      this.setSelectCheckKey();
    },

    //回显树形结构
    setSelectKey() {
      this.checkKey.forEach((i) => {
        let node = this.$refs.menu.getNode(i);
        if (node) {
          if (node.isLeaf) {
            this.$refs.menu.setChecked(node, true);
          }
        }
      });
    },

    //取消权限修改
    adminCancel() {
      this.updataAdmin = false;
      this.adminData.menuIds = [];
    },
    // 修改权限
    updateAdmin(row) {
      this.adminData.userId = row.userId;
      getpartyUserContronl({ userId: row.userId }).then((res) => {
        this.menuOptions = res.menus;
        this.checkKey = res.checkedKeys || [];
        this.adminData.menuIds = res.menus;
      });

      this.updataAdmin = true;
    },
    //修改登录状态
    editLoginStatus(e, id) {
      editLoginStatus({
        status: e,
        userId: id,
      }).then((res) => {
        if ((res.code = 200)) {
          // this.getList();
          this.$message.success("修改成功");
        }
      });
    },
    //显示子账号信息
    getSonAdminInfo(row) {
      this.subId = row.userId;
      getSonAdminInfo(row.userId).then((res) => {
        this.sonList = res.userList;
        this.sonAdmin = true;
      });
    },
    //修改树形结构
    editSetSelectCheckKey() {
      let child = this.$refs.menu.getCheckedKeys();
      let parent = this.$refs.menu.getHalfCheckedKeys();
      this.adminData.menuIds = parent.concat(child);
    },
    //新增获取选取的树形结构
    setSelectCheckKey() {
      let child = this.$refs.menu.getCheckedKeys();
      let parent = this.$refs.menu.getHalfCheckedKeys();
      this.formData.menuIds = parent.concat(child);
    },
    //新增
    addUser() {
      this.formData.partyFirstId = this.row.partyFirstId;
      getpartyUserContronl({ userId: "" }).then((res) => {
        this.userAvisible = true;
        this.menuOptions = res.menus;
      });
    },

    cancel() {
      this.formData = {
        userName: "",
        password: "",
        productIds: [],
        phonenumber: "",
        status: "0",
        name: "",
        menuIds: [],
        areaList: [],
      };
      this.select_options = [];
      this.userAvisible = false;
      this.$refs.formData.resetFields();
    },

    flatArrr(arr) {
      let result = [];
      for (let item of arr) {
        var res = JSON.parse(JSON.stringify(item));
        delete res["children"];
        result.push(res);
        if (item.children instanceof Array && item.children.length > 0) {
          result = result.concat(this.flatArrr(item.children));
        }
      }
      return result;

      // let array = [];
      // const handleArr = (arr) => {
      //   arr.forEach((item) => {
      //     if (item.children && item.children.length > 0) {
      //       handleArr(item.children);
      //       return;
      //     } else {
      //       array.push({
      //         id: item.id,
      //         label: item.label,
      //       });
      //     }
      //   });
      // };
      // handleArr(arr);
      // return array;
    },
    submitPartyAForm() {
      let menuArrCN = this.flatArrr(this.menuOptions);
      let menuName = [];
      menuArrCN.forEach((item) => {
        this.formData.menuIds.forEach((citem) => {
          if (item.id == citem) {
            menuName.push(item.label);
          }
        });
      });
      // console.log(menuName);
      if (menuName.includes("充值(hmc)") && menuName.includes("充值(zxbc)")) {
        return this.$message.error("充值平台只能单选");
      }

      this.formData.areaList = this.$refs["cascader"]
        .getCheckedNodes({
          leafOnly: true,
        })
        .map((item) => {
          return {
            cityCode: item.value,
            cityName: item.label,
          };
        });
      this.$refs.formData.validate((valid) => {
        if (valid) {
          addPartyaUser(this.formData).then((res) => {
            if ((res.code = 200)) {
              this.$message.success("新增成功");
              this.cancel();
              this.getList();
            }
          });
        }
      });
    },
    getList() {
      this.loading = true;
      getPartyaUserList(this.row.partyFirstId).then((res) => {
        this.loading = false;

        this.products = res.productList;
        this.userList = res.userList;
      });
    },
    editStatus(e, id) {
      aupdateUserStatus({
        status: e,
        userId: id,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success("状态更新成功");
          // this.getList();
        }
      });
    },

    //显示修改框
    updataPassword(row) {
      this.updataAvisible = true;
      this.editFormData.userId = row.userId;
    },
    //取消修改
    editCancel() {
      this.updataAvisible = false;
      this.editFormData.password = "";
      this.editFormData.userId = "";
      this.$refs.formEidtData.resetFields();
    },
    //提交修改密码
    submitEditForm() {
      this.$refs.formEidtData.validate((valid) => {
        if (valid) {
          updateUserPassword(this.editFormData).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.updataAvisible = false;
            }
          });
        }
      });
    },
    //修改信息
    updateInfo(row) {
      getAllProducts({
        partyFirstId: this.row.partyFirstId,
        userId: row.userId,
      }).then((res) => {
        this.updataProduct = res.productList;
        let arr = [];
        res.citys.forEach((item) => {
          this.cityList.forEach((i) => {
            if (i.children) {
              i.children.forEach((citem) => {
                if (citem.value == item) {
                  arr = [...arr, [i.value, item]];
                }
              });
            }
          });
        });
        this.back_options = arr;
      });

      this.infoFormData.name = row.nickName;
      this.infoFormData.phonenumber = row.phonenumber;
      this.infoFormData.userId = row.userId;
      this.infoFormData.productIds = row.products[0].id
        ? row.products.map((item) => item.id)
        : [];
      this.updataInfo = true;
    },
    infoCancel() {
      this.infoFormData = {
        name: "",
        phonenumber: "",
        productIds: [],
        userId: "",
        areaList: [],
      };
      this.back_options = [];
      this.updataInfo = false;
      this.$refs.formInfotData.resetFields();
    },
    //修改权限
    submitAdminForm() {
      let menuArrCN = this.flatArrr(this.menuOptions);
      let menuName = [];

      menuArrCN.forEach((item) => {
        this.adminData.menuIds.forEach((citem) => {
          if (item.id == citem) {
            menuName.push(item.label);
          }
        });
      });

      if (menuName.includes("充值(hmc)") && menuName.includes("充值(zxbc)")) {
        console.log(2222222);
        return this.$message.error("充值平台只能单选");

      }

      this.$refs.adminForm.validate((valid) => {
        if (valid) {
          updatePartyaUserRole(this.adminData).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.adminCancel();
              this.getList();
            }
          });
        }
      });
    },
    //提交修改信息
    submitInfoForm() {
      this.infoFormData.areaList = this.$refs["cascader1"]
        .getCheckedNodes({
          leafOnly: true,
        })
        .map((item) => {
          return {
            cityCode: item.value,
            cityName: item.label,
          };
        });
      this.$refs.formInfotData.validate((valid) => {
        if (valid) {
          if (
            this.infoFormData.phonenumber &&
            !/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(this.infoFormData.phonenumber)
          ) {
            return this.$message.error("手机号格式错误");
          }
          updatePartyaUser(this.infoFormData).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.infoCancel();
              this.getList();
            } else {
              this.$confirm(
                `[${res.data
                  .map((item) => item.name)
                  .join(",")}]当前在线，请在投放产品列表下线后再取消绑定`,
                "提示",
                {
                  confirmButtonText: "确定",
                  showCancelButton: false,
                  type: "warning",
                }
              ).then(() => {
                this.infoCancel();
              });
            }
          });

          // else {
          //   updatePartyaUser(this.infoFormData).then((res) => {
          //     if (res.code == 200) {
          //       this.$message.success("修改成功");
          //       this.infoCancel();
          //       this.getList();
          //     } else {
          //       this.$confirm(`[${res.data.map(item => item.name).join(',')}]当前在线，请在投放产品列表下线后再取消绑定`, '提示', {
          //         confirmButtonText: '确定',
          //         showClose: false,
          //         type: 'warning'
          //       }).then(() => {
          //         this.infoCancel();
          //       })
          //     }
          //   });
          // }
        }
      });
    },
    //删除主账号
    deladminUser(row) {
      this.$confirm(`确认删除${row.userName}吗？`, {
        type: "warning",
      })
        .then((res) => {
          let data = new FormData();
          data.append("userId", row.userId);
          delPartyAUserAdmin(data).then((res) => {
            this.getList();
            this.$message.success("删除成功");
          });
        })
        .catch((err) => {});
    },
    //删除子账号
    delSubUser(row) {
      this.$confirm(`确认删除${row.nickName}吗？`, {
        type: "warning",
      })
        .then((res) => {
          let data = new FormData();
          data.append("userId", row.userId);
          delPartySubAdmin(data).then((res) => {
            getSonAdminInfo(row.subId).then((res) => {
              this.userList = this.userList.map((item) => ({
                ...item,
                list:
                  row.userId == item.userId ? res.userList : item.list || [],
                subId:
                  row.userId == item.userId ? row.userId : item.subId || "",
              }));
            });
            this.$message.success("删除成功");
          });
        })
        .catch((err) => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.el-button--text:hover,
.el-button--text:focus {
  color: #0052d9;
}

.dialog-title {
  color: #3d3d3d;
  font-size: 18px;
  margin-bottom: 10px;
  position: relative;

  &::before {
    content: "";
    display: block;
    width: 5px;
    height: 20px;
    background: #e37318;
    position: absolute;
    left: -12px;
    top: 3px;
  }
}

::v-deep .el-tree {
  display: flex;
  flex-wrap: wrap;
}

::v-deep .el-tree-node {
  width: 50%;
  margin-bottom: 5px;
}

::v-deep .el-table__expand-icon > .el-icon {
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.4);
  color: rgba(0, 0, 0, 0.4);
  font-size: 12px;
  padding-top: 1px;
}

::v-deep .el-cascader__tags {
  max-height: 200px;
  overflow-y: scroll;
}

::v-deep .el-cascader .el-input__inner {
  max-height: 200px;
  overflow-y: scroll;
}

::v-deep .el-cascader .el-cascader__search-input {
  max-height: 200px;
  overflow-y: scroll;
}

::v-deep .el-cascader__tags::-webkit-scrollbar {
  width: 0;
}

::v-deep .el-dialog--center .el-dialog__body {
  max-height: calc(100vh - 200px);

  overflow-y: auto;
}
</style>
