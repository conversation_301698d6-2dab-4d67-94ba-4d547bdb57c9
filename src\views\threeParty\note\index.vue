<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="短信名称" prop="status">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入短信名称"
          size="small"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="addNote"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" border :data="noteList">
      <el-table-column label="短信名称" prop="name" align="center" />
      <el-table-column label="短信内容" prop="content" align="center" />
      <el-table-column label="key类型" prop="smsTemplateKey" align="center" />
      <el-table-column label="配置类型" prop="templateName" align="center" />
      <el-table-column label="状态" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.status == 1 ? "启用" : "禁用" }}
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="短信类型" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ genre[row.genre] }}
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column
        label="参数数量"
        align="center"
        prop="parametersNumber"
      />

      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              type="text"
              size="small"
              icon="el-icon-edit-outline"
              @click="handleEdit(row)"
              >修改</el-button
            >
            <el-button
              type="text"
              size="small"
              icon="el-icon-delete"
              @click="handleDel(row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="isAdd ? '新增短信模板' : '修改短信模板'"
      :visible.sync="noteAvisible"
      @close="cancel"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="短信名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入短信名称" />
        </el-form-item>
        <el-form-item label="key类型" prop="smsTemplateKey">
          <el-select
            v-model="formData.smsTemplateKey"
            placeholder="请选择key类型"
            clearable
            filterable
            size="small"
            style="width: 100%"
          >
            <el-option
              :value="item.smsTemplateKey"
              :label="item.keyName"
              v-for="item in keyList"
              :key="item.smsTemplateKey"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配置类型" prop="templateId">
          <el-select
            v-model="formData.templateId"
            placeholder="请选择配置类型"
            clearable
            filterable
            size="small"
            style="width: 100%"
          >
            <el-option
              :value="item.id"
              :label="item.name"
              v-for="item in configList"
              :key="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短信状态" prop="status" v-if="formData.status">
          <el-radio v-model="formData.status" :label="1">启用</el-radio>
          <el-radio v-model="formData.status" :label="2">禁用</el-radio>
        </el-form-item>

        <el-form-item label="短信内容" prop="content">
          <el-input
            type="textarea"
            v-model="formData.content"
            placeholder="请输入短信类容"
          />
        </el-form-item>

        <el-form-item label="参数数量" prop="parametersNumber">
          <el-input
            v-model.number="formData.parametersNumber"
            placeholder="请输入参数数量"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addNoteOne,
  getNoteList,
  editNoteOne,
  delNoteOne,
  getSmsConfigurate,
  getsmsTemplateKey,
} from "@/api/threeParty/note";
export default {
  name: "Note",
  data() {
    return {
      noteAvisible: false,
      isAdd: true,
      total: 0,
      loading: false,
      isAddNote: false,
      noteList: [],
      keyList: [],
      configList: [],
      queryParams: {
        name: "",
        pageNum: 1,
        pageSize: 10,
      },

      formData: {
        content: "",
        name: "",
        smsTemplateKey: "",
        templateId: "",
        status: 1,
        parametersNumber: null,
      },
      rules: {
        content: [
          { required: true, message: "请输入短信类容", trigger: "blur" },
        ],
        smsTemplateKey: [
          { required: true, message: "请选择Key", trigger: "blur" },
        ],
        templateId: [{ required: true, message: "请选择Key", trigger: "blur" }],
        name: [{ required: true, message: "请输入短信名称", trigger: "blur" }],
        parametersNumber: [
          { required: true, message: "请输入参数数量", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    getList() {
      this.loading = true;
      getNoteList(this.queryParams).then((res) => {

        this.noteList = res.rows;
        this.total = res.total;
        this.loading = false;
        this.noteList.forEach((item) => {
          if (item && item.genre == 1) {
            this.isAddNote = true;
          } else {
            this.isAddNote = false;
          }
        });
        if (this.noteList.length == 0 && this.queryParams.pageNum > 1) {
          this.queryParams.pageNum = this.queryParams.pageNum - 1;
          this.getList();
        }
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    cancel() {
      this.formData = {
        content: "",
        name: "",
        smsTemplateKey: "",
        templateId: "",
        status: 1,
        parametersNumber: null,
      };
      this.noteAvisible = false;
      this.$refs.formData.resetFields();
    },
    addNote() {
      this.noteAvisible = true;
      this.isAdd = true;
      getsmsTemplateKey().then((res) => {
        this.keyList = res.data;
      });
      getSmsConfigurate().then((res) => {
        this.configList = res.data;
      });
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    handleDel(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delNoteOne({ id: row.id })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {});
        })
        .catch((err) => {});
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addNoteOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("新增成功");
              }
            });
          } else {
            editNoteOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("修改成功");
              }
            });
          }
        }
      });
    },

    handleEdit(row) {
      this.formData.content = row.content;
      this.formData.name = row.name;
      this.formData.status = row.status;
      this.formData.smsTemplateKey = row.smsTemplateKey;
      this.formData.templateId = row.templateId;
      this.formData.parametersNumber = row.parametersNumber;
      this.noteAvisible = true;
      this.isAdd = false;
      this.formData.id = row.id;
      getsmsTemplateKey({key:row.smsTemplateKey}).then((res) => {
        this.keyList = res.data;
      });
      getSmsConfigurate().then((res) => {
        this.configList = res.data;
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-textarea__inner {
  height: 200px;
}
</style>
