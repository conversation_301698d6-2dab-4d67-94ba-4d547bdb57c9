import request from '@/utils/request'

//获取apiq渠道列表
export const getApiChannelList = (data) => {
    return request({
        url: "/loan/api/config/channel/list",
        method: "get",
        params: data
    })
}
//新增渠道
export const addApiChannelOne = (data) => {
    return request({
        url: "/loan/api/config/channel/add",
        method: "post",
        data
    })
}

///查渠道配置详情
export const getApiChannelOne = (data) => {
    return request({
        url: `/loan/api/config/channel/query/${data}`,
        method: "post"
    })
}
///修改道配置状态
export const changeApiChannelOne = (data) => {
    return request({
        url: `/loan/api/config/channel/updateStatus`,
        method: "post",
        data
    })
}
//编辑渠道
export const editApiChannelOne = (data) => {
    return request({
        url: "/loan/api/config/channel/update",
        method: "post",
        data
    })
}