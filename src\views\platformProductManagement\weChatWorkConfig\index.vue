<template>
  <div class="app-container">
    <el-form 
      :model="formData" 
      :rules="rules" 
      ref="formData" 
      label-width="120px"
      size="small"
      :inline="true"
    >
      <el-form-item label="选择渠道" prop="channelIds">
        <el-select
          v-model="formData.channelIds"
          multiple
          filterable
          clearable
          collapse-tags
          placeholder="请选择渠道"
           style="width: 400px"
        >
          <el-option
            v-for="item in channelList"
            :key="item.id"
            :label="`${item.id}-${item.channelName}`"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </el-form-item>
    </el-form>

    <el-card style="margin-top: 20px;">
      <div slot="header">
        <span>已选渠道</span>
      </div>
      <div>
        <el-tag
          v-for="tag in formData.channelIds"
          :key="tag"
          closable
          :disable-transitions="false"
          @close="handleTagClose(tag)"
          style="margin-right: 10px; margin-bottom: 10px;"
        >
          {{ getChannelName(tag) }}
        </el-tag>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getWeChatWorkConfig, saveWeChatWorkConfig } from '@/api/platformProductManagement/weChatWorkConfig'
import { getChannelList } from '@/api/productManage/product'

export default {
  name: "WeChatWorkConfig",
  
  data() {
    return {
      channelList: [],
      formData: {
        channelIds: []
      },
      rules: {
      }
    };
  },

  created() {
    this.getChannelList()
    this.getWeChatWorkConfig()
  },

  methods: {
    async getChannelList() {
      const res = await getChannelList()
      this.channelList = res.data || []
    },

    async getWeChatWorkConfig() {
      const res = await getWeChatWorkConfig()
      if(res.code === 200 && res.data) {
        this.formData.channelIds = res.data.channelIds ? 
          res.data.channelIds.split(',').map(id => Number(id)) : []
      }
    },

    handleSubmit() {
      this.$refs.formData.validate(async valid => {
        if(valid) {
          const params = {
            ...this.formData,
            channelIds: this.formData.channelIds.join(',')
          }
          const res = await saveWeChatWorkConfig(params)
          if(res.code === 200) {
            this.$message.success('保存成功')
          }
        }
      })
    },
    handleTagClose(tag) {
      this.formData.channelIds.splice(this.formData.channelIds.indexOf(tag), 1);
    },
    getChannelName(id) {
      const channel = this.channelList.find(item => item.id === id);
      return channel ? `${channel.id}-${channel.channelName}` : '';
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
