import request from '@/utils/request'

// 获取企微类型配置组列表
export const getQwTypeConfigGroupList = (data) => {
  return request({
    url: '/loan/xm/qwTypeConfigGroup/list',
    method: 'get',
    params: data
  })
}

// 新增企微类型配置组
export const addQwTypeConfigGroup = (data) => {
  return request({
    url: '/loan/xm/qwTypeConfigGroup/add',
    method: 'post',
    data
  })
}

// 修改企微类型配置组
export const updateQwTypeConfigGroup = (data) => {
  return request({
    url: '/loan/xm/qwTypeConfigGroup/update',
    method: 'post',
    data
  })
}

// 启用禁用企微类型配置组
export const updateQwTypeConfigGroupStatus = (data) => {
  return request({
    url: '/loan/xm/qwTypeConfigGroup/updateStatus',
    method: 'post',
    data
  })
}
