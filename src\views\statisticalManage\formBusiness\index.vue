<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">


      <el-form-item label="日期类型" prop="dateType">
        <el-select v-model="queryParams.dateType" placeholder="日期类型" :disabled="!!dateRange&&dateRange.length>0"
          clearable size="small">
          <el-option value="1" label="今天"></el-option>
          <el-option value="2" label="昨天"></el-option>
          <el-option value="3" label="最近7天"></el-option>
          <el-option value="4" label="最近30天"></el-option>
          <el-option value="5" label="当月"></el-option>
          <el-option value="6" label="上月"></el-option>
          <el-option value="7" label="近半年"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="dateRange">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd" :disabled="!!queryParams.dateType"
          type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="客户端" prop="deviceType">
        <el-select v-model="queryParams.deviceType" placeholder="客户端" clearable size="small">
          <el-option :value="1" label="苹果"></el-option>
          <el-option :value="2" label="安卓"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="流量场景" prop="flowType">
        <el-select v-model="queryParams.flowType" placeholder="流量场景" clearable size="small">
          <el-option :value="1" label="H5"></el-option>
          <el-option :value="2" label="APP"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-select v-model="queryParams.productType" placeholder="产品名称" clearable size="small">
          <el-option value="1" label="表单产品"></el-option>
          <el-option value="2" label="接口产品"></el-option>
          <el-option value="3" label="线上产品"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>

    <el-table border :data="dataList">
      <el-table-column label="日期" prop="queryDate" align="center" />
      <el-table-column label="省份" prop="queryDate" align="center" />
      <el-table-column label="城市" prop="queryDate" align="center" />
      <el-table-column label="表单总量" prop="queryDate" align="center" />
      <el-table-column label="客户放弃申请量" prop="queryDate" align="center" />
      <el-table-column label="机构消耗量" prop="queryDate" align="center" />
      <el-table-column label="CPD展位消耗量" prop="queryDate" align="center" />
      <el-table-column label="机构未接单量" prop="queryDate" align="center" />

    </el-table>
  </div>
</template>
<script>
export default {
  data() {
    return {
      dataList: [],
      queryParams: {
        productName: "",
        channelIds: '',

        deviceType: '',
        productType: ""
      }
    }
  },
  methods: {
    handleQuery() { }
  }
}
</script>

<style lang="scss" scoped>

</style>
