<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="queryParams" ref="queryForm" class="demo-form-inline">
      <el-form-item label="短信发送批次号">
        <el-input v-model="queryParams.sendNo" placeholder="请输入短信发送批次号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="queryParams.sendPhone" placeholder="请输入手机号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发送状态">
        <el-select v-model="queryParams.sendStatus" placeholder="请选择发送状态" clearable @change="handleQuery">
          <el-option label="未完成" :value="0" />
          <el-option label="发送成功" :value="1" />
          <el-option label="发送失败" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="短信平台账号">
        <el-select
          v-model="queryParams.smsAccountId"
          filterable
          remote
          :remote-method="remoteSearch"
          :loading="selectLoading"
          clearable
          placeholder="请选择短信平台账号"
          style="width: 300px"
          @change="handleQuery"
        >
          <el-option
            v-for="item in accountOptions"
            :key="item.accountId"
            :label="item.accountName"
            :value="item.accountId">
            <span>{{ item.accountName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">ID: {{ item.accountId }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发送时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          style="width: 380px"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column prop="sendNo" label="短信发送批次号" align="center" />
      <el-table-column prop="uuid" label="短信uuid" align="center" />
      <el-table-column prop="sendPhone" label="手机号" align="center" />
      <el-table-column prop="phoneTypesDesc" label="运营商类型" align="center">
        <template slot-scope="scope">
          <el-tag
            size="mini"
            :type="scope.row.phoneTypesDesc == '移动' ? 'success' : 
                   scope.row.phoneTypesDesc == '联通' ? 'primary' : 
                   scope.row.phoneTypesDesc == '电信' ? 'warning' : 'info'">
            {{ scope.row.phoneTypesDesc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sendStatusDesc" label="发送状态" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" :type="scope.row.sendStatus == 0 ? 'info' : scope.row.sendStatus == 1 ? 'success' : 'danger'" style="white-space: normal;height: auto;">
            {{ scope.row.sendStatus == 2 ? scope.row.sendStatusDesc + '：' + scope.row.failMsg : scope.row.sendStatusDesc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" width="180" />
      <el-table-column prop="sendTime" label="发送时间" align="center" width="180" />
      <el-table-column prop="completionTime" label="完成时间" align="center" width="180" />
      <el-table-column prop="smsContent" label="短信内容" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="getSmsContent(scope.row.detailId)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination"
    />

    <!-- 短信内容弹窗 -->
    <el-dialog
      title="短信内容"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div style="word-break: break-all;">{{ currentSmsContent }}</div>
    </el-dialog>
  </div>
</template>

<script>
import { getSmsReportDetails, getSmsAccountList } from '@/api/openSmsManage'
import dayjs from 'dayjs'
import request from '@/utils/request'

export default {
  name: 'SmsReportDetail',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 下拉框加载状态
      selectLoading: false,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 账号选项
      accountOptions: [],
      // 日期范围
      dateRange: [
        dayjs().format('YYYY-MM-DD 00:00:00'),
        dayjs().format('YYYY-MM-DD 23:59:59')
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sendNo: undefined,
        sendPhone: undefined,
        sendStatus: undefined,
        smsAccountId: undefined,
        searchStartDateTime: dayjs().format('YYYY-MM-DD 00:00:00'),
        searchEndDateTime: dayjs().format('YYYY-MM-DD 23:59:59')
      },
      // 弹窗显示状态
      dialogVisible: false,
      // 当前短信内容
      currentSmsContent: ''
    }
  },
  created() {
    const localPageData = window.sessionStorage.getItem('pageData_smsReportDetail')
    const { sendNo, searchStartDateTime, searchEndDateTime } = localPageData ? JSON.parse(localPageData) : {}
    if (sendNo) {
      this.queryParams.sendNo = sendNo
    }
    if (searchStartDateTime && searchEndDateTime) {
      this.dateRange = [searchStartDateTime, searchEndDateTime]
    }
    
    this.getList()
  },
  methods: {
    /** 获取请求参数 */
    getQueryParams() {
      const params = {
        ...this.queryParams
      }
      if (this.dateRange && this.dateRange[0] && this.dateRange[1]) {
        params.searchStartDateTime = this.dateRange[0]
        params.searchEndDateTime = this.dateRange[1]
      }
      return params
    },
    /** 远程搜索 */
    remoteSearch(query) {
      if (query !== '') {
        this.selectLoading = true
        getSmsAccountList({
          accountName: query
        }).then(res => {
          this.accountOptions = res.data || []
          this.selectLoading = false
        })
      } else {
        this.accountOptions = []
      }
    },
    /** 查询列表 */
    getList() {
      const params = this.getQueryParams()
      getSmsReportDetails(params).then(res => {
        this.tableData = res.rows || []
        this.total = res.total
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (!this.dateRange || !this.dateRange[0] || !this.dateRange[1]) {
        this.$message.warning('请选择发送时间范围')
        return
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      window.sessionStorage.removeItem('pageData_smsReportDetail')
      this.dateRange = [
        dayjs().format('YYYY-MM-DD 00:00:00'),
        dayjs().format('YYYY-MM-DD 23:59:59')
      ]
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        sendNo: undefined,
        sendPhone: undefined,
        sendStatus: undefined,
        smsAccountId: undefined,
        searchStartDateTime: this.dateRange[0],
        searchEndDateTime: this.dateRange[1]
      }
      this.$refs.queryForm.resetFields()
      this.getList()
    },
    /** 分页大小改变 */
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    /** 分页页码改变 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    /** 获取短信内容 */
    async getSmsContent(detailId) {
      try {
        const content = await request({
          url: `/loan/open/sms/statistics/getSmsContent/${detailId}`,
          method: 'get'
        })
        this.currentSmsContent = content.data
        this.dialogVisible = true
      } catch (error) {
        this.currentSmsContent = '获取短信内容失败'
        this.dialogVisible = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style> 