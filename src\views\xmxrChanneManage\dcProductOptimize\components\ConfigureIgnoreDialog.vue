<template>
  <el-dialog
    title="配置子平台贷超优化产品"
    :visible.sync="dialogVisibleInternal"
    width="fit-content"
    append-to-body
    @open="onDialogOpened"
    @close="onDialogClose"
  >
    <div>
      <transfer-pro
        v-if="dialogVisibleInternal"
        :data="transferData"
        v-model="selectedProductKeys"
        :key-prop="'key'"
        :label-prop="'label'"
        :source-title="'所有产品'"
        :target-title="'已选优化产品'"
        :filterable="true"
      >
      </transfer-pro>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getChannelPlatformGroupProductList } from "@/api/platformProductManagement/platformProductConfig";
import TransferPro from "@/components/transfer-pro/Transfer.vue";

export default {
  name: "ConfigureIgnoreDialog",
  components: { TransferPro },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    groupedList: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisibleInternal: this.visible,
      allProductsForDialog: [],
      transferData: [],
      selectedProductKeys: [],
      dialogQueryParams: {
        type: 1, // 企微类型产品
      },
    };
  },
  watch: {
    visible(newVal) {
      this.dialogVisibleInternal = newVal;
    },
    dialogVisibleInternal(newVal) {
      this.$emit("update:visible", newVal);
    },
  },
  methods: {
    async getProductList(){
      const requestList = [
        getChannelPlatformGroupProductList({type: 3}),
        getChannelPlatformGroupProductList({type: 4}),
      ];
      const [res1, res2] = await Promise.all(requestList);
      return [...res1.data, ...res2.data];
    },
    async onDialogOpened() {
      const resData = await this.getProductList();
      console.log("res:",resData)
          this.allProductsForDialog = resData || [];
          this.transferData = this.allProductsForDialog.map((item) => {
            const uniqueKeyValue = `${item.platformType}-${item.productId}`;
            return {
              key: uniqueKeyValue,
              id: uniqueKeyValue, // transfer-pro可能需要id
              label: `${item.platform} - ${item.productName} (ID: ${item.productId})`,
              originalItem: item, // 保留原始项，方便后续转换
            };
        })
      const currentSelectedKeys = [];
      for (const platformId in this.groupedList) {
        const platform = this.groupedList[platformId];
        platform.products.forEach((p) => {
          // 确保使用与transferData中key相同的格式
          currentSelectedKeys.push(`${platformId}-${p.productId}`);
        });
      }
      this.selectedProductKeys = currentSelectedKeys;
    },
    onDialogClose() {
      // Reset data on close
      this.allProductsForDialog = [];
      this.transferData = [];
      this.selectedProductKeys = [];
      this.dialogVisibleInternal = false; // Ensure visibility is synced
    },
    handleCancel() {
      this.dialogVisibleInternal = false;
    },
    handleConfirm() {
      // Emit the selected keys and the full product details for convenience
      const selectedProductsPayload = this.selectedProductKeys
        .map((key) => {
          const originalProductWrapper = this.transferData.find(td => td.key === key);
          if (originalProductWrapper && originalProductWrapper.originalItem) {
             const item = originalProductWrapper.originalItem;
            return {
              productId: item.productId,
              productName: item.productName,
              platformName: item.platform,
              platformId: item.platformType,
            };
          }
          return null;
        })
        .filter((item) => item !== null);

      this.$emit("confirm-selection", selectedProductsPayload);
      // this.dialogVisibleInternal = false; // Parent will hide dialog upon successful submission
    },
  },
};
</script>

<style scoped>
/* Styles for the dialog can be added here if needed */
.dialog-footer {
  text-align: right;
}

::v-deep .transfer-panel {
  min-width: 350px !important; /* 使用 !important 确保覆盖 transfer-pro 内部样式 */
}
</style>
