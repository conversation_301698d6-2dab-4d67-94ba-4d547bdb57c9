<template>
  <div class="app-container">
    <!-- 添加搜索表单 -->
    <el-form :model="queryParams" :inline="true">
      <el-form-item label="渠道名称" prop="channelName">
        <el-input v-model.trim="queryParams.channelName" placeholder="请输入渠道名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker v-model="queryParams.dateRange" type="datetimerange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']" @change="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border style="width: 100%" row-key="rowKey" default-expand-all
      :tree-props="{ children: 'subStat' }">
      <el-table-column prop="channelId" label="渠道ID" />
      <el-table-column prop="channelName" label="渠道名称" />
      <el-table-column prop="sendTotal" label="发送总条数" />
      <el-table-column prop="successTotal" label="发送成功条数">
        <template slot-scope="scope">
          <span class="success-text">{{ scope.row.successTotal }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="failTotal" label="发送失败条数">
        <template slot-scope="scope">
          <span class="fail-text">{{ scope.row.failTotal }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="unUpdateTotal" label="未更新数量" />
      <el-table-column prop="uvTotal" label="UV数量" />
      <el-table-column prop="totalProfit" label="总收益" />
      <el-table-column prop="roi" label="ROI" />
    </el-table>
  </div>
</template>

<script>
import { getSmsChannelRevenueV2 } from "@/api/smsChannelRevenue";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

export default {
  name: "SmsChannelRevenue",
  data() {
    return {
      tableData: [],
      queryParams: {
        channelName: "",
        dateRange: [],
      },
    };
  },
  created() {
    this.setDefaultDate();
    this.handleQuery();
  },
  methods: {
    handleTableData(arr) {
      arr.forEach((row) => {
        row.rowKey = uuidv4();

        if (row.subStat && row.subStat.length) {
          row.subStat.forEach((innerRow) => {
            innerRow.rowKey = uuidv4();

            innerRow.isInner = true;
          });
        } else {
          row.subStat = null;
        }
      });
    },

    getParams() {
      const [startDateTime = "", endDateTime = ""] =
        this.queryParams.dateRange || [];

      return {
        channelName: this.queryParams.channelName,
        startDateTime,
        endDateTime,
      };
    },
    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format("YYYY-MM-DD 00:00:00");
      const endTime = today.format("YYYY-MM-DD 23:59:59");

      this.queryParams.dateRange = [startTime, endTime];
    },

    async getList() {
      const params = this.getParams();

      if (!params.startDateTime || !params.endDateTime) {
        this.$message.warning("请选择时间范围");
        return;
      }

      const { data } = await getSmsChannelRevenueV2(params);
      this.handleTableData(data);
      this.tableData = data;
    },

    // 搜索按钮操作
    handleQuery() {
      this.getList();
    },

    // 重置按钮操作
    resetQuery() {
      this.queryParams = {
        channelName: "",
        dateRange: [],
      };
      this.setDefaultDate();
      this.getList();
    },
  },
};
</script>

<style scoped>
.success-text {
  color: #67C23A;
}

.fail-text {
  color: #F56C6C;
}
</style>
