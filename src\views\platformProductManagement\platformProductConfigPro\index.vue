<template>
  <div class="app-container platform-product-config-pro">
    <!-- 右侧固定按钮 -->
    <div class="fixed-actions">
      <div
        class="action-btn add-btn"
        @click="handleAddGroup"
      >
        <i class="el-icon-plus"></i>
      </div>
      <div
        class="action-btn nav-btn"
        @click="showNav = true"
      >
        <i class="el-icon-menu"></i>
      </div>
      <div
        class="action-btn top-btn"
        @click="scrollToTop"
      >
        <i class="el-icon-top"></i>
      </div>
    </div>

    <!-- 分组导航抽屉 -->
    <el-drawer
      title="分组导航"
      :visible.sync="showNav"
      direction="rtl"
      size="400px"
      :modal-append-to-body="false"
    >
      <div class="drawer-content">
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="搜索分组"
            prefix-icon="el-icon-search"
            clearable
            size="small"
          />
        </div>
        <div class="nav-list">
          <div
            v-for="group in filteredGroups"
            :key="group.renderKey"
            class="nav-item"
            :class="{ active: currentGroupIndex == groups.findIndex(g => g.renderKey == group.renderKey) }"
            @click="handleNavItemClick(groups.findIndex(g => g.renderKey == group.renderKey))"
          >
            {{ group.groupName }}
          </div>
        </div>
      </div>
    </el-drawer>

    <div class="content" ref="groupContent">
      <!-- 添加空状态显示 -->
      <el-empty 
        v-if="groups.length == 0"
        description="暂无分组配置"
        class="no-group-empty"
      >
        <el-button type="primary" @click="handleAddGroup">
          新增分组
        </el-button>
      </el-empty>

      <!-- 原有的分组列表显示 -->
      <div v-for="(group, groupIndex) in groups" :key="group.renderKey" class="group-wrapper" :id="'group-' + groupIndex">
        <el-collapse v-model="activeGroupIds">
          <el-collapse-item :name="group.id">
            <template slot="title">
              <div class="collapse-header">
                <div class="collapse-header-left">
                  <i :class="activeGroupIds.includes(group.id) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"></i>
                  <span>{{ group.groupName }}</span>
                </div>
                <div class="header-actions">
                  <el-button type="text" @click.stop="handleEditGroupName(groupIndex)">编辑</el-button>
                  <el-button 
                    type="text" 
                    style="color: #67C23A;"
                    @click.stop="handleSaveConfig(groupIndex)"
                  >
                    保存配置
                  </el-button>
                </div>
              </div>
            </template>
            <div class="group-content">
              <!-- 使用渠道选择器组件 -->
              <channel-selector
                v-model="group.channelIds"
                :selected-channels="getOtherGroupsSelectedChannels(groupIndex)"
                :channel-list="channelList"
              />

              <!-- 四个平台展示区域 -->
              <div class="platforms-container">
                <el-row :gutter="20" style="margin-bottom: 20px;">
                  <el-col :span="12">
                    <!-- 使用表单平台组件 -->
                    <platform-group
                      v-model="group.formPlatformGroup"
                      :group-key="group.renderKey"
                      type="formPlatformGroup"
                      title="表单平台"
                      :platform-list="platformList"
                    />
                  </el-col>
                  <el-col :span="12">
                    <!-- 使用半流程平台组件 -->
                    <half-platform-group
                      v-model="group.halfPlatformGroup"
                      :group-key="group.renderKey"
                      :half-product-list="halfProductList"
                    />
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <!-- 使用企微平台组件 -->
                    <platform-group
                      v-model="group.qwPlatformGroup"
                      :group-key="group.renderKey"
                      type="qwPlatformGroup"
                      title="企微平台"
                      :platform-list="platformList"
                    />
                  </el-col>
                  <el-col :span="12">
                    <!-- 使用贷超平台组件 -->
                    <platform-group
                      v-model="group.dcPlatformGroup"
                      :group-key="group.renderKey"
                      type="dcPlatformGroup"
                      title="贷超平台"
                      :platform-list="platformList"
                    />
                  </el-col>
                </el-row>
              </div>
              <div class="card-footer">
                <el-button
                  type="primary"
                  @click="handleSaveConfig(groupIndex)"
                >
                  保存配置
                </el-button>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 使用分组名称弹窗组件 -->
    <group-name-dialog
      :visible.sync="groupNameDialogVisible"
      :edit-data="currentEditGroupData"
      @save="handleSaveGroupName"
    />
  </div>
</template>

<script>
import { getPlatformList } from "@/api/xmxrChannelManage/channelList";
import { getChannelList } from '@/api/productManage/product'
import { getHalfPlatformProduct } from '@/api/distributeStatistics/halfProcess';
import {
  getChannelPlatformGroupList, 
  addChannelPlatformGroup, 
  updateChannelPlatformGroup 
} from "@/api/platformProductManagement/platformProductConfig";
import { v4 as uuidv4 } from 'uuid';
import ChannelSelector from './components/ChannelSelector.vue';
import HalfPlatformGroup from './components/HalfPlatformGroup.vue';
import PlatformGroup from './components/PlatformGroup.vue';
import GroupNameDialog from './components/GroupNameDialog.vue';
import { throttle } from 'lodash';

export default {
  name: 'PlatformProductConfigPro',
  components: {
    ChannelSelector,
    HalfPlatformGroup,
    PlatformGroup,
    GroupNameDialog
  },
  data() {
    return {
      // 分组数据
      groups: [],
      // 分组名称弹窗相关数据
      groupNameDialogVisible: false,
      currentEditGroupIndex: -1,
      currentEditGroupData: {},
      // 所可选平台列表
      platformList: [],
      currentGroupIndex: 0, // 当前激活的分组引
      searchQuery: '', // 搜索关键词
      observer: null, // Intersection Observer实例
      channelList: [], // 渠道列表数据
      halfProductList: [], // 半流程产品列表数据
      isManualScrolling: false, // 添加手动滚动标记
      showNav: false, // 控制导航菜单显示/隐藏
      activeGroupIds: [], // 控制分组展开的数组
    }
  },
  computed: {
    // 过滤后的分组列表
    filteredGroups() {
      if (!this.searchQuery) {
        return this.groups
      }
      const query = this.searchQuery.toLowerCase()
      return this.groups.filter(group => 
        group.groupName.toLowerCase().includes(query)
      )
    }
  },
  created() {
    this.fetchPlatformList()
    this.fetchChannelPlatformGroupList()
    this.fetchChannelList()
    this.fetchHalfProductList() // 添加获取半流程产品列表的调用
  },

  mounted() {
    // 创建节流后的滚动处理函数
    this.throttledHandleScroll = throttle(this._handleScroll, 200)
    this.initGroupScroll()
  },

  beforeDestroy() {
    this.destroyGroupScroll()
  },

  beforeRouteLeave(to, from, next) {
    this.$confirm('确认离开页面？未保存的内容将会丢失', '提示', {
      confirmButtonText: '离开',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      next()
    }).catch(() => {
      next(false)
    })
  },

  methods: {
    // 初始化分组滚动
    initGroupScroll() {
      const el = this.$refs.groupContent
      el.addEventListener('scroll', this.throttledHandleScroll)
    },

    // 处理组滚动的具体实现
    _handleScroll() {
      // 如果是手动滚动，不处理高亮更新
      if (this.isManualScrolling) return

      const container = this.$refs.groupContent;
      const containerTop = container.getBoundingClientRect().top;
      let closestGroup = null;
      let minDistance = Infinity;

      // 使用 children 替 querySelectorAll，性能更好
      Array.from(container.children).forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        const distance = Math.abs(rect.top - containerTop);
        
        if (distance < minDistance) {
          minDistance = distance;
          closestGroup = index;
        }
      });

      if (closestGroup !== null && this.currentGroupIndex !== closestGroup) {
        this.currentGroupIndex = closestGroup;
      }
    },

    // 销毁分组滚动
    destroyGroupScroll() {
      const el = this.$refs.groupContent
      if (el) {
        el.removeEventListener('scroll', this.throttledHandleScroll)
      }
      // 取消未行的节流函数
      if (this.throttledHandleScroll && this.throttledHandleScroll.cancel) {
        this.throttledHandleScroll.cancel()
      }
    },

    // 获取其他分组已选择的渠道
    getOtherGroupsSelectedChannels(currentGroupIndex) {
      const selectedChannels = new Set()
      this.groups.forEach((group, index) => {
        if (index !== currentGroupIndex) {
          group.channelIds.forEach(channelId => {
            selectedChannels.add(channelId)
          })
        }
      })
      return selectedChannels
    },

    // 检查分组名称是否重复
    checkGroupNameDuplicate(groupName, excludeIndex = -1) {
      return this.groups.some((group, index) =>
        group.groupName == groupName && index !== excludeIndex
      )
    },

    // 处理编辑分组名称
    handleEditGroupName(groupIndex) {
      this.currentEditGroupIndex = groupIndex
      this.currentEditGroupData = {
        groupName: this.groups[groupIndex].groupName,
        renderKey: this.groups[groupIndex].renderKey
      }
      this.groupNameDialogVisible = true
    },

    // 更新分组数据
    async updateGroupData(groupIndex, newData = {}) {
      const group = this.groups[groupIndex]
      const params = {
        id: group.id,
        groupName: group.groupName,
        channelIds: group.channelIds.join(','),
        formPlatformGroup: JSON.stringify(group.formPlatformGroup),
        halfPlatformGroup: JSON.stringify(group.halfPlatformGroup),
        qwPlatformGroup: JSON.stringify(group.qwPlatformGroup),
        dcPlatformGroup: JSON.stringify(group.dcPlatformGroup),
        groupType: 1,
        ...newData
      }
      
      await updateChannelPlatformGroup(params)
    },

    // 处理保存分组名称
    async handleSaveGroupName(formData, callback) {
      // 检查分组名称是否重复
      if (this.checkGroupNameDuplicate(formData.groupName, this.currentEditGroupIndex)) {
        this.$message.error('分组名称已存在')
        callback(false)
        return
      }

      try {
        if (this.currentEditGroupIndex == -1) {
          // 新增分组
          await addChannelPlatformGroup({ groupName: formData.groupName, groupType: 1 })
          
          // 重新获取列表
          await this.fetchChannelPlatformGroupList()
          this.$message.success('添加分组成功')

          // 找到新添加的分组并展开它
          const newGroup = this.groups.find(group => group.groupName == formData.groupName)
          if (newGroup) {
            this.activeGroupIds.push(newGroup.id)
            this.$nextTick(() => {
              const newGroupIndex = this.groups.length - 1
              if (newGroupIndex >= 0) {
                setTimeout(() => {
                  this.scrollToGroup(newGroupIndex)
                }, 300)
              }
            })
          }
        } else {
          // 编辑分组名称
          await this.updateGroupData(this.currentEditGroupIndex, { groupName: formData.groupName, groupType: 1 })
          
          // 重新获取列表
          await this.fetchChannelPlatformGroupList()
          this.$message.success('编辑分组名称成功')
        }
        callback(true)
      } catch (error) {
        callback(false)
      }
    },

    // 处理添加新分组
    handleAddGroup() {
      this.currentEditGroupIndex = -1
      this.currentEditGroupData = {}
      this.groupNameDialogVisible = true
    },

    // 获取平台列表
    async fetchPlatformList() {
      const res = await getPlatformList()
      if (res.data) {
        this.platformList = res.data
      }
    },

    // 保存配置
    async handleSaveConfig(groupIndex) {
      try {
        await this.updateGroupData(groupIndex)
        this.$message.success('保存配置成功')
        this.fetchChannelPlatformGroupList()
      } catch (error) {
        // 错误已在拦截器中处理
      }
    },

    // 滚动到指定分组
    scrollToGroup(index) {
      if (index == -1) return // 如果找不到对应的分组，直接返回
      
      // 设置手动滚动标记
      this.isManualScrolling = true
      
      // 直接更新高亮状态
      this.currentGroupIndex = index

      const element = document.getElementById(`group-${index}`)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' })
        
        // 滚动结束后重置标记
        setTimeout(() => {
          this.isManualScrolling = false
        }, 1000) // 设置一个比滚动动画稍长的时间
      }
    },

    // 获取渠道平台分组列表
    async fetchChannelPlatformGroupList() {
      const res = await getChannelPlatformGroupList({
        groupType: 1
      })
      if (res.data) {
        // 将后端数据转换为前端所需格式
        const processedGroups = res.data.map(item => {
          return {
            id: item.id,
            renderKey: uuidv4(),
            groupName: item.groupName,
            channelIds: item.channelIds ? item.channelIds.split(',') : [],
            channelCollapseActive: ['1'],
            formPlatformGroup: item.formPlatformGroup || [],
            halfPlatformGroup: item.halfPlatformGroup || [],
            qwPlatformGroup: item.qwPlatformGroup || [],
            dcPlatformGroup: item.dcPlatformGroup || []
          }
        })
        
        this.groups = processedGroups
        
        // 如果是首次加载，默认展开第一个分组
        if (this.activeGroupIds.length == 0 && processedGroups.length > 0) {
          this.activeGroupIds = [processedGroups[0].id]
        }
        
        this.$forceUpdate()
      }
    },

    // 获取渠道列表
    async fetchChannelList() {
      const res = await getChannelList()
      if (res.data) {
        this.channelList = res.data.map(item => ({
          ...item,
          renderLabel: `${item.channelName}-${item.id}`,
          id: String(item.id)
        }))
      }
    },

    // 获取半流程产品列表
    async fetchHalfProductList() {
      const res = await getHalfPlatformProduct({ type: 1 })
      if (res.data) {
        this.halfProductList = res.data.map(item => ({
          ...item,
          renderKey: uuidv4(),
          renderLabel: `${item.platform}-${item.productName}-${item.productId}`
        }))
      }
    },

    // 处理导航项点击
    handleNavItemClick(index) {
      this.scrollToGroup(index)
      this.showNav = false
    },

    // 滚动到顶部
    scrollToTop() {
      const container = this.$refs.groupContent
      if (container) {
        container.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.platform-product-config-pro {
  height: calc(100vh - 90px);
  background-color: transparent;
  padding: 0;
  display: flex;
  gap: 20px;
  position: relative;

  &:has(.no-group-empty) {
    background-color: #fff;
  }

  .fixed-actions {
    position: fixed;
    right: 24px;
    bottom: 24px;
    z-index: 99;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .action-btn {
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.25s ease;
      background-color: #fff;
      color: #909399;
      border-radius: 50%;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      i {
        font-size: 18px;
      }

      &:hover {
        color: #fff;
        background-color: #004DAB;
        box-shadow: 0 6px 16px rgba(103, 194, 58, 0.3);
      }
    }
  }

  .drawer-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px;

    .search-box {
      margin-bottom: 16px;

      .el-input {
        ::v-deep .el-input__inner {
          border-radius: 4px;
          
          &:hover {
            border-color: #DCDFE6;
          }
          
          &:focus {
            border-color: #004DAB;
          }
        }
      }
    }

    .nav-list {
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 8px;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #E4E7ED;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      .nav-item {
        padding: 8px 12px;
        cursor: pointer;
        font-size: 14px;
        color: #606266;
        border-radius: 4px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        user-select: none;

        &:hover {
          color: #004DAB;
          background-color: #ecf5ff;
        }

        &.active {
          color: #fff;
          background-color: #004DAB;
          font-weight: 500;
        }
      }
    }
  }

  .content {
    flex: 1;
    overflow-y: auto;

    // 添加空状态样式
    ::v-deep .no-group-empty {
      margin-top: 120px;

      .el-empty__description {
        cursor: pointer;
        color: #606266;
        
        &:hover {
          color: #004DAB;
        }
      }

      .el-button {
        margin-top: 20px;
      }
    }

    .group-wrapper {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      ::v-deep .el-collapse-item__header {
        padding: 12px 20px;
        background-color: #fff;
        border: none;
        
        .collapse-header {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .collapse-header-left {
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }

        .header-actions {
          
        }

        .el-collapse-item__arrow {
          display: none;
        }
      }

      ::v-deep .el-collapse-item__wrap {
        background-color: #fff;
        border: none;
      }

      ::v-deep .el-collapse-item__content {
        padding: 0 20px 20px;
      }
    }

    .card-footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
  }
}
</style>
