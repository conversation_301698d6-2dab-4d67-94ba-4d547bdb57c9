<template>
  <div class="loan-quota-container">
    <!-- 左侧预览区域 -->
    <div class="preview-area">
      <Preview ref="previewRef" :config-data="configData" />
    </div>

    <!-- 右侧控制面板 -->
    <div class="config-panel">
      <Controller
        @capture-screenshot="handleCapture"
        :preview-url="screenshotPreviewUrl"
        :config-data="configData"
        @update:config="updateConfig"
      />
    </div>
  </div>
</template>

<script>
import Preview from './components/preview.vue'
import Controller from './components/controller.vue'

export default {
  name: '<PERSON>anQuota', // 修改组件名称
  components: {
    Preview,
    Controller
  },
  data() {
    return {
      screenshotPreviewUrl: null,
      configData: { // 初始化基本的configData结构
        device: {
          os: 'ios'
        },
        statusBar: {
          time: '9:41',
          showSignal: true,
          showWifi: true,
          showBattery: true,
          batteryLevel: 100,
          mode: 'dark' // 将默认模式改为暗色
        },
        // 贷款额度相关的配置项
        loanAmount: '2,094.01',  // 贷款金额
        reserveDesc: '已获得额外专享额度',  // 备用金描述
        couponDesc: '提升可用额度',  // 额度券描述
        otherQuotas: [  // 其他额度列表
          {
            name: '借呗',
            desc: '最高可借20万',
            iconUrl: 'https://jst.oss-utos.hmctec.cn/common/path/1dc0f81721054d14b5c61dbcf1af7516.png'
          },
          {
            name: '备用金',
            desc: '到账快 不用愁',
            iconUrl: 'https://jst.oss-utos.hmctec.cn/common/path/698360b918dd43a6be7f2bcc28dd2d72.png'
          },
          {
            name: '网商贷',
            desc: '随借随还秒到账',
            iconUrl: 'https://jst.oss-utos.hmctec.cn/common/path/7cab2ba324ab41ff832c40f76d56d004.png'
          }
        ]
      }
    };
  },
  methods: {
    async handleCapture() {
      if (this.$refs.previewRef) {
        // 假设preview组件也有captureScreenshot方法
        const dataUrl = await this.$refs.previewRef.captureScreenshot(); 
        if (dataUrl) {
          this.screenshotPreviewUrl = dataUrl;
        } else {
          this.screenshotPreviewUrl = null;
        }
      } else {
        console.error('Preview component reference not found.');
      }
    },
    updateConfig(newConfig) {
      // 使用Object.assign进行浅合并，或者根据需要进行深合并
      this.configData = Object.assign({}, this.configData, newConfig);
    }
  }
}
</script>

<style lang="scss" scoped>
.loan-quota-container { // 修改类名
  display: flex;
  background-color: #fff;

  .preview-area {
    padding: 20px;
    background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    background-color: #ffffff;
    height: max-content;
  }

  .config-panel {
    flex: 1;
    margin-left: 20px;
    padding: 20px;
    overflow-y: auto;
  }
}
</style> 