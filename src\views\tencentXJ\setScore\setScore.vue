<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" class="demo-form-inline">
      <el-form-item label="手机号">
        <el-input v-model.trim="queryParams.phone" placeholder="请输入手机号"></el-input>
      </el-form-item>
      <!-- 新增姓名筛选输入框 -->
      <el-form-item label="姓名">
        <el-input v-model.trim="queryParams.name" placeholder="请输入姓名"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="name" label="姓名"></el-table-column>
      <el-table-column prop="phone" label="手机号"></el-table-column>
      <el-table-column prop="progressTotal" label="进度百分比">
        <template #default="scope">
          {{ scope.row.progressTotal }}%
        </template>
      </el-table-column>
      <!-- 新增操作列 -->
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
      :current-page="queryParams.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size="queryParams.pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="total" style="margin-top: 20px;">
    </el-pagination>

    <!-- 新增编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="1000px">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="手机号">
          <el-input v-model.trim="editForm.phone" :disabled="editMode" placeholder="请输入手机号" maxlength="11"></el-input>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model.trim="editForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="评分项目">
          <el-button type="primary" size="small" @click="addItem">新增行</el-button>
          <el-table :data="editForm.itemProgressConfigList" border style="width: 100%; margin-top: 10px;">
            <el-table-column label="项目名称">
              <template #default="scope">
                <el-input v-model.trim="scope.row.itemName" placeholder="请输入项目名称" maxlength="20"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="项目进度">
              <template #default="scope">
                <el-input-number v-model="scope.row.itemProgress" :min="0" :max="100" step-strictly :step="1" placeholder="请输入项目进度"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="是否已拥有">
              <template #default="scope">
                <el-switch v-model="scope.row.hasProgress"></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button type="danger" size="small" @click="removeItem(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveEdit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { fetchPhoneScoreConfigPage, savePhoneScoreConfig } from '@/api/tencentXJ/score'
import test from '@/utils/test'
import { sum } from '@/utils/calculate'

export default {
  name: 'SetScore',
  data() {
    return {
      queryParams: {
        phone: '',
        name: '', // 新增姓名字段
        pageNum: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      dialogVisible: false,
      editForm: {
        id: null,
        name: '',
        phone: '',
        status: 1,
        itemProgressConfigList: []
      },
      dialogTitle: '',
      editMode: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const response = await fetchPhoneScoreConfigPage(this.queryParams)
      if (response.code === 200) {
        this.tableData = response.rows
        this.total = response.total
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.queryParams = {
        phone: '',
        name: '', // 重置姓名字段
        pageNum: 1,
        pageSize: 10
      }
      this.getList()
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    handleAdd() {
      this.dialogVisible = true
      this.dialogTitle = '新增评分信息'
      this.editMode = false
      this.editForm = {
        name: '', // 新增姓名字段
        phone: '',
        status: 1,
        itemProgressConfigList: [
          {
            itemName: '网贷行为记录',
            itemProgress: 20,
            hasProgress: true
          },
          {
            itemName: '身份核实',
            itemProgress: 20,
            hasProgress: true
          },
          {
            itemName: '守信行为记录',
            itemProgress: 20,
            hasProgress: true
          },
          {
            itemName: '金融行为记录',
            itemProgress: 40,
            hasProgress: false
          }
        ]
      }
    },
    handleEdit(row) {
      this.dialogVisible = true
      this.dialogTitle = '编辑评分信息'
      this.editMode = true
      const itemProgressConfigList = row.itemProgressConfigList ? JSON.parse(JSON.stringify(row.itemProgressConfigList)) : []
      this.editForm = {
        id: row.id,
        name: row.name || '', // 编辑时填充姓名字段
        phone: row.phone,
        status: row.status || 1,
        itemProgressConfigList
      }
    },
    addItem() {
      this.editForm.itemProgressConfigList.push({
        itemName: '',
        itemProgress: 0,
        hasProgress: false
      })
    },
    removeItem(index) {
      this.editForm.itemProgressConfigList.splice(index, 1)
    },
    async saveEdit() {
      // 验证手机号
      if (!this.editForm.phone) {
        this.$message.error('请输入手机号')
        return
      }

      if (!test.mobile(this.editForm.phone)) {
        this.$message.error('请输入正确的手机号')
        return
      }

      if (this.editForm.itemProgressConfigList.length === 0) {
        this.$message.error('请添加评分项目')
        return
      }

      // 验证 itemProgressConfigList 中的每个项目
      for (const item of this.editForm.itemProgressConfigList) {
        if (!item.itemName) {
          this.$message.error('请填写完整的项目信息')
          return
        }
      }

      // 所有项目进度百分比之和不能超过100%
      const total = sum(this.editForm.itemProgressConfigList.map(item => item.itemProgress))
      if (total > 100) {
        this.$message.error('项目进度百分比之和不能超过100%')
        return
      }

      // 创建请求参数
      const requestData = { ...this.editForm }
      if (!requestData.name) {
        delete requestData.name // 如果名字为空，则删除该字段
      }

      const response = await savePhoneScoreConfig(requestData)
      if (response.code === 200) {
        this.$message.success(this.editMode ? '保存成功' : '新增成功')
        this.dialogVisible = false
        this.getList()
      }
    }
  }
}
</script>

<style scoped>
.set-score-container {
  padding: 20px;
}
</style>
