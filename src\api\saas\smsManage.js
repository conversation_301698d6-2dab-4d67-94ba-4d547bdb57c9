import request from '@/utils/request'
//查询短信模板列表
export function getSmsTemplateList(data) {
    return request({
        url: "/saas/sms/template/getList",
        method: 'get',
        params: data
    })
}
//查询短信模板列表
export function addSmsTemplateOne(data) {
    return request({
        url: "/saas/sms/template/addSmsTemplate",
        method: 'post',
        data
    })
}

//查询配置列表
export function getConfigList() {
    return request({
        url: "/saas/sms/template/getConfigList",
        method: "get"
    })
}
//查询配置列表
export function editSmsTemplateOne(data) {
    return request({
        url: "/saas/sms/template/updateSmsTemplate",
        method: "post",
        data
    })
}