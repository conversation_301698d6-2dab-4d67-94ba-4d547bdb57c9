<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <!-- <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="状态"
          clearable
          size="small"
        >
          <el-option value="" label="全部"></el-option>
          <el-option value="1" label="正常"></el-option>
          <el-option value="2" label="禁用"></el-option>
        </el-select>
      </el-form-item> -->

      <el-form-item label="渠道名称" prop="channelName">
        <el-input
          clearable
          placeholder="请输入渠道名称"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道ID" prop="channelId">
        <el-input
          clearable
          placeholder="请输入渠道ID"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model.number="queryParams.channelId"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 渠道列表 -->

    <el-table :data="channelList">
      <el-table-column label="渠道ID" prop="id" align="center" />
      <el-table-column label="渠道名称" prop="channelName" align="center" />

      <el-table-column
        label="企微忽略状态"
        prop="cityCrashStatus"
        align="center"
      >
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-model="row.qwCityStatus"
              :active-text="row.qwCityStatus == 1 ? '开启' : ''"
              :inactive-text="row.qwCityStatus == 2 ? '关闭' : ''"
              :active-value="1"
              :inactive-value="2"
              @change="changeQwCityStatus($event, row)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="忽略城市" prop="qwCityIgnores" align="center">
        <template slot-scope="{ row }">
          <template v-if="row.qwCityIgnores && row.qwCityIgnores.length > 20">
            <el-tooltip placement="top">
              <template slot="content">
                <p style="max-width: 500px">{{ row.qwCityIgnores }}</p>
              </template>
              <div class="info-mes">{{ row.qwCityIgnores }}</div>
            </el-tooltip>
          </template>
          <div v-else>{{ row.qwCityIgnores }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="cityCrashStatus" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button type="text" @click="handleOpen(row)">
              修改城市</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="修改企微限制城市"
      :visible.sync="avisible"
      @close="cancel"
      width="900px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formData" :model="formData">
        <el-form-item label="城市">
          <el-cascader
            v-model="formData.city"
            :options="cityList"
            clearable
            filterable
            :props="{ multiple: true, emitPath: false }"
            style="width: 100%"
          >
          </el-cascader>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getQwCityList,
  editQwCityStatus,
  editQwCityText,
} from "@/api/xmxrChannelManage/qwCity";
import { getCityList } from "@/api/statisticalManage";
export default {
  name: "qwCityManage",
  data() {
    return {
      total: 0,
      avisible: false,
      cityList: [],
      formData: {},
      row: {},
      channelList: [],
      queryParams: {
        status: "",
        commerce: "",
        channelName: "",
        partyUsername: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    getList() {
      getQwCityList(this.queryParams).then((res) => {
        this.channelList = res.rows;
        this.total = res.total;
      });
    },
    //查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    changeQwCityStatus(e, row) {
      this.$confirm(
        `确定${row.cityCrashStatus == 2 ? "关闭" : "启用"}吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          editQwCityStatus({ id: row.id, status: e })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              this.getList();
            });
        })
        .catch((err) => {
          this.getList();
        });
    },
    handleOpen(row) {
      this.avisible = true;
      this.row = row;
      let data = [];
      if (row.qwCityIgnores) {
        row.qwCityIgnores.split(",").map((item) => {
          if (item.substr(-1) != "市") {
            data.push(item + "市");
          } else {
            data.push(item);
          }
        });
      }

      this.formData.city = data;
    },
    submitForm() {
      let data = [];
      this.formData.city.map((item) => {
        if (item.substr(-1) == "市") {
          data.push(item.substr(0, item.length - 1));
        } else {
          data.push(item);
        }
      });
      console.log(data.join(","));

      editQwCityText({ id: this.row.id, text: data.join(",") }).then((res) => {
        this.avisible = false;
        this.$message({
          message: "操作成功",
          type: "success",
        });
        this.getList();
      });
    },
    cancel() {
      this.formData.city = [];
      this.avisible = false;
    },
  },

  async mounted() {
    this.getList();
    getCityList().then((res) => {
      let data = null;
      data = res.data.map((item) => {
        if (item.citys) {
          {
            return {
              value: item.name,
              label: item.name,
              disabled: false,
              children: [
                ...item.citys.map((citem) => {
                  return {
                    value: citem.name,
                    label: citem.name,
                    disabled: false,
                  };
                }),
              ],
            };
          }
        } else {
          return {
            value: item.name,
            label: item.name,
            disabled: false,
          };
        }
      });
      this.cityList = JSON.parse(JSON.stringify(data));
    });
  },
};
</script>

<style lang="scss" scoped>
.el-switch__label.el-switch__label--left.is-active {
  color: #333;
}
.el-input.el-input--medium {
  text-align: center !important;
}

::v-deep .el-cascader__tags {
  max-height: 300px;
  overflow-y: scroll;
}
::v-deep .el-cascader .el-input__inner {
  max-height: 300px;
  overflow-y: scroll;
}

::v-deep .el-cascader .el-cascader__search-input {
  max-height: 300px;
  overflow-y: scroll;
}

::v-deep .el-cascader__tags::-webkit-scrollbar {
  width: 0;
}
.info-mes {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
</style>
