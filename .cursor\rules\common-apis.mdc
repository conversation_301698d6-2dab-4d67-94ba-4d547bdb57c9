---
description: 
globs: 
alwaysApply: true
---
# 常用 API 接口

项目中常用的 API 接口定义位于 `src/api` 目录下，按功能模块划分。

## 常用接口示例

### 获取所有平台列表
```js
// 路径：@/api/xmxrChannelManage/channelList.js
import { getPlatformList } from '@/api/xmxrChannelManage/channelList'
```

### 获取所有渠道列表
```js
// 路径：@/api/channeManage/channelList.js
import { getAllChannelList } from '@/api/channeManage/channelList'

// 返回数据结构：
// {
//   // 渠道ID
//   "id": 1,
//   // 渠道名称
//   "channelName": "渠道名称"
// }
```

### 字典数据相关接口
```js
// 路径：@/api/system/dict/data.js
import { getDicts } from "@/api/system/dict/data";

// 使用示例：
getDicts("sys_normal_disable").then(response => {
  this.statusOptions = response.data;
});
```

### 通用下载方法
```js
// 路径：@/utils/request.js
import { download } from '@/utils/request'

// 使用示例：
download('/system/user/export', {
  params
}, filename)
```

## API 请求规范

1. 所有 API 请求应在 `src/api` 目录下定义
2. 按功能模块组织文件结构
3. 导出命名应具有描述性，表明操作类型和操作对象
4. 使用预配置的 axios 实例进行请求
5. 返回 Promise 对象，让调用方处理响应数据
6. 统一响应格式：
   ```js
   // 成功响应
   {
     code: 200,
     msg: "操作成功",
     data: {} // 实际数据
   }
   
   // 失败响应
   {
     code: 500, // 或其他错误码
     msg: "错误信息"
   }
   ```

