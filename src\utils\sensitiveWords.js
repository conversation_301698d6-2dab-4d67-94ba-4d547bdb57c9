import { sensitiveWordList } from '@/const/sensitiveWordList';

/**
 * 一个简单的敏感词检查器。
 */
class SensitiveWordChecker {
  constructor() {
    this.wordList = new Set(sensitiveWordList);
    // 创建一个正则表达式来检查任何敏感词。
    // 这是一个简单的实现。一个更健壮的解决方案可能会使用更高级的算法
    // (例如 Aho-Corasick 自动机) 以在处理大型词汇表时获得更好的性能。
    this.regex = new RegExp(sensitiveWordList.join('|'), 'i');
  }

  /**
   * 检查给定文本是否包含任何敏感词。
   * @param {string} text 要检查的文本。
   * @returns {boolean} 如果文本包含敏感词，则返回 true，否则返回 false。
   */
  contains(text) {
    if (!text) {
      return false;
    }
    return this.regex.test(text);
  }

  /**
   * 查找给定文本中的所有敏感词。
   * @param {string} text 要检查的文本。
   * @returns {string[]} 在文本中找到的敏感词数组。
   */
  findAll(text) {
    if (!text) {
      return [];
    }
    const matches = text.match(this.regex);
    return matches || [];
  }
}

const checker = new SensitiveWordChecker();

export default checker; 