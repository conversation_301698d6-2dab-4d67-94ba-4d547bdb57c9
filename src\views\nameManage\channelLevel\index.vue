<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item label="渠道id">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入渠道id"
          size="small"
          clearable
          v-model="queryParams.channelId"
        ></el-input>
      </el-form-item>
      <el-form-item label="产品ID">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入产品ID"
          size="small"
          clearable
          v-model.number="queryParams.productId"
        ></el-input>
      </el-form-item>
      <el-form-item label="推送时间" prop="pushDate">
        <el-date-picker
          v-model="queryParams.pushDate"
          type="date"
          size="small"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          placeholder="选择日期"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border>
      <el-table-column
        label="渠道ID"
        align="center"
        prop="id"
      ></el-table-column>
      <el-table-column
        label="推送时间"
        align="center"
        prop="pushDay"
      ></el-table-column>
      <el-table-column
        label="0星数量"
        align="center"
        prop="zeroStarNum"
      ></el-table-column>
      <el-table-column
        label="1星数量"
        align="center"
        prop="oneStarNum"
      ></el-table-column>
      <el-table-column
        label="2星数量"
        align="center"
        prop="twoStarNum"
      ></el-table-column>

      <el-table-column
        label="3星数量"
        align="center"
        prop="threeStarNum"
      ></el-table-column>
      <el-table-column
        label="4星数量"
        align="center"
        prop="fourStarNum"
      ></el-table-column>
      <el-table-column
        label="5星数量"
        align="center"
        prop="fiveStarNum"
      ></el-table-column>
      <el-table-column
        label="合计"
        align="center"
        prop="totalNum"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.id != '合计'">
            <el-button type="text" size="mini" @click="handleDetail(row)"
              >查看详情</el-button
            >
            <!-- <el-button type="text" @click="handleDetail(row)"></el-button> -->
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :visible.sync="dialogVisible"
      title="详情"
      width="80%"
      append-to-body
    >
      <el-table :data="detailList" border>
        <el-table-column
          label="产品ID"
          align="center"
          prop="id"
        ></el-table-column>
        <el-table-column
          label="产品名称"
          align="center"
          prop="productName"
        ></el-table-column>
        <el-table-column
          label="推送时间"
          align="center"
          prop="pushDay"
        ></el-table-column>
        <el-table-column
          label="0星数量"
          align="center"
          prop="zeroStarNum"
        ></el-table-column>
        <el-table-column
          label="1星数量"
          align="center"
          prop="oneStarNum"
        ></el-table-column>
        <el-table-column
          label="2星数量"
          align="center"
          prop="twoStarNum"
        ></el-table-column>

        <el-table-column
          label="3星数量"
          align="center"
          prop="threeStarNum"
        ></el-table-column>
        <el-table-column
          label="4星数量"
          align="center"
          prop="fourStarNum"
        ></el-table-column>
        <el-table-column
          label="5星数量"
          align="center"
          prop="fiveStarNum"
        ></el-table-column>
        <el-table-column
          label="合计"
          align="center"
          prop="totalNum"
        ></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { getStarRatingStat, getStarRatingStatDetails } from "@/api/nameManage";
export default {
  data() {
    return {
      total: 0,
      dialogVisible: false,
      dataList: [],
      detailList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        channelId: "",
        pushDate: "",
        productId: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now() ||
            time.getTime() < Date.now() - 3600 * 3 * 24 * 1000
          );
        },
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    getList() {
      getStarRatingStat(this.queryParams).then((res) => {
        this.dataList = res.data.pageResult.rows;
        if (this.dataList.length == 0) return;
        this.dataList.push({
          id: "合计",
          pushDay: "",
          oneStarNum: res.data.total.oneStarNum,
          twoStarNum: res.data.total.twoStarNum,
          threeStarNum: res.data.total.threeStarNum,
          fourStarNum: res.data.total.fourStarNum,
          fiveStarNum: res.data.total.fiveStarNum,
          zeroStarNum: res.data.total.zeroStarNum,
          totalNum: res.data.total.totalNum,
        });
        this.total = res.data.pageResult.total;
      });
    },
    handleDetail(row) {
      this.detailList = [];
      getStarRatingStatDetails({
        channelId: row.id,
        pushDate: this.queryParams.pushDate,
      }).then((res) => {
        this.detailList = res.data;
        this.dialogVisible = true;
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped></style>
