<template>
  <div class="android-status-bar" :class="`android-status-bar-${mode}`">
    <!-- 左侧：时间 -->
    <div class="android-status-bar-left">
      <div class="android-status-bar-time">{{ time }}</div>
    </div>
    <!-- 右侧：信号、WiFi、电池 -->
    <div class="android-status-bar-right">
      <img v-if="showSignal" class="android-status-bar-signal"
        :src="signalIconUrl">
      <img v-if="showWifi" class="android-status-bar-wifi"
        :src="wifiIconUrl">
      <div v-if="showBattery" class="android-status-bar-battery">
        <img class="android-status-bar-battery-img"
          :src="batteryIconUrl">
        <div class="android-status-bar-battery-text" :style="batteryLevelStyle"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AndroidStatusBar',
  props: {
    mode: {
      type: String,
      default: 'light',
      validator: (value) => ['light', 'dark'].includes(value)
    },
    time: {
      type: String,
      default: '12:30'
    },
    showSignal: {
      type: Boolean,
      default: true
    },
    showWifi: {
      type: Boolean,
      default: true
    },
    showBattery: {
      type: Boolean,
      default: true
    },
    batteryLevel: {
      type: Number,
      default: 100
    }
  },
  computed: {
    isDarkMode() {
      return this.mode === 'dark';
    },
    signalIconUrl() {
      return this.isDarkMode
        ? 'https://jst.oss-utos.hmctec.cn/common/path/4218dc241d484bacb48982747e159fd5.png'
        : 'https://jst.oss-utos.hmctec.cn/common/path/4e43cc0c8005468883a41c91ebf89d8a.png';
    },
    wifiIconUrl() {
      return this.isDarkMode
        ? 'https://jst.oss-utos.hmctec.cn/common/path/df80fb9c647a44f99ca0e4b9851ba2a1.png'
        : 'https://jst.oss-utos.hmctec.cn/common/path/5b8505a31b3743aabdf1b776b0a7ac88.png';
    },
    batteryIconUrl() {
      return this.isDarkMode
        ? 'https://jst.oss-utos.hmctec.cn/common/path/392ccb0131ec4d7096d9e06a060ae2cb.png'
        : 'https://jst.oss-utos.hmctec.cn/common/path/520b3630f52c4cf691da70d7e1ba63fe.png';
    },
    batteryLevelStyle() {
      // 电池图标内部的填充区域
      const maxWidth = 37; // 最大宽度(px)与CSS中保持一致
      const height = 15; // 高度(px)与CSS中保持一致
      // 电量计算
      const level = Math.max(0, Math.min(100, this.batteryLevel));
      const width = Math.max(0, Math.min(maxWidth, (level / 100) * maxWidth));
      return {
        width: `${width}px`,
        height: `${height}px`,
        backgroundColor: this.isDarkMode ? '#000000' : '#fff',
        borderRadius: '3px',
        position: 'absolute',
        top: '3px',
        left: '3px'
      };
    }
  }
}
</script>

<style scoped lang="scss">
.android-status-bar {
  position: relative;
  z-index: 1;
  width: 100%;
  padding: 20px 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .android-status-bar-left,
  .android-status-bar-right {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .android-status-bar-time {
    font-size: 28px;
  }

  .android-status-bar-signal {
    width: 34px;
    height: 27px;
  }

  .android-status-bar-wifi {
    width: 40px;
    height: 40px;
  }

  .android-status-bar-battery {
    position: relative;
    width: 47px;
    height: 21px;
    transform: translateY(3px);

    .android-status-bar-battery-img {
      width: 47px;
      height: 21px;
      position: absolute;
      top: 0;
      left: 0;
    }
    .android-status-bar-battery-text {
      position: absolute;
      top: 3px;
      left: 3px;
      width: 37px;
      height: 15px;
      border-radius: 3px;
    }
  }
}

// 添加暗色模式的样式
.android-status-bar-dark {
  .android-status-bar-time {
    color: #000000;
  }
}

// 亮色模式样式
.android-status-bar-light {
  .android-status-bar-time {
    color: #fff;
  }
}
</style> 