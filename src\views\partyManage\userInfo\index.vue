<template>
  <div class="content">
    <div class="tab" v-if="isXy">
      <div
        :class="['tab-item', tabIndex == 1 ? 'active' : '']"
        style="border-radius: 8px 0px 0px 8px"
        @click="toggleTab(1)"
      >
        线索信息
      </div>
      <div
        :class="['tab-item', tabIndex == 2 ? 'active' : '']"
        style="border-radius: 0px 8px 8px 0px"
        @click="toggleTab(2)"
      >
        全景雷达
      </div>
    </div>
    <div class="tab" v-else>
      <div class="tab-item active" style="border-radius: 8px">线索信息</div>
    </div>
    <div class="info" v-if="tabIndex == 1">
      <div class="userInfo">线索信息</div>
      <div class="userInfo-base">
        <div class="userInfo-base-item">
          <span>姓名</span>
          <div>{{ userInfo.name }}</div>
        </div>
        <div class="userInfo-base-item">
          <span>电话</span>
          <div style="width: 250px" class="btn-wrap">
            <section>{{ userInfo.phone }}</section>
            <el-button type="primary" size="mini" @click="getPhone"
              >获取线索</el-button
            >
          </div>
        </div>
        <div class="userInfo-base-item">
          <span>年龄</span>
          <div>{{ userInfo.age }}</div>
        </div>
        <div class="userInfo-base-item">
          <span>性别</span>
          <div>{{ userInfo.sex == "0" ? "男" : "女" }}</div>
        </div>
      </div>
      <div class="userInfo" style="margin-top: 20px">基础信息</div>
      <div class="base">
        <div class="base-list">
          <div class="base-item">
            <span>职业</span>
            <div style="width: 260px">{{ userInfo.vocationName }}</div>
          </div>
          <div class="base-item">
            <span>地址</span>
            <div style="width: 260px">{{ userInfo.address }}</div>
          </div>
          <div class="base-item">
            <span>教育程度</span>
            <div style="width: 260px">{{ userInfo.educationName || "无" }}</div>
          </div>
        </div>
        <div class="base-list">
          <div class="base-item">
            <span>贷款目的</span>
            <div style="width: 260px">无</div>
          </div>
          <div class="base-item">
            <span>贷款额度</span>
            <div style="width: 260px">{{ userInfo.demandAmount }}</div>
          </div>
          <div class="base-item">
            <span>贷款期限</span>
            <div style="width: 260px">无</div>
          </div>
        </div>
      </div>
      <div class="userInfo" style="margin-top: 20px">资质信息</div>
      <div class="base">
        <div class="base-list">
          <div class="base-item">
            <span>社保缴纳</span>
            <div style="width: 260px">{{ userInfo.socialName }}</div>
          </div>
          <div class="base-item">
            <span>公积金缴纳</span>
            <div style="width: 260px">{{ userInfo.providentName }}</div>
          </div>
          <div class="base-item">
            <span>商业保险</span>
            <div style="width: 260px">{{ userInfo.insureName }}</div>
          </div>
        </div>
        <div class="base-list">
          <div class="base-item">
            <span>逾期记录</span>
            <div style="width: 260px">{{ userInfo.overdueName }}</div>
          </div>
          <div class="base-item">
            <span>房产信息</span>
            <div style="width: 260px">{{ userInfo.houseName }}</div>
          </div>
          <div class="base-item">
            <span>车辆信息</span>
            <div style="width: 260px">{{ userInfo.vehicleName }}</div>
          </div>
        </div>
        <div class="base-list">
          <div class="base-item">
            <span>车辆价值</span>
            <div style="width: 260px">无</div>
          </div>
          <div class="base-item">
            <span>车辆年限</span>
            <div style="width: 260px">无</div>
          </div>
          <div class="base-item">
            <span>车辆是否本人名下</span>
            <div style="width: 260px">无</div>
          </div>
        </div>
        <div class="base-list">
          <div class="base-item">
            <span>信用卡额度</span>
            <div style="width: 260px">{{ userInfo.creditName }}</div>
          </div>
          <div class="base-item">
            <span>微粒贷额度</span>
            <div style="width: 260px">{{ userInfo.particleName }}</div>
          </div>
          <div class="base-item">
            <span>京东白条额度</span>
            <div style="width: 260px">{{ userInfo.jdwhiteName }}</div>
          </div>
        </div>
        <div class="base-list">
          <div class="base-item">
            <span>花呗额度</span>
            <div style="width: 260px">{{ userInfo.tokioName }}</div>
          </div>
          <div class="base-item" v-if="userInfo.sesameName">
            <span>芝麻分</span>
            <div style="width: 260px">{{ userInfo.sesameName }}</div>
          </div>
          <div class="base-item" v-if="userInfo.sesameName">
            <!-- <span>芝麻分</span> -->
            <!-- <div style="width: 260px">{{ userInfo.tokioName }}</div> -->
          </div>
        </div>
      </div>
    </div>
    <div v-if="tabIndex == 2">
      <div class="card">
        <div class="titles">申请雷达报告详情</div>
        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              申请准入分(1-1000)
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ apply.a22160001 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              近1个月机构总查询笔数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ apply.a22160008 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              申请命中机构数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ apply.a22160003 }}
            </div></el-col
          >
        </el-row>
        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              最后一次查询时间
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ apply.a22160007 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              近3个月机构总查询笔数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ apply.a22160009 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              申请命中消金类机构数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ apply.a22160004 }}
            </div></el-col
          >
        </el-row>

        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              申请准入置信度(50-1000)
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ apply.a22160002 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              近6个月机构总查询笔数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ apply.a22160010 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              申请命中网络贷款类机构数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ apply.a22160005 }}
            </div></el-col
          >
        </el-row>
      </div>

      <div class="card m_top32">
        <div class="titles">行为雷达报告详情</div>
        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              贷款行为分(1-1000)
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170001 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              最后一次贷款放款时间
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170054 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              贷款已结清订单数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170052 }}
            </div></el-col
          >
        </el-row>
        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              最后一次履约距今天数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170050 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              信用贷款时长
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170053 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              正常还款订单比例
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170034 }}
            </div></el-col
          >
        </el-row>
      </div>

      <div class="card" style="margin-top: 10px">
        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item color_6363">行为时间</div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item color_6363">贷款笔数</div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item color_6363">贷款金额</div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item color_6363">贷款机构数</div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item color_6363">失败扣款笔数</div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item color_6363">履约贷款总金额</div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item color_6363">履约贷款次数</div></el-col
          >
        </el-row>
        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">近1个月</div></el-col
          >

          <el-col :span="3"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170002 }}
            </div></el-col
          >

          <el-col :span="3"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170007 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170016 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170035 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170040 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170045 }}
            </div></el-col
          >
        </el-row>
        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">近3个月</div></el-col
          >

          <el-col :span="3"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170003 }}
            </div></el-col
          >

          <el-col :span="3"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170008 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170017 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170036 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170041 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170046 }}
            </div></el-col
          >
        </el-row>
        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">近6个月</div></el-col
          >

          <el-col :span="3"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170004 }}
            </div></el-col
          >

          <el-col :span="3"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170009 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170018 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170037 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170042 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170047 }}
            </div></el-col
          >
        </el-row>
        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">近12个月</div></el-col
          >

          <el-col :span="3"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170005 }}
            </div></el-col
          >

          <el-col :span="3"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170010 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170019 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170038 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170043 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170048 }}
            </div></el-col
          >
        </el-row>
        <el-row :gutter="10" class="row color_6363">
          <el-col :span="4"
            ><div class="grid-item bg-f8f9">近24个月</div></el-col
          >

          <el-col :span="3"
            ><div class="grid-item bg-f8f9">
              {{ info.b22170006 }}
            </div></el-col
          >

          <el-col :span="3"
            ><div class="grid-item bg-f8f9">
              {{ info.b22170011 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-f8f9">
              {{ info.b22170020 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-f8f9">
              {{ info.b22170040 }}
            </div></el-col
          >
          <el-col :span="3"
            ><div class="grid-item bg-f8f9">
              {{ info.b22170044 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9">
              {{ info.b22170049 }}
            </div></el-col
          >
        </el-row>
      </div>

      <div class="card" style="margin-top: 10px">
        <el-row :gutter="10" class="row">
          <el-col :span="5"
            ><div class="grid-item bg-f8f9 color_6363">
              近12个月贷款金额
            </div></el-col
          >
          <el-col :span="5"
            ><div class="grid-item bg-f8f9 color_6363">1K及以下</div></el-col
          >
          <el-col :span="5"
            ><div class="grid-item bg-f8f9 color_6363">1K-3K</div></el-col
          >
          <el-col :span="5"
            ><div class="grid-item bg-f8f9 color_6363">3K-10K</div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">1W以上</div></el-col
          >
        </el-row>
        <el-row :gutter="10" class="row">
          <el-col :span="5"
            ><div class="grid-item bg-eff2 color_6363">贷款笔数</div></el-col
          >
          <el-col :span="5"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170012 }}
            </div></el-col
          >
          <el-col :span="5"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170013 }}
            </div></el-col
          >
          <el-col :span="5"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170014 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170015 }}
            </div></el-col
          >
        </el-row>
      </div>
      <div class="card m_top32">
        <div class="titles">逾期详情</div>
        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              近6个月M0+逾期贷款笔数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170025 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              近6个月M1+逾期贷款笔数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170028 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              近6个月累计逾期金额
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170031 }}
            </div></el-col
          >
        </el-row>

        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              近12个月M0+逾期贷款笔数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170026 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              近12个月M1+逾期贷款笔数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170029 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              近12个月累计逾期金额
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-eff2 color_6363">
              {{ info.b22170032 }}
            </div></el-col
          >
        </el-row>
        <el-row :gutter="10" class="row">
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              近24个月M0+逾期贷款笔数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170027 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              近24个月M1+逾期贷款笔数
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170029 }}
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              近24个月累计逾期金额
            </div></el-col
          >
          <el-col :span="4"
            ><div class="grid-item bg-f8f9 color_6363">
              {{ info.b22170033 }}
            </div></el-col
          >
        </el-row>
      </div>
    </div>
    <el-dialog
      title="获取手机号"
      :visible.sync="userAvisible"
      @close="cancel"
      width="900px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="150px"
      >
        <el-form-item label="手机号">
          <el-input v-model="phone" placeholder="手机号" />
        </el-form-item>
        <el-form-item label="用户状态" prop="status">
          <el-select
            v-model="formData.status"
            placeholder="状态"
            clearable
            size="small"
          >
            <el-option value="0" label="未联系"></el-option>
            <el-option value="1" label="感兴趣"></el-option>
            <el-option value="2" label="不感兴趣"></el-option>
            <el-option value="3" label="未接通"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="formData.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getReportDetail,
  getUserPhone,
  editUserPhoneStatus,
} from "@/api/partyManage";
export default {
  data() {
    return {
      isXy: true,
      tabIndex: 1,
      info: {},
      userInfo: {},
      apply: {},
      phone: "",
      userAvisible: false,
      rules: {
        status: [
          {
            required: true,
            message: "请选择状态",
            trigger: "blur",
          },
        ],
      },
      formData: {
        status: "",
        remark: "",
        id: "",
      },
    };
  },
  methods: {
    toggleTab(e) {
      this.tabIndex = e;
    },
    //获取手机号
    getPhone() {
      getUserPhone(this.userInfo.id).then((res) => {
        this.phone = res.data.phone;
        this.formData.id = res.data.id;
        this.formData.remark = res.data.remark;
        this.formData.status = res.data.status;
        this.userAvisible = true;
      });
    },
    cancel() {
      this.formData = {
        status: "",
        remark: "",
        id: "",
      };
      this.userAvisible = false;
      this.$refs.formData.resetFields();
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          editUserPhoneStatus(this.formData).then((res) => {
            if (res.code == 200) {
              this.$message.success("状态更新成功");
              this.cancel();
            }
          });
        }
      });
    },
  },
  mounted() {
    getReportDetail(this.$route.query.id).then((res) => {
      this.info = res.behavior || {};
      this.userInfo = res.pushUser;
      this.isXy = res.isXy;
      this.apply = res.apply || {};

    });
  },
};
</script>

<style lang="scss" scoped>
.content {
  font-family: Microsoft YaHei;
  width: 100%;
  min-height: calc(100vh - 50px);
  background: #eff2f9;
  padding: 20px;
}
.info {
  background-color: #fff;
  overflow: auto;
  width: 100%;
  border-radius: 20px;
  padding: 30px 60px 50px;
}
.tab {
  padding: 20px 0;
  background-color: #fff;
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  border-radius: 8px;
  &-item {
    width: 180px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    font-size: 16px;
    color: #999;
    background: #eff2f9;
    font-weight: bold;
    cursor: pointer;
  }

  & .active {
    background: #399ffb;
    color: #fff;
  }
}

.userInfo {
  font-size: 20px;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #353535;
  position: relative;
  &-base {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    &-item {
      margin-top: 20px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #636363;
      span {
        width: 140px;
        text-align: end;
      }
      & div {
        padding-left: 10px;
        margin-left: 10px;
        background: #eff2f9;
        line-height: 36px;
        width: 200px;
        height: 36px;
      }
    }
  }
}

.userInfo::before {
  content: "";
  display: block;
  position: absolute;
  width: 4px;
  height: 20px;
  left: -20px;
  top: 4px;
  background: #5374e9;
}
.base {
  &-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  &-item {
    flex: 1;
    margin-top: 20px;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #636363;
    span {
      width: 150px;
      text-align: end;
    }
    div {
      padding-left: 10px;
      margin-left: 10px;
      background: #eff2f9;
      line-height: 36px;
      width: 200px;
      height: 36px;
    }
  }
}
.card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px 12px;
  overflow: auto;

  .titles {
    font-size: 18px;
    font-weight: bold;
    color: #5374e9;
    padding: 0 0 18px;
  }
  .row {
    padding: 5px 0;
  }
  .grid-item {
    padding: 0 10px;
    height: 36px;
    line-height: 36px;
    font-size: 15px;
    cursor: pointer;
  }
}
.m_top32 {
  margin: 32px auto 0;
}
.m_top12 {
  margin: 12px auto 0;
}
.bg-f8f9 {
  background: #f8f9fb;
}
.bg-eff2 {
  background: #eff2f9;
}

.color_6363 {
  color: #636363;
}

.color_009b7 {
  color: #09b79b;
}
.f_weight {
  font-weight: bold;
}
::v-deep .table_td {
  border: none !important;
}
.btn-wrap {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 4px;
}
@media (max-width: 768px) {
  ::v-deep .el-row {
    width: 100%;
  }
  ::v-deep .el-row .el-col {
    width: 100%;
  }
}
</style>
