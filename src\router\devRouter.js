
import Layout from '@/layout'

export default [
  //   // src\views\materialLibrary\customScreenshot\index.vue
  //   {
  //       path: '/customScreenshot',
  //       component: Layout,
  //       children: [{
  //           path: 'index',
  //           component: () => import('@/views/materialLibrary/customScreenshot/index'),
  //           name: 'CustomScreenshot',
  //           meta: { title: '自定义截图', icon: 'icon-1' }
  //       }]
  //   },
  // // 平台广告管理页面
  // {
  //   path: '/platformAdvertising',
  //   component: Layout,
  //   children: [{
  //     path: 'index',
  //     component: () => import('@/views/platformAdvertising/index'),
  //     name: 'PlatformAdvertising',
  //     meta: { title: '平台广告管理', icon: 'icon-1' }
  //   }]
  // },
  // // 平台留言板页面
  // {
  //   path: '/platformMessageBoard',
  //   component: Layout,
  //   children: [{
  //     path: 'index',
  //     component: () => import('@/views/platformMessageBoard/index'),
  //     name: 'PlatformMessageBoard',
  //     meta: { title: '平台留言板', icon: 'icon-1' }
  //   }]
  // },

  // // 话务渠道整合页面
  // {
  //   path: '/distributionStatistics/outboundChannelIntegration',
  //   component: Layout,
  //   children: [{
  //     path: 'index',
  //     component: () => import('@/views/distributionStatistics/outboundChannelIntegration/index'),
  //     name: 'OutboundChannelIntegration',
  //     meta: { title: '话务渠道整合', icon: 'icon-1' }
  //   }]
  // },
  // // 话务标记统计页面
  // {
  //   path: '/callSystem/markStatistics',
  //   component: Layout,
  //   children: [{
  //     path: 'index',
  //     component: () => import('@/views/callSystem/markStatistics/index'),
  //     name: 'MarkStatistics',
  //     meta: { title: '话务标记统计', icon: 'icon-1' }
  //   }]
  // },
  // // 跟进客户统计页面
  // {
  //   path: '/callSystem/followStatistics',
  //   component: Layout,
  //   children: [{
  //     path: 'index',
  //     component: () => import('@/views/callSystem/followStatistics/index'),
  //     name: 'FollowStatistics',
  //     meta: { title: '跟进客户统计', icon: 'icon-1' }
  //   }]
  // },
  // // 员工统计明细页面
  // {
  //   path: '/callSystem/employeeDetail',
  //   component: Layout,
  //   children: [{
  //     path: 'index',
  //     component: () => import('@/views/callSystem/employeeDetail/index'),
  //     name: 'EmployeeDetail',
  //     meta: { title: '员工统计明细', icon: 'icon-1' }
  //   }]
  // }
]
