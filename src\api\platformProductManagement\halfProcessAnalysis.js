import request from '@/utils/request'

// 半流程分析
export function getHalfProcessAnalysis(params) {
  return request({
    url: '/loan/xm/half/output/stat',
    method: 'get',
    params
  })
}

// 半流程分析-渠道统计
export function getHalfProcessAnalysisByChannel(params) {
  return request({
    url: '/loan/xm/half/output/statByChannel',
    method: 'get',
    params
  })
}

// 半流程分析-失败记录列表
export function getHalfProcessFailList(params) {
  return request({
    url: '/loan/xm/half/output/listHalfFailRecord',
    method: 'get',
    params
  })
}

// 半流程分析-未回调记录列表
export function getHalfProcessNotCallbackList(params) {
  return request({
    url: '/loan/xm/half/output/listHalfNotCallbackRecord',
    method: 'get',
    params
  })
}
