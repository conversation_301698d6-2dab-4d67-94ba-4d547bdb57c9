import request from '@/utils/request'
//查询项目列表
export const getProjectList = (data) => {
    return request({
        url: "/loan/project/list",
        method: "get",
        params: data,
    })
}
//新增项目
export const AddProjectOne = (data) => {
    return request({
        url: "/loan/project/add",
        method: "post",
        data,
    })
}
//修改项目
export const editProjectOne = (data) => {
    return request({
        url: "/loan/project/edit",
        method: "post",
        data,
    })
}
//删除项目
export const delProjectOne = (data) => {
    return request({
        url: `/loan/project/del/${data}`,
        method: "post",

    })
}

//查询app版本信息
export const getAppInfo = (data) => {
    return request({
        url: "/loan/app/findList",
        method: "get",
        params: data
    })
}
//查询app版本信息
export const addAppInfo = (data) => {
    return request({
        url: "/loan/app/addOne",
        method: "post",
        data
    })
}
//查询app版本信息
export const editAppInfo = (data) => {
    return request({
        url: "/loan/app/updateone",
        method: "post",
        data
    })
}