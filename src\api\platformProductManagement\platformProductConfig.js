import request from '@/utils/request'

// 获取渠道平台分组列表
export function getChannelPlatformGroupList(params) {
  return request({
    url: '/loan/xm/channelPlatformGroup/list',
    method: 'get',
    params
  })
}

// 新增渠道平台分组
export function addChannelPlatformGroup(data) {
  return request({
    url: '/loan/xm/channelPlatformGroup/add',
    method: 'post',
    data
  })
}

// 更新渠道平台分组
export function updateChannelPlatformGroup(data) {
  return request({
    url: '/loan/xm/channelPlatformGroup/update',
    method: 'post',
    data
  })
} 

// 获取渠道平台分组产品列表
export function getChannelPlatformGroupProductList(params) {
  return request({
    url: '/loan/xm/channelPlatformGroup/product/list',
    method: 'get',
    params
  })
}
