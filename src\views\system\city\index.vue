<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="城市名称" prop="status">
        <el-input
          placeholder="请输入城市名称"
          clearable
          v-model="queryParams.keywords"
          size="mini"
        >
        </el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="addPayName"
          >新增</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-form-item>
    </el-form>

    <el-table border :data="cityList">
      <el-table-column label="省名称" prop="provinceName" align="center" />
      <el-table-column label="省编码" prop="provinceCode" align="center" />
      <el-table-column label="市名称" prop="cityName" align="center" />
      <el-table-column label="市编码" prop="cityCode" align="center" />
      <el-table-column label="区名称" prop="countyName" align="center" />
      <el-table-column label="区编码" prop="countyCode" align="center" />
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              type="text"
              icon="el-icon-edit-outline"
              @click="editPay(row)"
              >编辑</el-button
            >
            <el-button type="text" icon="el-icon-delete" @click="handleDel(row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="新增城市"
      :visible.sync="addAvisible"
      @close="cancel"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="父级编码" prop="pcode">
          <el-input v-model="formData.pcode" placeholder="请输入父级编码" />
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入编码" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="修改城市"
      :visible.sync="editAvisible"
      @close="editCancel"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="city" :model="editForm" :rules="editRules">
        <el-form-item label="省名称" prop="provinceName">
          <el-input
            v-model="editForm.provinceName"
            placeholder="请输入省名称"
          />
        </el-form-item>
        <el-form-item label="省编码" prop="provinceCode">
          <el-input
            v-model="editForm.provinceCode"
            placeholder="请输入省编码"
          />
        </el-form-item>
        <el-form-item label="市名称" prop="cityName">
          <el-input v-model="editForm.cityName" placeholder="请输入市名称" />
        </el-form-item>
        <el-form-item label="市编码" prop="cityCode">
          <el-input v-model="editForm.cityCode" placeholder="请输入市编码" />
        </el-form-item>
        <el-form-item label="区名称" prop="countyName">
          <el-input v-model="editForm.countyName" placeholder="请输入区名称" />
        </el-form-item>
        <el-form-item label="区编码" prop="countyCode">
          <el-input v-model="editForm.countyCode" placeholder="请输入区编码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editSubmitForm">确 定</el-button>
        <el-button @click="editCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCity,
  addCity,
  delCity,
  exportCity,
  editCity,
} from "@/api/system/city";
export default {
  name: "PayManage",
  data() {
    return {
      addAvisible: false,
      editAvisible: false,
      formData: {
        code: "",
        pcode: "",
        name: "",
      },
      editForm: {
        cityCode: null,
        cityName: "",
        countyCode: null,
        countyName: "",
        provinceCode: null,
        provinceName: "",
      },
      rules: {
        code: [{ required: true, message: "请输入编码", trigger: "blur" }],
        pcode: [{ required: true, message: "请输入父级编码", trigger: "blur" }],
        name: [{ required: true, message: "请输出名称", trigger: "blur" }],
      },
      editRules: {
        cityCode: [
          { required: true, message: "请输入市编码", trigger: "blur" },
        ],
        cityName: [
          { required: true, message: "请输入市名称", trigger: "blur" },
        ],
        countyCode: [
          { required: true, message: "请输入区编码", trigger: "blur" },
        ],
        countyName: [
          { required: true, message: "请输入区名称", trigger: "blur" },
        ],
        provinceCode: [
          { required: true, message: "请输入省编码", trigger: "blur" },
        ],
        provinceName: [
          { required: true, message: "请输出市名称", trigger: "blur" },
        ],
      },
      total: 0,

      cityList: [],
      queryParams: {
        keywords: "",
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    handleQuery() {
      //   this.queryParams.pageNum = 1;
      this.getList();
    },
    editPay(row) {
      this.editAvisible = true;
      this.editForm = JSON.parse(JSON.stringify(row));
    },
    addPayName() {
      this.addAvisible = true;
    },
    cancel() {
      this.addAvisible = false;
      this.formData = {
        code: "",
        pcode: "",
        name: "",
      };
      this.$refs.formData.resetFields();
    },
    editCancel() {
      this.editForm = {
        cityCode: null,
        cityName: "",
        countyCode: null,
        countyName: "",
        provinceCode: null,
        provinceName: "",
      };
      this.editAvisible = false;
      this.$refs.city.resetFields();
    },
    handleDel(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          delCity({ code: row.code })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {});
        })
        .catch((err) => {});
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          addCity(this.formData).then((res) => {
            if (res.code == 200) {
              this.$message.success("新增成功");
              this.getList();
              this.cancel();
            }
          });
        }
      });
    },
    editSubmitForm() {
      this.$refs.city.validate((valid) => {
        if (valid) {
          editCity(this.editForm).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.getList();
              this.editCancel();
            }
          });
        }
      });
    },
    getList() {
      getCity(this.queryParams).then((res) => {
        this.cityList = res.rows;
        this.total = res.total;
      });
    },
    handleExport() {
      this.$modal
        .confirm("是否确认导出所有城市")
        .then(() => {
          return exportCity({ type: 1 });
        })
        .then((response) => {
          this.$download.name(response.data);
          //   this.exportLoading = false;
        })
        .catch(() => {});
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
