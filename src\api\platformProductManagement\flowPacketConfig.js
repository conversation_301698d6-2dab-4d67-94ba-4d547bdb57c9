import request from '@/utils/request'

// 查询流量包列表
export function listFlowPacket(query) {
  return request({
    url: '/loan/xm/flowPacketConfig/list',
    method: 'get',
    params: query
  })
}

// 新增流量包
export function addFlowPacket(data) {
  return request({
    url: '/loan/xm/flowPacketConfig/add',
    method: 'post',
    data: data
  })
}

// 修改流量包
export function updateFlowPacket(data) {
  return request({
    url: '/loan/xm/flowPacketConfig/update',
    method: 'post',
    data: data
  })
}

// 更新流量包状态——开关
export function updateFlowPacketStatus(data) {
  return request({
    url: '/loan/xm/flowPacketConfig/updateStatus',
    method: 'post',
    data: data
  })
}

// 生成流量包
export function generateFlowPacket(id) {
  return request({
    url: '/loan/xm/flowPacketConfig/createFlowPacketUsage',
    method: 'get',
    params: { id }
  })
}

// 查询流量包发放记录
export function getFlowPacketUsageRecord(query) {
  return request({
    url: '/loan/xm/flowPacketConfig/flowPacketUsageRecord/pageList',
    method: 'get',
    params: query
  })
} 
