<template>
  <div class="screen-container">
    <!-- 状态栏 iOS/Android -->
    <component :is="configData.device && configData.device.os == 'android' ? 'AndroidStatusBar' : 'IOSStatusBar'"
      :time="configData.statusBar.time" :show-signal="configData.statusBar.showSignal"
      :show-wifi="configData.statusBar.showWifi" :show-battery="configData.statusBar.showBattery"
      :battery-level="configData.statusBar.batteryLevel" :mode="configData.statusBar.mode" />

    <!-- iOS/Android 导航栏 -->
    <component :is="configData.device && configData.device.os == 'android' ? 'AndroidNavBar' : 'IOSNavBar'" />

    <div class="screen-container-inner">
      <!-- Background Image -->
      <img class="background-img" src="@/assets/images/materialLibrary/sesame-score/backgrounds/main-bg.png"
        alt="Background">

      <!-- Original zmxy image -->
      <img class="zmxy-img" src="@/assets/images/materialLibrary/sesame-score/backgrounds/zmxy-logo.png" alt="Background">

      <!-- 随机分布的小圆点 -->
      <div class="random-dots">
        <div v-for="(dot, index) in dots" :key="index">
          <img v-if="index % 2 === 0" src="@/assets/images/materialLibrary/sesame-score/decorations/dot1.png" alt="Background" class="dot"
            :style="{ width: dot.size + 'px', height: dot.size + 'px', left: dot.left + '%', top: dot.top + '%', }">
          <img v-else src="@/assets/images/materialLibrary/sesame-score/decorations/dot2.png" alt="Background" class="dot"
            :style="{ width: dot.size + 'px', height: dot.size + 'px', left: dot.left + '%', top: dot.top + '%', }">
        </div>
      </div>

      <!-- 仪表盘区域 -->
      <Dashboard :user-name="configData.userName" :score="configData.score" :arc-settings="configData.arcSettings" :score-font="configData.scoreFont" />

      <!-- 服务区域 -->
      <ServiceItems :services="configData.services" />

      <!-- 消息助手 -->
      <MessageAssistant :message-count="configData.messageCount" :messages="configData.messages" />

      <!-- 广告图 -->
      <AdImage :ad-image-url="configData.adImage.url" />
    </div>

    <!-- tabbar图 -->
    <TabBar />
  </div>
</template>
<script>
// Added import for html2canvas
import html2canvas from 'html2canvas';
import IOSStatusBar from '../../components/iOSStatusBar.vue';
import AndroidStatusBar from '../../components/AndroidStatusBar.vue';
import IOSNavBar from './preview/iOSNavBar.vue';
import AndroidNavBar from './preview/AndroidNavBar.vue';
import Dashboard from './preview/Dashboard.vue';
import ServiceItems from './preview/ServiceItems.vue';
import MessageAssistant from './preview/MessageAssistant.vue';
import AdImage from './preview/AdImage.vue';
import TabBar from './preview/TabBar.vue';

export default {
  name: 'SesameScorePreview',
  components: {
    IOSStatusBar,
    AndroidStatusBar,
    IOSNavBar,
    AndroidNavBar,
    Dashboard,
    ServiceItems,
    MessageAssistant,
    AdImage,
    TabBar
  },
  props: {
    configData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dots: this.generateRandomDots(15) // 生成30个随机点
    }
  },
  methods: {
    // Added method to capture screenshot
    async captureScreenshot() {
      const targetElement = this.$el;
      if (!targetElement) {
        console.error('Target element .screen-container not found (this.$el is null?).');
        return null;
      }

      try {
        const canvas = await html2canvas(targetElement, {
          useCORS: true,
          // proxy: 'https://hmc-test-001.oss-cn-chengdu.aliyuncs.com/common/path/',
          scale: 1.5,
          backgroundColor: null,
          // 打印日志
          logging: false,
          width: targetElement.scrollWidth,
          height: targetElement.scrollHeight
        });

        // 根据平台类型决定图片格式
        const imageType = this.configData.device.os === 'android' ? 'image/jpeg' : 'image/png';
        const dataUrl = canvas.toDataURL(imageType);
        return dataUrl;

      } catch (error) {
        console.error('Error capturing screenshot:', error);
        return null;
      }
    },
    generateRandomDots(count) {
      const dots = [];
      for (let i = 0; i < count; i++) {
        dots.push({
          size: Math.random() * 17 + 5,           // 大小
          left: Math.random() * 100,             // 0-100% 水平位置
          top: Math.random() * 100,              // 0-100% 垂直位置（覆盖整个背景）
        });
      }
      return dots;
    }
  }
}
</script>
<style scoped lang="scss">
// 隐藏所有滚动条
::-webkit-scrollbar {
  display: none;
}

.screen-container {
  position: relative;
  width: 750px;
  // height: 1334px;
  height: 1624px;
  font-family: 'SourceHanSansSC-Regular';
  display: flex;
  flex-direction: column;

  .screen-container-inner {
    flex: 1;
    overflow-y: hidden;
    background-color: #F0F3F7;
  }
}

.background-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 750px;
  height: 832rpx;
  object-fit: cover;
  z-index: 0;
}

.zmxy-img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 750px;
  height: 336px;
  object-fit: cover;
  z-index: 0;
}

.random-dots {
  position: absolute;
  top: 0;
  left: 0;
  width: 750px;
  height: 741px;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
  /* 确保圆点不会超出容器 */
}

.dot {
  position: absolute;
  width: var(--size);
  height: var(--size);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}
</style>