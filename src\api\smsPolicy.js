import request from '@/utils/request'

// 获取短信策略列表
export function getSmsPolicyList(params) {
  return request({
    url: '/loan/sms/policy',
    method: 'get',
    params
  })
}

// 创建短信策略
export function createSmsPolicy(data) {
  return request({
    url: '/loan/sms/policy',
    method: 'post',
    data
  })
}

// 更新短信策略
export function updateSmsPolicy(id, data) {
  return request({
    url: `/loan/sms/policy/${id}`,
    method: 'put',
    data
  })
}

// 删除短信策略
export function deleteSmsPolicy(id) {
  return request({
    url: `/loan/sms/policy/${id}`,
    method: 'delete'
  })
} 