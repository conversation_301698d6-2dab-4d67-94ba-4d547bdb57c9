<template>
  <div class="platform-config-container">
    <el-tabs 
      :value="activePlatform" 
      type="border-card" 
      class="platform-tabs"
      tab-position="top"
      @tab-click="handleTabChange"
    >
      <el-tab-pane
        v-for="platform in platforms"
        :key="platform.platformType"
        :name="platform.platformType.toString()"
      >
        <span slot="label">
          <i class="fas fa-desktop" style="font-size: 13px;"></i>
          {{ platform.platformName }}
        </span>
        <platform-config-panel
          v-if="activePlatform === platform.platformType.toString()"
          :platform="platform"
          :platform-id="platform.platformType"
          @update="handleConfigUpdate"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import PlatformConfigPanel from './PlatformConfigPanel.vue'

export default {
  name: 'PlatformTabs',
  components: {
    PlatformConfigPanel
  },
  props: {
    platforms: {
      type: Array,
      default: () => []
    },
    activePlatform: {
      type: String,
      default: ''
    }
  },
  
  methods: {
    handleTabChange(tab) {
      this.$emit('tab-change', tab.name)
    },
    
    handleConfigUpdate(updateData) {
      this.$emit('config-update', updateData)
    }
  }
}
</script>

<style lang="scss" scoped>
.platform-config-container {
  flex: 1;
  padding: 0 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .platform-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: none !important; // 移除阴影效果

    ::v-deep .el-tabs__header {
      margin-bottom: 8px;
      background: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e8eaed;
      flex-shrink: 0;
      min-height: 40px;
      box-shadow: none !important; // 移除header阴影

      .el-tabs__nav {
        border: none;
        box-shadow: none !important; // 移除nav阴影
      }

      .el-tabs__item {
        border: none;
        color: #5f6368;
        padding: 8px 16px;
        font-weight: 500;
        font-size: 14px;
        height: 38px;
        line-height: 22px;
        box-shadow: none !important; // 移除tab项阴影

        &.is-active {
          color: #dc382d;
          background: #ffffff;
          box-shadow: none !important; // 移除激活状态阴影
        }

        :deep(.icon-park-icon) {
          margin-right: 4px;
        }
      }
    }

    ::v-deep .el-tabs__content {
      flex: 1;
      padding: 0;
      overflow: hidden;
      box-shadow: none !important; // 移除content阴影

      .el-tab-pane {
        height: 100%;
        overflow-y: auto;
        box-shadow: none !important; // 移除tab面板阴影
      }
    }
  }
}
</style>