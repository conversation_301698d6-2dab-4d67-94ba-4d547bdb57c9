<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="">
        <el-date-picker
          value-format="yyyy-MM-dd"
          v-model="queryParams.dateStr"
          type="date"
          placeholder="选择日期"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道" prop="">
        <el-select
          ref="select"
          size="small"
          v-model="checkValue"
          filterable
          collapse-tags
          multiple
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in optionsList"
            :key="item.id"
            :label="`id:${item.id}--${item.channelName}`"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="dataList" @sort-change="handleSortChange">
      <el-table-column label="渠道ID" prop="channelId" align="center" />
      <el-table-column label="渠道名称" prop="channelName" align="center" />
      <el-table-column
        label="总表单"
        :sort-orders="['descending', 'ascending']"
        prop="totalCount"
        sortable="custom"
        align="center"
      />
      <el-table-column
        label="芝麻分600"
        prop="zmf600Num"
        :formatter="(row) => formatterDate('zmf600Num', row)"
        align="center"
      />
      <el-table-column
        label="芝麻分600-650"
        prop="zmf650Num"
        :formatter="(row) => formatterDate('zmf650Num', row)"
        align="center"
      />
      <el-table-column
        label="芝麻分650-700"
        prop="zmf700Num"
        :formatter="(row) => formatterDate('zmf700Num', row)"
        align="center"
      />
      <el-table-column
        label="芝麻分700+"
        prop="zmf700sNum"
        :formatter="(row) => formatterDate('zmf700sNum', row)"
        align="center"
      />
      <el-table-column
        label="房"
        prop="fnum"
        :formatter="(row) => formatterDate('fnum', row)"
        align="center"
      />
      <el-table-column
        label="车"
        prop="cnum"
        :formatter="(row) => formatterDate('cnum', row)"
        align="center"
      />
      <el-table-column
        label="社保"
        prop="sbNum"
        :formatter="(row) => formatterDate('sbNum', row)"
        align="center"
      />
      <el-table-column
        label="公积金"
        prop="gjjNum"
        :formatter="(row) => formatterDate('gjjNum', row)"
        align="center"
      />
      <el-table-column
        label="保单"
        prop="bdNum"
        :formatter="(row) => formatterDate('bdNum', row)"
        align="center"
      />
      <el-table-column
        label="逾期"
        prop="yqNum"
        :formatter="(row) => formatterDate('yqNum', row)"
        align="center"
      />
      <el-table-column label="城市占比" prop="  " align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button type="text" @click="hanldeShowCity(row)"
              >城市占比</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.size"
      @pagination="getList" /> -->
    <el-dialog
      :visible.sync="dialogVisible"
      title="城市占比"
      append-to-body

    >
      <el-table :data="rateList" border @sort-change="handleSortChangea">
        <el-table-column label="城市" align="center" prop="cityName" />
        <el-table-column label="占比" align="center" prop="cityNum" />
        <el-table-column
          label="个数"
          align="center"
          :sort-orders="['descending', 'ascending']"
          sortable="custom"
          prop="citysNum"
        />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { exportlist, exportDetail, exportChannelList } from "@/api/crmReport";

export default {
  data() {
    return {
      dataRange: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      dataList: [],
      rateList: [],
      optionsList: [],
      total: 0,
      dialogVisible: false,
      queryParams: {
        dateStr: this.getTime(),
        channelIds: "",
      },
      checkValue: [],
      row: {},
    };
  },
  methods: {
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    //搜索
    handleQuery() {
      this.getList();
    },

    getList() {
      this.queryParams.channelIds = this.checkValue.join(",");
      exportlist(this.queryParams).then((res) => {
        this.dataList = res.data;
        let data = res.data;
        this.dataList.unshift({
          channelId: "合计",
          cnum: this.getTotal(data, "cnum"),
          bdNum: this.getTotal(data, "bdNum"),
          fnum: this.getTotal(data, "fnum"),
          gjjNum: this.getTotal(data, "gjjNum"),
          sbNum: this.getTotal(data, "sbNum"),
          totalCount: this.getTotal(data, "totalCount"),
          yqNum: this.getTotal(data, "yqNum"),
          zmf600Num: this.getTotal(data, "zmf600Num"),
          zmf650Num: this.getTotal(data, "zmf650Num"),
          zmf700Num: this.getTotal(data, "zmf700Num"),
          zmf700sNum: this.getTotal(data, "zmf700sNum"),
        });
      });
    },
    getTotal(arr, type) {
      return arr.reduce((pre, data) => {
        return pre + data[type];
      }, 0);
    },
    formatterDate(key, row) {
      return ((row[key] / row.totalCount) * 100).toFixed(2) + "%";
    },
    hanldeShowCity(row) {
      this.dialogVisible = true;
      this.row = row;
      exportDetail({
        dateStr: this.queryParams.dateStr,
        channelIds: row.channelId,
      }).then((res) => {
        this.rateList = res.data.map((item) => {
          return {
            cityName: item.cityName,
            citysNum: item.cityNum * 1,
            cityNum:
              ((item.cityNum / this.row.totalCount) * 100).toFixed(2) + "%",
          };
        });
        let data = JSON.parse(JSON.stringify(this.rateList));
        this.rateList.unshift({
          cityName: "合计",
          citysNum: this.getTotal(this.rateList, "citysNum"),
        });
      });
    },
    //前端排序
    handleSortChange(column) {
      this.proptype = column.prop;
      let sortingType = column.order;
      this.sumData = this.dataList[0];
      if (sortingType == "ascending") {
        //正序
        this.dataList = this.dataList
          .splice(1)
          .sort((a, b) => b[this.proptype] - a[this.proptype]);

        this.dataList.unshift(this.sumData);
      }
      if (sortingType == "descending") {
        // 倒序
        this.dataList = this.dataList
          .splice(1)
          .sort((a, b) => a[this.proptype] - b[this.proptype]);
        this.dataList.unshift(this.sumData);
      }
    },
    handleSortChangea(column) {
      this.proptype1 = column.prop;
      let sortingType = column.order;
      this.sumData1 = this.rateList[0];
      if (sortingType == "ascending") {
        //正序
        this.rateList = this.rateList
          .splice(1)
          .sort((a, b) => b[this.proptype1] - a[this.proptype1]);

        this.rateList.unshift(this.sumData1);
      }
      if (sortingType == "descending") {
        // 倒序
        this.rateList = this.rateList
          .splice(1)
          .sort((a, b) => a[this.proptype1] - b[this.proptype1]);
        this.rateList.unshift(this.sumData1);
      }
    },
  },
  mounted() {
    this.getList();
    exportChannelList().then((res) => {
      this.optionsList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped></style>
