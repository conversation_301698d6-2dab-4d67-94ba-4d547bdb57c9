<template>
  <div>
    <!-- <div class="title">新增产品</div> -->

    <div class="container">
      <el-form :model="queryParams" ref="queryForm" :rules="rules" :inline="true" label-width="200px"
        label-position="left">
        <div class="flex">
          <div>
            <el-form-item label="推广名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入推广名称" clearable size="small" />
            </el-form-item>
          </div>
          <div>
            <el-form-item label="LOGO" prop="file">
              <el-upload class="avatar-uploader" action :auto-upload="false" :on-change="changeUpImg"
                :show-file-list="false">
                <img v-if="imageUrl" style="vertical-align: middle; max-height: 100px" :src="imageUrl" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </div>
        </div>
        <!-- <div class="flex">
          <div>
            <el-form-item label="会员产品" prop="vip">
              <el-radio v-model="queryParams.vip" :label="true">是</el-radio>
              <el-radio v-model="queryParams.vip" :label="false">否</el-radio>
            </el-form-item>
          </div>
        </div> -->
        <div class="flex">
          <div>
            <el-form-item label="商户类型" prop="mid">
              <el-select clearable v-model="queryParams.mid" placeholder="请选择商户类型" size="small" style="width: 100%">
                <el-option label="银行机构" :value="1"> </el-option>
                <el-option label="线上-贷超" :value="2"> </el-option>
                <el-option label="线上持牌机构" :value="3"> </el-option>
                <el-option label="一级机构" :value="4"> </el-option>
                <el-option label="二级机构" :value="5"> </el-option>
                <el-option label="三级机构" :value="6"> </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div v-if="
            (queryParams.mid == 2 || queryParams.mid == 3) &&
            queryParams.cooperationMode != 1 &&
            queryParams.mid &&
            queryParams.cooperationMode
          ">
            <el-form-item label="所属接口" prop="productApiId">
              <el-select clearable v-model="queryParams.productApiId" placeholder="请选择所属接口" size="small"
                style="width: 100%">
                <el-option v-for="item in prodApiList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div v-if="
            queryParams.mid == 1 ||
            queryParams.mid == 4 ||
            queryParams.mid == 5 ||
            queryParams.mid == 6
          ">
            <el-form-item label="接单类型" prop="loanId">
              <el-select clearable filterable v-model="queryParams.loanId" placeholder="请选择接单类型" size="small"
                @focus="getloanList" style="width: 100%">
                <el-option :label="item.name" :value="item.id" :key="item.id" v-for="item in LoanList">{{ item.name }}
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <div v-if="queryParams.mid == 2 || queryParams.mid == 3">
            <el-form-item label="推广方式" prop="cooperationMode">
              <el-select clearable v-model="queryParams.cooperationMode" placeholder="请选择推广方式" size="small"
                style="width: 100%">
                <el-option label="链接合作" :value="1"> </el-option>
                <el-option label="接口合作" :value="2"> </el-option>
                <el-option label="撞库+H5" :value="3"> </el-option>
                <el-option label="撞库+连登H5" :value="4"> </el-option>
                <el-option label="仅连登" :value="5"> </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="所属甲方" prop="partyaId" v-if='$route.query.isEdit != "0"'>
              <el-select @focus="getPartyaId" clearable filterable v-model="queryParams.partyaId" placeholder="请选择所属甲方"
                size="small" style="width: 100%">
                <el-option :label="item.name" :value="item.partyFirstId" :key="item.partyFirstId"
                  v-for="item in acquirePartyList">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <div v-if="queryParams.mid == 2 || queryParams.mid == 3">
            <el-form-item label="推广链接" prop="cooperationLink">
              <el-input v-model="queryParams.cooperationLink" placeholder="请输入推广链接" clearable size="small" />
            </el-form-item>
          </div>
          <div>
            <el-form-item label="推广类型" prop="cooperationType">
              <el-select @focus="getPackageList" clearable filterable v-model="queryParams.cooperationType"
                placeholder="请选择推广类型" size="small" style="width: 100%">
                <el-option v-for="item in packageList" :key="item.value" :label="item.name" :value="item.value">
                </el-option>
                <!-- <el-option label="CPS" :value="2"> </el-option>
                <el-option label="CPC" :value="3"> </el-option> -->
              </el-select>
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <div>
            <el-form-item label="推广描述" prop="describe">
              <el-input v-model="queryParams.describe" placeholder="请输入推广描述" clearable size="small" />
            </el-form-item>
          </div>
          <div>
            <el-form-item label="推广价格" prop="cooperationCost">
              <el-input type="number" v-model="queryParams.cooperationCost" placeholder="请输入推广价格" clearable
                size="small" />
            </el-form-item>
          </div>
        </div>
        <!-- <div class="flex">
          <div>
            <el-form-item label="区域要求" prop="district">
              <BaseCascader
                :options="cityList"
                :is_deep="true"
                :has_all_select="true"
                @getOptions="disableEare"
                :back_options="queryParams.district"
              />

            </el-form-item>
          </div>
          <div>
            <el-form-item label="手机号归属地" prop="attribution">
              <BaseCascader
                :options="phoneList"
                :is_deep="true"
                :has_all_select="true"
                @getOptions="disablePhone"
                :back_options="queryParams.attribution"
              />

            </el-form-item>
          </div>
        </div> -->
        <div class="flex">
          <div>
            <el-form-item label="机构名称" prop="organName">
              <el-input v-model="queryParams.organName" placeholder="请输入机构名称" clearable size="small" />
            </el-form-item>
          </div>
          <div>
            <el-form-item label="机构咨询热线" prop="helpline">
              <el-input v-model="queryParams.helpline" placeholder="请输入机构咨询热线" clearable size="small" />
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <div>
            <el-form-item label="机构简介" prop="synopsis">
              <el-input v-model="queryParams.synopsis" placeholder="请输入机构简介" clearable size="small" />
            </el-form-item>
          </div>
        </div>
        <div style="display: flex">
          <div>
            <el-form-item label="额度范围" prop="loanableFundsLittle">
              <el-input v-model="queryParams.loanableFundsLittle" placeholder="请输入可贷额度小" clearable
                oninput="value=value.replace(/[^0-9]/g,'')" size="small" />
            </el-form-item>
            <el-form-item :label="isLable ? ' ' : ''" prop="loanableFundsBig">
              <el-input v-model="queryParams.loanableFundsBig" placeholder="请输入可贷额度大" clearable
                oninput="value=value.replace(/[^0-9]/g,'')" size="small" />
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <div>
            <el-form-item label="利率单位" prop="interestRateUnit">
              <el-select clearable v-model="queryParams.interestRateUnit" placeholder="请选择利率单位" size="small"
                style="width: 100%">
                <!-- <el-option label="日利率" :value="1"> </el-option> -->
                <el-option label="月利率" :value="2"> </el-option>
                <el-option label="年利率" :value="3"> </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <el-row>
          <el-col :span="24">
            <el-form-item label="利率范围" prop="interestRateLittle">
              <el-input v-model="queryParams.interestRateLittle" placeholder="请输入利率小" clearable
                oninput="value=value.replace(/[^0-9.]/g,'')" size="small" />
            </el-form-item>
            <el-form-item :label="isLable ? ' ' : ''" prop="interestRateBig">
              <el-input v-model="queryParams.interestRateBig" placeholder="请输入利率大" clearable
                oninput="value=value.replace(/[^0-9.]/g,'')" size="small" />
            </el-form-item>
          </el-col>
        </el-row>
        <div class="flex">
          <div>
            <el-form-item label="贷款期限单位" prop="loanPeriodUnit">
              <el-select clearable v-model="queryParams.loanPeriodUnit" placeholder="请选择利率单位" size="small"
                style="width: 100%">
                <el-option label="月" :value="1"> </el-option>
                <!-- <el-option label="天" :value="2"> </el-option> -->
              </el-select>
            </el-form-item>
          </div>
        </div>
        <el-row>
          <el-col :span="24">
            <el-form-item label="贷款期限" prop="loanPeriodLittle">
              <el-input type="number" v-model="queryParams.loanPeriodLittle" placeholder="请输入贷款期限开始" clearable
                size="small" />
            </el-form-item>
            <el-form-item :label="isLable ? ' ' : ''" prop="loanPeriodBig">
              <el-input type="number" v-model="queryParams.loanPeriodBig" placeholder="请输入贷款期限结束" clearable
                size="small" />
            </el-form-item>
          </el-col>
        </el-row>
        <div class="flex">
          <div>
            <el-form-item label="办理时间单位" prop="handlingTimeUnit">
              <el-select clearable v-model="queryParams.handlingTimeUnit" placeholder="请选择办理时间单位" size="small"
                style="width: 100%">
                <el-option label="分钟" :value="1"> </el-option>
                <el-option label="小时" :value="2"> </el-option>
                <el-option label="天" :value="3"> </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <el-row>
          <el-col :span="24">
            <el-form-item label="办理时间" prop="handlingTimeLittle">
              <el-input v-model="queryParams.handlingTimeLittle" placeholder="请输入办理时间(开始)" clearable
                oninput="value=value.replace(/[^0-9.]/g,'')" size="small" />
            </el-form-item>
            <el-form-item :label="isLable ? ' ' : ''" prop="handlingTimeBig">
              <el-input v-model="queryParams.handlingTimeBig" oninput="value=value.replace(/[^0-9.]/g,'')"
                placeholder="请输入办理时间(结束)" clearable size="small" />
            </el-form-item>
          </el-col>
        </el-row>
        <div class="flex">
          <div>
            <el-form-item label="还款方式" prop="refundMode">
              <el-select clearable v-model="queryParams.refundMode" placeholder="请选择还款方式" size="small"
                style="width: 100%">
                <el-option label="随借随还" :value="1"> </el-option>
                <el-option label="按月还款" :value="2"> </el-option>
                <el-option label="等额本息" :value="3"> </el-option>
                <el-option label="等额本金" :value="4"> </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="  ">
              <el-button type="primary" @click="submitData">{{
                  $route.query.id && $route.query.isEdit == "0"
                    ? "修 改"
                    : "新 增"
              }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import {
  getloanModeListsAll,
  getAcquirePartyAall,
  addProductOne,
  getProductOne,
  editProductCompile,
  getCityAll,
  getProductApiList,
  getFlowPackageList,
} from "@/api/productManage/product";
import BaseCascader from "@/components/cascader";
export default {
  name: "ProductAdd",
  data() {
    var imgRule1 = (rule, value, callback) => {
      if (this.queryParams.file == "" && this.imageUrl == "") {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#F56C6C";
        callback(new Error("请上传LOGO"));
      } else if (this.queryParams.file || this.imageUrl) {
        callback();
      }
    };
    return {
      screenWidth: null, //屏幕尺寸
      isLable: false,
      imageUrl: "",
      LoanList: [],
      fileList: [],
      acquirePartyList: [],
      cityList: [],
      phoneList: [],
      prodApiList: [],
      packageList: [],
      props: { multiple: true },
      queryParams: {
        name: "",
        mid: "",
        cooperationMode: "",
        cooperationLink: "",
        cooperationType: "",
        cooperationCost: "",
        describe: "",
        // district: [],
        // attribution: [],
        organName: "",
        helpline: "",
        synopsis: "",
        loanPeriodUnit: "",
        loanableFundsLittle: "",
        loanableFundsBig: "",
        interestRateUnit: "",
        interestRateLittle: "",
        interestRateBig: "",
        loanPeriodLittle: "",
        loanPeriodBig: "",
        handlingTimeUnit: "",
        refundMode: "",
        handlingTimeBig: "",
        handlingTimeLittle: "",
        file: "",
        loanId: "",
        productApiId: "",
        partyaId: "",
        vip: false,
      },
      rules: {
        name: [{ required: true, message: "请输入推广名称", trigger: "blur" }],
        vip: [{ required: true, message: "请选择推广类型", trigger: "blur" }],
        file: [{ required: true, message: "请上传LOGO", validator: imgRule1 }],
        mid: [{ required: true, message: "请选择商户类型", trigger: "blur" }],
        loanId: [
          { required: true, message: "请选择接单类型", trigger: "blur" },
        ],
        cooperationMode: [
          { required: true, message: "请选择推广方式", trigger: "blur" },
        ],
        partyaId: [
          { required: true, message: "请选择所属商户", trigger: "blur" },
        ],
        cooperationLink: [
          { required: true, message: "请输入推广链接", trigger: "blur" },
        ],
        cooperationType: [
          { required: true, message: "请输入推广类型", trigger: "blur" },
        ],
        describe: [
          { required: true, message: "请输入推广描述", trigger: "blur" },
        ],
        cooperationCost: [
          { required: true, message: "请输入推广价格", trigger: "blur" },
        ],
        loanableFundsLittle: [
          { required: true, message: "请输入可贷额度", trigger: "blur" },
        ],
        loanableFundsBig: [
          { required: true, message: "请输入可贷额度", trigger: "blur" },
        ],
        interestRateUnit: [
          { required: true, message: "请选择利率单位", trigger: "blur" },
        ],
        interestRateLittle: [
          { required: true, message: "请输入利率", trigger: "blur" },
        ],
        interestRateBig: [
          { required: true, message: "请输入利率", trigger: "blur" },
        ],
        loanPeriodUnit: [
          { required: true, message: "请选择贷款期限单位", trigger: "blur" },
        ],
        loanPeriodLittle: [
          { required: true, message: "请选择贷款期限", trigger: "blur" },
        ],
        loanPeriodBig: [
          { required: true, message: "请选择贷款期限", trigger: "blur" },
        ],
        handlingTimeUnit: [
          { required: true, message: "请选择办理时间单位", trigger: "blur" },
        ],
        handlingTimeLittle: [
          { required: true, message: "请选择办理时间", trigger: "blur" },
        ],
        handlingTimeBig: [
          { required: true, message: "请选择办理时间", trigger: "blur" },
        ],
        refundMode: [
          { required: true, message: "请选择还款方式", trigger: "blur" },
        ],
        productApiId: [
          { required: true, message: "请选择所属接口", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    //上传图片
    changeUpImg(e) {
      const isJPG =
        e.raw.type === "image/jpeg" ||
        e.raw.type === "image/jpg" ||
        e.raw.type === "image/png";
      const isLt2M = e.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG/JPEG/PNG 格式!");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
        return;
      }
      this.queryParams.file = e.raw;
      this.imageUrl = URL.createObjectURL(e.raw);
      if (
        document.getElementsByClassName("el-form-item__error").length > 0 &&
        this.$route.query.isEdit != "1"
      ) {
        document.getElementsByClassName(
          "el-form-item__error"
        )[1].style.display = "none";
      }
      if (
        document.getElementsByClassName("el-form-item__error").length > 0 &&
        this.$route.query.isEdit == "1"
      ) {
        document.getElementsByClassName(
          "el-form-item__error"
        )[0].style.display = "none";
      }
      if (document.getElementsByClassName("el-upload").length > 0) {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#8c939d";
      }
    },
    //获取推广类型
    getPackageList() {
      getFlowPackageList().then((res) => {
        let arr = [
          {
            name: "点击",
            value: 1,
          },
          {
            name: "CPS",
            value: 2,
          },
          {
            name: "CPC",
            value: 3,
          },
        ];
        let data = res.data.map((item) => {
          return {
            name: item.name,
            value: item.id,
          };
        });
        this.packageList = [...arr, ...data];
      });
    },
    //获取助贷
    getloanList() {
      getloanModeListsAll().then((res) => {
        this.LoanList = res.data;
      });
    },
    //获取商户
    getPartyaId() {
      getAcquirePartyAall().then((res) => {
        this.acquirePartyList = res.data;
      });
    },
    //提交
    submitData() {
      if (
        this.queryParams.mid == 1 ||
        this.queryParams.mid == 4 ||
        this.queryParams.mid == 5 ||
        this.queryParams.mid == 6
      ) {
        delete this.queryParams.cooperationMode;
        delete this.queryParams.cooperationLink;
      }
      if (this.queryParams.mid == 2 || this.queryParams.mid == 3) {
        delete this.queryParams.loanId;
        if (this.queryParams.cooperationMode == 1) {
          delete this.queryParams.productApiId;
        }
      }
      if (this.queryParams.district) {
        delete this.queryParams.district;
      }

      let data = new FormData();
      for (let i in this.queryParams) {
        data.append(i, this.queryParams[i]);
      }
      this.$refs.queryForm.validate((valid) => {
        if (valid) {
          // this.queryParams.district = this.queryParams.district.map((item) => {
          //   if (item.length > 1) {
          //     return item[1];
          //   } else {
          //     return item[0];
          //   }
          // });
          // this.queryParams.attribution = this.queryParams.attribution.map(
          //   (item) => {
          //     if (item.length > 1) {
          //       return item[1];
          //     } else {
          //       return item[0];
          //     }
          //   }
          // );
          if (this.$route.query.id && this.$route.query.isEdit == "0") {
            editProductCompile(data).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.$store.dispatch("tagsView/delView", this.$route);
                this.$router.replace({ path: "/productManage/productList" });
              }
            });
          } else {
            addProductOne(data).then((res) => {
              if (res.code == 200) {
                this.$message.success("添加成功");
                this.$store.dispatch("tagsView/delView", this.$route);
                this.$router.replace({ path: "/productManage/productList" });
              }
            });
          }
        }
      });
    },
  },
  watch: {
    screenWidth: {
      handler: function (val, oldVal) {
        if (val < 1300 && val > 710) {
          this.isLable = true;
        } else {
          this.isLable = false;
        }
      },
      immediate: true,
    },
  },
  components: {
    BaseCascader,
  },
  mounted() {
    if (this.$route.query.id) {
      getloanModeListsAll().then((res) => {
        this.LoanList = res.data;

      });
      getAcquirePartyAall().then((res) => {
        this.acquirePartyList = res.data;
      });
      this.getPackageList();
      //获取产品详情
      getProductOne({ id: this.$route.query.id }).then((res) => {
        if (this.$route.query.isEdit == "0") {
          this.imageUrl = res.data.logo;
        } else {
          this.imageUrl = "";
        }

        this.queryParams = res.data;
        this.queryParams.id = this.$route.query.id;

        this.queryParams = {
          ...this.queryParams,
          cooperationMode:
            res.data.cooperationMode == 0 ? "" : res.data.cooperationMode || "",
        };

        let index = this.LoanList.findIndex(item => item.id == this.queryParams.loanId)
        if (index == -1) {
          this.queryParams.loanId = ""
        }
        this.queryParams.file = "";
      });
    }

    getProductApiList().then((res) => {
      this.prodApiList = res.data;
    });
    this.screenWidth = document.body.clientWidth;
    window.onresize = () => {
      // 屏幕尺寸变化就重新赋值
      return (() => {
        this.screenWidth = document.body.clientWidth;
      })();
    };
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 20px;
  font-weight: 800;
  margin-top: 20px;
  margin-left: 10%;
}

.container {
  margin: 0 auto;
  width: 60%;
  padding-top: 30px;
  overflow: auto;
}

::v-deep .el-form-item--medium .el-form-item__label {
  letter-spacing: 9px;
  font-size: 16px;
}

::v-deep .el-input__inner::placeholder {
  color: #999;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.flex {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.min-wid {
  min-width: 375px;
  flex: 1;
}
</style>
