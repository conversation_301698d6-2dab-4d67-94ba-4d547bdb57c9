<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="文章名称" prop="title" size="small">
        <el-input placeholder="请输入文章名称" clearable v-model="queryParams.title"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="type">
        <el-select v-model="queryParams.type" placeholder="状态" clearable size="small">
          <el-option :value="1" label="在线"></el-option>
          <el-option :value="2" label="已下线"></el-option>
          <el-option :value="3" label="草稿"></el-option>
          <el-option :value="4" label="删除"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="hanldeQuery">搜索</el-button>
        <el-button icon="el-icon-plus" size="small" @click="handleAdd">添加
        </el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableList">
      <el-table-column label="序号" align="center" prop="id" width="100" />
      <el-table-column label="文章名称" align="center" prop="title" />
      <el-table-column label="作者" prop="authorName" align="center">
      </el-table-column>
      <el-table-column label="文章分类" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ cateJson[row.articleClassification] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="{row}">
          <div>
            {{ typeJson[row.articleStatus] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="发布时间" prop="releaseTime" align="center">
      </el-table-column>
      <el-table-column label="是否顶置" prop="istopping" align="center">
        <template slot-scope="{row}">
          <div>
            {{ row.istopping == 0 ? "否" : "是" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button @click="handleToggle(row)" type="text" v-if="row.articleStatus !== 4"><span
                v-if="row.articleStatus == 2">上架</span><span v-if="row.articleStatus == 1">下架</span></el-button>
            <el-button @click="handleEdit(row)" type="text">编辑</el-button>
            <el-button @click="handleDel(row)" type="text" v-if="row.articleStatus !== 4">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { getEncyclopediaList, delEncyclopediaOne, toggleEncyclopediaOne } from "@/api/operate/encyclopedias"
export default {
  data() {
    return {
      typeJson: {
        1: "在线", 2: "已下架", 3: "草稿", 4: "删除"
      },
      cateJson: {
        1: '小额现金贷', 2: '大额分期贷', 3: '信用贷'
      },
      total: 0,
      queryParams: {
        type: "",
        title: "",
        pageNum: 1,
        pageSize: 10
      },
      tableList: []
    }
  },
  methods: {
    handleAdd() {
      this.$router.push('/operate/wiki/encyclopediasEdit')
    },
    hanldeQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleEdit(row) {
      this.$router.push('/operate/wiki/encyclopediasEdit?id=' + row.id)
    },
    handleDel(row) {
      this.$confirm("确认删除该文章吗？", { type: "warning" }).then(() => {
        delEncyclopediaOne({ id: row.id }).then(res => {

          this.getList()
          this.$message.success("删除成功")
        })
      }).catch(err => { })

    },
    handleToggle(row) {
      this.$confirm(`是否${row.articleStatus == 1 ? "下架" : "上架"}该文章？`, { type: "warning" }).then(() => {
        toggleEncyclopediaOne({ id: row.id }).then(res => {
          this.$message.success("操作成功")
          this.getList()
        })
      })

    },
    getList() {
      getEncyclopediaList(this.queryParams).then(res => {

        this.tableList = res.rows
        this.total = res.total
      })
    }
  },
  mounted() {
    this.getList()
  }
}
</script>

<style lang="scss" scoped>
</style>
