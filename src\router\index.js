import Vue from 'vue'
import Router from 'vue-router'
import { isDev } from '@/utils/env'
import devRouter from './devRouter'

const originalPush = Router.prototype.push
Router.prototype.push = function push(location) {

  return originalPush.call(this, location).catch(err => err)
}

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/tencentXJ/userScore',
    component: (resolve) => require(['@/views/tencentXJ/setScore/setScore'], resolve),
    name: 'UserScore',
    hidden: true,
    meta: { title: '用户评分', icon: 'icon-1' }
  },
  {
    path: '/productLinkTest',
    component: (resolve) => require(['@/views/distributeStatistics/productLinkTest'], resolve),
    name: 'ProductLinkTest',
    hidden: true,
    meta: { title: '产品链路测试', icon: 'icon-1' }
  },
  {
    path: '/statisticsInformationFlowChannels',
    component: (resolve) => require(['@/views/distributeStatistics/statisticsInformationFlowChannels'], resolve),
    name: 'ProductLinkTest',
    hidden: true,
    meta: { title: '信息流渠道统计', icon: 'icon-1' }
  },
  {
    path: '/wechatTestLink',
    component: (resolve) => require(['@/views/distributeStatistics/productLinkTest/wechatTestLink'], resolve),
    name: 'WechatTestLink',
    hidden: true,
    meta: { title: '企微测试链接', icon: 'icon-1' }
  },
  {
    path: '/phoneStatusStatistics',
    component: (resolve) => require(['@/views/distributeStatistics/productLinkTest/phoneStatusStatistics'], resolve),
    name: 'PhoneStatusStatistics',
    hidden: true,
    meta: { title: '手机状态标识统计', icon: 'icon-1' }
  },
  {
    path: '/phoneStatusStatisticsPro',
    component: (resolve) => require(['@/views/distributeStatistics/productLinkTest/phoneStatusStatisticsPro'], resolve),
    name: 'PhoneStatusStatisticsPro',
    hidden: true,
    meta: { title: '手机状态标识统计Pro', icon: 'icon-1' }
  },
  {
    path: '/yxqbLogin',
    component: (resolve) => require(['@/views/yxqb/login/index.vue'], resolve),
    name: 'yxqbLogin',
    hidden: true,
    meta: { title: 'yxqbLogin', icon: 'icon-1' }
  },
  {
    path: '/yxqbPushList',
    component: (resolve) => require(['@/views/yxqb/push/index.vue'], resolve),
    name: 'yxqbPushList',
    hidden: true,
    meta: { title: 'yxqbPushList', icon: 'icon-1' }
  },

  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: (resolve) => require(['@/views/redirect'], resolve)
      }
    ]
  },
  {
    path: '/login',
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true
  },
  {
    path: '/register',
    component: (resolve) => require(['@/views/register'], resolve),
    hidden: true
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/error/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/error/401'], resolve),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: (resolve) => require(['@/views/index'], resolve),
        name: 'Index',
        meta: { title: '首页', icon: 'icon-1', affix: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: (resolve) => require(['@/views/system/user/profile/index'], resolve),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: (resolve) => require(['@/views/system/user/authRole'], resolve),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: (resolve) => require(['@/views/system/role/authUser'], resolve),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: (resolve) => require(['@/views/system/dict/data'], resolve),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/saasManage/dict-data',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: (resolve) => require(['@/views/saasManage/dict/data'], resolve),
        name: 'SaasData',
        meta: { title: '字典数据', activeMenu: '/saasManage/saasDict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index',
        component: (resolve) => require(['@/views/monitor/job/log'], resolve),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/saasManage/job-log',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index',
        component: (resolve) => require(['@/views/saasManage/job/log'], resolve),
        name: 'SaasJobLog',
        meta: { title: '调度日志', activeMenu: '/saasManage/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index',
        component: (resolve) => require(['@/views/tool/gen/editTable'], resolve),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  },
]

if (isDev) {
  constantRoutes.push(...devRouter)
}

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
