<template>
  <div class="quota-list">
    <img class="icon-quota" src="https://jst.oss-utos.hmctec.cn/common/path/f927f88ffe1c466d9c4c3e66cc87160b.png"
      alt="">
    <div v-for="(item, index) in listItems" :key="index" class="quota-item">
      <div class="quota-item-title">{{ item.title }}</div>
      <div class="quota-item-info">
        <div class="quota-item-value">{{ item.value }}</div>
        <img class="icon-arrow"
          src="https://jst.oss-utos.hmctec.cn/common/path/a41f3fca1bea48a787c81bb7b1bb3007.png" alt="">
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuotaList',
  props: {
    configData: {
      type: Object,
      default: () => ({
        listItems: [
          {
            title: '微信打白条',
            value: '可用8000'
          },
          {
            title: '购物/出行',
            value: '立减0.5元起'
          },
          {
            title: '分期购物',
            value: '大牌分期...'
          }
        ]
      })
    }
  },
  computed: {
    listItems() {
      return this.configData.listItems || [
        {
          title: '微信打白条',
          value: '可用8000'
        },
        {
          title: '购物/出行',
          value: '立减0.5元起'
        },
        {
          title: '分期购物',
          value: '大牌分期...'
        }
      ];
    }
  }
}
</script>

<style scoped lang="scss">
.quota-list {
  margin: 20px auto;
  padding: 20px;
  position: relative;
  width: 702px;
  height: 110px;
  background-image: url(https://jst.oss-utos.hmctec.cn/common/path/df96eb6564ac4fc5a27c1b1df3edcbe5.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;

  display: flex;
  align-items: center;

  .icon-quota {
    margin-right: 30px;
    width: 68px;
    height: 63px;
  }

  .quota-item {
    margin-right: 42px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    &:last-child {
      margin-right: 0;
    }

    .quota-item-title {
      font-weight: 400;
      font-size: 24px;
      color: #000000;
      line-height: 35px;
    }

    .quota-item-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .quota-item-value {
        font-weight: 400;
        font-size: 22px;
        color: #665E5F;
        line-height: 32px;
      }

      .icon-arrow {
        width: 9px;
        height: 17px;
        transform: translateY(3px);
      }
    }
  }
}
</style> 