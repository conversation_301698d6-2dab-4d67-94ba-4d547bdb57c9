import request from '@/utils/request'
// 获取saas租户列表
export const getTenantList = (data) => {
    return request({
        url: '/saas/tenant/getTenantList',
        method: 'get',
        params: data

    })
}
// 获取saas租户列表
export const getCityWhiteList = () => {
    return request({
        url: '/saas/tenant/getCityWhiteList',
        method: 'get',

    })
}
// 查询所有菜单
export const getAllTenantRoleMenu = () => {
    return request({
        url: '/saas/tenant/getAllTenantRoleMenu',
        method: 'get',

    })
}


//新增租户信息
export const addTenantOne = (data) => {
    return request({
        url: "/saas/tenant/addTenant",
        method: "post",
        data
    })
}

//修改状态
export const updateUserStatus = (data) => {
    return request({
        url: '/saas/tenant/updateUserStatus',
        method: "post",
        data
    })
}
//获取信息
export const getTenantInfoDetail = (data) => {
    return request({
        url: `/saas/tenant/getTenantInfo/${data}`,
        method: "get",
    })
}
//修改信息
export const editTenantInfoDetail = (data) => {
    return request({
        url: `/saas/tenant/updateTenant`,
        method: "post",
        data
    })
}
//修改信息
export const getTenantMeunsDetail = (data) => {
    return request({
        url: `/saas/tenant/getTenantRoleMenu/${data}`,
        method: "get",

    })
}
//修改权限
export const editMeunsDetail = (data) => {
    return request({
        url: `/saas/tenant/updateTenantRole`,
        method: "post",
        data
    })
}

//查询流量平台
export const getAllotPlatformListOptions = (data) => {
    return request({
        url: "/saas/tenant/getAllotPlatformList",
        method: "get"
    })
}