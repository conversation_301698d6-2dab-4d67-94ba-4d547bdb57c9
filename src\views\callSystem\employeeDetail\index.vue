<template>
  <div class="app-container">
    <el-form ref="queryForm" :inline="true" label-width="100px">
      <el-form-item label="平台" prop="platformId">
        <el-select v-model="platformId" placeholder="请选择平台" clearable @change="handlePlatformChange">
          <el-option v-for="platform in platformList" :key="platform.platformId" :label="platform.platformName" :value="platform.platformId" />
        </el-select>
      </el-form-item>
      <el-form-item label="团队" prop="teamIds">
        <el-select v-model="teamIds" multiple placeholder="请选择团队" clearable>
          <el-option v-for="team in teamList" :key="team.id" :label="team.name" :value="team.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="统计时间">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table ref="dataTable" :data="list" border @sort-change="handleSortChange">
      <el-table-column
        prop="ranking"
        label="排名"
        align="center"
        width="80"
        sortable="custom"
      />
      <el-table-column
        prop="followName"
        label="员工名称"
        align="center"
        min-width="120"
      />
      <el-table-column
        prop="teamName"
        label="团队名称"
        align="center"
        min-width="120"
      />
      <el-table-column
        prop="callTotal"
        label="拨打数"
        align="center"
        min-width="100"
        sortable="custom"
      />
      <el-table-column
        prop="successTotal"
        label="成功数"
        align="center"
        min-width="100"
        sortable="custom"
      />
      <el-table-column
        prop="successRate"
        label="成功率"
        align="center"
        min-width="100"
        sortable="custom"
      >
        <template slot-scope="scope">
          {{ formatPercentage(scope.row.successRate) }}
        </template>
      </el-table-column>
    </el-table>


  </div>
</template>

<script>
import { getTeamList, getEmployeeDetail, getPlatforms } from '@/api/callSystem'
import { getDefaultDateRange } from '@/utils'

export default {
  name: 'EmployeeDetail',
  data() {
    return {
      // 员工统计列表
      list: [],
      // 原始数据列表（用于排序）
      originalList: [],
      // 平台列表
      platformList: [],
      // 平台ID
      platformId: '',
      // 团队列表
      teamList: [],
      // 团队ID数组
      teamIds: [],
      // 日期范围
      dateRange: getDefaultDateRange()
    }
  },
  created() {
    this.getPlatformList()
  },
  methods: {
    /** 查询员工统计列表 */
    getList() {
      // 校验平台必选
      if (!this.platformId) {
        this.$message.warning('请先选择平台')
        return
      }

      const params = {
        teamIds: this.teamIds,
        platformId: this.platformId
      }
      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }

      console.log('请求参数:', params)
      getEmployeeDetail(params).then(response => {
        console.log('接口返回数据:', response)
        this.originalList = response.data || []
        this.list = [...this.originalList]
        // 重置表格排序状态
        this.$nextTick(() => {
          if (this.$refs.dataTable) {
            this.$refs.dataTable.clearSort()
          }
        })
      }).catch(error => {
        console.error('接口调用失败:', error)
      })
    },
    /** 获取平台列表 */
    getPlatformList() {
      getPlatforms().then(response => {
        this.platformList = response.data || []
      }).catch(error => {
        console.error('获取平台列表失败:', error)
      })
    },
    /** 处理平台选择变化 */
    handlePlatformChange() {
      // 清空团队选择
      this.teamIds = []
      // 重新获取团队列表
      this.getTeamList()
      // 清空列表数据
      this.list = []
    },
    /** 获取团队列表 */
    getTeamList() {
      if (!this.platformId) {
        this.teamList = []
        return
      }

      const params = { platformId: this.platformId }
      getTeamList(params).then(response => {
        this.teamList = response.data || []
      }).catch(error => {
        console.error('获取团队列表失败:', error)
        this.teamList = []
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.platformId = ''
      this.dateRange = getDefaultDateRange()
      this.teamIds = []
      this.teamList = []
      this.list = []
    },
    /** 处理表格排序 */
    handleSortChange({ prop, order }) {
      if (!prop || !order) {
        // 取消排序，恢复原始顺序
        this.list = [...this.originalList]
        return
      }

      // 分离汇总行和数据行（只有teamName字段可能是汇总）
      const summaryRow = this.originalList.find(item => item.teamName === '汇总')
      const dataRows = this.originalList.filter(item => item.teamName !== '汇总')

      // 对数据行进行排序
      const sortedDataRows = dataRows.sort((a, b) => {
        let aVal = a[prop]
        let bVal = b[prop]

        // 处理数值类型的排序
        if (typeof aVal === 'string' && !isNaN(parseFloat(aVal))) {
          aVal = parseFloat(aVal)
        }
        if (typeof bVal === 'string' && !isNaN(parseFloat(bVal))) {
          bVal = parseFloat(bVal)
        }

        if (order === 'ascending') {
          return aVal > bVal ? 1 : aVal < bVal ? -1 : 0
        } else {
          return aVal < bVal ? 1 : aVal > bVal ? -1 : 0
        }
      })

      // 重新组合数据：汇总行始终在第一位
      this.list = summaryRow ? [summaryRow, ...sortedDataRows] : sortedDataRows
    },

       // 格式化百分比显示
    formatPercentage(value) {
      if (value === null || value === undefined || value === '') {
        return '0%'
      }

      // 接口返回的是数字，直接添加百分号
      const numValue = Number(value)
      if (!isNaN(numValue)) {
        return numValue + '%'
      }

      return '0%'
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
