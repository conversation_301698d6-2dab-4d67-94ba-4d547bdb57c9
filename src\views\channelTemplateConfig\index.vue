<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-box">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="渠道ID" prop="id">
          <el-input v-model="queryParams.id" placeholder="请输入渠道ID" clearable size="small" @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="搜索内容" prop="searchText">
          <el-input v-model="queryParams.searchText" placeholder="渠道名称/配置名称/描述" clearable size="small" @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-toolbar">
      <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
    </div>

    <!-- 表格区域 -->
    <el-table :data="channelList" border>
      <el-table-column label="渠道ID" align="center" prop="id" />
      <el-table-column label="配置名称" align="center" prop="name" />
      <el-table-column label="渠道名称" align="center" prop="channelName" />
      <el-table-column label="配置描述" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="更新人" align="center" prop="updateBy" />
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-document-copy" @click="handleCopy(scope.row)">复制</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body @open="getChannelOptions">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="渠道" prop="id">
          <el-select v-model="form.id" placeholder="请选择渠道" style="width: 100%" :disabled="isEdit" filterable>
            <el-option
              v-for="item in channelOptions"
              :key="item.id"
              :label="`${item.id} - ${item.channelName}`"
              :value="item.id"
              :disabled="!item.selectable"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配置描述" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入配置描述" type="textarea" :rows="2" />
        </el-form-item>
        <el-form-item label="渠道配置">
          <div v-for="(item, index) in form.configList" :key="index" class="config-item">
            <el-form-item
              :prop="`configList.${index}.key`"
              :rules="[{ required: true, message: '配置项名称不能为空', trigger: 'blur' }]"
              style="margin-bottom: 0; margin-right: 10px; width: 40%;"
            >
              <el-input v-model="item.key" placeholder="配置项名称" :disabled="item.isPreset">
                <template slot="prepend">
                  <span v-if="item.isPreset">
                    {{ getKeyDescription(item.key) }}
                  </span>
                  <span v-else>key</span>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item
              :prop="`configList.${index}.value`"
              style="margin-bottom: 0; margin-right: 10px; width: 40%;"
            >
              <el-input v-model="item.value" placeholder="配置项值" @click.native="handleValueClick(item, index)" v-if="item.key !== 'isActivated'">
              </el-input>
              <el-switch 
                v-else
                v-model="item.value" 
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            <el-button 
              type="danger" 
              icon="el-icon-delete" 
              circle 
              size="mini" 
              @click="removeConfigItem(index)" 
              :disabled="item.isPreset">
            </el-button>
          </div>
          <div style="margin-top: 10px;">
            <el-button type="primary" icon="el-icon-plus" size="small" @click="addConfigItem">添加配置项</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 查看配置对话框 -->
    <el-dialog title="查看渠道配置" :visible.sync="configDialogVisible" width="600px" append-to-body>
      <pre class="config-preview">{{ configPreview }}</pre>
    </el-dialog>

    <!-- 富文本编辑器对话框 -->
    <el-dialog title="编辑内容" :visible.sync="richTextDialogVisible" width="800px" append-to-body>
      <div ref="editor" class="rich-text-editor"></div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelRichText">取 消</el-button>
        <el-button type="primary" @click="confirmRichText">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { searchChannelTemplate, createChannelTemplate, updateChannelTemplate, deleteChannelTemplate } from '@/api/channelTemplateConfig'
import { getChannelTemplateChannels } from '@/api/channeManage/channelList'
import E from 'wangeditor'
import Pagination from "@/components/Pagination"

export default {
  name: 'ChannelTemplateConfig',
  components: {
    Pagination
  },
  data() {
    return {
      // 总条数
      total: 0,
      // 渠道列表
      channelList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 配置预览对话框
      configDialogVisible: false,
      // 配置预览内容
      configPreview: '',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        searchText: undefined
      },
      // 渠道选项
      channelOptions: [],
      // 表单参数
      form: {
        id: undefined,
        name: undefined,
        remark: undefined,
        configList: []
      },
      // 表单校验
      rules: {
        id: [
          { required: true, message: '请选择渠道', trigger: 'change' }
        ],
        name: [
          { required: true, message: '请输入配置名称', trigger: 'blur' }
        ]
      },
      isEdit: false,
      // 富文本编辑器对话框
      richTextDialogVisible: false,
      // 富文本编辑器实例
      editor: null,
      // 当前编辑的配置项索引
      currentEditIndex: -1,
      // 预设配置项列表
      presetConfigs: [
        { key: 'logo', description: 'LOGO图片', defaultValue: '', isPreset: true },
        { key: 'theme', description: '主题名称', defaultValue: '', isPreset: true },
        { key: 'platformName', description: '平台名称', defaultValue: '', isPreset: true },
        { key: 'platformSlogan', description: '平台口号', defaultValue: '', isPreset: true },
        { key: 'declaration', description: '底部声明', defaultValue: '', isPreset: true },
        { key: 'agreementKeyQw', description: '企微协议', defaultValue: '', isPreset: true },
        { key: 'agreementKeyAuth', description: '留资协议', defaultValue: '', isPreset: true },
        { key: 'agreementKeyIndex', description: '首页协议', defaultValue: '', isPreset: true },
        { key: 'agreementKeyApply', description: '线下产品页协议', defaultValue: '', isPreset: true },
        { key: 'wxOfficialQrCode', description: '公众号二维码', defaultValue: 'https://jst.oss-utos.hmctec.cn/common/path/c61a4be3e567420e9d28d8d82ebd893d.jpg', isPreset: true },
        { key: 'wxOfficialName', description: '公众号名字', defaultValue: '想米用', isPreset: true },
        { key: 'isActivated', description: '激活类型', defaultValue: false, isPreset: true }
      ]
    }
  },
  created() {
    this.getList()
  },
  beforeDestroy() {
    // 组件销毁前清理富文本编辑器
    if (this.editor) {
      this.editor.destroy()
      this.editor = null
    }
  },
  methods: {
    // 获取渠道列表
    getList() {
      searchChannelTemplate(this.queryParams).then(response => {
        this.channelList = response.rows
        this.total = response.total
      })
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        id: undefined,
        searchText: undefined
      }
      this.handleQuery()
    },
    // 获取渠道选项
    getChannelOptions() {
      getChannelTemplateChannels().then(response => {
        this.channelOptions = response.data || []
      })
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        remark: undefined,
        configList: []
      }
      this.resetForm('form')
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 获取默认的预设配置项列表
    getDefaultConfigList() {
      return this.presetConfigs.map(item => ({
        key: item.key,
        value: item.defaultValue,
        isPreset: item.isPreset
      }));
    },
    // 处理配置数据
    processConfigData(config, namePrefix = '') {
      const parsedConfig = typeof config === 'object' ? config : JSON.parse(config)
      
      // 构建表单数据
      const formData = {
        id: namePrefix ? undefined : this.form.id,
        name: namePrefix ? `${this.form.name}${namePrefix}` : this.form.name,
        remark: this.form.remark,
        configList: Object.entries(parsedConfig).map(([key, value]) => {
          if (key === 'isActivated') {
            return {
              key,
              value: Boolean(value),
              isPreset: this.presetConfigs.some(item => item.key === key)
            }
          }
          return { 
            key, 
            value, 
            isPreset: this.presetConfigs.some(item => item.key === key) 
          }
        })
      }
      
      // 确保所有预设配置项都存在
      this.presetConfigs.forEach(presetItem => {
        if (!formData.configList.some(item => item.key === presetItem.key)) {
          // 为 isActivated 特殊处理，确保值为布尔类型 false
          if (presetItem.key === 'isActivated') {
            formData.configList.push({ key: presetItem.key, value: false, isPreset: true });
          } else {
            formData.configList.push({ key: presetItem.key, value: presetItem.defaultValue, isPreset: true });
          }
        }
      })
      
      return formData
    },
    // 新增按钮操作
    handleAdd() {
      this.reset()
      // 添加预设配置项
      this.form.configList = this.getDefaultConfigList()
      this.open = true
      this.title = '添加渠道模板配置'
      this.isEdit = false
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset()
      this.form = {
        ...this.processConfigData(row.config),
        id: row.id,
        name: row.name,
        remark: row.remark
      }
      this.open = true
      this.title = '修改渠道模板配置'
      this.isEdit = true
    },
    // 提交按钮
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 检查配置项列表是否为空
          if (this.form.configList.length === 0) {
            this.$message.error('请至少添加一个配置项')
            return
          }
          
          const config = {}
          this.form.configList.forEach(item => {
            if (item.key === 'isActivated') {
              config[item.key] = Boolean(item.value)
            } else {
              config[item.key] = item.value || ''
            }
          })
          
          const formData = {
            id: this.form.id,
            name: this.form.name,
            remark: this.form.remark,
            config
          }
          
          if (this.isEdit) {
            updateChannelTemplate(formData).then(response => {
              this.$message.success('操作成功')
              this.open = false
              this.getList()
            })
          } else {
            createChannelTemplate(formData).then(response => {
              this.$message.success('操作成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    // 删除按钮操作
    handleDelete(row) {
      this.$confirm('是否确认删除该渠道配置?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteChannelTemplate(row.id).then(() => {
          this.$message.success('删除成功')
          this.getList()
        })
      }).catch(() => {})
    },
    // 添加配置项
    addConfigItem() {
      this.form.configList.push({
        key: '',
        value: '',
        isPreset: false
      })
    },
    // 删除配置项
    removeConfigItem(index) {
      if (this.form.configList[index].isPreset) {
        this.$message.warning('预设配置项不允许删除')
        return
      }
      this.form.configList.splice(index, 1)
    },
    // 复制按钮操作
    handleCopy(row) {
      this.reset()
      this.form = this.processConfigData(row.config, '-副本')
      this.form.name = row.name ? `${row.name}-副本` : ''
      this.form.remark = row.remark
      
      this.open = true
      this.title = '复制渠道模板配置'
      this.isEdit = false
    },
    // 富文本编辑器取消按钮
    cancelRichText() {
      this.richTextDialogVisible = false
    },
    // 富文本编辑器确认按钮
    confirmRichText() {
      if (this.editor && this.currentEditIndex !== -1) {
        // 获取富文本内容并更新到对应的配置项
        const htmlContent = this.editor.txt.html()
        this.form.configList[this.currentEditIndex].value = htmlContent
      }
      this.richTextDialogVisible = false
    },
    // 处理value点击事件
    handleValueClick(item, index) {
      if (item.key === 'declaration') {
        this.currentEditIndex = index
        this.richTextDialogVisible = true
        // 在对话框打开后初始化编辑器
        this.$nextTick(() => {
          this.initEditor(item.value)
        })
      }
    },
    // 初始化富文本编辑器
    initEditor(content) {
      // 销毁之前的编辑器实例
      if (this.editor) {
        this.editor.destroy()
        this.editor = null
      }
      
      // 创建富文本编辑器
      const editor = new E(this.$refs.editor)
      
      // 配置编辑器
      editor.config.zIndex = 10
      editor.config.placeholder = '请输入内容...'
      
      // 创建编辑器
      editor.create()
      
      // 设置初始内容
      if (content) {
        editor.txt.html(content)
      }
      
      // 保存编辑器实例
      this.editor = editor
    },
    // 获取预设key的描述
    getKeyDescription(key) {
      const presetItem = this.presetConfigs.find(item => item.key === key)
      return presetItem ? presetItem.description : key
    }
  }
}
</script>

<style scoped>
.search-box {
  margin-bottom: 20px;
}
.table-toolbar {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.config-preview {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 22px;
}
.rich-text-editor {
  min-height: 300px;
}
</style> 