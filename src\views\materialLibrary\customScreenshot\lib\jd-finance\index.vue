<template>
  <div class="jd-finance-container">
    <!-- 左侧预览区域 -->
    <div class="preview-area">
      <Preview ref="previewRef" :config-data="configData" />
    </div>

    <!-- 右侧控制面板 -->
    <div class="config-panel">
      <Controller
        @capture-screenshot="handleCapture"
        :preview-url="screenshotPreviewUrl"
        :config-data="configData"
        @update:config="updateConfig"
      />
    </div>
  </div>
</template>

<script>
import Preview from './components/preview.vue'
import Controller from './components/controller.vue'

export default {
  name: 'JdFinance', 
  components: {
    Preview,
    Controller
  },
  data() {
    return {
      screenshotPreviewUrl: null,
      configData: { 
        device: {
          os: 'ios'
        },
        statusBar: {
          time: '9:41',
          showSignal: true,
          showWifi: true,
          showBattery: true,
          batteryLevel: 100,
          mode: 'dark'
        },
        // 京东金融特定的配置项
        quotaValue: '24,700.02',
        baseValue: '25,200.00',
        listItems: [
          {
            title: '微信打白条',
            value: '可用8000'
          },
          {
            title: '购物/出行',
            value: '立减0.5元起'
          },
          {
            title: '分期购物',
            value: '大牌分期...'
          }
        ]
      }
    };
  },
  methods: {
    async handleCapture() {
      if (this.$refs.previewRef) {
        const dataUrl = await this.$refs.previewRef.captureScreenshot(); 
        if (dataUrl) {
          this.screenshotPreviewUrl = dataUrl;
        } else {
          this.screenshotPreviewUrl = null;
        }
      } else {
        console.error('Preview component reference not found.');
      }
    },
    updateConfig(newConfig) {
      this.configData = Object.assign({}, this.configData, newConfig);
    }
  }
}
</script>

<style lang="scss" scoped>
.jd-finance-container {
  display: flex;
  background-color: #fff;

  .preview-area {
    padding: 20px;
    background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    background-color: #ffffff;
    height: max-content;
  }

  .config-panel {
    flex: 1;
    margin-left: 20px;
    padding: 20px;
    overflow-y: auto;
  }
}
</style>
