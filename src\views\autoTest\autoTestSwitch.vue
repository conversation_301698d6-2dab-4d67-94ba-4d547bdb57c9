<template>
  <div class="app-container">
    <div class="table-header">
      <el-button type="primary" @click="getStatus" icon="el-icon-refresh">刷新</el-button>
    </div>
    
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column
        prop="name"
        label="自动测试类型"
        align="center">
      </el-table-column>
      <el-table-column
        prop="status"
        label="启用状态"
        align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enabled"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="启用"
            inactive-text="禁用"
            @change="handleSwitchChange(scope.row.type, scope.row.enabled)">
          </el-switch>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getAutoTestStatus, updateAutoTestStatus } from '@/api/autoTest/autoTestSwitch'

export default {
  name: 'AutoTestSwitch',
  data() {
    return {
      tableData: [
        { 
          type: '企微', 
          name: '企微自动测试',
          enabled: false 
        },
        { 
          type: '贷超', 
          name: '贷超自动测试',
          enabled: false 
        }
      ],
      loading: false
    }
  },
  created() {
    this.getStatus()
  },
  methods: {
    // 获取自动测试状态
    async getStatus() {
      try {
        this.loading = true
        const response = await getAutoTestStatus()
        if (response.data) {
          // 更新表格数据的启用状态
          this.tableData.forEach(item => {
            if (response.data[item.type] !== undefined) {
              item.enabled = response.data[item.type] == 'true'
            }
          })
        }
      } finally {
        this.loading = false
      }
    },
    
    // 处理开关切换
    async handleSwitchChange(type, value) {
      // 恢复开关状态，等待用户确认
      const item = this.tableData.find(item => item.type === type)
      if (item) {
        // 先恢复到原状态
        item.enabled = !value
      }
      
      try {
        // 二次确认
        await this.$confirm(
          `确定要${value ? '启用' : '禁用'}${type}自动测试吗？`, 
          '确认操作', 
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        // 用户确认后再将状态切换回来
        if (item) {
          item.enabled = value
        }
        
        const params = {
          type: type,
          enabled: value
        }
        
        await updateAutoTestStatus(params)
        
        this.$message({
          type: 'success',
          message: `${type}自动测试已${value ? '启用' : '禁用'}`
        })
        
        // 重新获取状态以确保同步
        this.getStatus()
      } catch (error) {
        // 用户取消或操作失败，已经恢复了开关状态，不需要再处理
        if (error !== 'cancel' && error !== 'close') {
          this.$message({
            type: 'error',
            message: '操作失败，请稍后重试'
          })
        }
      }
    }
  }
}
</script>

<style scoped>
.table-header {
  margin-bottom: 20px;
}
</style> 