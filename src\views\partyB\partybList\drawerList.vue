<template>
  <el-drawer title="款项记录" :visible.sync="drawer_" size="1200px" :direction="direction" @close="handCloseDrawer">
    <div class="drawer-wrap">
      <div class="drawer-tabs flex">
        <div style="margin-right:20px" :class="['flex', 'justify-content-c', 'align-items-c', type == 1 ? 'active' : '']"
          @click="handleToggle(1)">
          请款记录
        </div>
        <div :class="['flex', 'justify-content-c', 'align-items-c', type == 2 ? 'active' : '']" @click="handleToggle(2)">
          退款记录</div>
      </div>

      <template v-if="type == 1">
        <el-form :inline="true" :model="requestQueryParams">
          <el-form-item label="时间查询">
            <el-col :span="11">
              <el-date-picker v-model="dateRange" type="datetimerange" size="small" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']"
                value-format="yyyy-MM-dd HH:mm:ss" @change="handleQuery">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="状态">
            <el-select clearable size="small" v-model="requestQueryParams.status" @change="handleQuery">
              <el-option :value="-1" label="全部"> </el-option>
              <el-option :value="0" label="待审核"> </el-option>
              <el-option :value="1" label="已通过"> </el-option>
              <el-option :value="2" label="已驳回"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="tableData" style="width: 100%" :key="type">
          <el-table-column prop="cashOutNumber" align="center" label="请款单号">
          </el-table-column>
          <el-table-column prop="money" label="请款金额" align="center" width="180">
          </el-table-column>
          <el-table-column prop="cashOutMs" align="center" label="请款提交时间" width="180">
          </el-table-column>
          <el-table-column prop="partyUsername" align="center" label="请款人" width="180">
          </el-table-column>
          <el-table-column label="当前审核人" align="center" width="180" show-overflow-tooltip>
            <template slot-scope="{row}">
              <div>
                {{ row.postName }} <br> {{ row.userNames }}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="审核状态" width="180">
            <template slot-scope="{ row }">
              <el-tag :type="colorType[row.status]" size="small" effect="plain">
                {{ typeStatus[row.status] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="查看凭证" prop="price" align="center">
            <template slot-scope="{ row }">
              <div v-if="row.fileName">
                <el-image style="width: 30px; height: 30px" :src="row.fileName" :preview-src-list="[row.fileName]">
                </el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="reason" align="center" label="备注" width="180" show-overflow-tooltip>
            <template slot-scope="{row}">
              <div>
                {{ row.reason || "-" }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="requestQueryParams.pageNum"
          :limit.sync="requestQueryParams.pageSize" @pagination="initList" />
      </template>
      <template v-if="type == 2">
        <el-form :inline="true" :model="queryParams">
          <el-form-item label="时间">
            <el-col :span="11">
              <el-date-picker v-model="dateRange1" size="small" type="datetimerange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']"
                value-format="yyyy-MM-dd HH:mm:ss" @change="handleRefundQuery">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="状态">
            <el-select clearable size="small" v-model="queryParams.status" @change="handleRefundQuery">
              <el-option :value="0" label="待审核"> </el-option>
              <el-option :value="1" label="已通过"> </el-option>
              <el-option :value="2" label="已驳回"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleRefundQuery" size="small">搜索</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="tableData1" :key="type">
          <el-table-column label="申请时间" align="center" prop="applyTime" />
          <el-table-column label="申请金额" align="center" prop="refundMoney" />
          <el-table-column label="审核状态" align="center">
            <template slot-scope="{ row }">
              <el-tag :type="colorType[row.status]" size="small" effect="plain">
                {{ typeStatus[row.status] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="申请人" align="center" prop="appUserName" />
          <el-table-column label="当前审核人" align="center">
            <template slot-scope="{row}">
              <div>
                {{ row.postName }} <br> {{ row.userNames }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="退款凭证" align="center" prop="partyUsername">
            <template slot-scope="{row}">
              <div v-if="row.fileName">
                <el-image style="width: 30px; height: 30px" :src="row.fileName" :preview-src-list="[row.fileName]">
                </el-image>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total1 > 0" :total="total1" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getRefundListData" />
      </template>
    </div>
  </el-drawer>
</template>

<script>
import { getCashOutList, getRefundList } from "@/api/partyB";
import { hasBool } from "@/directive/permission/hasBool"
export default {
  data() {

    return {
      type: 1,
      total: 0,
      total1: 0,
      typeStatus: {
        0: "待审核",
        1: "已通过",
        2: "已拒绝",
      },
      colorType: {
        0: "warning",
        1: "success",
        2: "danger",
      },
      requestQueryParams: {
        pageNum: 1,
        pageSize: 10,
        id: "",
        status: -1,
        startTime: "",
        stopTime: "",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        partybId: "",
        status: "",
        startTime: "",
        stopTime: ""
      },
      tableData: [],
      tableData1: [],
      dateRange: "",
      dateRange1: ""
    }
  },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    partybId: {
      type: [String, Number],
      default: ''
    },
    row: {
      type: Object,
      default: () => { }
    },
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer
      },
      set(v) {
        this.$emit("update:drawer", false)
      }
    }
  },
  watch: {
    drawer(val) {
      if (!hasBool('partyB:requestMoeny:add')) {
        this.type = 2
        this.getRefundListData()
      }
      if (val && this.type == 1) {
        this.initList()
      }
    }
  },
  methods: {
    handleToggle(type) {
      this.type = type
      if (this.type == 2 && !this.tableData1.length) {
        this.getRefundListData()
      }

    },

    handCloseDrawer() {
      this.type = 1
      this.drawer_ = false
    },
    getRefundListData() {
      this.queryParams.partybId = this.row.id
      getRefundList(this.queryParams).then(res => {
        this.tableData1 = res.rows
        this.total1 = res.total
      })
    },
    initList() {
      this.requestQueryParams.id = this.row.id
      if (this.type == 1) {
        getCashOutList(this.requestQueryParams).then((res) => {
          this.tableData = res.rows
          this.total = res.total;
        })
      } else {
        this.getRefundListData()
      }
    },
    handleQuery() {
      this.requestQueryParams.pageNum = 1
      if (this.dateRange != null) {
        this.requestQueryParams.startTime = this.dateRange[0];
        this.requestQueryParams.stopTime = this.dateRange[1];
      } else {
        this.requestQueryParams.startTime = "";
        this.requestrequestQueryParams.stopTime = "";
      }
      this.initList()
    },
    handleRefundQuery() {
      this.queryParams.pageNum = 1
      if (this.dateRange1 && this.dateRange1.length) {
        this.queryParams.startTime = this.dateRange1[0]
        this.queryParams.stopTime = this.dateRange1[1]
      } else {
        this.queryParams.startTime = ""
        this.queryParams.stopTime = ""
      }
      this.getRefundListData()
    }
  },
  mounted() {
  }
}
</script>

<style lang="scss" scoped>
.drawer-wrap {

  padding: 20px 20px;

  .drawer-tabs {
    margin-bottom: 20px;

    div {
      flex: 1;
      height: 40px;
      background: #FFEFE3;
      border-radius: 4px;
      font-size: 16px;
      color: #171717;
      cursor: pointer;

      &.active {
        color: #fff;
        background: #E37318;
      }
    }
  }
}
</style>
