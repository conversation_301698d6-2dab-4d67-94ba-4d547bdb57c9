<template>
  <div class="loan-quota-coupon">
    <div class="coupon-item-label">
      额度券
    </div>
    <div class="coupon-item-value">
      <div class="coupon-item-value-label">{{ configData.couponDesc || '提升可用额度' }}</div>
      <img src="https://jst.oss-utos.hmctec.cn/common/path/6915502722fc4387b7c17b0d0e894616.png" alt="" class="icon-arrow-right">
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoanQuotaCoupon',
  props: {
    configData: {
      type: Object,
      required: true
    }
  }
}
</script>

<style scoped lang="scss">
.loan-quota-coupon {
  margin: 22px auto 12px;
  width: 695px;
  height: 124px;
  background: #FFFFFF;
  border-radius: 20px 20px 20px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40px 20px;

  .coupon-item-label {
    font-weight: 500;
    font-size: 32px;
    color: #333333;
    line-height: 46px;
  }

  .coupon-item-value {
    display: flex;
    align-items: center;
    gap: 8px;

    .coupon-item-value-label {
      font-weight: 400;
      font-size: 28px;
      color: #9F9F9F;
      line-height: 41px;
    }

    .icon-arrow-right {
      width: 15px;
      height: 23px;
      transform: translateY(3px);
    }
  }
}
</style> 