<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          size="mini"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="推广名称">
        <el-input
          size="mini"
          @keyup.enter.native="handleQuery"
          clearable
          placeholder="请输入推广名称"
          v-model="queryParams.productName"
        ></el-input>
      </el-form-item>
      <el-form-item label="产品类型" v-if="isShowMore">
        <el-select
          v-model="prodType"
          size="small"
          clearable
          style="width: 140px"
        >
          <el-option :value="1" label="银行机构">银行机构</el-option>
          <el-option :value="4" label="一级机构">一级机构</el-option>
          <el-option :value="5" label="二级机构">二级机构</el-option>
          <el-option :value="6" label="三级机构">三级机构</el-option>
          <el-option :value="7" label="四级机构">四级机构</el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="产品是否在线">
        <el-select
          v-model="queryParams.productStatus"
          size="small"
          clearable
          style="width: 140px"
        >
          <el-option :value="1" label="在线"></el-option>
          <el-option :value="2" label="不在"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商户名称">
        <el-input
          v-model="queryParams.partyName"
          @keyup.enter.native="handleQuery"
          size="small"
          placeholder="请输入商户名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="城市">
        <el-cascader
          v-model="cityArr"
          :options="cityList"
          @change="handleQuery"
          clearable
          filterable
          size="mini"
        >
        </el-cascader>
      </el-form-item>
      <el-form-item label="商务" v-if="!isExternalUser">
        <el-select
          v-model="queryParams.userName"
          size="small"
          clearable
          filterable
          style="width: 140px"
        >
          <el-option
            v-for="item in userNameArr"
            :key="item"
            :value="item"
            :label="item"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['loan:productMidStatsList:export']"
          >导出</el-button
        >
      </el-form-item>
      <el-form-item>
        <div class="online">
          当前接单商户数量<span style="color: red; padding-left: 10px">{{
            productCount
          }}</span>
        </div>
      </el-form-item>
    </el-form>

    <el-table :data="dataListCopy" border @sort-change="handleSortChange">
      <el-table-column
        label="推广ID"
        prop="productId"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        align="center"
      />
      <el-table-column label="推广名称" prop="productName" align="center" />
      <el-table-column label="推广类型" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ type[row.mid * 1] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="投放城市"
        sortable="custom"
        prop="cityNames"
        align="center"
      >
        <template slot-scope="{ row }">
          <div v-if="row.cityNames && row.cityNames.split(',').length > 2">
            <div>
              <el-popover
                placement="top-start"
                width="400"
                trigger="hover"
                :content="row.cityNames"
              >
                <el-button slot="reference" type="text">查看城市</el-button>
              </el-popover>
            </div>
          </div>
          <div v-else>{{ row.cityNames == "全国" ? "" : row.cityNames }}</div>
        </template>
      </el-table-column>
      <el-table-column label="接单类型" prop="productMidName" align="center" />
      <el-table-column label="商务" prop="sysUserNickName" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-tag v-if="row.isTransfer == 1">转</el-tag>
            {{ row.sysUserNickName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        sortable="custom"
        label="最新推广价格"
        align="center"
        prop="cooperationCost"
        :sort-orders="['descending', 'ascending']"
      >
        <template slot-scope="{ row }">
          <div
            :class="[
              row.matchingPriceSort == null ||
              row.matchingPriceSort == row.cooperationCost
                ? ''
                : 'red',
            ]"
          >
            {{ row.cooperationCost }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        sortable="custom"
        label="推广价格"
        align="center"
        prop="matchingPriceSort"
        :sort-orders="['descending', 'ascending']"
        v-if="showPrice"
      >
        <template slot-scope="{ row }">
          <div
            :class="[
              row.matchingPriceSort == null ||
              row.matchingPriceSort == row.cooperationCost
                ? ''
                : 'red',
            ]"
          >
            {{ row.matchingPriceSort }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        sortable="custom"
        label="回调价格"
        prop="callbackPrice"
        align="center"
        :sort-orders="['descending', 'ascending']"
      />
      <el-table-column
        sortable="custom"
        label="曝光数量"
        prop="matchingNum"
        align="center"
        :sort-orders="['descending', 'ascending']"
      />
      <el-table-column
        label="有效曝光数量"
        sortable="custom"
        prop="pushNum"
        align="center"
        :sort-orders="['descending', 'ascending']"
      />

      <el-table-column
        label="转化率"
        sortable="custom"
        prop="pushRate"
        align="center"
        :sort-orders="['descending', 'ascending']"
      >
        <!-- <template  slot-scope="{row}">
          <div>
            {{
              row.matchingNum
                ? ((row.pushNum / row.matchingNum) * 100).toFixed(2)
                : 0
            }}%
          </div>
        </template> -->
        <template slot-scope="{ row }">
          <div>{{ row.pushRate }}%</div>
        </template>
      </el-table-column>
      <el-table-column
        label="预估收益"
        sortable="custom"
        prop="profit"
        align="center"
        :sort-orders="['descending', 'ascending']"
      />
      <el-table-column
        label="实际收益"
        sortable="custom"
        prop="realProfit"
        align="center"
        :sort-orders="['descending', 'ascending']"
      />
    </el-table>
    <!-- <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    /> -->
    <el-dialog
      title="导出"
      :visible.sync="exportVisible"
      center
      width="800px"
      append-to-body
      @close="dateRange = []"
      :close-on-click-modal="false"
    >
      <el-form label-position="right" label-width="180px">
        <el-form-item label="请选择导出时间段">
          <el-date-picker
            v-model="dateRange"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
          >
          </el-date-picker>
        </el-form-item>
        <el-button
          type="primary"
          style="margin-left: 200px"
          @click="exportSubmit"
          >确认导出</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import {
  getNewProductLoan,
  getMidProductCount,
  exportData,
  getCityList,
} from "@/api/statisticalManage";
import { sum } from '@/utils/calculate'

export default {
  name: "Loan",
  data() {
    return {
      showUsers: ["shenxun", "yutong", "sunyang", "madman", "xupengcheng"],
      prodType: "",
      value1: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      dataList: [],
      dateRange: [],
      cityList: [],
      cityArr: [],
      exportVisible: false,
      proptype: "",
      productCount: 0,
      sumData: {},
      type: {
        1: "银行机构",
        2: "线上-贷超",
        3: "线上持牌机构",
        4: "一级机构",
        5: "二级机构",
        6: "三级机构",
        7: "四级机构",
      },
      userNameArr: [],
      total: 0,
      queryParams: {
        partyName: "",
        productName: "",
        city: "",
        productStatus: "",
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
        userName: "",
      },
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.value1 !== null) {
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      if (this.cityArr[1]) {
        this.queryParams.city = this.cityArr[1];
      } else {
        this.queryParams.city = "";
      }
      this.getList();
    },

    getTotal(arr, type) {
      /* return arr.reduce((pre, data) => {
        return pre + data[type];
      }, 0); */

      return sum(arr.map(item => item[type]));
    },
    //前端排序
    handleSortChange(column) {
      this.proptype = column.prop;
      let sortingType = column.order;
      this.sumData = this.dataList[0];
      if (sortingType == "ascending") {
        //正序
        this.dataList = this.dataList
          .splice(1)
          .sort((a, b) => b[this.proptype] - a[this.proptype]);

        this.dataList.unshift(this.sumData);
      }
      if (sortingType == "descending") {
        // 倒序
        this.dataList = this.dataList
          .splice(1)
          .sort((a, b) => a[this.proptype] - b[this.proptype]);
        this.dataList.unshift(this.sumData);
      }
    },

    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    getList() {
      if (this.isExternalUser) {
        this.queryParams.userName = this.$store.getters.nickName;
      }

      getNewProductLoan(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total;

        this.dataList.unshift({
          productId: "合计",
          matchingNum: this.getTotal(this.dataList, "matchingNum"),
          pushNum: this.getTotal(this.dataList, "pushNum"),
          profit: this.getTotal(this.dataList, "profit").toFixed(2),
          realProfit: this.getTotal(this.dataList, "realProfit").toFixed(2),
          pushRate: this.getTotal(this.dataList, "matchingNum")
            ? (
                (this.getTotal(this.dataList, "pushNum") /
                  this.getTotal(this.dataList, "matchingNum")) *
                100
              ).toFixed(2)
            : 0,
          callbackPrice: this.getTotal(this.dataList, "callbackPrice"),
        });
        let userName = res.rows
          .map((item) => item.sysUserNickName)
          .filter(Boolean);
        this.userNameArr = [...new Set(userName)];
      });

      getMidProductCount().then((res) => {
        this.productCount = res.data;
      });
    },
    handleExport() {
      this.exportVisible = true;
    },
    //导出
    exportSubmit() {
      if (this.dateRange.length) {
        exportData({
          startTime: this.dateRange[0],
          stopTime: this.dateRange[1],
        }).then((res) => {
          let a = document.createElement("a");
          let blob = new Blob([res], { type: "application/vnd.ms-excel" });
          let objectUrl = URL.createObjectURL(blob);
          a.setAttribute("href", objectUrl);
          a.setAttribute("download", `助贷统计.xlsx`);
          a.click();
          this.exportVisible = false;
          this.dateRange = [];
          this.$message.success("导出成功");
        });
      }
    },
  },
  computed: {
    showPrice() {
      console.log(this.$store.getters.userInfo.userName);
      return ["sunyang", "yutong", "shenxun"].includes(
        this.$store.getters.userInfo.userName
      );
    },
    dataListCopy() {
      if (!this.prodType && !this.queryParams.userName) return this.dataList;
      if (!this.prodType && this.queryParams.userName) {
        let data = this.dataList.filter(
          (item) => item.sysUserNickName == this.queryParams.userName
        );
        data.unshift({
          productId: "合计",
          matchingNum: this.getTotal(data, "matchingNum"),
          pushNum: this.getTotal(data, "pushNum"),
          profit: this.getTotal(data, "profit").toFixed(2),
          realProfit: this.getTotal(data, "realProfit").toFixed(2),
          pushRate: this.getTotal(data, "matchingNum")
            ? (
                (this.getTotal(data, "pushNum") /
                  this.getTotal(data, "matchingNum")) *
                100
              ).toFixed(2)
            : 0,
          callbackPrice: this.getTotal(data, "callbackPrice"),
        });
        return data;
      }
      if (this.prodType && !this.queryParams.userName) {
        let data = this.dataList.filter((item) => item.mid == this.prodType);
        data.unshift({
          productId: "合计",
          matchingNum: this.getTotal(data, "matchingNum"),
          pushNum: this.getTotal(data, "pushNum"),
          profit: this.getTotal(data, "profit").toFixed(2),
          realProfit: this.getTotal(data, "realProfit").toFixed(2),
          pushRate: this.getTotal(data, "matchingNum")
            ? (
                (this.getTotal(data, "pushNum") /
                  this.getTotal(data, "matchingNum")) *
                100
              ).toFixed(2)
            : 0,
          callbackPrice: this.getTotal(data, "callbackPrice"),
        });
        return data;
      }
      if (this.prodType && this.queryParams.userName) {
        let data = this.dataList
          .filter((item) => item.mid == this.prodType)
          .filter((item) => item.sysUserNickName == this.queryParams.userName);
        data.unshift({
          productId: "合计",
          matchingNum: this.getTotal(data, "matchingNum"),
          pushNum: this.getTotal(data, "pushNum"),
          profit: this.getTotal(data, "profit").toFixed(2),
          realProfit: this.getTotal(data, "realProfit").toFixed(2),
          pushRate: this.getTotal(data, "matchingNum")
            ? (
                (this.getTotal(data, "pushNum") /
                  this.getTotal(data, "matchingNum")) *
                100
              ).toFixed(2)
            : 0,
          callbackPrice: this.getTotal(data, "callbackPrice"),
        });
        return data;
      }
    },
    isShowMore() {
      return this.showUsers.includes(this.$store.getters.userInfo.userName);
    },

    // 当前登录的是外部人员
    isExternalUser() {
      const userName = this.$store.getters.userName;
      const externalUsers = ['dingcan'];

      return externalUsers.includes(userName);
    }
  },
  mounted() {
    this.getList();
    //获取城市格式化
    getCityList().then((res) => {
      let data = null;
      data = res.data.map((item) => {
        if (item.citys) {
          {
            return {
              value: item.code,
              label: item.name,
              disabled: false,
              children: [
                ...item.citys.map((citem) => {
                  return {
                    value: citem.code,
                    label: citem.name,
                    disabled: false,
                  };
                }),
              ],
            };
          }
        } else {
          return {
            value: item.code,
            label: item.name,
            disabled: false,
          };
        }
      });
      this.cityList = JSON.parse(JSON.stringify(data));
    });
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  user-select: none;
}
.online {
  font-size: 20px;
  font-weight: 600;
}

.red {
  color: red;
}
</style>
