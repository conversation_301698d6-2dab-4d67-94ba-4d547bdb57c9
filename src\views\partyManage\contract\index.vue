<template>
  <div class="app-container">
    <div class="status" v-if="show">
      合同状态: <span :style="color[status]">{{ statusType[status] }}</span>
      <span v-if="status == 3" style="margin-left: 10px"
        >失败理由：{{ contractCheckRemark }}</span
      >
    </div>
    <el-form
      label-position="top"
      :model="formData"
      ref="formData"
      :rules="rules"
    >
      <div>
        <div class="account">账户认证</div>
        <div class="info">
          <el-row type="flex" justify="space-between">
            <el-col :span="6">
              <el-form-item label="认证类型" prop="authType">
                <el-select
                  disabled
                  v-model="formData.authType"
                  clearable
                  size="mini"
                  style="width: 100%"
                  placeholder="请选择认证类型"
                >
                  <el-option label="公司" :value="1"></el-option>
                  <el-option label="个人" :value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公司全称：">
                <el-input
                  maxlength="20"
                  disabled
                  size="mini"
                  v-model="formData.companyName"
                  placeholder="请输入公司全称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公司统一社会信用码:">
                <el-input
                  maxlength="30"
                  disabled
                  size="mini"
                  v-model="formData.companyCreditCode"
                  placeholder="请输入公司统一社会信用码"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" justify="space-between">
            <el-col :span="6">
              <el-form-item label="授权代表姓名:" prop="authName">
                <el-input
                  disabled
                  size="mini"
                  maxlength="6"
                  v-model="formData.authName"
                  placeholder="请输入授权代表姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="授权代表身份证号：" prop="authIdCard">
                <el-input
                  disabled
                  size="mini"
                  placeholder="请输入授权代表身份证号"
                  v-model.trim="formData.authIdCard"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="联系人：" prop="contactsName">
                <el-input
                  disabled
                  size="mini"
                  maxlength="6"
                  placeholder="请输入联系人"
                  v-model="formData.contactsName"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" justify="space-between">
            <el-col :span="6">
              <el-form-item label="联系人电话:" prop="contactsPhone">
                <el-input
                  disabled
                  size="mini"
                  maxlength="11"
                  v-model.trim="formData.contactsPhone"
                  placeholder="请输入联系人电话 ："
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="联系人邮箱:" prop="contactsEmail">
                <el-input
                  disabled
                  size="mini"
                  maxlength="50"
                  v-model="formData.contactsEmail"
                  placeholder="请输入联系人邮箱"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="联系人地址:" prop="contactsAddress">
                <el-input
                  maxlength="50"
                  disabled
                  size="mini"
                  v-model="formData.contactsAddress"
                  placeholder="请输入联系人地址"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
      <div>
        <div class="account">办公地址</div>
        <div class="info">
          <el-row type="flex" justify="space-between">
            <el-col :span="6">
              <el-form-item label="所在省份" prop="provinceCode">
                <el-input
                  maxlength="50"
                  disabled
                  size="mini"
                  v-model="formData.provinceName"
                  placeholder="所在省份"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在市区" prop="cityCode">
                <el-input
                  maxlength="50"
                  disabled
                  size="mini"
                  v-model="formData.cityName"
                  placeholder="所在市区"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="所在地区" prop="areaCode">
                <el-input
                  maxlength="50"
                  disabled
                  size="mini"
                  v-model="formData.areaName"
                  placeholder="所在地区"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="6">
              <el-form-item label="详细地址" prop="address">
                <el-input
                  maxlength="50"
                  disabled
                  size="mini"
                  placeholder="请输入详细地址"
                  v-model="formData.address"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" :offset="3">
              <el-form-item label="门牌号" prop="doorplate">
                <el-input
                  maxlength="50"
                  disabled
                  size="mini"
                  v-model="formData.doorplate"
                  placeholder="请输入门牌号"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="account">资质信息</div>
      <div class="images">
        <div
          class="images-item"
          :span="5"
          v-for="i in qualifications"
          :key="i.id"
        >
          <div class="iamge-info">
            <div class="iamge-info-title">{{ i.title }}</div>
            <div>
              <el-form-item :prop="i.prop">
                <el-upload
                  disabled
                  class="avatar-uploader"
                  action=""
                  :show-file-list="false"
                  :auto-upload="false"
                  :on-change="(e) => changeUpImg(e, i)"
                >
                  <img
                    v-if="i.imageUrl && !i.type"
                    style="vertical-align: middle; max-height: 150px"
                    :src="i.imageUrl"
                  />
                  <video
                    v-if="i.imageUrl && i.type"
                    :src="i.imageUrl"
                    style="
                      vertical-align: middle;
                      max-height: 150px;
                      width: 150px;
                    "
                    controls
                    autoplay
                  ></video>
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
              <div class="remove">
                <el-button
                  type="primary"
                  @click="downLoad(i.imageUrl)"
                  v-if="i.imageUrl"
                  >下载</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="account" style="margin-top: 20px">合同协议</div>
      <div class="images">
        <div :span="5" v-for="i in contact" :key="i.id" class="images-item">
          <div class="iamge-info">
            <div class="iamge-info-title">{{ i.title }}</div>
            <div>
              <el-form-item :prop="i.prop">
                <el-upload
                  disabled
                  class="avatar-uploader"
                  action=""
                  :show-file-list="false"
                  :auto-upload="false"
                  :on-change="(e) => changeUpfile(e, i)"
                >
                  <img
                    v-if="i.tag"
                    style="
                      vertical-align: middle;
                      max-height: 150px;
                      width: 150px;
                    "
                    :src="require(`@/assets/images/OIP-C.jpg`)"
                    alt=""
                  />
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
              <div class="tag" v-if="i.tag">
                {{ i.tag }}
              </div>
              <div class="file-remove" v-if="i.tag">
                <el-button
                  type="primary"
                  @click="downLoad(i.imageUrl)"
                  size="mini"
                  >下载</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import {
  contractUploadFile,
  AddPartyaAdminContract,
  getContractInfo,
  updataContractInfo,
} from "@/api/partyManage";
export default {
  data() {
    var validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("手机号不能为空"));
      } else if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
        callback(new Error("手机号格式错误"));
      } else {
        callback();
      }
    };
    var validateEamil = (rule, value, callback) => {
      if (!value) {
        callback(new Error("邮箱不能为空"));
      } else if (
        !/^[a-zA-Z0-9_-]+@([a-zA-Z0-9]+\.)+(com|cn|net|org)$/.test(value)
      ) {
        callback(new Error("邮箱格式错误"));
      } else {
        callback();
      }
    };
    var validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("名字不能为空"));
      } else if (!/^[\u4e00-\u9fa5]{2,6}$/.test(value)) {
        callback(new Error("名字格式错误"));
      } else {
        callback();
      }
    };
    // if (id.length === 18) {
    //   let regExp =
    //     ;
    //   return regExp.test(id);
    // }
    // if (id.length === 15) {
    //   let regExp =
    //     /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/;
    //   return regExp.test(id);
    // }

    var validateID = (rule, value, callback) => {
      if (!value) {
        callback(new Error("授权代表身份证号不能为空"));
      } else if (
        !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/.test(
          value
        ) ||
        !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/.test(
          value
        )
      ) {
        callback(new Error("格式错误"));
      } else {
        callback();
      }
    };
    return {
      imageUrl: "",
      cityList: [],
      provinceList: [],
      areaList: [],
      countrysList: [],
      fileList: [],
      contractCheckRemark: "",
      show: false,
      color: {
        0: "color:red",
        1: "color:	#DAA520",
        2: "color:#32CD32",
        3: "color:red",
      },
      qualifications: [
        {
          title: "营业执照",
          id: 1,
          prop: "businessLicenseFilename",
          path: "businessLicenseFilepath",
          imageUrl: "",
        },
        {
          title: "法人身份证正面",
          id: 2,
          prop: "frontIdCardFilename",
          path: "frontIdCardFilepath",
          imageUrl: "",
        },
        {
          title: "法人身份证反面",
          id: 3,
          prop: "reverseIdCardFilename",
          path: "reverseIdCardFilepath",
          imageUrl: "",
        },
        {
          title: "办公场地视频",
          id: 4,
          prop: "officeVideoFilename",
          path: "officeVideoFilepath",
          type: "mp4",
          imageUrl: "",
        },
      ],
      contact: [
        {
          title: "广告投放合同",
          id: 5,
          prop: "adContractFilename",
          path: "adContractFilepath",
          tag: "",
        },
        {
          title: "产品承诺函",
          id: 6,
          prop: "productCommitmentFilename",
          path: "productCommitmentFilepath",
          tag: "",
        },
        {
          title: "合作准入表",
          id: 7,
          prop: "cooperationAccessFilename",
          path: "cooperationAccessFilepath",
          tag: "",
        },
        {
          title: "其他",
          id: 8,
          prop: "otherFilename",
          path: "otherFilepath",
          tag: "",
        },
      ],
      statusType: {
        // @ApiModelProperty(value = "合同审核状态(0未上传/1待审核/2审核成功/3审核失败)")
        0: "未上传",
        1: "待审核",
        2: "审核成功",
        3: "审核失败",
      },
      status: 0,
      formData: {
        authType: null,
        companyName: null,
        companyCreditCode: null,
        authName: null,
        authIdCard: null,
        contactsName: null,
        contactsPhone: null,
        contactsEmail: null,
        contactsAddress: null,
        doorplate: null,
        areaCode: null,
        provinceCode: null,
        cityCode: null,
        businessLicenseFilename: null,
        businessLicenseFilepath: null,
        frontIdCardFilename: null,
        frontIdCardFilepath: null,
        reverseIdCardFilename: null,
        reverseIdCardFilepath: null,
        officeVideoFilename: null,
        officeVideoFilepath: null,
        adContractFilename: null,
        adContractFilepath: null,
        productCommitmentFilename: null,
        productCommitmentFilepath: null,
        cooperationAccessFilename: null,
        cooperationAccessFilepath: null,
        otherFilename: null,
        otherFilepath: null,
        address: null,
        provinceName: null,
        cityName: null,
        areaName: null,
      },
      rules: {
        authType: [
          { required: true, message: "请选择认证类型", trigger: "blur" },
        ],
        companyName: [
          { required: true, message: "请输入公司全称", trigger: "blur" },
        ],
        companyCreditCode: [
          {
            required: true,
            message: "请输入公司统一社会信用码",
            trigger: "blur",
          },
        ],
        authName: [
          { required: true, validator: validateName, trigger: "blur" },
        ],
        authIdCard: [
          {
            required: true,
            validator: validateID,
            trigger: "blur",
          },
        ],
        contactsName: [
          { required: true, validator: validateName, trigger: "blur" },
        ],
        contactsPhone: [
          { required: true, validator: validatePhone, trigger: "blur" },
        ],
        contactsEmail: [
          { required: true, validator: validateEamil, trigger: "blur" },
        ],
        contactsAddress: [
          { required: true, message: "请输入联系人地址", trigger: "blur" },
        ],
        areaCode: [{ required: true, message: "请选择区", trigger: "blur" }],
        cityCode: [{ required: true, message: "请选择市", trigger: "blur" }],
        provinceCode: [
          { required: true, message: "请选择省", trigger: "blur" },
        ],
        doorplate: [
          { required: true, message: "请输入门牌号", trigger: "blur" },
        ],
        address: [{ required: true, message: "请输入地址", trigger: "blur" }],
      },
    };
  },
  methods: {
    changeUpImg(e, i) {
      if (!i.type && e.raw.type.includes("video")) {
        this.$message.error("请上传图片");
        return;
      }
      if (i.type && e.raw.type.includes("image")) {
        this.$message.error("请上传视频");
        return;
      }
      if (!i.type) {
        const isJPG =
          e.raw.type === "image/jpg" ||
          e.raw.type === "image/jpeg" ||
          e.raw.type === "image/png";
        const isLt2M = e.size / 1024 / 1024 < 5;

        if (!isJPG) {
          this.$message.error("上传图片只能是 JPG/PNG 格式!");
          return;
        }
        if (!isLt2M) {
          this.$message.error("上传图片大小不能超过 2MB!");
          return;
        }
      } else {
        const isLt50M = e.size / 1024 / 1024 < 20;
        if (!isLt50M) {
          this.$message.error("视频大小不能超过20M");
          return;
        }
      }
      i.imageUrl = URL.createObjectURL(e.raw);
      let data = new FormData();
      data.append("fileNum", i.id);
      data.append("file", e.raw);
      contractUploadFile(data).then((res) => {
        this.formData[i.prop] = res.filename;
        this.formData[i.path] = res.filepath;
      });
    },
    // @ApiModelProperty(value = "合同审核状态(0未上传/1待审核/2审核成功/3审核失败)")
    // private Integer contractCheckStatus;
    downLoad(e) {
      const ele = document.createElement("a");
      ele.setAttribute("href", e); //设置下载文件的url地址
      ele.setAttribute("download", "download"); //用于设置下载文件的文件名
      ele.click();
      // document.body.removeChild(ele);
    },
    changeUpfile(e, i) {
      const isLt2M = e.size / 1024 / 1024 < 100;
      if (e.raw.type.includes("video")) {
        this.$message.error("请上传文件");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 5MB!");
        return;
      }
      let data = new FormData();
      data.append("fileNum", i.id);
      data.append("file", e.raw);
      contractUploadFile(data).then((res) => {
        this.formData[i.prop] = res.filename;
        this.formData[i.path] = res.filepath;
        i.tag = e.name;
      });
    },
    submitform() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          AddPartyaAdminContract(this.formData).then((res) => {
            if (res.code == 200) {
              this.getInfo();
              this.$message.success("上传成功");
            }
          });
        }
      });
    },
    //删除图片
    removeFile(e, i) {
      this.formData[e.prop] = null;
      this.formData[e.path] = null;
      this.qualifications[i].imageUrl = "";
    },
    //删除文件
    removeDocument(e, i) {
      this.formData[e.prop] = null;
      this.formData[e.path] = null;
      this.contact[i].tag = "";
    },
    getProvinceCode(e) {
      this.formData.areaCode = "";
      this.formData.cityCode = "";
      this.areaList = this.cityList.filter((item) => item.code == e)[0]?.citys;
    },
    getAreaCodeCode(e) {
      this.formData.areaCode = "";
      this.countrysList = this.areaList.filter(
        (item) => item.code == e
      )[0]?.countrys;
    },
    submitupDataform() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          updataContractInfo(this.formData).then((res) => {
            this.$message.success("修改成功");
            this.getInfo();
          });
        }
      });
    },
    getInfo() {
      getContractInfo().then((res) => {
        const data = res.data;
        this.show = true;
        this.qualifications[0].imageUrl = res.data.businessLicenseFilenameUrl;
        this.qualifications[1].imageUrl = res.data.frontIdCardFilenameUrl;
        this.qualifications[2].imageUrl = res.data.reverseIdCardFilenameUrl;
        this.qualifications[3].imageUrl = res.data.officeVideoFilenameUrl;
        this.contact[0].tag = res.data.adContractFilename;
        this.contact[1].tag = res.data.productCommitmentFilename;
        this.contact[2].tag = res.data.cooperationAccessFilename;
        this.contact[3].tag = res.data.otherFilename;
        this.contact[0].imageUrl = res.data.adContractFilenameUrl;
        this.contact[1].imageUrl = res.data.productCommitmentFilenameUrl;
        this.contact[2].imageUrl = res.data.cooperationAccessFilenameUrl;
        this.contact[3].imageUrl = res.data.otherFilenameUrl;
        this.status = res.data.contractCheckStatus;
        this.contractCheckRemark = res.data.contractCheckRemark;
        // 还原数据
        for (let key in data) {
          // 接口请求出来的
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = data[key];
          }
        }
      });
    },
  },
  mounted() {
    this.getInfo();
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px 40px;
}
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 150px;
  height: 150px;
}
::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 150px;
  text-align: center;
}

.account {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}
::v-deep .info .el-form-item__label {
  line-height: 0px !important;
  font-size: 12px;
}
.iamge-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 270px;
  border: 1px solid #9999;
  &:hover {
    border: 1px solid #1890ff;
  }
  &-title {
    margin-top: 20px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #666;
  }
}
.tag {
  color: #999;
  font-size: 14px;
  margin-top: -28px;
  width: 150px;
  height: 24px;
  padding: 0 5px;
  line-height: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.3s;
}
.tag:hover {
  background: #f5f5f5;
  cursor: pointer;
}
::v-deep .el-upload-list__item-name {
  width: 120px;
  overflow: hidden;
}
.submit {
  margin-top: 20px;
  width: 150px;
}
.center {
  display: flex;
  justify-content: center;
}
.status {
  margin-bottom: 20px;
  font-size: 16px;
  color: #333;
}
.images {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  &-item {
    margin-bottom: 20px;
    width: 300px;
  }
}
.remove {
  margin-top: -16px;
  width: 100%;
  display: flex;
  justify-content: center;
  button {
    padding: 0;
    width: 40px;
    height: 20px;
    line-height: 0;
  }
}
.file-remove {
  display: flex;
  width: 100%;
  height: 30px;
  justify-content: center;
  margin-top: 2px;
  button {
    padding: 0;
    width: 40px;
    height: 20px;
    line-height: 0;
  }
}
</style>