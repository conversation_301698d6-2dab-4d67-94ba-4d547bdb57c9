<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="创建时间" prop="">
        <el-date-picker
          v-model="dateRange"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="商户" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="商户名称或ID"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <template v-if="!isPhone">
        <el-form-item label="商务" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入商务"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="合同状态" prop="contractCheckStatusList">
          <el-select
            v-model="queryParams.contractCheckStatusList"
            clearable
            multiple
            size="mini"
            @change="handleQuery"
          >
            <el-option :value="0" label="未上传"></el-option>
            <el-option :value="1" label="待审核"></el-option>
            <el-option :value="2" label="审核成功"></el-option>
            <el-option :value="3" label="审核失败"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="商户活跃度"
          prop="activeStatus"
          v-if="hasBool('loan:partya:query_a')"
        >
          <el-select
            v-model="queryParams.activeStatus"
            clearable
            size="mini"
            @change="handleQuery"
          >
            <el-option :value="0" label="全部"></el-option>
            <el-option :value="1" label="近期活跃商户"></el-option>
            <el-option :value="2" label="静默商户"></el-option>
          </el-select>
        </el-form-item>
      </template>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          v-hasPermi="['loan:partya:query']"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="addPartyAuser"
          v-hasPermi="['loan:partya:add']"
          >添加</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button type="text" v-if="isShowBtn" @click="isPhone = !isPhone">{{
          isPhone ? "展开" : "收起"
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      ref="table"
      :data="partyAList"
      @sort-change="handleSortChange"
      :row-key="
        (row) => {
          return row.partyFirstId;
        }
      "
    >
      <el-table-column
        label="商户ID"
        align="center"
        prop="partyFirstId"
        width="100"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="160"
      />
      <el-table-column
        label="商户名称"
        align="center"
        show-overflow-tooltip
        width="220"
      >
        <template slot-scope="{ row }">
          <span @click="handleDetailDrawer(row)" class="f_c005 c-p">{{
            row.name
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="白名单商户" align="center">
        <template slot-scope="{ row }">
          <el-switch
            active-color="#004DAB"
            :active-value="1"
            :inactive-value="0"
            :disabled="!(row.isCreate && row.contractCheckStatus == 2)"
            v-model="row.whiteStatus"
            @change="handleChangeWhithStatus($event, row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="商户充值金额"
        prop="rechargeAmount"
        align="center"
        width="130"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        v-if="isShowMore"
      />
      <el-table-column label="保证金" align="center">
        <template slot-scope="{ row }">
          <div>
            <span
              @click="handleDetailDrawer(row)"
              :class="[row.depositAmount == 0 ? 'red' : 'f_c005', 'c-p']"
              >{{ row.depositAmount }}</span
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column label="余额提醒" align="center">
        <template slot-scope="{ row }">
          <el-switch
            active-color="#004DAB"
            v-model="row.balanceTipStatus"
            @change="handleBalanceTips($event, row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        label="商户可用余额"
        prop="availableAmount"
        align="center"
        width="130"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
      />
      <el-table-column label="退款金额" prop="refundAmount" align="center" />
      <el-table-column label="商务" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.contractCheckStatus !== 0">
            <el-tag v-if="row.highSeasFlag !== 0">公</el-tag>
            <el-tag v-if="row.isTransfer == 1">转</el-tag>
            {{ row.sysUser.nickName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="合同审核状态" align="center">
        <template slot-scope="scope">
          <!-- <el-tag
            v-if="scope.row.contractCheckStatus !== 0"
            :type="tagType[scope.row.contractCheckStatus]"
            effect="plain"
            size="small"
            style="cursor: pointer"
            @click="hanldeUploadContract(scope.row)"
          >
            {{ statusType[scope.row.contractCheckStatus] }}
          </el-tag> -->
          <div v-if="row.contractCheckStatus !== 0">
            <div
              v-if="scope.row.contractCheckStatus == 4"
              class="table-operation-col"
            >
              <el-button
                size="mini"
                type="text"
                :disabled="!scope.row.isCheck && !scope.row.isCreate"
                class="f_status"
                style="--i: #ccc"
                @click="hanldeUploadContract(scope.row)"
                v-hasPermi="['partyaAdmin:contract:add']"
                >上传合同</el-button
              >
            </div>
            <div
              v-if="scope.row.contractCheckStatus == 5"
              class="table-operation-col"
            >
              <el-button
                size="mini"
                type="text"
                :disabled="!scope.row.isCheck && !scope.row.isCreate"
                class="f_status"
                style="--i: #0052d9"
                @click="hanldeUploadContract(scope.row)"
                v-hasPermi="['partyaAdmin:contract:add']"
                >待提交</el-button
              >
            </div>
            <div
              v-if="scope.row.contractCheckStatus == 3"
              class="table-operation-col"
            >
              <el-button
                size="mini"
                type="text"
                class="f_status"
                style="--i: red"
                v-if="scope.row.isCreate"
                @click="hanldeUploadContract(scope.row)"
                v-hasPermi="['partyaAdmin:contract:update']"
                :disabled="!scope.row.isCheck && !scope.row.isCreate"
                >审核失败</el-button
              >
              <el-button
                size="mini"
                type="text"
                class="f_status"
                style="--i: red"
                v-else
                :disabled="!scope.row.isCheck && !scope.row.isCreate"
                @click="handContractInfo(scope.row)"
                >审核失败</el-button
              >
            </div>
            <div
              v-if="scope.row.contractCheckStatus == 1"
              class="table-operation-col"
            >
              <el-button
                size="mini"
                type="text"
                class="f_status"
                :style="{
                  '--i':
                    !scope.row.isCheck && !scope.row.isCreate
                      ? '#ccc'
                      : '#daa520',
                }"
                v-hasPermi="['loan:partya:checkcontract']"
                @click="handContractInfo(scope.row)"
                :disabled="!scope.row.isCheck && !scope.row.isCreate"
              >
                待审核
                <!-- {{ scope.row.isCheck ? "待审核" : "查看合同" }} -->
              </el-button>
            </div>
            <div
              v-if="scope.row.contractCheckStatus == 2"
              class="table-operation-col"
            >
              <el-button
                size="mini"
                type="text"
                class="f_status"
                style="--i: #32cd32"
                @click="hanldeUploadContract(scope.row)"
                >审核成功
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        width="320"
        class-name="small-padding fixed-width"
      >
        <div
          slot-scope="scope"
          class="table-operation-row"
          v-if="scope.row.blackType == 1"
        >
          <el-button
            size="mini"
            type="text"
            class="f_c005"
            :disabled="
              scope.row.contractCheckStatus == 1 ||
              scope.row.contractCheckStatus == 5 ||
              (scope.row.contractCheckStatus == 3 && !scope.row.isCreate)
            "
            @click="editPartyAuser(scope.row)"
            v-hasPermi="['loan:partya:edit']"
            >修改信息</el-button
          >
          <!-- scope.row.contractCheckStatus == 1 ||
              (!scope.row.isCheck &&
                (!scope.row.isCreate ||
                  scope.row.contractCheckStatus == 0 ||
                  scope.row.contractCheckStatus == 4) &&
                scope.row.contractCheckStatus != 2) -->
          <!-- scope.row.contractCheckStatus == 1 ||
              (!scope.row.isCheck &&
                !scope.row.isCreate &&
                scope.row.contractCheckStatus != 2) -->
          <el-button
            size="mini"
            type="text"
            class="f_c005"
            :disabled="scope.row.contractCheckStatus !== 2"
            @click="hanldeMoeny(scope.row)"
            v-if="
              hasBool('loan:partya:recharge') || hasBool('loan:partya:refund')
            "
            >款项管理</el-button
          >
          <el-button
            size="mini"
            type="text"
            class="f_c005"
            :disabled="
              !scope.row.isCreate && scope.row.contractCheckStatus != 2
            "
            @click="handleAccout(scope.row)"
            v-hasPermi="['loan:partya:usermanage']"
          >
            账号管理</el-button
          >
          <el-button
            size="mini"
            type="text"
            v-if="scope.row.contractCheckStatus == 0"
            class="f_c005"
            v-hasPermi="['partyaAdmin:contract:add']"
            @click="handleAddContact(scope.row)"
            >上传资料</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            class="f_c005"
            v-hasPermi="['partyaAdmin:contract:add']"
            :disabled="
              scope.row.contractCheckStatus == 1 ||
              (!scope.row.isCreate &&
                !scope.row.isCheck &&
                scope.row.contractCheckStatus != 2) ||
              (!scope.row.isCreate && scope.row.contractCheckStatus == 4)
            "
            @click="handleAddContact(scope.row)"
            >上传资料</el-button
          >

          <!-- <template v-if="false">
            <div
              v-if="scope.row.contractCheckStatus == 0"
              class="table-operation-col"
            >
              <el-button
                size="mini"
                type="text"
                :disabled="!scope.row.isCheck && !scope.row.isCreate"
                class="f_c005"
                @click="handleAddContact(scope.row)"
                v-hasPermi="['partyaAdmin:contract:add']"
                >上传合同</el-button
              >
            </div>
            <div
              v-if="scope.row.contractCheckStatus == 3"
              class="table-operation-col"
            >
              <el-button
                size="mini"
                type="text"
                class="f_c005"
                style="color:`${!scope.row.isCheck&&!scope.row.isCreate? #C0C4CC:red}`"
                v-if="scope.row.isCreate"
                @click="handleAddContact(scope.row)"
                v-hasPermi="['partyaAdmin:contract:update']"
                :disabled="!scope.row.isCheck && !scope.row.isCreate"
                >修改合同</el-button
              >
              <el-button
                size="mini"
                type="text"
                class="f_c005"
                style="color:`${!scope.row.isCheck&&!scope.row.isCreate? #C0C4CC:red}`"
                v-else
                :disabled="!scope.row.isCheck && !scope.row.isCreate"
                @click="handContractInfo(scope.row)"
                >查看合同</el-button
              >
            </div>
            <div
              v-if="scope.row.contractCheckStatus == 1"
              class="table-operation-col"
            >
              <el-button
                size="mini"
                type="text"
                class="f_c005"
                v-hasPermi="['loan:partya:checkcontract']"
                @click="handContractInfo(scope.row)"
                :disabled="!scope.row.isCheck && !scope.row.isCreate"
                >{{ scope.row.isCheck ? "审核合同" : "查看合同" }}</el-button
              >
            </div>
            <div
              v-if="scope.row.contractCheckStatus == 2"
              class="table-operation-col"
            >
              <el-button
                size="mini"
                type="text"
                class="f_c005"
                @click="handleAddContact(scope.row)"
                >查看合同
              </el-button>

            </div>
          </template> -->
          <div class="table-operation-col" v-if="scope.row.blackType == 1">
            <el-button
              size="mini"
              type="text"
              class="f_c005"
              v-hasPermi="['loan:partya:black']"
              @click="handleBlack(scope.row)"
            >
              拉黑</el-button
            >
          </div>
        </div>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 新增商户 -->
    <el-dialog
      :title="isAdd ? '新增商户' : '修改商户'"
      :visible.sync="partyAvisible"
      @close="cancel"
      width="900px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formPartyData"
        :rules="rules"
        label-width="150px"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="商户名称" prop="name">
              <el-input
                v-model.trim="formPartyData.name"
                placeholder="请输入商户名称"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="商户类型" prop="type">
              <el-select
                placeholder="请选择商户类型"
                clearable
                v-model="formPartyData.type"
                style="width: 100%"
              >
                <el-option label="门店商户" value="0"></el-option>
                <el-option label="个人商户" value="1"></el-option>
                <el-option label="线上商户" value="2"></el-option>
                <!-- <el-option label="线上个人" value="3"></el-option> -->
              </el-select>
            </el-form-item></el-col
          >
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12"
            ><el-form-item label="打款主体名称">
              <el-input
                v-model="formPartyData.subjectName"
                placeholder="请输入打款主体名称"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="打款银行卡号">
              <el-input
                v-model="formPartyData.bankCardNo"
                :change="
                  (formPartyData.bankCardNo = formPartyData.bankCardNo.replace(
                    /[^\d.]/g,
                    ''
                  ))
                "
                :maxlength="19"
                placeholder="请输入打款银行卡号"
              /> </el-form-item
          ></el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="开户行" prop="bankName">
              <el-input
                v-model="formPartyData.bankName"
                placeholder="请输入开户行"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="纳税人识别号">
              <el-input
                v-model="formPartyData.tin"
                placeholder="请输入纳税人识别号"
              /> </el-form-item
          ></el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="商务" prop="userId">
              <el-select
                placeholder="请选择商务"
                style="width: 100%"
                clearable
                :disabled="isDisablePart"
                v-model="formPartyData.userId"
              >
                <el-option
                  v-for="item in businessList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId"
                >
                </el-option>
              </el-select> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="商户联系方式" :prop="isAdd ? 'tel' : ''">
              <el-input
                v-model="formPartyData.tel"
                placeholder="请输入商户联系方式"
              />
            </el-form-item>
            <div class="tips" v-if="!isAdd">Tips:为空则不修改手机号</div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer justify-content-c">
        <el-button type="primary" @click="submitPartyAForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 审核资质或合同 -->
    <el-dialog
      :visible.sync="failAvisible"
      width="60%"
      title="审核资质"
      append-to-body
      center
      @close="clearCheckData"
      :close-on-click-modal="false"
    >
      <el-form ref="checkData" :model="checkData">
        <el-form-item
          label="审核状态"
          prop="contractCheckStatus"
          :rules="{
            required: true,
            message: '请选择审核状态',
            trigger: 'blur',
          }"
        >
          <el-radio v-model="checkData.contractCheckStatus" :label="2"
            >审核通过</el-radio
          >
          <el-radio v-model="checkData.contractCheckStatus" :label="3"
            >审核不通过</el-radio
          >
        </el-form-item>

        <el-form-item label="合同编号">
          <el-input
            placeholder="请输入合同编号"
            v-model="checkData.contractNo"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="理由"
          prop="contractCheckRemark"
          :rules="{
            required: checkData.contractCheckStatus == 3,
            message: '请选择审核状态',
            trigger: 'blur',
          }"
        >
          <el-input
            v-model="checkData.contractCheckRemark"
            type="textarea"
            maxlength="100"
            show-word-limit
            placeholder="请输入理由"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="checkSubmit">确 定</el-button>
        <el-button @click="clearCheckData">取 消</el-button>
      </div>
    </el-dialog>

    <drawer-detail :drawer.sync="showDetailDrawer" :row="row"></drawer-detail>
    <drawer-money
      :drawer.sync="showMoneyDrawer"
      :row="row"
      @getList="getList"
    ></drawer-money>
    <drawer-accout :drawer.sync="showAccoutDrawer" :row="row"></drawer-accout>
    <!-- 商户合同线下 -->
    <dialog-contact
      :drawer.sync="contactAvisible"
      :row="row"
      :partyFirstId="id"
      :isLoanType="isLoanType"
      @getList="getList"
    ></dialog-contact>
    <!-- 商户合同线下 -->
    <drawer-line
      :drawer.sync="contactLineAvisible"
      :row="row"
      :partyFirstId="id"
      @getList="getList"
    ></drawer-line>
    <!-- 线下资料 -->
    <drawer-contract-deatil
      :drawer.sync="contactDeatailAvisible"
      :row="row"
      :partyFirstId="id"
      @getList="getList"
    ></drawer-contract-deatil>

    <!-- 线上合同 -->
    <drawer-contract-on
      :drawer.sync="contactOnAvisible"
      :row="row"
      :partyFirstId="id"
      @getList="getList"
    ></drawer-contract-on>
    <!-- 线下合同 -->
    <drawer-contract-down
      :drawer.sync="contactDownAvisible"
      :row="row"
      :isLoanType="isLoanType"
      :partyFirstId="id"
      @getList="getList"
    ></drawer-contract-down>

    <el-dialog
      :visible.sync="showSetDialog"
      width="400px"
      title="提醒设置"
      append-to-body
      center
      @close="cancelSet"
      :close-on-click-modal="false"
    >
      <div class="set-row">
        <span>余额低于</span>
        <el-input
          placeholder="请输入余额"
          v-model.number="balanceSetForm.balanceTip"
        ></el-input>
        元
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitSet">确 定</el-button>
        <el-button @click="cancelSet">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="showBlackPartya"
      width="400px"
      title="拉黑商户"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <div>是否拉黑{{ row.name }}?</div>
      <div>
        当前余额
        <span style="color: red">{{ row.availableAmount }} </span> 保证金余额
        <span style="color: red">{{ row.depositAmount }}</span>
      </div>
      <div style="color: red">拉黑后商户投放产品将不可上线</div>

      <span>备注：</span> <el-input v-model="remark" type="textarea"></el-input>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="hanldeBlackSubmit">确 定</el-button>
        <el-button @click="hanldeBlackCanlce">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
/**
 * 合同状态说明
 * 0：为上传资料和合同
 * 1：合同待审核
 * 2:合同审核成功
 * 3：合同审核失败
 * 4：提交资料，合同待上传
 * 5：审核成功后，重新修改了资料
 */
import {
  getPartyAlist,
  getBusinessList,
  addPartyAOne,
  editPartyAOne,
  getPartyAOne,
  getContractInfo,
  checkContractInfo,
  updateContractNo,
  updateContractVideo,
  partyaBlock,
  editBalanceTip,
  changeWhithStatus,
} from "@/api/partyA";
import drawerDetail from "@/views/partyA/partyaList/drawerDetail.vue";
import drawerMoney from "@/views/partyA/partyaList/drawerMoney.vue";
import drawerAccout from "@/views/partyA/partyaList/drawerAccout.vue";
import dialogContact from "@/views/partyA/partyaList/dialogContact.vue";
import drawerLine from "@/views/partyA/partyaList/drawerLine.vue";
import drawerContractDeatil from "@/views/partyA/partyaList/drawerContractDeatil.vue";
import drawerContractOn from "@/views/partyA/partyaList/drawerContractOn.vue";
import drawerContractDown from "@/views/partyA/partyaList/drawerContractDown.vue";
import { hasBool } from "@/directive/permission/hasBool";
export default {
  name: "partyaList",
  data() {
    var validatePhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("商户联系方式不能为空"));
      } else if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
        callback(new Error("商户联系方式格式错误"));
      } else {
        callback();
      }
    };
    return {
      showUsers: ["shenxun", "sunyang","wudan","xusa"],
      statusType: {
        4: "上传",
        1: "待审核",
        2: "审核成功",
        3: "审核失败",
      },
      color: {
        4: "color:red",
        1: "color:#DAA520",
        2: "color:#32CD32",
        3: "color:red",
      },
      tagType: {
        4: "info",
        1: "warning",
        2: "success",
        3: "danger",
      },
      visibleBalance: false,
      showDetailDrawer: false, //详情弹窗
      showMoneyDrawer: false, //款项操作
      showAccoutDrawer: false, //账户操作
      showSetDialog: false,
      showBlackPartya: false,
      balanceSetForm: {
        balanceTip: "",
        balanceTipStatus: "",
        partyFirstId: "",
      },
      row: {},
      dateRange: [],
      id: "",
      otherList: [],
      isContractCheck: true,
      isLoanType: false, //是否个人商户
      isDisablePart: false, //禁用商务
      contactAvisible: false,
      contactLineAvisible: false,
      contactOnAvisible: false, //线上合同
      contactDownAvisible: false, //线下合同
      partyAList: [],
      businessList: [],
      total: 0,
      loading: false,
      partyAvisible: false, //新增甲方对话框
      contactDeatailAvisible: false, //甲方助贷详情
      failAvisible: false, //资质审核
      isOnline: false, //是否是线上合同
      isAdd: true, //是否新增或修改甲方
      imageUrl: "", //入账凭证图片
      formPartyData: {
        name: "",
        type: "",
        subjectName: "",
        bankCardNo: "",
        bankName: "",
        tin: "",
        userId: "",
        tel: "",
      },
      statusJson: {
        0: "发起",
        1: "通过",
        2: "驳回",
        3: "结束",
      },
      infoData: {}, //甲方助贷合同
      remark:"",
      contractCheckStatus: 0,
      contractCheckRemarkInfo: "",
      checkData: {
        contractCheckStatus: null,
        contractCheckRemark: null,
        partyFirstId: null,
        contractNo: null,
      },
      rules: {
        name: [
          {
            required: true,
            message: "请输入商户名称",
            trigger: "blur",
          },
        ],
        type: [
          {
            required: true,
            message: "请选择商户类型",
            trigger: "blur",
          },
        ],
        subjectName: [
          {
            message: "请输入打款主体名称",
            trigger: "blur",
          },
        ],

        bankCardNo: [
          {
            message: "请输入打款银行卡号",
            trigger: "blur",
          },
        ],
        bankName: [
          {
            message: "请输入开户行",
            trigger: "blur",
          },
        ],
        tin: [
          {
            required: true,
            message: "请输入纳税人识别号",
            trigger: "blur",
          },
        ],
        userId: [
          {
            required: true,
            message: "请选择商务",
            trigger: "blur",
          },
        ],
        tel: [
          {
            required: true,
            validator: validatePhone,
            trigger: "blur",
          },
        ],
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: "",
        userName: "",
        contractCheckStatusList: [],
        contractStatus: "",
        stopTime: "",
        startTime: "",
        rechargeSort: false,
        availableSort: false,
        params: {
          sort: "",
        },
        activeStatus: 0,
      },
      isPhone: false,
      isShowBtn: false,
    };
  },
  computed: {
    processColor: () => {
      return (item) => {
        if (item.status == -1) {
          return "";
        } else if (item.status == 2) {
          return "#ff0000";
        } else {
          return "#00a607";
        }
      };
    },

    isShowMore() {
      return this.showUsers.includes(this.$store.getters.userInfo.userName);
    },
  },
  methods: {
    //查看详情drawer
    handleDetailDrawer(row) {
      this.row = row;
      // this.$router.push(`/partyA/detail?id=${row.partyFirstId}`)
      // return
      this.showDetailDrawer = true;
    },
    //获取商户方列表数据
    getList() {
      this.loading = true;
      this.queryParams.contractStatus =
        this.queryParams.contractCheckStatusList.join(",");

      getPartyAlist(this.queryParams)
        .then((res) => {
          this.partyAList = JSON.parse(JSON.stringify(res.rows));
          this.total = res.total;
          this.loading = false;
          // if (this.partyAList.length) {
          //   this.visibleBalance = this.partyAList[0].visibleBalance;
          // }
        })
        .catch((err) => {
          this.loading = false;
        });
    },

    //查询
    handleQuery() {
      if (this.dateRange !== null) {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.stopTime = this.dateRange[1];
      } else {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = "";
        this.queryParams.stopTime = "";
      }
      this.getList();
    },
    handleSortChange(column, prop, order) {
      console.log(column);
      if (column.prop == "rechargeAmount") {
        this.queryParams.rechargeSort = true;
        this.queryParams.availableSort = false;
        this.queryParams.params.sort =
          column.order == "ascending" ? "asc" : "desc";
      } else if (column.prop == "availableAmount") {
        this.queryParams.rechargeSort = false;
        this.queryParams.availableSort = true;
        this.queryParams.params.sort =
          column.order == "ascending" ? "asc" : "desc";
      } else {
        this.queryParams.rechargeSort = false;
        this.queryParams.availableSort = false;
        this.queryParams.params.sort = "";
      }
      this.getList();
    },

    //打开新增弹窗
    addPartyAuser() {
      this.isAdd = true;
      this.partyAvisible = true;
      this.reset();
      if (this.formPartyData.partyFirstId) {
        delete this.formPartyData.partyFirstId;
      }

      getBusinessList().then((res) => {
        this.businessList = res.data.list;
        this.formPartyData.userId = res.data.userId;
        this.isDisablePart = true;
      });
    },
    //编辑商户
    editPartyAuser(row) {
      this.isAdd = false;
      getBusinessList().then((res) => {
        this.businessList = res.data.list;
        this.isDisablePart = res.data.disabled;
        getPartyAOne(row.partyFirstId).then((res) => {
          this.partyAvisible = true;
          this.formPartyData.name = res.partyFirst.name;
          this.formPartyData.type = res.partyFirst.type;
          this.formPartyData.subjectName = res.partyFirst.subjectName;
          this.formPartyData.bankCardNo = res.partyFirst.bankCardNo;
          this.formPartyData.bankName = res.partyFirst.bankName;
          this.formPartyData.userId = res.partyFirst.userId;
          this.formPartyData.tel = res.partyFirst.tel;
          this.formPartyData.tin = res.partyFirst.tin;
          this.formPartyData.partyFirstId = res.partyFirst.partyFirstId;
        });
      });
    },
    //添加商户
    submitPartyAForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addPartyAOne(this.formPartyData).then((res) => {
              if (res.code == 200) {
                this.partyAvisible = false;
                this.$message.success("添加成功");
                this.getList();
              }
            });
          } else {
            if (this.formPartyData.tel) {
              if (
                !/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(this.formPartyData.tel)
              ) {
                this.$message.error("手机号格式不正确");
                return;
              } else {
                editPartyAOne(this.formPartyData).then((res) => {
                  if (res.code == 200) {
                    this.partyAvisible = false;
                    this.$message.success("编辑成功");
                    this.getList();
                  }
                });
              }
            } else {
              editPartyAOne(this.formPartyData).then((res) => {
                if (res.code == 200) {
                  this.partyAvisible = false;
                  this.$message.success("编辑成功");
                  this.getList();
                }
              });
            }
          }
        }
      });
    },
    //取消
    cancel() {
      this.isAdd = true;
      this.partyAvisible = false;
      this.reset();
      this.$refs.formData.resetFields();
    },
    //重置商户
    reset() {
      this.formPartyData = {
        name: "",
        type: "",
        subjectName: "",
        bankCardNo: "",
        bankName: "",
        tin: "",
        userId: "",
        tel: "",
      };
      this.isDisablePart = false;
    },

    //新增合同
    handleAddContact(e) {
      if (e.type == "0" || e.type == "1") {
        this.contactAvisible = true;
      } else {
        this.contactLineAvisible = true;
      }
      if (e.type == "1") {
        this.isLoanType = true;
      } else {
        this.isLoanType = false;
      }
      this.id = e.partyFirstId;
    },

    //获取商户合同信息
    handContractInfo(e) {
      this.contactDeatailAvisible = true;
      this.id = e.partyFirstId;
      this.row = e;
      this.isContractCheck = e.isCheck;

      getContractInfo(e.partyFirstId).then((res) => {
        this.checkData.partyFirstId = e.partyFirstId;
        this.infoData = res.data || {};
        this.otherList = res.data.fileOrderList || [];
        this.contractCheckStatus = res.data.contractCheckStatus;
        this.contractCheckRemarkInfo = res.data.contractCheckRemark;
        this.contactDeatailAvisible = true;

        if (e.type == "1") {
          this.isLoanType = true;
        } else {
          this.isLoanType = false;
        }
        if (e.type == "0" || e.type == "1") {
          this.isOnline = true;
        } else {
          this.isOnline = false;
        }
      });
    },
    //关闭资质审核
    clearCheckData() {
      this.checkData.contractCheckStatus = null;
      this.checkData.contractCheckRemark = null;
      this.checkData.contractNo = null;
      this.failAvisible = false;
      this.$refs.checkData.resetFields();
    },
    //下载合同
    downLoad(e) {
      const ele = document.createElement("a");
      ele.setAttribute("href", e); //设置下载文件的url地址
      ele.setAttribute("download", "download"); //用于设置下载文件的文件名
      ele.click();
    },
    //款项操作
    hanldeMoeny(row) {
      this.showMoneyDrawer = true;
      this.row = row;
    },
    //审核资质
    checkSubmit() {
      this.$refs.checkData.validate((valid) => {
        if (valid) {
          checkContractInfo(this.checkData).then((res) => {
            if (res.code == 200) {
              this.failAvisible = false;
              this.contactDeatailAvisible = false;
              this.getList();
              this.$message.success("操作成功");
            }
          });
        }
      });
    },
    //修改合同编号
    changeContranctNo() {
      if (!this.infoData.contractNo) {
        this.$message.error("合同编号不能为空");
        return;
      }
      this.$confirm("确定修改合同编号吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          updateContractNo({
            contractNo: this.infoData.contractNo,
            partyFirstId: this.id,
          }).then((res) => {
            this.$message.success("修改成功");
            this.contactDeatailAvisible = false;
          });
        })
        .catch(() => {});
    },
    //修改场地视频
    changeUpfile(e) {
      if (!e.raw.type.includes("video")) {
        this.$message.error("请上传视频");
        return;
      }
      const isLt2M = e.size / 1024 / 1024 < 50;
      if (!isLt2M) {
        this.$message.error("视频大小不能超过50M");
        return;
      }

      let data = new FormData();
      data.append("file", e.raw);
      updateContractVideo(data, this.id).then((res) => {
        this.$message.success("修改成功");
        this.infoData.officeVideoFilename = e.raw.name;
      });
    },
    //拉黑商户
    handleBlack(row) {
      this.showBlackPartya = true;
      this.row = row;
      // return;
      // const h = this.$createElement;
      // const newDatas = [];
      // newDatas.push(h("p", null, `是否确认拉黑    ${row.name} ？`));
      // newDatas.push(
      //   h("p", null, [
      //     h("span", null, "当前余额"),
      //     h(
      //       "span",
      //       { style: "color: red;padding:0px 5px" },
      //       `${row.availableAmount}`
      //     ),
      //     h("span", null, "保证金"),
      //     h(
      //       "span",
      //       { style: "color: red;padding:0px 5px" },
      //       `${row.depositAmount}`
      //     ),
      //   ])
      // );
      // newDatas.push(
      //   h("p", { style: "color: red" }, "拉黑后商户投放产品将不可上线")
      // );

      // this.$confirm("提示", {
      //   type: "warning",
      //   message: h("div", null, newDatas),
      // })
      //   .then(() => {
      //     partyaBlock({ partyFirstId: row.partyFirstId }).then((res) => {
      //       this.$message.success("操作成功");
      //       this.getList();
      //     });
      //   })
      //   .catch((err) => {});
    },
    hanldeBlackSubmit() {
      if (!this.remark) return this.$message.error("请填写备注");
      partyaBlock({
        partyFirstId: this.row.partyFirstId,
        remark: this.remark,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getList();
        this.hanldeBlackCanlce();
      });
    },
    hanldeBlackCanlce() {
      this.showBlackPartya = false;
      this.remark = "";
      this.row = {};
    },
    //操作账号
    handleAccout(row) {
      this.row = row;
      this.showAccoutDrawer = true;
    },
    //操作余额提醒
    handleBalanceTips(e, row) {
      this.row = row;

      if (e) {
        this.showSetDialog = true;
      } else {
        editBalanceTip({
          balanceTip: "",
          balanceTipStatus: false,
          partyFirstId: row.partyFirstId,
        }).then((res) => {
          this.$message.success("操作成功");
          this.getList();
        });
      }
      this.balanceSetForm.balanceTipStatus = e;
      this.balanceSetForm.partyFirstId = row.partyFirstId;
    },
    submitSet() {
      if (!this.balanceSetForm.balanceTip) {
        this.$message.error("请输入金额");
        return;
      }
      editBalanceTip(this.balanceSetForm).then((res) => {
        this.showSetDialog = false;
        this.balanceSetForm = {
          balanceTip: "",
          balanceTipStatus: "",
          partyFirstId: "",
        };
        this.$message.success("操作成功");
        this.getList();
      });
    },
    cancelSet() {
      this.showSetDialog = false;
      this.balanceSetForm = {
        balanceTip: "",
        balanceTipStatus: "",
        partyFirstId: "",
      };
      this.row.balanceTipStatus = false;
      this.row = {};
    },
    handleChangeWhithStatus(event, row) {
      this.$confirm(
        event
          ? `确定将${row.name}设置为白名单吗？`
          : `确定将${row.name}取消白名单吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          changeWhithStatus({ partyFirstId: row.partyFirstId })
            .then((res) => {
              this.$message.success("设置成功");
            })
            .catch((err) => {
              row.whiteStatus = row.whiteStatus == 0 ? 1 : 0;
            });
        })
        .catch(() => {
          row.whiteStatus = row.whiteStatus == 0 ? 1 : 0;
        });
    },
    //上传合同分离资料
    hanldeUploadContract(e) {
      console.log(22222222);
      // scope.row.contractCheckStatus == 1 ||
      //   (!scope.row.isCheck &&
      //     !scope.row.isCreate &&
      //     scope.row.contractCheckStatus != 2);
      // if (
      //   scope.row.contractCheckStatus == 1 ||
      //   (!e.isCreate && e.contractCheckStatus != 2)
      // )
      //   return;
      this.id = e.partyFirstId;
      this.row = e;
      this.isContractCheck = e.isCheck;

      if (e.type == "0" || e.type == "1") {
        this.contactDownAvisible = true;
      } else {
        this.contactOnAvisible = true;
      }

      if (e.type == "1") {
        this.isLoanType = true;
      } else {
        this.isLoanType = false;
      }
    },
  },
  components: {
    "drawer-detail": drawerDetail,
    "drawer-money": drawerMoney,
    "drawer-accout": drawerAccout,
    "dialog-contact": dialogContact,
    "drawer-line": drawerLine,
    "drawer-contract-deatil": drawerContractDeatil,
    "drawer-contract-on": drawerContractOn,
    "drawer-contract-down": drawerContractDown,
  },
  watch: {
    "formChargeData.depositAmount": {
      handler() {
        this.formChargeData.price =
          this.formChargeData.depositAmount * 1 +
          this.formChargeData.chargeMoeny * 1;
      },
    },
    "formChargeData.chargeMoeny": {
      handler() {
        this.formChargeData.price =
          this.formChargeData.depositAmount * 1 +
          this.formChargeData.chargeMoeny * 1;
      },
    },
    "formChargeData.deposit": {
      handler(newVualue, old) {
        if (!newVualue) {
          this.formChargeData.price = "";
          this.formChargeData.depositAmount = 0;
          this.formChargeData.chargeMoeny = 0;
        } else {
          this.formChargeData.price =
            this.formChargeData.depositAmount * 1 +
            this.formChargeData.chargeMoeny * 1;
        }
      },
    },
  },
  mounted() {
    if (this.$route.query.name) {
      this.queryParams.name = this.$route.query.name;
    }
    if (this.$route.query.type) {
      // this.queryParams.contractCheckStatus = this.$route.query.type.split(',').map(item => item * 1)
      this.queryParams.contractCheckStatusList = this.$route.query.type
        .split(",")
        .map((item) => item * 1);
    }
    if (
      navigator.userAgent.match(/Mobi/i) ||
      navigator.userAgent.match(/Android/i) ||
      navigator.userAgent.match(/iPhone/i)
    ) {
      this.isPhone = true;
      this.isShowBtn = true;
    } else {
      this.isPhone = false;
      this.isShowBtn = false;
    }
    // this.queryParams.contractStatus = this.$route.query.type || ''
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.red {
  color: red;
  cursor: pointer;
}

.status {
  margin: 10px;
  font-size: 20px;
  font-weight: 600;
}

.table-operation-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .table-operation-col {
    padding-left: 10px;
  }
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.account {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

::v-deep .info .el-form-item__label {
  line-height: 16px !important;
  font-size: 12px;
}

.flex {
  display: flex;
  justify-content: space-between;
}

.file {
  display: flex;

  flex-wrap: wrap;

  &-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 16px;

    &-title {
      color: #111;
      font-size: 14px;
      font-weight: 600;
    }

    &-name {
      width: 130px;
      color: #111;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      margin: 0 10px;
      background: #ccc;
      padding: 3px 2px;
    }
  }
}

::v-deep .el-dialog__body {
  max-height: calc(100vh - 120px);
  overflow: auto;
}

.check-info {
  max-height: 300px;
  overflow: auto;
  width: 100%;
  margin-top: 10px;
}

.check-title {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  position: sticky;
  top: 0;
}

.c_red {
  color: #f00;
}

.tips {
  font-size: 14px;
  color: #666;
  padding-left: 50px;
  margin-bottom: 10px;
}

.el-button--text:hover,
.el-button--text:focus {
  color: #0052d9;
}

.set-row {
  display: flex;
  align-items: center;

  span {
    width: 100px;
  }
}
.f_status {
  color: var(--i);
  border: 1px solid var(--i);
  line-height: 0;
  padding: 10px 8px;
}

.f_status:hover,
.f_status:focus,
.f_status:active {
  color: var(--i);
}
</style>
