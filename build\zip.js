const fs = require('fs')
const archiver = require('archiver')
const chalk = require('chalk')
const path = require('path')
const dayjs = require('dayjs')
const { exec } = require('child_process')

// 获取当前时间戳
function getTimeString() {
  return dayjs().format('YYYY年MM月DD日HH时mm分ss秒')
}

// 根据环境获取构建目录
function getBuildDir(mode) {
  switch(mode) {
    case 'staging':
      return 'dist-dev'
    case 'production':
      return 'dist-prod'
    default:
      return 'dist-prod'
  }
}

// 获取zip文件名
function getZipName(mode) {
  const timeStr = getTimeString()
  const prefix = mode === 'staging' ? '小马admin测试' : '小马admin正式'
  return `${prefix}-${timeStr}.zip`
}

// 清理旧的zip文件
function cleanOldZips(mode, zipsDir) {
  const prefix = mode === 'staging' ? '小马admin测试' : '小马admin正式'

  try {
    // 读取zips目录中的所有文件
    const files = fs.readdirSync(zipsDir)

    // 过滤出对应模式的zip文件
    const oldZips = files.filter(file => {
      return file.startsWith(prefix) && file.endsWith('.zip')
    })

    // 删除旧的zip文件
    oldZips.forEach(file => {
      const filePath = path.join(zipsDir, file)
      fs.unlinkSync(filePath)
      console.log(chalk.yellow(`> 已删除旧压缩包: ${file}`))
    })

    if (oldZips.length > 0) {
      console.log(chalk.green(`> 共删除 ${oldZips.length} 个旧压缩包`))
    }
  } catch (error) {
    console.log(chalk.yellow(`> 清理旧压缩包时出现错误: ${error.message}`))
  }
}

function zipDist(mode) {
  const buildDir = getBuildDir(mode)
  const zipName = getZipName(mode)
  
  // 检查构建目录是否存在
  if (!fs.existsSync(buildDir)) {
    console.log(chalk.red(`构建目录 ${buildDir} 不存在!`))
    return
  }

  // 创建zips目录（如果不存在）
  const zipsDir = 'zips'
  if (!fs.existsSync(zipsDir)) {
    fs.mkdirSync(zipsDir)
  }

  // 清理旧的压缩包
  cleanOldZips(mode, zipsDir)

  // 将zip文件保存到zips目录
  const zipPath = path.join(zipsDir, zipName)
  const output = fs.createWriteStream(zipPath)
  const archive = archiver('zip', {
    zlib: { level: 9 }
  })

  output.on('close', () => {
    const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2)
    console.log(chalk.green(`> 压缩完成,文件保存为 ${zipPath}, 大小: ${sizeInMB} MB`))
    
    // 获取压缩包所在目录的绝对路径
    const zipAbsolutePath = path.resolve(process.cwd(), zipsDir)
    
    // Windows 使用 explorer，Mac 使用 open，Linux 使用 xdg-open
    const command = process.platform === 'win32' ? 'explorer' : process.platform === 'darwin' ? 'open' : 'xdg-open'
    exec(`${command} "${zipAbsolutePath}"`)
  })

  archive.on('error', (err) => {
    throw err
  })

  archive.pipe(output)
  archive.directory(buildDir, false)
  archive.finalize()
}

// 如果直接执行此文件
if (require.main === module) {
  const mode = process.argv[2] || 'production'
  zipDist(mode)
}

module.exports = zipDist 