import request from '@/utils/request'
import request2 from '@/utils/request2'

// 获取用户列表
export const fetchUserList = (params) => {
  return request({
    url: "/loan/xm/tx/xj/pageList",
    method: 'get',
    params
  })
}

// 更新用户信息
export function updateUser(data) {
  return request({
    url: '/loan/xm/tx/xj/updateAssets',
    method: 'post',
    data
  })
}

// 匹配
export function matchProduct(params) {
  return request2({
    url: '/api/tx/xj/match',
    method: 'get',
    params
  })
}

// 推送
export function pushProduct(data) {
  return request2({
    url: '/api/tx/xj/apply',
    method: 'post',
    data
  })
}

// 查询子平台配置
export function fetchSubPlatformConfig(params) {
  return request({
    url: '/loan/group/product/getChildPlatformConfig',
    method: 'get',
    params
  })
}

// 查询各子平台在线产品信息
export function fetchSubPlatformProduct(params) {
  return request({
    url: '/loan/xm/tx/xj/findOnlineProductList',
    method: 'get',
    params
  })
}

// 匹配专线产品
export function matchSpecialProduct(params) {
  return request2({
    url: '/api/tx/xj/vip/crm/match',
    method: 'get',
    params
  })
}

// 推送专线产品
export function pushSpecialProduct(data) {
  return request2({
    url: '/api/tx/xj/vip/crm/apply',
    method: 'post',
    data
  })
}

// 移入待处理
export function moveToPending(params) {
  return request({
    url: '/loan/xm/tx/xj/move/pending',
    method: 'get',
    params
  })
}

// 查询接入比例配置
export function getConfig(params) {
  return request({
    url: '/loan/xm/tx/xj/getConfig',
    method: 'get',
    params
  })
}

// 修改接入比例配置
export function updateConfig(params) {
  return request({
    url: '/loan/xm/tx/xj/config/update', 
    method: 'get',
    params
  })
}

