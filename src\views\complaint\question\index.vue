<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="commerce">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期" size="small">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="推广计划" prop="productKeywords">
        <el-input placeholder="请输入推广状态" v-model="queryParams.productKeywords" size="small" clearable></el-input>
      </el-form-item>
      <el-form-item label="投诉单号" prop="productKeywords">
        <el-input placeholder="请输入投诉单号" oninput="value=value.replace(/[^0-9]/g,'')"
          @blur="queryParams.billno = $event.target.value" v-model="queryParams.billno" size="small" clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="解决状态" prop="solveStatus">
        <el-select v-model="queryParams.solveStatus" placeholder="请选择推广计划" clearable size="small">
          <el-option :value="1" label="未解决"></el-option>
          <el-option :value="2" label="已解决"></el-option>
          <el-option :value="3" label="已关闭"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="平台确认状态" prop="allotStatus">
        <el-select v-model="queryParams.allotStatus" placeholder="请选择平台确认状态" clearable size="small">
          <el-option :value="1" label="待分配"></el-option>
          <el-option :value="2" label="已分配"></el-option>
          <el-option :value="3" label="待确认"></el-option>
          <el-option :value="4" label="已确认"></el-option>
          <el-option :value="5" label="客户反馈未解决"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['loan:feedback:add']">
          新增</el-button>
      </el-form-item>
    </el-form>
    <el-table border :data="tableData">
      <el-table-column label="投诉单号" prop="billno" align="center" />
      <el-table-column label="投诉时间" width="160" prop="feedbackTime" align="center" />
      <el-table-column label="被投诉计划" prop="productName" align="center" />
      <el-table-column label="处理截止时间" width="160" prop="deadline" align="center" />
      <el-table-column label="扣费金额" prop="deduction" align="center" />
      <el-table-column label="等级" prop="level" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ levelJson[row.level] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="投诉内容" prop="content" align="center">
        <template  slot-scope="{row}">
          <el-popover placement="top-start" title="投诉内容" width="350" trigger="hover" :content="row.content">
            <div slot="reference" class="level">
              {{ row.content }}
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="对接人员" prop="partyFirstName" align="center" />
      <el-table-column label="投诉分配时间" width="160" prop="allotTime" align="center" />
      <el-table-column label="解决状态" prop="solveStatusStr" align="center" />
      <el-table-column label="平台确认状态" prop="allotStatusStr" align="center">
        <template  slot-scope="{row}">
          <div :class="[row.allotStatus == 5 ? 'c_red' : '']">
            {{ row.allotStatusStr }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="解决时间" width="160" prop="solveTime" align="center" />
      <el-table-column label="操作" prop="content" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button type="text" @click="hanldeDetail(row)" v-hasPermi="['loan:feedback:detail']">查看详情</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog title="新增投诉" :visible.sync="showAdd" width="700px" append-to-body center :close-on-click-modal="false"
      @close="cancel">
      <el-form ref="formData" label-width="120px" :model="formData" :rules="rules">
        <el-form-item label="投诉用户手机号" prop="phone">
          <div style="display:flex">
            <el-input v-model="formData.phone" placeholder="请输入用户手机号"></el-input>
            <el-button type="primary" @click="handleProdcut">查询</el-button>
          </div>
        </el-form-item>
        <el-form-item label="推广计划" prop="productId">
          <el-select v-model="formData.productId" placeholder="请选择推广计划" style="width:100%" clearable size="small"
            @change="hanldeProduct">
            <el-option :label="'推广ID:' + item.productId + '---' + item.productName + '---' + item.username"
              :key="item.userId" :value="item.productId" v-for="item in userOptions"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分配至" prop="partyFirstUser">
          <el-input v-model="partyFirstName" placeholder="请选择推广计划" disabled></el-input>
        </el-form-item>
        <el-form-item label="投诉等级" prop="level">
          <el-select v-model="formData.level" placeholder="请选择投诉等级" clearable size="small">
            <el-option :value="1" label="一级"></el-option>
            <el-option :value="2" label="二级"></el-option>
            <el-option :value="3" label="三级"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处理截止时间" prop="deadline">
          <el-date-picker v-model="formData.deadline" :picker-options="pickerOptions" format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm" type="datetime" placeholder="请选择截止时间" />
        </el-form-item>
        <el-form-item label="扣费金额" prop="deduction">
          <el-input v-model="formData.deduction" oninput="value=value.replace(/[^0-9.]/g,'')"
            @blur="formData.deduction = $event.target.value" placeholder="请输入扣费金额"></el-input>
        </el-form-item>
        <el-form-item label="补充资料">
          <el-upload class="upload-demo" ref="upload" action="#" multiple :auto-upload="false"
            :on-change="(e, fileList) => handleUpload(e, fileList, 'upload')"
            :on-remove="(e, fileList) => handleRemove(e, fileList, 'upload')">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传压缩文件格式zip/rar/7z，且不超过50M</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="投诉详情" :visible.sync="showDetail" width="800px" append-to-body center :close-on-click-modal="false"
      @close="cancelDistributionFormData">
      <table border="1" class="table">
        <tr class="table-warp">
          <td class="table-title">投诉时间</td>
          <td class="table-title">被投诉计划</td>
          <td class="table-title">投诉内容</td>
        </tr>
        <tr class="table-warp">
          <td class="table-content table-min">{{ detailInfo.feedbackTime || "-" }}</td>
          <td class="table-content table-min">{{ detailInfo.productName || "-" }}</td>
          <td class="table-content table-min">{{ detailInfo.content || "-" }}</td>
        </tr>
        <tr class="table-warp">
          <td class="table-title">对接人员</td>
          <td class="table-title">分配时间
          </td>
          <td class="table-title">当前解决状态</td>
        </tr>
        <tr class="table-warp">
          <td class="table-content table-min">{{ detailInfo.username || '-' }}</td>
          <td class="table-content table-min">{{ detailInfo.allotTime || '-' }}</td>
          <td class="table-content table-min">{{ detailInfo.solveStatusStr || '-' }}</td>
        </tr>
        <tr class="table-warp">
          <td class="table-title">平台确认状态</td>
          <td class="table-title">投诉用户</td>
          <td class="table-title"></td>
        </tr>
        <tr class="table-warp">
          <td class="table-content table-min">{{ detailInfo.allotStatusStr || '-' }}</td>
          <td class="table-content table-min">{{ detailInfo.phone || '-' }}</td>
          <td class="table-content table-min"></td>
        </tr>
      </table>
      <div class="enclosure">
        <div>投诉附件信息</div>
        <div v-if="attachmentsArray.length == 0">暂无数据</div>
        <div v-for="item in attachmentsArray" :key="item">
          <el-image :src="item" :preview-src-list="[item]"></el-image>
        </div>
      </div>

      <el-form label-width="120px" style="margin-top:20px" :model="distributeformData"
        v-if="detailInfo.allotStatus == 1" ref="distributeform" :rules="feedRules">
        <el-form-item label="分配至">
          <el-input v-model="distinfo" placeholder="请输入分配至" disabled></el-input>
        </el-form-item>
        <el-form-item label="投诉等级" prop="level">
          <el-select v-model="distributeformData.level" placeholder="请选择投诉等级" clearable size="small">
            <el-option :value="1" label="一级"></el-option>
            <el-option :value="2" label="二级"></el-option>
            <el-option :value="3" label="三级"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处理截止时间" prop="deadline">
          <el-date-picker v-model="distributeformData.deadline" :picker-options="pickerOptions"
            format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm" type="datetime" placeholder="请选处理截止时间" />
        </el-form-item>
        <el-form-item label="扣费金额" prop="deduction">
          <el-input v-model="distributeformData.deduction" oninput="value=value.replace(/[^0-9.]/g,'')"
            @blur="formData.deduction = $event.target.value" placeholder="请输入扣费金额"></el-input>
        </el-form-item>
        <el-form-item label="补充资料">
          <el-upload class="upload-demo" ref="distUpload" action="#" multiple :auto-upload="false"
            :on-change="(e, fileList) => handleUpload(e, fileList, 'distUpload')"
            :on-remove="(e, fileList) => handleRemove(e, fileList, 'distUpload')">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传压缩文件格式zip/rar/7z，且不超过50M</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="distributeformData.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <template v-if="detailInfo.allotStatus != 1 && detailList.length">

        <div style="fontSize:16px">分配至：商户ID：{{ detailInfo.partyFirstId }} <span style="margin:0px 20px">商户账号：{{
            detailInfo.partyFirstNickName
        }}</span> <span style=" margin-left:20px">商户名称：{{
    detailInfo.partyFirstName
}}</span></div>
        <div class="info-list" v-if="detailList.length">
          <div class="info-item" v-for="(item, index) in detailList " :key="index">
            <div class="info-handle">
              <span> 操作人：{{ item.handleUser || "-" }} </span>
              <span> 操作时间：{{ item.handleTime || "-" }} </span>
              <span :class="[item.status == 5 ? 'c_red' : '',item.status == 4?'c_green':'']"> {{ item.type == 1 ? allowJson[item.status] :
                  solveJson[item.status]
              }}</span>
            </div>
            <div style="margin:10px 0px">
              <span> {{ item.type == 1 ? "备注:" : "解决说明:" }}</span> {{ item.remark || "-" }}
            </div>
            <div> {{ item.type == 1 ? "补充材料:" : " 解决凭证:" }}： <span class="info-document">{{ item.fileName || '-'
            }}</span>
              <el-button type="text" @click="handleDownload(item.fileUrl)" v-if="item.fileUrl">下载</el-button>
            </div>
          </div>
        </div>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-if="detailInfo.allotStatus == 1" @click="submitDistributionFormData"
          v-hasPermi="['loan:feedback:handle']">确认分配
        </el-button>
        <el-button type="primary" v-if="detailInfo.allotStatus == 1" @click="hanldeDelFeedback"
          v-hasPermi="['loan:feedback:handle']">关闭投诉
        </el-button>
        <el-button type="primary" v-if="detailInfo.allotStatus == 3 || detailInfo.allotStatus == 5"
          @click="hanldechangetSolved" v-hasPermi="['loan:feedback:handle']">已同客户确认解决</el-button>
        <el-button type="primary" v-if="detailInfo.allotStatus == 3 || detailInfo.allotStatus == 5"
          @click="handleFeedback" v-hasPermi="['loan:feedback:handle']">客户反馈未解决</el-button>
      </div>
    </el-dialog>

    <el-dialog title="上传用户反馈信息" :visible.sync="showFeedback" width="700px" append-to-body center @close="cancelFeedForm"
      :close-on-click-modal="false">
      <el-form ref="feedBackForm" label-width="120px" :model="feedBackFormData" :rules="feedRules">

        <el-form-item label="投诉附件信息">
          <el-upload class="upload-demo" ref="feedUpload" action="#" multiple :auto-upload="false"
            :on-change="(e, fileList) => handleUpload(e, fileList, 'feedUpload')"
            :on-remove="(e, fileList) => handleRemove(e, fileList, 'feedUpload')">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传压缩文件格式zip/rar/7z，且不超过50M</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="feedBackFormData.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFeedForm">确 定</el-button>
        <el-button @click="cancelFeedForm">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {
  getUserApplyProduct,
  getPartyFirstAdmin,
  addComplaintOne,
  getComplaintList,
  getFeedbackDetail,
  addDistributionOne,
  changenotSolved,
  changetSolved,
  delFeedback
} from "@/api/reply";


export default {
  data() {

    const validateNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error("金额不能为空"));
      } else if (!/^(([1-9]{1}\d{0,3})|(0{1}))(\.\d{1,2})?$/.test(value)) {
        callback(new Error("输入不合法"));
      } else {
        callback();
      }
    };

    var validatePhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入手机号"));
      } else if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
        callback(new Error("手机号格式错误"));
      } else {
        callback();
      }
    };
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 86400 * 1000;
          return false
        }
      },
      dateRange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        allotStatus: "",
        productKeywords: "",
        solveStatus: "",
        startTime: "",
        stopTime: ""
      },
      total: 0,
      allowJson: {
        1: "待分配", 2: "已分配", 3: "待确认", 4: "已确认", 5: "客户反馈未解决"
      },
      solveJson: {
        1: "待解决", 2: "已解决"
      },
      levelJson: {
        1: "一级",
        2: "二级",
        3: "三级"
      },
      tableData: [],
      userOptions: [],
      detailInfo: {},
      distinfo: "",//显示商户id和名称
      attachmentsArray: [],
      detailList: [],
      formData: {
        phone: "",
        productId: "",
        partyFirstUser: "",
        remark: "",
        file: "",
        level: "",
        deadline: "",
        deduction: ""
      },
      distributeformData: {
        file: "",
        billno: "",
        remark: "",
        level: "",
        deadline: "",
        deduction: ""
      },
      feedBackFormData: {
        file: "",
        billno: "",
        remark: ""
      },
      partyFirstName: "",
      showAdd: false,
      showDetail: false,
      showFeedback: false,
      rules: {
        phone: [
          {
            required: true,
            validator: validatePhone,
            trigger: "blur",
          },
        ],
        productId: [
          { required: true, message: "请选择推广计划", trigger: "blur" },
        ],
        partyFirstUser: [
          { required: true, message: "请选择分配至", trigger: "blur" },
        ],
        remark: [
          { required: true, message: "请输入备注", trigger: "blur" },
        ],
        deadline: [
          { required: true, message: "请选择处理截止时间", trigger: "change" },
        ],
        level: [
          { required: true, message: "请选择等级", trigger: "change" },
        ],

        deduction: [
          { required: true, validator: validateNumber, trigger: "blur" },
        ],
      },
      feedRules: {
        remark: [
          { required: true, message: "请输入备注", trigger: "blur" },
        ],
        deadline: [
          { required: true, message: "请选择处理截止时间", trigger: "change" },
        ],
        level: [
          { required: true, message: "请选择等级", trigger: "change" },
        ],
        deduction: [
          { required: true, validator: validateNumber, trigger: "blur" },
        ],
      },
    }
  },
  methods: {
    //查询列表
    getList() {
      getComplaintList(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
      })
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      if (this.dateRange !== null && this.dateRange.length) {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.stopTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.stopTime = "";
      }
      this.getList()
    },
    //新增
    handleAdd() {
      this.showAdd = true
    },
    //上传文件
    handleUpload(e, fileList, type) {
      if (fileList && fileList.length >= 2) {
        fileList.shift();
      }
      const isLt50M = e.size / 1024 / 1024 < 50;
      if (!this.isZipFile(e.raw.name)) {
        this.$message.error("上传文件只能是 zip/rar/7z 格式!");
        this.$refs[type].clearFiles();
        return;
      }
      if (!isLt50M) {
        this.$refs[type].clearFiles();
        this.$message.error("上传大小不能超过 50MB!");
        return;
      }
      switch (type) {
        case "upload":
          this.formData.file = e.raw
          break;
        case "distUpload":
          this.distributeformData.file = e.raw
          break;
        case "feedUpload":
          this.feedBackFormData.file = e.raw
          break;
        default:
          return
      }
    },
    //删除上传文件
    handleRemove(e, fileList, type) {
      switch (type) {
        case "upload":
          if (!fileList.length) {
            this.formData.file = ""
          }
          break;
        case "distUpload":
          if (!fileList.length) {
            this.distributeformData.file = ""
          }
          break;
        case "feedUpload":
          if (!fileList.length) {
            this.feedBackFormData.file = ""
          }
          break;
        default:
          return
      }
    },
    //取消新增
    cancel() {
      this.showAdd = false
      this.formData = {
        phone: "",
        productId: "",
        partyFirstUser: "",
        remark: "",
        file: "",
        level: "",
        deadline: "",
        deduction: ""
      }
      this.userOptions = []
      this.partyFirstName = ""
      if (this.$refs.formData) {
        this.$refs.formData.resetFields();
      }

    },
    //查询详情
    hanldeDetail(row) {
      this.distributeformData.billno = row.billno
      getFeedbackDetail({ billno: row.billno }).then(res => {
        this.showDetail = true
        this.detailInfo = res.data.basic
        this.distinfo = `${res.data.basic.partyFirstId}--${res.data.basic.partyFirstNickName}--${res.data.basic.partyFirstName}`
        this.attachmentsArray = res.data?.basic?.attachmentsArray || []
        this.detailList = res.data.items
      })
    },
    //删除投诉
    hanldeDelFeedback() {
      this.$confirm("是否关闭投诉？", "提示", {
        type: "warning"
      }).then(() => {
        delFeedback({ billno: this.distributeformData.billno }).then(res => {
          this.$message.success("操作成功")
          this.cancelDistributionFormData()
          this.getList()
        })
      })
    },
    //根据用户手机号查询申请的产品
    handleProdcut() {
      if (!this.formData.phone) {
        this.$message.error("手机号不能为空")
        return
      }
      if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(this.formData.phone)) {
        this.$message.error("手机格式错误")
        return
      }
      this.userOptions = []
      this.formData.partyFirstUser = ""
      this.partyFirstName = ""
      this.formData.productId = ""
      getUserApplyProduct({ phone: this.formData.phone }).then(res => {
        this.userOptions = res.data
        if (res.data.length) {
          this.$message.success("查询成功")
        } else {
          this.$message.error("该手机号没有申请产品")
        }

      })
    },
    //查询商户
    hanldeProduct(e) {
      if (!e) return
      getPartyFirstAdmin({ productId: e }).then(res => {
        this.formData.partyFirstUser = res.data.partyFirstId
        this.partyFirstName = res.data.partyFirstId + '---' + res.data.partyFirstName
      })
    },
    //新增投诉
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          let data = new FormData()
          for (let key in this.formData) {
            data.append(key, this.formData[key])
          }
          addComplaintOne(data).then(res => {
            this.cancel()
            this.getList()
            this.$message.success("新增成功")
          })
        }
      })
    },
    //下载附件
    handleDownload(e) {
      const ele = document.createElement("a");
      ele.setAttribute("href", e); //设置下载文件的url地址
      ele.setAttribute("download", "download"); //用于设置下载文件的文件名
      ele.click();
    },
    //取消分配
    cancelDistributionFormData() {
      this.showDetail = false
      this.distributeformData = {
        file: "",
        billno: "",
        remark: "",
        level: "",
        deadline: "",
        deduction: ""
      }
      this.distinfo = ""
      if (this.$refs.distributeform) {
        this.$refs.distributeform.resetFields()
      }

    },
    //确认分配
    submitDistributionFormData() {
      this.$refs.distributeform.validate((valid) => {
        if (valid) {
          let data = new FormData()
          for (let key in this.distributeformData) {
            data.append(key, this.distributeformData[key])
          }
          addDistributionOne(data).then(res => {
            this.$message.success("操作成功")
            this.getList()
            this.cancelDistributionFormData()
          })
        }
      })
    },
    //用户反馈信息
    handleFeedback() {
      this.showFeedback = true
      this.feedBackFormData.billno = this.distributeformData.billno
    },
    //提交用户反馈信息
    submitFeedForm() {
      this.$refs.feedBackForm.validate((valid) => {
        if (valid) {
          let data = new FormData()
          for (let key in this.feedBackFormData) {
            data.append(key, this.feedBackFormData[key])
          }
          changenotSolved(data).then(res => {
            this.$message.success("操作成功")
            this.cancelFeedForm()
            this.cancelDistributionFormData()
            this.getList()
          })
        }
      })
    },
    //取消用户上传反馈信息
    cancelFeedForm() {
      this.showFeedback = false
      this.feedBackFormData.file = ""
      this.feedBackFormData.remark = ""
      this.$refs.feedBackForm.resetFields();
    },
    //用户确认解决
    hanldechangetSolved() {
      this.$confirm("是否确认该投诉已解决？", "提示", {
        type: "warning"
      }).then(() => {
        changetSolved({ billno: this.distributeformData.billno }).then(res => {
          this.$message.success("操作成功")
          this.cancelDistributionFormData()
          this.getList()
        })
      })
    },
    // //删除投诉
    // hanldeDelFeedback() {
    //   this.$confirm("是否删除投诉？", "提示", {
    //     type: "warning"
    //   }).then(() => {
    //     delFeedback({ billno: this.distributeformData.billno }).then(res => {
    //       this.$message.success("操作成功")
    //       this.cancelDistributionFormData()
    //       this.getList()
    //     })
    //   })
    // },
    // 验证是否是压缩文件
    isZipFile(fileName) {
      var fileType = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()
      const extension = fileType == 'zip' || fileType == 'rar' || fileType == '7z'
      var bool = false;
      if (extension) {
        bool = true;
      } else {
        bool = false;
      }
      return bool;
    }
  },
  mounted() {
    this.getList()
  }
}
</script>

<style lang="scss" scoped>
.table {
  width: 99.99%;
  border-collapse: collapse;
  border: 1px solid #ececec;
}

.table-title {
  height: 30px;
  text-align: center;
  background: #89bcee;
  color: #fff;
  line-height: 30px;
  font-size: 16px;
  font-weight: 500;
  flex: 1;
}

.table-warp {
  font-size: 14px;
  display: flex;
}

.table-content {
  flex: 1;
  text-align: center;
}

.table-min {
  min-height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.enclosure {
  margin: 20px 0px;
  display: flex;

  div {
    width: 100px;
    height: 100px;
    margin-right: 30px;
    line-height: 100px;
    color: #606266;
    font-weight: 600;
    font-size: 14px;
  }
}

.info-list {
  color: #666;
  font-size: 16px;
  max-height: 400px;
  overflow: auto;
  border: 1px solid #ececec;
  padding: 20px;
}

.info-item {
  min-height: 100px;
  border: 1px solid #ececec;
  border-radius: 10px;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  padding: 5px 20px;
  margin-bottom: 10px;
}

.info-handle {
  span {
    margin-right: 20px;
  }
}

.info-document {
  margin: 0px 20px;
  color: #1890ff;
}

.c_red {
  color: red;
}
.c_green{
  color: #3bbb45;
}

.level {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
