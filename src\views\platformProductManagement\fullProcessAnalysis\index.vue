<template>
    <div class="app-container">
      <el-form :inline="true" :model="queryParams" size="small">
        <el-form-item label="日期" prop="time">
          <el-date-picker
            v-model="queryParams.dateRange"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="fetchTableData"
          />
        </el-form-item>
        <el-form-item label="渠道" prop="channelId">
          <el-select
            v-model="queryParams.channelId"
            filterable
            clearable
            placeholder="请选择渠道"
            style="width: 200px"
            @change="fetchTableData"
          >
            <el-option
              v-for="item in channelList"
              :key="item.id"
              :label="`${item.id}-${item.channelName}`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="fetchTableData">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
  
      <el-table :data="tableData" border @sort-change="handleSortChange" ref="tableRef">
        <el-table-column label="平台名称" prop="platformName" align="center" />
        <el-table-column label="产品名称" prop="name" align="center">
          <template slot-scope="{row}">
            <span v-if="!row.isSummary">{{ row.name }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column 
          label="成功总价" 
          prop="successTotalPrice" 
          align="center"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template slot-scope="{row}">
            {{ row.successTotalPrice || 0 }}
          </template>
        </el-table-column>
        <el-table-column 
          label="成功均价" 
          prop="successAvePrice" 
          align="center"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template slot-scope="{row}">
            <span v-if="!row.isSummary">{{ row.successAvePrice || 0 }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column 
          label="申请总数" 
          prop="total" 
          align="center"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template slot-scope="{row}">
            {{ row.total || 0 }}
          </template>
        </el-table-column>
        <el-table-column 
          label="成功数" 
          prop="successNum" 
          align="center"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template slot-scope="{row}">
            {{ row.successNum || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="成功率" prop="successRate" align="center" 
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template slot-scope="{row}">
            <span v-if="!row.isSummary">
              {{ row.successRate ? row.successRate + '%' : '0%' }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          label="撞库数"
          prop="checkNum"
          align="center"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template slot-scope="{row}">
            {{ row.checkNum || 0 }}
          </template>
        </el-table-column>
        <el-table-column
          label="撞库成功数"
          prop="checkSuccessNum"
          align="center"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template slot-scope="{row}">
            {{ row.checkSuccessNum || 0 }}
          </template>
        </el-table-column>
        <el-table-column
          label="撞库失败数"
          prop="checkFailNum"
          align="center"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template slot-scope="{row}">
            {{ row.checkFailNum || 0 }}
          </template>
        </el-table-column>
        <el-table-column
          label="撞库成功率"
          prop="checkSuccessRate"
          align="center"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template slot-scope="{row}">
            <span v-if="!row.isSummary">
              {{ row.checkSuccessRate ? row.checkSuccessRate + '%' : '0%' }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template slot-scope="{row}">
            <el-button
              v-if="!row.isSummary"
              type="text"
              size="small"
              @click="handleShowDistribution(row)"
            >
              渠道分布
            </el-button>
            <el-button
              v-if="!row.isSummary"
              type="text"
              size="small"
              @click="handleShowFailRecord(row)"
            >
              失败记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <channel-distribution-dialog
        :visible.sync="dialogVisible"
        :params="dialogParams"
      />
      <fail-record-dialog
        :visible.sync="failRecordDialogVisible"
        :params="dialogParams"
      />
    </div>
  </template>
  
  <script>
  import { getFullProcessAnalysis } from "@/api/platformProductManagement/fullProcessAnalysis";
  import { getChannelList } from '@/api/productManage/product'
  import { sum } from "@/utils/calculate";
  import dayjs from "dayjs";
  import ChannelDistributionDialog from './components/channelDistributionDialog'
  import FailRecordDialog from './components/failRecordDialog'
  
  export default {
    name: "FullProcessAnalysis",
    
    components: {
      ChannelDistributionDialog,
      FailRecordDialog
    },
    
    data() {
      return {
        queryParams: {
          dateRange: [],
          channelId: ''
        },
        channelList: [],
        tableData: [],
        dialogVisible: false,
        dialogParams: {
          startTime: '',
          endTime: '',
          platformType: '',
          productId: ''
        },
        failRecordDialogVisible: false
      };
    },
  
    mounted() {
      this.getChannelList();
      this.setDefaultDate();
      this.fetchTableData();
    },
  
    methods: {
      async getChannelList() {
        const res = await getChannelList();
        this.channelList = res.data || [];
      },
  
      setDefaultDate() {
        const today = dayjs();
        const startTime = today.format("YYYY-MM-DD 00:00:00");
        const endTime = today.format("YYYY-MM-DD 23:59:59");
  
        this.queryParams.dateRange = [startTime, endTime];
      },
  
      getParams() {
        const [startTime = "", endTime = ""] = this.queryParams.dateRange || [];
        return {
          startTime,
          endTime,
          channelId: this.queryParams.channelId
        };
      },
  
      createSummaryRow(data) {
        const summaryRow = {
          platformName: '合计',
          successTotalPrice: sum(data.map(item => Number(item.successTotalPrice || 0))),
          total: sum(data.map(item => Number(item.total || 0))),
          successNum: sum(data.map(item => Number(item.successNum || 0))),
          checkNum: sum(data.map(item => Number(item.checkNum || 0))),
          checkSuccessNum: sum(data.map(item => Number(item.checkSuccessNum || 0))),
          checkFailNum: sum(data.map(item => Number(item.checkFailNum || 0))),
          isSummary: true
        };

        return summaryRow;
      },
  
      async fetchTableData() {
        const params = this.getParams();
        const res = await getFullProcessAnalysis(params);
        this.dialogParams.startTime = params.startTime;
        this.dialogParams.endTime = params.endTime;
        const list = res.data || [];
        
        if (list.length > 0) {
          this.tableData = [this.createSummaryRow(list), ...list];
        } else {
          this.tableData = list;
        }

        this.$nextTick(() => {
          this.$refs.tableRef.clearSort()
        })
      },
  
      resetQuery() {
        this.queryParams.channelId = '';
        this.setDefaultDate();
        this.fetchTableData();
      },
  
      handleSortChange({ prop, order }) {
        if (!this.tableData.length) {
          return;
        }

        // 取出合计行
        const summaryRow = this.tableData[0];
        const sortRows = this.tableData.slice(1);

        switch (order) {
          case "ascending":
            sortRows.sort((a, b) => Number(a[prop] || 0) - Number(b[prop] || 0));
            break;
          case "descending":
            sortRows.sort((a, b) => Number(b[prop] || 0) - Number(a[prop] || 0));
            break;
          default:
            break;
        }

        // 将合计行放回首位
        this.tableData = [summaryRow, ...sortRows];
      },
  
      handleShowDistribution(row) {
        this.dialogParams = {
          startTime: this.dialogParams.startTime,
          endTime: this.dialogParams.endTime,
          platformType: row.platformId,
          productId: row.id
        }
        this.dialogVisible = true
      },
  
      handleShowFailRecord(row) {
        this.dialogParams = {
          startTime: this.dialogParams.startTime,
          endTime: this.dialogParams.endTime,
          platformType: row.platformId,
          productId: row.id
        }
        this.failRecordDialogVisible = true
      }
    }
  };
  </script>
  
  <style scoped>
  .app-container {
    padding: 20px;
  }
  </style>
