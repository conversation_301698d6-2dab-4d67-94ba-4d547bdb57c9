<template>
  <div class="app-container">
    <el-form :inline="true">
      <el-form-item label="主体名称">
        <el-input size="small" clearable placeholder="请输入主体名称" v-model="queryParams.subjectName"
          @keyup.enter.native="handleQuery"></el-input>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input size="small" clearable placeholder="请输入手机号" v-model="queryParams.phone"
          @keyup.enter.native="handleQuery"></el-input>
      </el-form-item>
      <el-form-item label="姓名">
        <el-input size="small" clearable placeholder="请输入姓名" v-model="queryParams.name"
          @keyup.enter.native="handleQuery"></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['saas:tenant:add']">添加
        </el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableList" border>
      <el-table-column label="ID" align="center" prop="tenantId" />

      <el-table-column label="主体名称" align="center" prop="subjectName" />

      <el-table-column label="姓名" align="center" prop="name" />

      <el-table-column label="登录名" prop="username" align="center">
      </el-table-column>
      <el-table-column label="联系号码" prop="phone" align="center">
      </el-table-column>
      <el-table-column label="过期时间" prop="expirationTime" align="center">
      </el-table-column>

      <el-table-column label="账号状态" prop="refundAmount" align="center" v-hasPermi="['saas:tenant:update_userstatus']">
        <template  slot-scope="{row}">
          <div>
            <el-switch v-model="row.userStatus" style="display: block"
              active-text="启用" inactive-text="禁用" :active-value="0" :inactive-value="1"
              @change="changeUserStatus($event, row)">
            </el-switch>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="280" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button size="mini" type="text" icon="el-icon-edit-outline" @click="handleEdit(row)"
              v-hasPermi="['saas:tenant:update']">修改信息</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit-outline" @click="handleAdmin(row)"
              v-hasPermi="['saas:tenant:update_role']">修改权限</el-button>
            <!-- <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="updataPassword(row)"
              >修改密码</el-button
            >

            -->
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <!--
      修改和新增信息
     -->
    <!-- 新增 -->
    <el-dialog title="新增账号" :visible.sync="userAvisible" append-to-body center width="75%" :close-on-click-modal="false"
      @opened="setTreeAll" @close="cancel">
      <el-form ref="formData" :model="formData" :rules="rules" label-width="110px">
        <el-row :gutter="0">
          <el-col :span="12">
            <el-form-item label="主体名称" prop="subjectName">
              <el-input v-model.trim="formData.subjectName" placeholder="请输入主体名称" maxlength="50" />
            </el-form-item>
            <el-form-item label="姓名" prop="name">
              <el-input v-model.trim="formData.name" maxlength="10" placeholder="请输入姓名" />
            </el-form-item>
            <el-form-item label="登录名" prop="username">
              <el-input maxlength="20" v-model.trim="formData.username" placeholder="请输入登录名" />
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input maxlength="50" v-model.trim="formData.password" placeholder="请输入密码" />
            </el-form-item>
            <el-form-item label="联系号码" prop="phone">
              <el-input v-model.trim="formData.phone" placeholder="请输入联系号码" />
            </el-form-item>
            <el-row>
              <el-col :span="12">
                <el-form-item label="流量平台" prop="allotPlatformIdList">
                  <el-select v-model="formData.allotPlatformIdList" multiple clearable>
                    <el-option v-for="item in PlatformOptions" :key="item.allotPlatformId" :value="item.allotPlatformId"
                      :label="item.platformName"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大客户数量" prop="maxCustomerNum">
                  <el-input v-model.number="formData.maxCustomerNum" placeholder="请输入最大客户数量"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="过期时间" prop="expirationTime">
                  <el-date-picker v-model="formData.expirationTime" value-format="yyyy-MM-dd" type="date"
                    :picker-options="pickerOptions" placeholder="选择过期时间" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大部门数量" prop="maxDeptNum">
                  <el-input v-model.number="formData.maxDeptNum" placeholder="请输入最大部门数量"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="城市白名单" prop="areaList">
                  <BaseCascader :options="cityList" :is_deep="true" :has_all_select="true" @getOptions="getCityList" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大产品数量" prop="maxProductNum">
                  <el-input v-model.number="formData.maxProductNum" placeholder="请输入最大产品数量"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="最大角色数量" prop="maxRoleNum">
                  <el-input v-model.number="formData.maxRoleNum" placeholder="请输入最大角色数量"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大员工数量" prop="maxUserNum">
                  <el-input v-model.number="formData.maxUserNum" placeholder="请输入最大员工数量"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="每日公海最大领取客户数量" prop="maxReceiveNum" label-width="200px">
              <el-input v-model.number="formData.maxReceiveNum" placeholder="请输入每日公海最大领取客户数量"></el-input>
            </el-form-item>
            <div>
              <el-form-item label="短信通知有新客过期时间" label-width="200" prop="isSmsNotice">
                <el-radio v-model="formData.isSmsNotice" :label="true">启用</el-radio>
                <el-radio v-model="formData.isSmsNotice" :label="false">禁用</el-radio>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="短信过期时间"  prop="smsNoticeExpireTime">
                <el-date-picker v-model="formData.smsNoticeExpireTime" value-format="yyyy-MM-dd" type="date"
                  :picker-options="pickerOptions" placeholder="请选择短信过期时间" />
              </el-form-item>
            </div>
            <div>
              <el-form-item label="状态" prop="userStatus">
                <el-radio v-model="formData.userStatus" :label="0">启用</el-radio>
                <el-radio v-model="formData.userStatus" :label="1">禁用</el-radio>
              </el-form-item>
            </div>
          </el-col>

          <el-col :span="12">
            <el-form-item label="菜单权限" prop="menuIds">
              <el-tree class="tree-border" :props="{
                children: 'children',
                label: 'label',
              }" :default-expand-all="false" :data="menuOptions" show-checkbox ref="menu" node-key="id"
                @check-change="setSelectCheckKey"></el-tree>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormData">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 修改 -->
    <el-dialog title="修改账号" :visible.sync="editAvisible" append-to-body center width="70%" :close-on-click-modal="false"
      @close="editCancel">
      <el-form ref="editformData" :model="editformData" :rules="rules" label-width="130px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="主体名称" prop="subjectName">
              <el-input v-model.trim="editformData.subjectName" placeholder="请输入主体名称" maxlength="50" />
            </el-form-item>
            <el-form-item label="姓名" prop="name">
              <el-input v-model.trim="editformData.name" maxlength="10" placeholder="请输入姓名" />
            </el-form-item>
            <el-form-item label="登录名" prop="username">
              <el-input maxlength="20" disabled v-model.trim="editformData.username" placeholder="请输入登录名" />
            </el-form-item>
            <el-form-item label="密码">
              <el-input maxlength="50" v-model.trim="editformData.password" placeholder="请输入密码" />
            </el-form-item>
            <el-form-item label="联系号码" prop="phone">
              <el-input v-model.trim="editformData.phone" placeholder="请输入联系号码" />
            </el-form-item>
            <el-form-item label="过期时间" prop="expirationTime">
              <el-date-picker v-model="editformData.expirationTime" value-format="yyyy-MM-dd" type="date"
                :picker-options="pickerOptions" placeholder="选择过期时间" />
            </el-form-item>
            <el-form-item label="短信过期时间" prop="smsNoticeExpireTime">
              <el-date-picker v-model="editformData.smsNoticeExpireTime" value-format="yyyy-MM-dd" type="date"
                :picker-options="pickerOptions" placeholder="请选择短信过期时间" />
            </el-form-item>
            <el-form-item label="短信通知有新客过期时间" label-width="200" prop="isSmsNotice">
              <el-radio v-model="editformData.isSmsNotice" :label="true">启用</el-radio>
              <el-radio v-model="editformData.isSmsNotice" :label="false">禁用</el-radio>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大客户数量" prop="maxCustomerNum">
              <el-input v-model.number="editformData.maxCustomerNum" placeholder="请输入最大客户数量"></el-input>
            </el-form-item>
            <el-form-item label="最大角色数量" prop="maxRoleNum">
              <el-input v-model.number="editformData.maxRoleNum" placeholder="请输入最大角色数量"></el-input>
            </el-form-item>
            <el-form-item label="最大员工数量" prop="maxUserNum">
              <el-input v-model.number="editformData.maxUserNum" placeholder="请输入最大员工数量"></el-input>
            </el-form-item>
            <el-form-item label="最大产品数量" prop="maxProductNum">
              <el-input v-model.number="editformData.maxProductNum" placeholder="请输入最大产品数量"></el-input>
            </el-form-item>

            <el-form-item label="每日公海最大领取客户数量" prop="maxReceiveNum" label-width="200px">
              <el-input v-model.number="editformData.maxReceiveNum" placeholder="请输入每日公海最大领取客户数量"></el-input>
            </el-form-item>
            <el-form-item label="最大部门数量" prop="maxDeptNum">
              <el-input v-model.number="editformData.maxDeptNum" placeholder="请输入最大部门数量"></el-input>
            </el-form-item>

            <el-form-item label="城市白名单" prop="areaList">
              <BaseCascader ref="editCascader" :options="cityList" :is_deep="true" :has_all_select="true"
                :back_options="back_options" @getOptions="getCityList" />
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditFormData">确 定</el-button>
        <el-button @click="editCancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 修改权限 -->
    <el-dialog title="修改权限" :visible.sync="adminAvisible" append-to-body center width="600px"
      :close-on-click-modal="false" @opened="setSelectKey" @close="menusCancel">
      <el-form ref="editformData" :model="editformData" :rules="rules" label-width="100px">
        <el-form-item label="修改权限" prop="">
          <el-tree class="tree-border" :props="{
            children: 'children',
            label: 'label',
          }" :default-expand-all="false" :data="editMenuOptions" show-checkbox ref="editMenu" node-key="id"
            @check-change="editSetSelectCheckKey"></el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMeunsForm">确 定</el-button>
        <el-button @click="menusCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTenantList,
  getCityWhiteList,
  getAllTenantRoleMenu,
  getAllotPlatformListOptions,
  addTenantOne,
  updateUserStatus,
  getTenantInfoDetail,
  editTenantInfoDetail,
  getTenantMeunsDetail,
  editMeunsDetail,
} from "@/api/saas/merchant";
import BaseCascader from "@/components/cascader1";
export default {
  data() {

    var validatePhone = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("手机号不能为空"));
      } else if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
        callback(new Error("手机号格式错误"));
      } else {
        callback();
      }
    };
    var validateAdmin = (rule, value, callback) => {
      if (!value) {
        callback(new Error("登录名不能为空"));
      } else if (!/^(?![0-9]+$)[0-9A-Za-z]{4,16}$/.test(value)) {
        callback(new Error("登录名必须由6-20位必须包含字母"));
      } else {
        callback();
      }
    };
    var validatePwd = (rule, value, callback) => {
      if (!value) {
        callback(new Error("密码不能为空"));
      } else if (
        !/(?=.*[0-9])(?=.*[a-zA-Z])[a-zA-Z0-9!@#$%^&*,.;]{6,10}/.test(value)
      ) {
        callback(new Error("密码必须由6-16位组成必须包含字母和数字"));
      } else {
        callback();
      }
    };
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        }
      },
      tableList: [],
      cityOptions: [],
      menuOptions: [],
      editMenuOptions: [],
      PlatformOptions: [],
      cityList: [],
      back_options: [],
      checkKey: [],
      total: 0,
      formData: {
        areaList: [],
        menuIds: [],
        name: "",
        password: "",
        phone: "",
        remark: "",
        subjectName: "",
        userStatus: 0,
        username: "",
        expirationTime: "",
        maxCustomerNum: "",
        maxDeptNum: "",
        maxProductNum: "",
        maxReceiveNum: "",
        maxRoleNum: "",
        maxUserNum: "",
        allotPlatformIdList: [],
        isSmsNotice: false,
        smsNoticeExpireTime: ""
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        subjectName: undefined,
        name: undefined,
        phone: undefined,
      },
      adminData: { menuIds: [] },
      editformData: {
        areaList: [],
        name: "",
        password: "",
        phone: "",
        subjectName: "",
        userStatus: 0,
        username: "",
        expirationTime: "",
        maxCustomerNum: "",
        maxDeptNum: "",
        maxProductNum: "",
        maxReceiveNum: "",
        maxRoleNum: "",
        maxUserNum: "",
        isSmsNotice: false,
        smsNoticeExpireTime: ""
      },
      userAvisible: false,
      editAvisible: false,
      adminAvisible: false,
      rules: {
        menuIds: [
          {
            type: "array",
            required: true,
            message: "请选择菜单权限",
            trigger: "change",
          },
        ],
        subjectName: [
          { required: true, message: "请输入主体名称", trigger: "blur" },
        ],
        isSmsNotice: [
          { required: true, message: "请选择是否启用短信", trigger: "blur" },
        ],
        smsNoticeExpireTime: [
          { required: true, message: "请选择短信过期时间", trigger: "blur" },
        ],
        allotPlatformIdList: [
          { required: true, message: "请选择流量平台", trigger: "change" },
        ],
        maxCustomerNum: [
          { required: true, message: "请输入最大客户数量", trigger: "blur" },
        ],
        maxDeptNum: [
          { required: true, message: "请输入最大部门数量", trigger: "blur" },
        ],
        maxProductNum: [
          { required: true, message: "请输入最大产品数量", trigger: "blur" },
        ],
        maxReceiveNum: [
          { required: true, message: "请输入每日公海最大领取客户数量", trigger: "blur" },
        ],
        maxRoleNum: [
          { required: true, message: "请输入最大角色数量", trigger: "blur" },
        ],
        maxUserNum: [
          { required: true, message: "请输入最大员工数量", trigger: "blur" },
        ],
        expirationTime: [
          { required: true, message: "请选择过期时间", trigger: "change" },
        ],
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        username: [
          { required: true, validator: validateAdmin, trigger: "blur" },
        ],
        password: [{ required: true, validator: validatePwd, trigger: "blur" }],
        userStatus: [
          { required: true, message: "请选择状态", trigger: "blur" },
        ],
        phone: [{ required: true, validator: validatePhone, trigger: "blur" }],
        productIds: [
          { required: true, message: "请选择负责产品", trigger: "blur" },
        ],
        areaList: [
          {
            type: "array",
            required: true,
            message: "请选择城市白名单",
            trigger: "blur",
          },
        ],
      },
    };
  },
  components: {
    BaseCascader,
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    //新增
    handleAdd() {
      getAllTenantRoleMenu().then((res) => {
        this.userAvisible = true;
        this.menuOptions = res.data;
      });
      getAllotPlatformListOptions().then((res) => {

        this.PlatformOptions = res.data;
      });
    },
    //实现全选树形结构
    setTreeAll() {
      this.$refs.menu.setCheckedNodes(this.menuOptions);
      this.setSelectCheckKey();
    },

    //新增获取选取的树形结构
    setSelectCheckKey() {
      let child = this.$refs.menu.getCheckedKeys();
      let parent = this.$refs.menu.getHalfCheckedKeys();
      this.formData.menuIds = parent.concat(child);
    },
    //确认新增
    submitFormData() {
      this.formData.areaList = this.cityOptions.map((item) => {
        return {
          cityId: item.value,
          cityName: item.label,
        };
      });
      this.$refs.formData.validate((valid) => {
        if (valid) {
          addTenantOne(this.formData).then((res) => {
            this.$message.success("新增成功");
            this.getList();
            this.cancel();
          });
        }
      });
    },
    //新增取消
    cancel() {
      this.formData = {
        areaList: [],
        menuIds: [],
        name: "",
        password: "",
        phone: "",
        remark: "",
        subjectName: "",
        userStatus: 0,
        username: "",
        expirationTime: "",
        maxCustomerNum: "",
        maxDeptNum: "",
        maxProductNum: "",
        maxReceiveNum: "",
        maxRoleNum: "",
        maxUserNum: "",
        allotPlatformIdList: [],
        smsNoticeExpireTime: "",
        isSmsNotice: false
      };
      this.userAvisible = false;
      this.cityOptions = [];
      this.$refs.formData.resetFields();
    },
    //获取城市
    getCityList(e, options) {
      this.cityOptions = options;
    },
    //编辑弹窗
    handleEdit(row) {
      this.editAvisible = true;
      getTenantInfoDetail(row.tenantId).then((res) => {

        this.editformData.name = res.data.name;
        this.editformData.subjectName = res.data.subjectName;
        this.editformData.phone = res.data.phone;
        this.editformData.username = res.data.username;
        this.editformData.tenantId = res.data.tenantId;
        this.editformData.expirationTime = res.data.expirationTime;
        this.editformData.maxCustomerNum = res.data.maxCustomerNum;
        this.editformData.maxDeptNum = res.data.maxDeptNum;
        this.editformData.maxProductNum = res.data.maxProductNum;
        this.editformData.maxReceiveNum = res.data.maxReceiveNum;
        this.editformData.maxRoleNum = res.data.maxRoleNum;
        this.editformData.maxUserNum = res.data.maxUserNum;
        this.editformData.isSmsNotice = res.data.isSmsNotice;
        this.editformData.smsNoticeExpireTime = res.data.smsNoticeExpireTime;
        if (res.data.cityIdList.length) {
          if (res.data.cityIdList[0] != 1) {
            let arr = [];
            res.data.cityIdList.forEach((item) => {
              this.cityList.forEach((i) => {
                if (i.children) {
                  i.children.forEach((citem) => {
                    if (citem.value == item) {
                      arr = [...arr, [i.value, item]];
                    }
                  });
                }
              });
            });
            this.back_options = arr;
          } else {
            this.back_options = [[1]];
          }
        }
      });
    },

    //修改取消
    editCancel() {
      this.editformData = {
        areaList: [],
        name: "",
        password: "",
        phone: "",
        subjectName: "",
        tenantId: "",
        username: "",
        maxCustomerNum: "",
        maxDeptNum: "",
        maxProductNum: "",
        maxReceiveNum: "",
        maxRoleNum: "",
        maxUserNum: "",
        smsNoticeExpireTime: "",
        isSmsNotice: false
      };
      this.editAvisible = false;
      this.$refs.editformData.resetFields();
    },
    //编辑权限
    handleAdmin(row) {
      getTenantMeunsDetail(row.tenantId).then((res) => {
        this.adminData.tenantId = row.tenantId;
        this.editMenuOptions = res.data.treeList;
        this.checkKey = res.data.menuIdList;
        this.adminAvisible = true;
      });
    },

    //确认修改
    submitEditFormData() {
      this.editformData.areaList = this.$refs.editCascader.$refs["cascader"]
        .getCheckedNodes({
          leafOnly: true,
        })
        .map((item) => {
          return {
            cityId: item.value,
            cityName: item.label,
          };
        });
      this.$refs.editformData.validate((valid) => {
        if (valid) {
          editTenantInfoDetail(this.editformData).then((res) => {
            this.$message.success("修改成功");
            this.getList();
            this.editCancel();
          });
        }
      });
    },

    //回显树形结构
    setSelectKey() {
      this.checkKey.forEach((i) => {
        let node = this.$refs.editMenu.getNode(i);
        if (node) {
          if (node.isLeaf) {
            this.$refs.editMenu.setChecked(node, true);
          }
        }
      });
    },
    //修改树形结构
    editSetSelectCheckKey() {
      let child = this.$refs.editMenu.getCheckedKeys();
      let parent = this.$refs.editMenu.getHalfCheckedKeys();
      this.adminData.menuIds = parent.concat(child);
    },
    //编辑菜单权限
    submitMeunsForm() {
      if (!this.adminData.menuIds.length) {
        this.$message.error("权限不能为空");
        return;
      }
      editMeunsDetail(this.adminData).then((res) => {
        this.$message.success("修改成功");
        this.adminAvisible = false;
      });
    },
    //取消此修改
    menusCancel() {
      this.adminData.menuIds = [];
      this.adminData.tenantId = "";
      this.checkKey = [];
      this.editMenuOptions = [];
      this.adminAvisible = false;
    },
    //修改状态
    changeUserStatus(e, row) {
      this.$confirm("确认修改吗？", {
        type: "warning",
      })
        .then((res) => {
          updateUserStatus({
            status: e,
            tenantId: row.tenantId,
          }).catch((err) => {
            this.getList();
          });
        })
        .catch((err) => {
          this.getList();
        });
    },
    getList() {
      getTenantList(this.queryParams).then((res) => {
        this.tableList = res.rows;
        this.total = res.total;
      });
    },
  },

  mounted() {
    this.getList();
    getCityWhiteList().then((res) => {
      let data = null;
      data = res.data.map((item) => {
        if (item.citys) {
          {
            return {
              value: item.id,
              label: item.name,
              children: [
                ...item.citys.map((citem) => {
                  return {
                    value: citem.id,
                    label: citem.name,
                  };
                }),
              ],
            };
          }
        } else {
          return {
            value: item.id,
            label: item.name,
          };
        }
      });
      this.cityList = JSON.parse(JSON.stringify(data));
    });
  },
};
</script>

<style lang="scss" scoped>
.title {
  text-align: center;
  font-size: 22px;
  font-weight: 800;
}

::v-deep .el-cascader__tags {
  max-height: 120px;
  overflow-y: scroll;
}

::v-deep .el-cascader .el-input__inner {
  max-height: 120px;
  overflow-y: scroll;
}

::v-deep .el-cascader .el-cascader__search-input {
  max-height: 120px;
  overflow-y: scroll;
}

::v-deep .el-cascader__tags::-webkit-scrollbar {
  width: 0;
}

.tree-border {
  height: 400px;
  overflow: auto;
}
</style>
