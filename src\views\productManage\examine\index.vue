<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="时间">
        <el-date-picker size="small" v-model="value1" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleQuery">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="推广名称" prop="name">
        <el-input v-model="queryParams.productLike" placeholder="请输入推广名称" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="商户名称" prop="partyLike">
        <el-input v-model="queryParams.partyLike" placeholder="请输入商户名称" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="商务名称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入商务名称" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select clearable size="small" @change="handleQuery" v-model="queryParams.auditStatus">
          <el-option label="审核中" :value="0"></el-option>
          <el-option label="审核通过" :value="1"></el-option>
          <el-option label="审核拒绝" :value="2"></el-option>
          <el-option label="全部" :value="3"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">筛选</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="productList">
      <el-table-column label="推广ID" prop="productId" align="center" />
      <el-table-column label="平台名称" prop="platformName" align="center" />
      <el-table-column label="推广名称" prop="productName" align="center" />
      <el-table-column label="商户" prop="partyName" align="center" />
      <el-table-column label="商务" prop="userName" align="center" />
      <el-table-column label="审核类型" prop="" align="center">
        <template slot-scope="{ row }">
            <el-link v-if="row.auditType" :underline="false" type="danger">修改</el-link>
            <el-link v-if="!row.auditType" :underline="false">新增</el-link>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" prop="auditStatus" align="center" >
        <template slot-scope="{ row }">
          <el-button type="primary" size="mini" plain v-if="row.auditStatus == 0">待审核</el-button>
          <el-button type="success" size="mini" plain v-if="row.auditStatus == 1">审核通过</el-button>
          <el-button type="danger" size="mini" plain v-if="row.auditStatus == 2" style="color: red">审核拒绝</el-button>
        </template>
      </el-table-column>
      <el-table-column label="推广价格" prop="cooperationCost" align="center" />
      <el-table-column label="提交时间" prop="createTime" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-button
                type="text"
                icon="el-icon-edit-outline"
                @click="getProductInfo(row)"
                v-if="row.auditStatus == 0"
              >
                审核
              </el-button>
              <el-link v-if="row.auditStatus == 1" type="info" :underline="false" disabled>已操作</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page-sizes="[30, 50, 80, 100]" :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize" @pagination="getList" />
    <!-- 新增推广 -->
    <el-dialog title="推广详情" :visible.sync="addVisible" center :width="auditType ? '1300px' : '1100px'" append-to-body
      @close="cancle" :close-on-click-modal="false">
      <div class="sept">
        <div class="active">1</div>
        <span style="margin: 0 5px"> 投放信息</span>
        <span style="margin: 0 10px">-----</span>
        <div :class="[step == 1 ? 'active' : '']">2</div>
        <span style="margin: 0 5px">投放要求</span>
      </div>
      <el-row>
        <el-col :span="auditType ? 12 : 24">
          <div class="desc" v-if="auditType">当前数据</div>
          <el-form inline ref="formData" label-position="right" :model="formData" label-width="150px" v-if="step == 0"
            class="addProduct">
            <el-row>
              <el-col :span="24">
                <el-form-item label="推广LOGO" :class="[isUpdate.includes('file') ? 'info' : '']" prop="file">
                  <img class="image" :src="imageUrl" />
                </el-form-item>
              </el-col>
            </el-row>
            <div>
              <el-form-item label="推广名称" :class="[isUpdate.includes('name') ? 'info' : '']" prop="name">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入推广名称"
                  maxlength="50" v-model="formData.name"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="合作类型" :class="[isUpdate.includes('cooperationTypeStr') ? 'info' : '']"
                prop="cooperationTypeStr">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入合作类型"
                  v-model="formData.cooperationTypeStr"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="商户类型" prop="productMid" :class="[isUpdate.includes('productMid') ? 'info' : '']">
                <el-select clearable disabled :style="auditType ? 'width:220px' : 'width:350px'"
                  v-model="formData.productMid" placeholder="请选择商户类型" size="small" style="width: 100%">
                  <el-option label="银行机构" :value="1"> </el-option>
                  <el-option label="线上-贷超" :value="2"> </el-option>
                  <el-option label="线上持牌机构" :value="3"> </el-option>
                  <el-option label="一级机构" :value="4"> </el-option>
                  <el-option label="二级机构" :value="5"> </el-option>
                  <el-option label="三级机构" :value="6"> </el-option>
                  <el-option label="四级机构" :value="7"> </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="合作链接" :class="[isUpdate.includes('cooperationLink') ? 'info' : '']" prop="cooperationLink">
                <el-input disabled style="width:400px"
                  v-model="formData.cooperationLink"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="企微类型" :class="[isUpdate.includes('qwTypeFlag') ? 'info' : '']" prop="qwTypeFlag">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'"
                  :value="getQwTypeFlagText(formData.qwTypeFlag)"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="合作价格" :class="[isUpdate.includes('cooperationCost') ? 'info' : '']"
                prop="cooperationCost">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入推广名称"
                  maxlength="50" v-model="formData.cooperationCost"></el-input>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="放款城市" :class="[isUpdate.includes('district1') ? 'info' : '']" prop="district">
                <BaseCascader class="cascader" :options="cityList" :is_deep="true" :isDisable="true"
                  :has_all_select="true" :back_options="formData.district" />
              </el-form-item>
            </div>
            <div>
              <el-form-item label="可贷额度(万)" :class="[
                isUpdate.includes('loanableFundsLittle') ||
                isUpdate.includes('loanableFundsBig')
                  ? 'info'
                  : '',
              ]" prop="loanableFundsLittle">
                <el-input :style="auditType ? 'width:220px' : 'width:350px'" disabled placeholder="请输入可贷额度小"
                  maxlength="7" v-model="formData.loanableFundsLittle"></el-input>
              </el-form-item>
              <el-form-item prop="loanableFundsBig">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入可贷额度大"
                  maxlength="7" v-model="formData.loanableFundsBig"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="办理时间(小时)" :class="[
                isUpdate.includes('handlingTimeLittle') ||
                isUpdate.includes('handlingTimeBig')
                  ? 'info'
                  : '',
              ]" prop="handlingTimeLittle">
                <el-input :style="auditType ? 'width:220px' : 'width:350px'" disabled placeholder="请输入办理时间开始"
                  v-model="formData.handlingTimeLittle"></el-input>
              </el-form-item>
              <el-form-item prop="handlingTimeBig">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入办理时间结束"
                  v-model="formData.handlingTimeBig"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="利率(年%)" :class="[
                isUpdate.includes('interestRateLittle') ||
                isUpdate.includes('interestRateBig')
                  ? 'info'
                  : '',
              ]" prop="interestRateLittle">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入利率小" maxlength="7"
                  v-model="formData.interestRateLittle"></el-input>
              </el-form-item>
              <el-form-item prop="interestRateBig">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入利率大" maxlength="7"
                  v-model="formData.interestRateBig"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="贷款期限(月)" :class="[
                isUpdate.includes('loanPeriodLittle') ||
                isUpdate.includes('loanPeriodBig')
                  ? 'info'
                  : '',
              ]" prop="loanPeriodLittle">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入贷款期限开始"
                  maxlength="5" v-model="formData.loanPeriodLittle"></el-input>
              </el-form-item>
              <el-form-item prop="loanPeriodBig">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" maxlength="5"
                  placeholder="请输入贷款期限结束" v-model="formData.loanPeriodBig"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="还款方式" :class="[isUpdate.includes('refundMode') ? 'info' : '']" prop="refundMode">
                <el-select clearable disabled v-model="formData.refundMode" placeholder="请选择还款方式" size="small"
                  :style="auditType ? 'width:220px' : 'width:350px'">
                  <el-option label="随借随还" :value="1"> </el-option>
                  <el-option label="按月还款" :value="2"> </el-option>
                  <el-option label="等额本息" :value="3"> </el-option>
                  <el-option label="等额本金" :value="4"> </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="推广描述" :class="[isUpdate.includes('describe') ? 'info' : '']" prop="describe">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" type="textarea"
                  placeholder="请输入推广描述" v-model="formData.describe"></el-input>
              </el-form-item>
            </div>
          </el-form>
        </el-col>
        <el-col :span="12" v-if="auditType">
          <div class="desc" v-if="auditType">上次修改数据</div>
          <el-form inline label-position="right" :model="formData" label-width="150px" v-if="step == 0"
            class="addProduct">
            <el-row>
              <el-col :span="24">
                <el-form-item label="推广LOGO" prop="file">
                  <img class="image" :src="imageUrlOld" />
                </el-form-item>
              </el-col>
            </el-row>
            <div>
              <el-form-item label="推广名称" prop="name">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入推广名称"
                  maxlength="50" v-model="formDataOld.name"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="合作类型" prop="name">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入合作类型"
                  maxlength="50" v-model="formDataOld.cooperationTypeStr"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="商户类型" prop="productOldMid">
                <el-select clearable disabled :style="auditType ? 'width:220px' : 'width:350px'"
                  v-model="formDataOld.productOldMid" placeholder="请选择商户类型" size="small" style="width: 100%">
                  <el-option label="银行机构" :value="1"> </el-option>
                  <el-option label="线上-贷超" :value="2"> </el-option>
                  <el-option label="线上持牌机构" :value="3"> </el-option>
                  <el-option label="一级机构" :value="4"> </el-option>
                  <el-option label="二级机构" :value="5"> </el-option>
                  <el-option label="三级机构" :value="6"> </el-option>
                  <el-option label="四级机构" :value="7"> </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="合作链接" prop="cooperationLink">
                <el-input disabled style="width:400px"
                  v-model="formDataOld.cooperationLink"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="企微类型" prop="qwTypeFlag">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'"
                  :value="getQwTypeFlagText(formDataOld.qwTypeFlag)"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="合作价格" prop="cooperationCost">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入推广名称"
                  maxlength="50" v-model="formDataOld.cooperationCost"></el-input>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="放款城市" prop="district">
                <BaseCascader class="cascader" :options="cityList" :is_deep="true" :isDisable="true"
                  :has_all_select="true" :back_options="formDataOld.district" />
              </el-form-item>
            </div>
            <div>
              <el-form-item label="可贷额度(万)" prop="loanableFundsLittle">
                <el-input :style="auditType ? 'width:220px' : 'width:350px'" disabled placeholder="请输入可贷额度小"
                  maxlength="7" v-model="formDataOld.loanableFundsLittle"></el-input>
              </el-form-item>
              <el-form-item prop="loanableFundsBig">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入可贷额度大"
                  maxlength="7" v-model="formDataOld.loanableFundsBig"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="办理时间(小时)" prop="handlingTimeLittle">
                <el-input :style="auditType ? 'width:220px' : 'width:350px'" disabled placeholder="请输入办理时间开始"
                  v-model="formDataOld.handlingTimeLittle"></el-input>
              </el-form-item>
              <el-form-item prop="handlingTimeBig">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入办理时间结束"
                  v-model="formDataOld.handlingTimeBig"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="利率(年%)" prop="interestRateLittle">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入利率小" maxlength="7"
                  v-model="formDataOld.interestRateLittle"></el-input>
              </el-form-item>
              <el-form-item prop="interestRateBig">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入利率大" maxlength="7"
                  v-model="formDataOld.interestRateBig"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="贷款期限(月)" prop="loanPeriodLittle">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" placeholder="请输入贷款期限开始"
                  maxlength="5" v-model="formDataOld.loanPeriodLittle"></el-input>
              </el-form-item>
              <el-form-item prop="loanPeriodBig">
                <el-input disabled :style="auditType ? 'width:220px' : 'width:350px'" maxlength="5"
                  placeholder="请输入贷款期限结束" v-model="formDataOld.loanPeriodBig"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="还款方式" prop="refundMode">
                <el-select clearable disabled v-model="formDataOld.refundMode" placeholder="请选择还款方式" size="small"
                  :style="auditType ? 'width:220px' : 'width:350px'">
                  <el-option label="随借随还" :value="1"> </el-option>
                  <el-option label="按月还款" :value="2"> </el-option>
                  <el-option label="等额本息" :value="3"> </el-option>
                  <el-option label="等额本金" :value="4"> </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="推广描述" prop="describe">
                <el-input disabled style="width: 200px" type="textarea" placeholder="请输入推广描述"
                  v-model="formDataOld.describe"></el-input>
              </el-form-item>
            </div>
          </el-form>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="auditType ? 12 : 24">
          <el-form label-width="140px" v-if="step == 1">
            <div>
              <el-form-item label="推广规则状态" :class="[isUpdate.includes('status') ? 'info' : '']">
                <el-radio v-model="formData.status" disabled label="0">启用</el-radio>
                <el-radio v-model="formData.status" disabled label="1">停用</el-radio>
              </el-form-item>
              <el-form-item label="年龄范围" :class="[
                isUpdate.includes('startAge') || isUpdate.includes('endAge')
                  ? 'info'
                  : '',
              ]" style="height: 20px">
                <div style="display: flex">
                  <el-input max="100" disabled style="width: 220px" maxlength="2" placeholder="请输入最小年龄"
                    v-model.number="formData.startAge" oninput="value=value.replace(/[^0-9]/g,'')" size="small">
                  </el-input>
                  <el-input max="100" disabled style="width: 220px; margin-left: 10px" maxlength="2"
                    placeholder="请输入最大年龄" oninput="value=value.replace(/[^0-9]/g,'')" v-model="formData.endAge"
                    size="small"></el-input>
                </div>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="需求额度范围" :class="[
                isUpdate.includes('startQuota') ||
                isUpdate.includes('endQuota')
                  ? 'info'
                  : '',
              ]" style="height: 20px">
                <div style="display: flex">
                  <el-input maxlength="8" disabled style="width: 220px" placeholder="请输入最小范围"
                    v-model="formData.startQuota" oninput="value=value.replace(/[^0-9]/g,'')" size="small"></el-input>

                  <el-input maxlength="8" disabled style="width: 220px; margin-left: 10px" placeholder="请输入最大范围"
                    v-model="formData.endQuota" oninput="value=value.replace(/[^0-9]/g,'')" size="small"></el-input>
                </div>
              </el-form-item>
            </div>
            <el-form-item style="margin-bottom: 0px" v-for="item in basic_type" :key="item.dictId"
              :label="item.dictName" :class="[isUpdate.includes(getSettingId(item)) ? 'info' : '']">
              <el-checkbox-group v-model="checkList" style="display: flex; flex-wrap: wrap">
                <el-checkbox disabled v-for="citem in item.dataRules" :label="
                  item.paramName.split('_')[
                    item.paramName.split('_').length - 1
                  ] +
                  'Ids-' +
                  citem.dictCode
                " :key="citem.dictCode">{{ citem.dictLabel }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item style="margin-bottom: 0px" :class="[isUpdate.includes(getSettingId(item)) ? 'info' : '']"
              v-for="item in assets_typeList" :key="item.dictId" :label="item.dictName">
              <el-checkbox-group v-model="checkList" style="display: flex; flex-wrap: wrap">
                <el-checkbox disabled v-for="citem in item.dataRules" :label="
                  item.paramName.split('_')[
                    item.paramName.split('_').length - 1
                  ] +
                  'Ids-' +
                  citem.dictCode
                " :key="citem.dictCode">{{ citem.dictLabel }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="条件关系" :class="[isUpdate.includes('ruleStatus') ? 'info' : '']">
              <el-radio v-model="formData.ruleStatus" disabled label="0">选中的满足任意一条即可</el-radio>
              <el-radio v-model="formData.ruleStatus" disabled label="1">选中的都需要满足</el-radio>
            </el-form-item>
            <el-form-item label="匹配价格" :class="[isUpdate.includes('matchingPriceSort') ? 'info' : '']">
              <el-input disabled style="width: 220px" placeholder="请输入匹配价格" v-model.number="formData.matchingPriceSort"
                size="small"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12" v-if="auditType">
          <el-form label-width="140px" v-if="step == 1">
            <div>
              <el-form-item label="推广规则状态">
                <el-radio v-model="formDataOld.status" disabled label="0">启用</el-radio>
                <el-radio v-model="formDataOld.status" disabled label="1">停用</el-radio>
              </el-form-item>
              <el-form-item label="年龄范围" style="height: 20px">
                <div style="display: flex">
                  <el-input max="100" disabled style="width: 220px" maxlength="2" placeholder="请输入最小年龄"
                    v-model.number="formDataOld.startAge" oninput="value=value.replace(/[^0-9]/g,'')" size="small">
                  </el-input>
                  <el-input max="100" disabled style="width: 220px; margin-left: 10px" maxlength="2"
                    placeholder="请输入最大年龄" oninput="value=value.replace(/[^0-9]/g,'')" v-model="formDataOld.endAge"
                    size="small"></el-input>
                </div>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="需求额度范围" style="height: 20px">
                <div style="display: flex">
                  <el-input maxlength="8" disabled style="width: 220px" placeholder="请输入最小范围"
                    v-model="formDataOld.startQuota" oninput="value=value.replace(/[^0-9]/g,'')" size="small">
                  </el-input>

                  <el-input maxlength="8" disabled style="width: 220px; margin-left: 10px" placeholder="请输入最大范围"
                    v-model="formDataOld.endQuota" oninput="value=value.replace(/[^0-9]/g,'')" size="small"></el-input>
                </div>
              </el-form-item>
            </div>
            <el-form-item style="margin-bottom: 0px" v-for="item in basic_type" :key="item.dictId"
              :label="item.dictName">
              <el-checkbox-group v-model="checkListOld" style="display: flex; flex-wrap: wrap">
                <el-checkbox disabled v-for="citem in item.dataRules" :label="
                  item.paramName.split('_')[
                    item.paramName.split('_').length - 1
                  ] +
                  'Ids-' +
                  citem.dictCode
                " :key="citem.dictCode">{{ citem.dictLabel }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item style="margin-bottom: 0px" v-for="item in assets_typeList" :key="item.dictId"
              :label="item.dictName">
              <el-checkbox-group v-model="checkListOld" style="display: flex; flex-wrap: wrap">
                <el-checkbox disabled v-for="citem in item.dataRules" :label="
                  item.paramName.split('_')[
                    item.paramName.split('_').length - 1
                  ] +
                  'Ids-' +
                  citem.dictCode
                " :key="citem.dictCode">{{ citem.dictLabel }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="条件关系">
              <el-radio v-model="formDataOld.ruleStatus" disabled label="0">选中的满足任意一条即可</el-radio>
              <el-radio v-model="formDataOld.ruleStatus" disabled label="1">选中的都需要满足</el-radio>
            </el-form-item>
            <el-form-item label="匹配价格">
              <el-input disabled style="width: 220px" placeholder="请输入匹配价格"
                v-model.number="formDataOld.matchingPriceSort" size="small"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" v-if="step == 0" @click="septNext">下一步</el-button>
        <el-button v-if="step == 1" @click="step = 0">上一步</el-button>
        <el-button type="primary" v-if="step == 1" @click="confireCheck">审 核</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="failAvisible" width="40%" title="推广审核" append-to-body center :close-on-click-modal="false"
      @close="closeConfire">
      <el-form ref="checkData" :model="checkData">
        <el-form-item label="审核状态" prop="status" :rules="{
          required: true,
          message: '请选择审核状态',
          trigger: 'blur',
        }">
          <el-radio v-model="checkData.status" :label="1">审核通过</el-radio>
          <el-radio v-model="checkData.status" :label="2">审核不通过</el-radio>
        </el-form-item>
        <el-form-item label="理由" prop="remarks" :rules="{
          required: checkData.status == 2,
          message: '请选择审核状态',
          trigger: 'blur',
        }">
          <el-input v-model="checkData.remarks" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="checkSubmit">确 定</el-button>
        <el-button @click="closeConfire">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAuditProductList,
  getAuditProductInfo,
  getparyACityAll,
  editProductAudit,
} from "@/api/productManage/product";
import BaseCascader from "@/components/cascader";
export default {
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },

      total: 0,
      step: 0,
      auditType: 0,
      addVisible: false,
      failAvisible: false,
      imageUrl: "",
      imageUrlOld: "",
      id: "",
      isUpdate: [],
      productList: [],
      cityList: [],
      checkList: [],
      checkListOld: [],
      value1: [],
      assets_typeList: [],
      basic_type: [],
      queryParams: {
        pageNum: 1,
        pageSize: 30,
        partyLike: "",
        productLike: "",
        userName: "",
        startTime: "",
        stopTime: "",
        auditStatus: 0,
      },

      checkData: {
        status: 1,
        remarks: null,
      },
      formData: {
        refundMode: null,
        name: null,
        file: null,
        loanableFundsLittle: null,
        loanableFundsBig: null,
        handlingTimeBig: null,
        handlingTimeLittle: null,
        ruleStatus: "1",
        loanPeriodBig: null,
        loanPeriodLittle: null,
        interestRateLittle: null,
        interestRateBig: null,
        district: [],
        describe: null,
        startAge: "",
        endAge: "",
        startQuota: "",
        endQuota: "",
        status: "",
        cooperationCost: null,
        matchingPriceSort: null,
        productMid: null,
        cooperationTypeStr: null,
        cooperationLink: null,
        qwTypeFlag: null,
      },
      formDataOld: {
        refundMode: null,
        name: null,
        file: null,
        loanableFundsLittle: null,
        loanableFundsBig: null,
        handlingTimeBig: null,
        handlingTimeLittle: null,
        ruleStatus: "1",
        loanPeriodBig: null,
        loanPeriodLittle: null,
        interestRateLittle: null,
        interestRateBig: null,
        district: [],
        describe: null,
        startAge: "",
        endAge: "",
        startQuota: "",
        endQuota: "",
        status: "",
        cooperationCost: null,
        matchingPriceSort: null,
        productOldMid: null,
        cooperationTypeStr: null,
        cooperationLink: null,
        qwTypeFlag: null,
      },
      currentRow: {}
    };
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.value1 !== null) {
        // this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    //查询详情
    getProductInfo(row) {
      this.currentRow = row
      this.id = row.productId;
      this.auditType = row.auditType;
      this.step = 0;
      const params = {
        productId: row.productId,
        platformId: row.platformId
      }
      getAuditProductInfo(params).then((res) => {
        this.assets_typeList = res.assets_typeList;
        this.basic_type = res.basic_type;
        this.addVisible = true;
        //处理对比字段

        for (let key in res.product) {
          if (
            res.product[key] != res.productOld[key] &&
            Object.keys(res.productOld).length
          ) {
            this.isUpdate.push(key);
          }
        }

        for (let key in res.productRule) {
          if (
            res.productRule[key] != res.productRuleOld[key] &&
            Object.keys(res.productRuleOld).length
          ) {
            this.isUpdate.push(key);
          }
        }
        if (res.productMid !== res.productOldMid) {
          this.isUpdate.push("productMid");
        }


        let cittCodeOld =
          res.cittCodeOld.length && res.cittCodeOld[0] == "1"
            ? [1]
            : res.cittCodeOld;
        let cittCode =
          res.cittCode.length && res.cittCode[0] == "1" ? [1] : res.cittCode;
        if (cittCodeOld.toString() != cittCode.toString()) {
          this.isUpdate.push("district1");
        }
        for (let key in res.product) {
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = res.product[key];
          }
        }

        this.formDataOld = res.productOld;

        // 设置企微类型
        this.formData.qwTypeFlag = res.product.qwTypeFlag;
        this.formDataOld.qwTypeFlag = res.productOld.qwTypeFlag;
        this.imageUrl = res.product.logo;
        this.imageUrlOld = res.productOld.logo;

        if (res.cittCode) {
          if (res.cittCode[0] != 1) {
            let arr = [];
            res.cittCode.forEach((item) => {
              this.cityList.forEach((i) => {
                if (i.children) {
                  i.children.forEach((citem) => {
                    if (citem.value == item) {
                      arr = [...arr, [i.value, item]];
                    }
                  });
                }
              });
            });
            this.formData.district = arr;
          } else {
            this.formData.district = [[1]];
          }
        }
        if (res.cittCodeOld) {
          if (res.cittCodeOld[0] != 1) {
            let arr = [];
            res.cittCodeOld.forEach((item) => {
              this.cityList.forEach((i) => {
                if (i.children) {
                  i.children.forEach((citem) => {
                    if (citem.value == item) {
                      arr = [...arr, [i.value, item]];
                    }
                  });
                }
              });
            });
            this.formDataOld.district = arr;
          } else {
            this.formDataOld.district = [[1]];
          }
        }

        this.formData.ruleStatus = res.productRule.ruleStatus;
        this.formData.startAge = res.productRule.startAge;
        this.formData.endAge = res.productRule.endAge;
        this.formData.startQuota = res.productRule.startQuota;
        this.formData.endQuota = res.productRule.endQuota;
        this.formData.status = res.productRule.status;
        this.formData.matchingPriceSort = res.productRule.matchingPriceSort;
        this.formData.productMid = res.productMid;
        //老的数据
        this.formDataOld.ruleStatus = res.productRuleOld.ruleStatus;
        this.formDataOld.startAge = res.productRuleOld.startAge;
        this.formDataOld.endAge = res.productRuleOld.endAge;
        this.formDataOld.startQuota = res.productRuleOld.startQuota;
        this.formDataOld.endQuota = res.productRuleOld.endQuota;
        this.formDataOld.status = res.productRuleOld.status;
        this.formDataOld.matchingPriceSort =
          res.productRuleOld.matchingPriceSort;
        this.formDataOld.productOldMid = res.productOldMid;

        //新的数据
        let obj = {};
        let ids = Object.keys(res.productRule);
        let newIds = ids.filter((item) => item.includes("Ids"));
        newIds.forEach((item) => {
          obj[item] = res.productRule[item];
        });
        for (let key in obj) {
          if (obj[key]) {
            obj[key].split(",").forEach((i) => {
              this.checkList.push(key + "-" + i);
            });
          }
        }
        //老的数据
        let objOld = {};
        let idsOld = Object.keys(res.productRuleOld);
        let newIdsOld = idsOld.filter((item) => item.includes("Ids"));
        newIdsOld.forEach((item) => {
          objOld[item] = res.productRuleOld[item];
        });
        for (let key in obj) {
          if (objOld[key]) {
            objOld[key].split(",").forEach((i) => {
              this.checkListOld.push(key + "-" + i);
            });
          }
        }
      });
    },

    //下一步
    septNext() {
      this.step = 1;
    },
    //提交审核
    checkSubmit() {
      this.$refs.checkData.validate((valid) => {
        if (valid) {
          this.checkData.id = this.id;
          this.checkData.platformId = this.currentRow.platformId;
          editProductAudit(this.checkData).then((res) => {
            this.addVisible = false;
            this.closeConfire();
            this.id = "";
            this.getList();
            this.$message.success("操作成功");
          });
        }
      });
    },

    confireCheck() {
      this.failAvisible = true;
    },

    cancle() {
      this.formData = {
        refundMode: null,
        name: null,
        file: null,
        loanableFundsLittle: null,
        loanableFundsBig: null,
        handlingTimeBig: null,
        handlingTimeLittle: null,
        ruleStatus: "1",
        loanPeriodBig: null,
        loanPeriodLittle: null,
        interestRateLittle: null,
        interestRateBig: null,
        district: [],
        describe: null,
        cooperationCost: null,
        matchingPriceSort: null,
        cooperationTypeStr: null,
        cooperationLink: null,
        qwTypeFlag: null,
      };
      this.formDataOld = {
        refundMode: null,
        name: null,
        file: null,
        loanableFundsLittle: null,
        loanableFundsBig: null,
        handlingTimeBig: null,
        handlingTimeLittle: null,
        ruleStatus: "1",
        loanPeriodBig: null,
        loanPeriodLittle: null,
        interestRateLittle: null,
        interestRateBig: null,
        district: [],
        describe: null,
        cooperationCost: null,
        matchingPriceSort: null,
        cooperationTypeStr: null,
        cooperationLink: null,
        qwTypeFlag: null,
      };
      this.checkList = [];
      this.checkListOld = [];
      this.isUpdate = [];
      this.step = 0;
    },
    //关闭审核

    closeConfire() {
      this.checkData = {
        status: 1,
        remark: "",
      };
      this.failAvisible = false;
      this.$refs.checkData.resetFields();
    },
    getList() {
      this.loading = true;
      getAuditProductList(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.productList = res.rows;
          this.productList.forEach((item) => {
            item.isEdit = true;
          });
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    getSettingId(item) {
      return item.paramName.split("_").pop() + "Ids";
    },
    // 获取企微类型文本
    getQwTypeFlagText(qwTypeFlag) {
      switch (qwTypeFlag) {
        case 0:
          return '正常';
        case 1:
          return '城市企微';
        case 2:
          return '低价企微';
        default:
          return '';
      }
    },
  },
  components: {
    BaseCascader,
  },
  computed: {},
  mounted() {
    this.getList();
    getparyACityAll().then((res) => {
      let data = null;
      data = res.data.map((item) => {
        if (item.citys) {
          {
            return {
              value: item.code,
              label: item.name,
              children: [
                ...item.citys.map((citem) => {
                  return {
                    value: citem.code,
                    label: citem.name,
                  };
                }),
              ],
            };
          }
        } else {
          return {
            value: item.code,
            label: item.name,
          };
        }
      });
      this.cityList = JSON.parse(JSON.stringify(data));
    });
  },
};
</script>

<style lang="scss" scoped>
.sept {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  margin-bottom: 30px;

  div {
    height: 40px;
    width: 40px;
    background-color: #cccccc;
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
  }

  .active {
    background: #e37318;
    color: #fff;
  }
}

.image {
  width: 120px;
  height: 120px;
  border: 1px solid #fff;
}

::v-deep .cascader {
  .el-input__inner {
    width: 220px;
  }
}

::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #46a6ff;
}

::v-deep .info .el-form-item__label {
  color: red;
}

.desc {
  padding-left: 150px;
  margin-bottom: 20px;
  font-size: 16px;
  color: red;
}
</style>
