<template>
  <div class="channel-selector">
    <!-- 已选渠道展示区域 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="card-content">
          <el-divider v-if="!value.length" class="empty-divider"></el-divider>
          <div v-if="!value.length" class="empty-content">
            <el-empty :image-size="60">
              <template slot="description">
                <el-button type="text" @click="handleSelectChannels">暂无渠道，点击选择渠道</el-button>
              </template>
            </el-empty>
          </div>
          <template v-else>
            <el-collapse v-model="channelCollapseActive">
              <el-collapse-item name="1">
                <template slot="title">
                  <div class="collapse-header">
                    <div>
                      <i :class="channelCollapseActive.includes('1') ? 'el-icon-arrow-down' : 'el-icon-arrow-right'" style="margin-right: 8px;"></i>
                      <span>已选择 {{ value.length }} 个渠道</span>
                    </div>
                    <el-button type="text" icon="el-icon-plus" @click.stop="handleSelectChannels">选择渠道</el-button>
                  </div>
                </template>
                <div class="channel-list">
                  <el-tag 
                    v-for="channelId in value" 
                    :key="channelId" 
                    closable 
                    type="primary"
                    @close="handleRemoveChannel(channelId)" 
                    class="channel-tag"
                  >
                    {{ getChannelLabel(channelId) }}
                  </el-tag>
                </div>
              </el-collapse-item>
            </el-collapse>
          </template>
        </div>
      </el-col>
    </el-row>

    <!-- 渠道选择弹窗 -->
    <el-dialog 
      title="选择渠道" 
      :visible.sync="channelSelectVisible" 
      width="1200px" 
      @close="handleDialogClose"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <el-form 
          :model="channelSelectForm" 
          :rules="channelSelectRules" 
          ref="channelSelectForm" 
          label-position="top"
        >
          <el-form-item label="选择渠道" prop="selectedChannels">
            <el-transfer
              filterable
              v-model="tempSelectedChannels"
              :data="allChannels"
              :titles="['待选渠道', '已选渠道']"
              :props="{
                key: 'id',
                label: 'renderLabel',
                disabled: 'disabled'
              }"
              @change="handleChannelSelectionChange"
            ></el-transfer>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="channelSelectVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleConfirmChannelSelect">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ChannelSelector',
  
  props: {
    // 选中的渠道ID数组
    value: {
      type: Array,
      default: () => []
    },
    // 其他分组已选择的渠道ID集合
    selectedChannels: {
      type: Set,
      default: () => new Set()
    },
    channelList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      channelCollapseActive: ['1'],
      channelSelectVisible: false,
      tempSelectedChannels: [],
      allChannels: [],
      channelSelectForm: {
        selectedChannels: []
      },
      channelSelectRules: {
        selectedChannels: [
          { required: true, message: '请至少选择一个渠道', trigger: 'blur' }
        ]
      }
    }
  },

  watch: {
    channelList: {
      immediate: true,
      handler(newVal) {
        this.allChannels = newVal.map(channel => ({
          ...channel,
          disabled: this.selectedChannels.has(channel.id)
        }))
      }
    }
  },

  methods: {
    // 打开选择渠道弹窗
    handleSelectChannels() {
      this.channelSelectForm.selectedChannels = [...this.value]
      this.tempSelectedChannels = [...this.value]
      
      // 更新渠道列表的禁用状态
      this.allChannels = this.allChannels.map(channel => ({
        ...channel,
        disabled: this.selectedChannels.has(channel.id)
      }))
      
      this.channelSelectVisible = true
      this.$nextTick(() => {
        this.$refs.channelSelectForm?.clearValidate()
      })
    },

    // 监听渠道选择变化
    handleChannelSelectionChange(value) {
      this.channelSelectForm.selectedChannels = value
      this.tempSelectedChannels = value
    },

    // 确认选择渠道
    handleConfirmChannelSelect() {
      this.$refs.channelSelectForm.validate(valid => {
        if (valid) {
          this.$emit('input', [...this.tempSelectedChannels])
          this.channelSelectVisible = false
        }
      })
    },

    // 移除渠道
    handleRemoveChannel(channelId) {
      // 判断是否是最后一个渠道
      if (this.value.length <= 1) {
        this.$message.warning('至少需要保留一个渠道')
        return
      }
      
      this.$confirm('确认移除该渠道吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const newValue = this.value.filter(id => id !== channelId)
        this.$emit('input', newValue)
      }).catch(() => {})
    },

    // 获取渠道标签文本
    getChannelLabel(channelId) {
      const channel = this.allChannels.find(item => item.id == channelId)
      return channel ? `${channel.channelName}-${channel.id}` : ''
    },

    // 处理弹窗关闭
    handleDialogClose() {
      this.tempSelectedChannels = []
      this.channelSelectForm.selectedChannels = []
      this.$refs.channelSelectForm?.clearValidate()
    }
  }
}
</script>

<style lang="scss" scoped>
.channel-selector {
  .card-content {
    .empty-content {
      padding: 20px 0;
      
      ::v-deep .el-button {
        font-size: 14px;
        padding: 0;
      }
    }

    .empty-divider {
      margin: 0;
    }
  }

  .channel-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px 0;

    .channel-tag {
      margin-right: 0;
      max-width: 100%;

      &.el-tag {
        display: inline-flex;
        align-items: center;
        height: auto;
        padding: 6px 10px;
        line-height: 1.4;
        white-space: normal;
        word-break: break-all;
      }
    }
  }

  .collapse-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .dialog-content {
    padding: 20px 20px 0;

    ::v-deep .el-transfer {
      display: flex;
      justify-content: center;
      align-items: center;

      .el-transfer-panel {
        flex: 1;
      }
    }
  }
}
</style> 