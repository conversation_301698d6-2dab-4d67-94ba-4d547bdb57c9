<template>
  <el-drawer title="商户合同" :visible.sync="drawer_" size="600px" :wrapper-closable="false" :direction="direction"
    @close="hanldeClose">
    <div class="drawer-container">
      <div v-if="contractCheckStatus == 2">
        <span style="margin-left: 10px">备注：{{ contractCheckRemarkInfo }}</span>
      </div>

      <div class="drawer-title justify-content-sb"><span>账户认证</span> <el-button size="mini" type="primary"
          v-if="contractCheckStatus != 2 && isContractCheck" @click="failAvisible = true">审核资质
        </el-button></div>
      <div class="drawer-wrap">
        <div class="flex">
          <div class="drawer-label">合同编号</div>
          <div class="drawer-value flex align-items-c"><span v-if="!showEditNum">{{ infoData.contractNo }}</span>
            <el-input type="text" v-model="infoData.contractNo" size="mini" v-if="showEditNum" />
            <el-button type="primary" size="mini" v-if="showEditNum && contractCheckStatus == 2"
              @click="changeContranctNo">确认修改
            </el-button>
            <el-button type="primary" size="mini" v-if="!showEditNum && contractCheckStatus == 2"
              @click="showEditNum = true">修改编号
            </el-button>
          </div>
        </div>
        <div class="flex">
          <div class="drawer-label">认证类型</div>
          <div class="drawer-value">{{ infoData.authType == 1 ? '公司' : '个人' }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">公司全称</div>
          <div class="drawer-value">{{ infoData.companyName }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">公司统一社会信用码</div>
          <div class="drawer-value">{{ infoData.companyCreditCode }}</div>
        </div>
        <div class="flex" v-if="isOnline">
          <div class="drawer-label">授权代表姓名</div>
          <div class="drawer-value">{{ infoData.authName }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">授权代表身份证号</div>
          <div class="drawer-value">{{ infoData.authIdCard }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">联系人</div>
          <div class="drawer-value">{{ infoData.contactsName }}</div>drawer_
        </div>
        <div class="flex">
          <div class="drawer-label">联系人电话</div>
          <div class="drawer-value">{{ infoData.contactsPhone }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">联系人邮箱</div>
          <div class="drawer-value">{{ infoData.contactsEmail }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">联系人地址</div>
          <div class="drawer-value">{{ infoData.contactsAddress }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">备注</div>
          <div class="drawer-value">{{ infoData.remark }}</div>
        </div>
      </div>
      <div class="drawer-title" v-if="isOnline">办公地址</div>
      <div class="drawer-wrap" v-if="isOnline">
        <div class="flex">
          <div class="drawer-label">所在省份</div>
          <div class="drawer-value">{{ infoData.provinceName || "-" }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">所在市区</div>
          <div class="drawer-value">{{ infoData.cityName || '-' }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">所在地区</div>
          <div class="drawer-value">{{ infoData.areaName || '-' }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">详细地址</div>
          <div class="drawer-value">{{ infoData.address || '-' }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">门牌号</div>
          <div class="drawer-value">{{ infoData.doorplate || '-' }}</div>
        </div>
      </div>
      <div class="drawer-title">资质信息</div>
      <div class="drawer-wrap">
        <div class="align-items-c ">
          <div class="drawer-label">营业执照</div>
          <div class="drawer-value align-items-c ">
            <template v-if="infoData.businessLicenseFilename">
              <el-image :src="infoData.businessLicenseFilenameUrl"
                :preview-src-list="[infoData.businessLicenseFilenameUrl]" style="width: 20px; height: 20px"></el-image>
              <i class="el-icon-download drawer-icon"></i>
            </template>
            <span v-else>-</span>

          </div>
        </div>
        <div class="flex">
          <div class="drawer-label">营业执照注册时间</div>
          <div class="drawer-value">{{ infoData.businessLicenseRegisterDate || "-" }}</div>
        </div>
        <div class="flex">
          <div class="drawer-label">营业执照有效时间</div>
          <div class="drawer-value">{{ infoData.businessLicenseValidityDate || "长期" }}</div>
        </div>
        <div class="align-items-c " v-if="isOnline">
          <div class="drawer-label">身份证正面</div>
          <div class="drawer-value align-items-c ">
            <template v-if="infoData.frontIdCardFilename">
              <el-image :src="infoData.frontIdCardFilenameUrl" style="width: 20px; height: 20px"
                :preview-src-list="[infoData.frontIdCardFilenameUrl]"></el-image>
              <i class="el-icon-download drawer-icon" @click="downLoad(infoData.frontIdCardFilenameUrl)"></i>
            </template>
            <span v-else>-</span>

          </div>
        </div>
        <div class="align-items-c " v-if="isOnline">
          <div class="drawer-label">身份证反面</div>
          <div class="drawer-value align-items-c ">
            <template v-if="infoData.reverseIdCardFilename">
              <el-image :src="infoData.reverseIdCardFilenameUrl" :preview-src-list="[infoData.reverseIdCardFilenameUrl]"
                style="width: 20px; height: 20px"></el-image>
              <i class="el-icon-download drawer-icon" @click="downLoad(infoData.reverseIdCardFilenameUrl)"></i>
            </template>
            <span v-else>-</span>
          </div>
        </div>
        <div class="flex">
          <div class="drawer-label">授权类型</div>
          <div class="drawer-value">{{ infoData.idGrantType == 1 ? '法人' : '授权人' }}</div>
        </div>
        <div class="align-items-c " v-if="infoData.leaseAgreementFilename && isOnline">
          <div class="drawer-label">租赁协议(营业执照注册未满半年)</div>
          <div class="drawer-value align-items-c ">
            <template v-if="infoData.leaseAgreementFilename">
              {{ infoData.leaseAgreementFilename || "-" }}
              <i class="el-icon-download drawer-icon" @click="downLoad(infoData.leaseAgreementFileUrl)"></i>
            </template>
            <span v-else>-</span>
          </div>
        </div>
        <div class="flex" v-if="infoData.leaseAgreementFilename && isOnline">
          <div class="drawer-label">承租类型</div>
          <div class="drawer-value">{{ infoData.leaseType == 1 ? '承租人为法人或公司' : '承租人为个人' }}</div>
        </div>
        <div class="flex" v-if="infoData.leaseAgreementGrantFilename && isOnline">
          <div class="drawer-label">租赁授权协议</div>
          <div class="drawer-value">
            {{ infoData.leaseAgreementGrantFilename || '-' }} <i class="el-icon-download drawer-icon"
              @click="downLoad(infoData.leaseAgreementGrantFileUrl)"></i>
          </div>
        </div>
        <div class="flex" v-if="infoData.certificateEmploymentFilename && isOnline && isLoanType">
          <div class="drawer-label">员工在职证明</div>
          <div class="drawer-value">
            {{ infoData.certificateEmploymentFilename || '-' }} <i class="el-icon-download drawer-icon"
              @click="downLoad(infoData.certificateEmploymentFileUrl)"></i>
          </div>
        </div>
        <div class="flex" v-if="isOnline">
          <div class="drawer-label">办公场地视频</div>
          <div class="drawer-value flex">
            {{ infoData.officeVideoFilename || '-' }} <i class="el-icon-download drawer-icon"
              @click="downLoad(infoData.officeVideoFilenameUrl, true)"></i>
            <el-upload :show-file-list="false" :auto-upload="false" :on-change="changeUpfile" action="#">
              <span v-hasPermi="['partyaAdmin:contract:update_video']" style="margin-left:10px" class="f_c005">修改</span>
            </el-upload>
          </div>
        </div>
        <div class="flex">
          <div class="drawer-label">合同文件</div>
          <div class="drawer-value">
            <span v-if="checkSuffix(infoData.contractFilename)" @click="hanldeShow({ fileUrl: infoData.contractFileUrl })"
              class="link"> {{ infoData.contractFilename
              }}</span>
            <span v-else>{{ infoData.contractFilename || '-' }}</span>
            <i class="el-icon-download drawer-icon" @click="downLoad(infoData.contractFileUrl)"></i>
          </div>
        </div>
        <template v-if="otherList.length">
          <div class="drawer-label">其他文件</div>
          <div class="flex file-item" v-for="(item, index) in otherList" :key="index">
            <span v-if="checkSuffix(item.filename)" @click="hanldeShow(item)" class="link"> {{ item.filename }}</span>
            <span v-else>{{ item.filename }}</span><i class="el-icon-download drawer-icon"
              @click="downLoad(item.fileUrl)"></i>
          </div>

        </template>
      </div>
      <div class="drawer-title">审批流程</div>
      <div class="drawer-process" v-if="infoData.processList && infoData.processList.length">
        <div v-for="(item, index) in infoData.processList" :key="index">
          <div class="flex align-items-c">
            <span
              :class="['iconfont f-suceess drawer-process-icon', iconList[item.status], colorList[item.status]]"></span>
            <span class="drawer-process-user">{{ item.name }} </span>
            <span :class="['drawer-process-status', tagList[item.status]]" v-if="item.status != 0 && item.status != -2">
              {{
                statusJson[item.status] || "" }}</span>
          </div>
          <div :class="['drawer-process-line', infoData.processList.length - 1 == index ? 'boder-none' : '']">
            <div class="drawer-process-time">{{ item.checkTime || "-" }}</div>
            <div :class="['drawer-process-remark', item.status == 2 ? 'fail' : '']"
              v-if="item.checkRemark && item.status != -2">{{
                item.checkRemark || "-" }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审核资质或合同 -->
    <el-dialog :visible.sync="failAvisible" width="60%" title="审核资质" append-to-body center @close="clearCheckData"
      :close-on-click-modal="false">
      <el-form ref="checkData" :model="checkData">
        <el-form-item label="审核状态" prop="contractCheckStatus" :rules="{
          required: true,
          message: '请选择审核状态',
          trigger: 'blur',
        }">
          <el-radio v-model="checkData.contractCheckStatus" :label="2">审核通过</el-radio>
          <el-radio v-model="checkData.contractCheckStatus" :label="3">审核不通过</el-radio>
        </el-form-item>

        <el-form-item label="合同编号">
          <el-input placeholder="请输入合同编号" v-model="checkData.contractNo"></el-input>
        </el-form-item>
        <el-form-item label="理由" prop="contractCheckRemark" :rules="{
          required: checkData.contractCheckStatus == 3,
          message: '请选择审核状态',
          trigger: 'blur',
        }">
          <el-input v-model="checkData.contractCheckRemark" type="textarea" maxlength="100" show-word-limit
            placeholder="请输入理由">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="checkSubmit">确 定</el-button>
        <el-button @click="clearCheckData">取 消</el-button>
      </div>
    </el-dialog>
  </el-drawer>
</template>

<script>
import {
  getPartyAOne,
  getContractInfo,
  checkContractInfo,
  updateContractNo,
  updateContractVideo,

} from "@/api/partyA";
export default {
  data() {
    return {

      statusJson: {
        0: "发起",
        1: "已通过",
        2: "已驳回",
        3: "结束",
        "-1": '待审核'
      },
      iconList: {
        0: 'icon-a-chaji6',
        1: 'icon-a-paichu3',
        2: 'icon-a-paichu2',
        3: 'icon-a-paichu3',
        "-1": 'icon-a-paichu1',
        "-2": 'icon-a-paichu3',
      },

      colorList: {
        0: 'f-suceess',
        1: 'f-suceess',
        2: 'f-danger',
        3: 'f-suceess',
        "-1": 'f-info',
      },
      tagList: {
        0: 'success',
        1: 'success',
        2: 'danger',
        3: 'success',
        "-1": 'info',
      },
      otherList: [],
      infoData: {},//甲方助贷合同
      isOnline: false,
      isLoanType: false,
      isContractCheck: true,
      failAvisible: false,
      contractCheckStatus: 0,
      showEditNum: false,
      contractCheckRemarkInfo: "",
      checkData: {
        contractCheckStatus: null,
        contractCheckRemark: null,
        partyFirstId: null,
        contractNo: null,
      },
    }
  },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    partybId: {
      type: [String, Number],
      default: ''
    },
    row: {
      type: Object,
      default: () => { }
    },
    partyFirstId: {
      type: [Number, String],
      default: ''
    },
    row: {
      type: Object,
      default: () => { }
    },

  },
  methods: {
    checkSuffix(url) {
      if (!url) return false
      let suffix = url.slice(url.lastIndexOf('.') + 1)
      return ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf'].includes(
        suffix
      )

    },

    hanldeShow(item) {
      console.log(item.fileUrl);

      if (item.fileUrl.slice(item.fileUrl.lastIndexOf('.') + 1) == 'pdf') {
        window.open('https://file.kkview.cn/onlinePreview?url=' + encodeURIComponent(window.btoa(item.fileUrl)), '_blank')

      } else {
        window.open('https://view.officeapps.live.com/op/embed.aspx?src=' + item.fileUrl, '_blank')
      }

    },
    hanldeClose() {
      this.showEditNum = false
      this.otherList = []
      this.infoData = {}
    },
    initPage() {
      this.isContractCheck = this.row.isCheck
      if (this.row.type == '1') {
        this.isLoanType = true
      } else {
        this.isLoanType = false
      }
      if (this.row.type == "0" || this.row.type == "1") {
        this.isOnline = true
      } else {
        this.isOnline = false
      }
      getContractInfo(this.partyFirstId).then((res) => {
        this.checkData.partyFirstId = this.partyFirstId;
        this.infoData = res.data || {}
        this.otherList = res.data.fileOrderList || []
        this.contractCheckStatus = res.data.contractCheckStatus;
        this.contractCheckRemarkInfo = res.data.contractCheckRemark;


      });
    },
    //下载合同
    downLoad(e, type) {

      window.open(e, '_blank')
      // const ele = document.createElement("a");
      // ele.setAttribute("href", e); //设置下载文件的url地址
      // ele.setAttribute("download", "download"); //用于设置下载文件的文件名
      // ele.click();
    },
    //关闭资质审核
    clearCheckData() {
      this.checkData.contractCheckStatus = null;
      this.checkData.contractCheckRemark = null;
      this.checkData.contractNo = null;
      this.failAvisible = false;
      this.$refs.checkData.resetFields();
    },
    //审核资质
    checkSubmit() {
      this.$refs.checkData.validate((valid) => {
        if (valid) {
          checkContractInfo(this.checkData).then((res) => {
            if (res.code == 200) {
              this.failAvisible = false;
              // this.contactDeatailAvisible = false;
              // this.getList();
              this.$emit('getList')
              this.failAvisible = false
              this.drawer_ = false
              this.$message.success("操作成功");
            }
          });
        }
      });
    },
    //修改合同编号
    changeContranctNo() {
      if (!this.infoData.contractNo) {
        this.$message.error("合同编号不能为空");
        return;
      }
      this.$confirm("确定修改合同编号吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          updateContractNo({
            contractNo: this.infoData.contractNo,
            partyFirstId: this.partyFirstId,
          }).then((res) => {
            this.$message.success("修改成功");
            this.drawer_ = false;
          });
        })
        .catch(() => { });
    },
    //修改场地视频
    changeUpfile(e) {
      if (!e.raw.type.includes("video")) {
        this.$message.error("请上传视频");
        return;
      }
      const isLt2M = e.size / 1024 / 1024 < 50;
      if (!isLt2M) {
        this.$message.error("视频大小不能超过50M");
        return;
      }

      let data = new FormData();
      data.append("file", e.raw);
      updateContractVideo(data, this.partyFirstId).then((res) => {
        this.$message.success("修改成功");
        this.infoData.officeVideoFilename = e.raw.name
      });
    },
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer
      },
      set(v) {
        this.$emit("update:drawer", false)
      }
    }
  },
  watch: {
    drawer(val) {
      if (val) {
        this.initPage()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-container {
  padding: 20px;
}

.drawer-title {
  font-size: 18px;
  font-weight: 400;
  color: #3D3D3D;
  position: relative;
  padding-left: 12px;
  margin-bottom: 20px;

  &::after {
    content: "";
    display: block;
    position: absolute;
    width: 4px;
    height: 20px;
    background: #e37318;
    top: 4px;
    left: 0;
  }
}

.drawer-wrap {
  .drawer-label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    width: 150px;
    text-align: right;
    margin-right: 30px;
  }

  .drawer-value {
    font-size: 14px;
    color: #000000;

    img {
      width: 20px;
      height: 20px;
    }
  }

  .drawer-icon {
    color: #0256FF;
    margin-left: 10px;
    font-size: 20px;
    cursor: pointer;
  }
}

.file-item {
  margin-top: 10px;
  padding-left: 120px;
}

.flex {
  margin-bottom: 10px;
}

.align-items-c {
  margin-bottom: 10px;
}

.drawer-process {
  padding: 20px;

  &-icon {
    margin-right: 10px;
  }

  &-user {
    font-size: 16px;
    color: #181716;
    width: 250px;
  }

  &-status {
    margin-left: 100px;
    font-size: 12px;
    padding: 2px 4px;
    background: #E5F9E9;
    border-radius: 2px;

    &.success {
      background: #E5F9E9;
      color: #3FA372;
      border: 1px solid #3FA372;
    }

    &.danger {
      color: #FF0000;
      background: #FFECEC;
      border: 1px solid #f00;
    }

    &.info {
      color: #FF8F1F;
      background: #FFE8D1;
      border: 1px solid #FF8F1F;
    }
  }

  &-line {
    margin: 3px 0px 5px 8px;
    border-left: 1px dashed #D8D8D8;
    padding-left: 20px;
    // padding-top: 10px;
    padding-bottom: 10px;

  }

  &-time {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 10px;
  }

  &-remark {
    width: 100%;
    background: #FAFAFA;
    border-radius: 4px 4px 4px 4px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    padding: 20px;

    &.fail {
      background: #FFECEC;
      color: #f00;
      border: 1px solid #f00;
    }
  }
}



.boder-none {
  border: none;
}

.link {
  cursor: pointer;
  color: #0052d9;
  text-decoration: underline;
}
</style>
