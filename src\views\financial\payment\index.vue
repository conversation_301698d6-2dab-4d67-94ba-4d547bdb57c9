<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" class="demo-form-inline">
      <el-form-item label="时间查询">
        <el-date-picker v-model="value2" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss"
          @change="Scounfung">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态">
        <el-select clearable size="small" v-model="queryParams.status" @change="getList">
          <el-option :value="-1" label="全部"> </el-option>
          <el-option :value="0" label="待审核"> </el-option>
          <el-option :value="1" label="已通过"> </el-option>
          <el-option :value="2" label="已拒绝"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="Scounfung">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="cashOutNumber" align="center" label="请款单号">
      </el-table-column>
      <el-table-column prop="money" label="请款金额" align="center" width="180">
      </el-table-column>
      <el-table-column prop="cashOutMs" align="center" label="请款提交时间">
      </el-table-column>
      <el-table-column prop="partyUsername" align="center" label="请款人" width="180">
      </el-table-column>
      <!-- <el-table-column align="center" label="审核状态" width="180">
        <template slot-scope="{ row }">
          <div :style="colorType[row.status]">
            {{ typeStatus[row.status] }}
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="查看凭证" prop="price" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.status == 1">
            <!-- <el-popover>
              <el-button slot="reference" type="text">查看凭证</el-button>
              <img class="replayImage" :src="row.fileName" alt="" />
            </el-popover> -->
            <el-image style="width: 100px; height: 100px" :src="row.fileName" :preview-src-list="[row.fileName]">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="reason" align="center" label="备注" width="180">
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button v-if="row.status == 0" icon="el-icon-check" type="text" @click="handleConfire(row)">确认
            </el-button>
            <el-button v-if="row.status == 1" type="text" @click="handleConfired(row)"> 已通过</el-button>
            <el-button v-if="row.status == 2" type="text" style="color: red">
              已拒绝</el-button>
            <el-button v-if="row.status == 0" icon="el-icon-close" type="text" style="color: red"
              @click="handleReject(row)">拒绝
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <el-dialog title="拒绝请款" :visible.sync="rejectAvisible" @close="rejectCancel" width="600px" append-to-body center
      :close-on-click-modal="false">
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="拒绝理由" prop="rejectReason">
          <el-input type="textarea" v-model="formData.rejectReason" placeholder="请输入拒绝理由" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="rejectCancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="请款确认" :visible.sync="confireAvisible" @close="agreeCancel" width="600px" append-to-body center
      :close-on-click-modal="false">
      <el-form label-position="left" :model="agreeFormData" :rules="agreeRules" label-width="100px" ref="agreedData">
        <el-form-item label="收款账户">
          <el-input v-model="detailInfo.proceedsAccount" disabled></el-input>
        </el-form-item>
        <el-form-item label="收款账号">
          <el-input v-model="detailInfo.proceedsNumber" disabled></el-input>
        </el-form-item>
        <el-form-item label="开户行">
          <el-input v-model="detailInfo.openingBank" disabled></el-input>
        </el-form-item>
        <el-form-item label="请款金额">
          <el-input v-model="detailInfo.money" disabled></el-input>
        </el-form-item>
        <el-form-item label="是否开票">
          <el-input v-model="detailInfo.invoiceTypeStr" disabled></el-input>
        </el-form-item>
        <el-form-item label="请款事项">
          <el-input v-model="detailInfo.matterStr" disabled></el-input>
        </el-form-item>
        <el-form-item label="打款主体">
          <el-input v-model="detailInfo.subject" disabled></el-input>
        </el-form-item>
        <el-form-item label="请款说明">
          <el-input v-model="detailInfo.description" disabled></el-input>
        </el-form-item>
        <el-form-item label="凭证上传" prop="file">
          <el-upload class="avatar-uploader" action="" :show-file-list="false" :auto-upload="false"
            :on-change="changeUpImg">
            <img v-if="imageUrl" style="vertical-align: middle; max-height: 100px" :src="imageUrl" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAgreeForm">确 定</el-button>
        <el-button @click="agreeCancel">取 消</el-button>
      </div>
    </el-dialog>


    <el-dialog title="已确认" :visible.sync="confiredAvisible" width="600px" append-to-body center
      :close-on-click-modal="false">
      <el-form label-position="left" :model="agreeFormData" :rules="agreeRules" label-width="100px" ref="agreedData">
        <el-form-item label="收款账户">
          <el-input v-model="detailInfo.proceedsAccount" disabled></el-input>
        </el-form-item>
        <el-form-item label="收款账号">
          <el-input v-model="detailInfo.proceedsNumber" disabled></el-input>
        </el-form-item>
        <el-form-item label="开户行">
          <el-input v-model="detailInfo.openingBank" disabled></el-input>
        </el-form-item>
        <el-form-item label="请款金额">
          <el-input v-model="detailInfo.money" disabled></el-input>
        </el-form-item>
        <el-form-item label="是否开票">
          <el-input v-model="detailInfo.invoiceTypeStr" disabled></el-input>
        </el-form-item>
        <el-form-item label="请款事项">
          <el-input v-model="detailInfo.matterStr" disabled></el-input>
        </el-form-item>
        <el-form-item label="打款主体">
          <el-input v-model="detailInfo.subject" disabled></el-input>
        </el-form-item>
        <el-form-item label="请款说明">
          <el-input v-model="detailInfo.description" disabled></el-input>
        </el-form-item>

      </el-form>

    </el-dialog>
  </div>
</template>

<script>
import {
  getPartBCashOutList,
  rejectPayment,
  agreePayment,
  getFinanceDetail,
} from "@/api/financial";
export default {
  data() {
    var imgRule1 = (rule, value, callback) => {
      if (this.agreeFormData.file == "" && this.imageUrl == "") {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#F56C6C";
        callback(new Error("请上传凭证"));
      } else if (this.agreeFormData.file || this.imageUrl) {
        callback();
      }
    };
    return {
      total: 0,
      rejectAvisible: false,
      confireAvisible: false,
      confiredAvisible: false,
      typeStatus: {
        0: "待审核",
        1: "已通过",
        2: "已拒绝",
      },
      colorType: {
        0: "color:#1E90FF",
        1: "color:#008000",
        2: "color:red",
      },
      imageUrl: "",

      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: -1,
        startTime: "",
        stopTime: "",
      },
      agreeFormData: {
        id: "",
        file: "",
      },
      detailInfo: {
        openingBank: "",
        description: "",
        invoiceTypeStr: "",
        matterStr: "",
        money: "",
        openingBank: "",
        proceedsAccount: "",
        proceedsNumber: "",
        subject: "",
      },
      formData: {
        id: "",
        rejectReason: null,
      },
      rules: {
        rejectReason: [
          { required: true, message: "请输入拒绝理由", trigger: "blur" },
        ],
      },
      agreeRules: {
        file: [
          {
            required: true,
            message: "请上传凭证",
            validator: imgRule1,
          },
        ],
      },

      tableData: [],
      value2: "",
    };
  },
  methods: {
    //请款乙方列表
    getList() {
      getPartBCashOutList(this.queryParams).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
      });
    },
    //时间查询

    Scounfung() {
      if (this.value2 != null) {
        var end = this.value2[1];
        var start = this.value2[0];
        this.queryParams.startTime = start;
        this.queryParams.stopTime = end;
      } else {
        this.queryParams.startTime = "";
        this.queryParams.stopTime = "";
      }
      this.getList();
    },
    //上传凭证
    changeUpImg(e) {
      const isJPG =
        e.raw.type === "image/jpeg" ||
        e.raw.type === "image/jpg" ||
        e.raw.type === "image/png";
      const isLt2M = e.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传图片只能是 JPG/JPEG/PNG 格式!");
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
        return;
      }
      this.imageUrl = URL.createObjectURL(e.raw);
      this.agreeFormData.file = e.raw;
      if (document.getElementsByClassName("el-form-item__error").length > 0) {
        document.getElementsByClassName(
          "el-form-item__error"
        )[0].style.display = "none";
      }
      if (document.getElementsByClassName("el-upload").length > 0) {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#8c939d";
      }
    },
    //确认
    handleConfire(row) {
      this.confireAvisible = true;
      this.agreeFormData.id = row.id;
      getFinanceDetail({ id: row.id }).then((res) => {
        for (let key in res.data) {
          if (Object.keys(this.detailInfo).includes(key)) {
            this.detailInfo[key] = res.data[key];
          }
        }

      });
    },
    //已确认
    handleConfired(row) {
      getFinanceDetail({ id: row.id }).then((res) => {
        this.confiredAvisible = true;
        for (let key in res.data) {
          if (Object.keys(this.detailInfo).includes(key)) {
            this.detailInfo[key] = res.data[key];
          }
        }
      });
    },
    //拒绝
    handleReject(row) {
      this.formData.id = row.id;
      this.rejectAvisible = true;
    },
    //拒绝取消
    rejectCancel() {
      this.rejectAvisible = false;
      this.formData.rejectReason = "";
      this.formData.id = "";
      this.$refs.formData.resetFields();
    },
    agreeCancel() {
      this.confireAvisible = false;
      this.agreeFormData = {
        id: "",
        file: "",
      };
      this.imageUrl = "";
      this.detailInfo = {
        openingBank: "",
        description: "",
        invoiceTypeStr: "",
        matterStr: "",
        money: "",
        openingBank: "",
        proceedsAccount: "",
        proceedsNumber: "",
        subject: "",
      };
      if (document.getElementsByClassName("el-upload").length > 0) {
        document.getElementsByClassName("el-upload")[0].style.borderColor =
          "#8c939d";
      }
      this.$refs.agreedData.resetFields();
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          rejectPayment(this.formData).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              this.rejectCancel();
              this.getList();
            }
          });
        }
      });
    },
    submitAgreeForm() {
      this.$refs.agreedData.validate((valid) => {
        if (valid) {
          let formData = new FormData();
          formData.append("file", this.agreeFormData.file);
          formData.append("id", this.agreeFormData.id);
          agreePayment(formData).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");
              this.getList();
              this.agreeCancel();
            }
          });
        }
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.replayImage {
  border: 0;
  width: 70vw;
  max-height: 100vh;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  // border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
</style>
