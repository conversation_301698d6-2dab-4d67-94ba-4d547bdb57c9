import request from '@/utils/request'

//企微产品规则分页查询
export function getQwProductRules(data){
  return request({
    url:"/loan/productRule/qwProductRules",
    method:"get",
    params:data
  })
}

export function qwProductTemplate(data){
  return request({
    url:"/loan/productRule/qwProductTemplate",
    method:"get",
    params:data
  })
}

//新增企微产品规则
export function addQwProductRule(data){
  return request({
    url:"/loan/productRule/addQwProductRule",
    method:"post",
    data
  })
}
export function updateQwProductRule(data) {
  return request({
    url: `/loan/productRule/updateQwProductRule/${data.id}`,
    method:"post",
    data
  })
}
// 获取企微类型标志
export function getQwTypeFlag() {
  return request({
    url: "/loan/productRule/qwTypeFlag",
    method: "get",
  });
}
