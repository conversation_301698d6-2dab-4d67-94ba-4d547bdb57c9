<template>
  <div class="config-editor">
    <!-- JSON类型配置 -->
    <div v-if="type == 1" class="json-config-section">
      <CodeMirrorEditor
        :value="value"
        mode="json"
        placeholder="请输入有效的JSON格式..."
        height="300px"
        class="json-editor"
        :saving="saving"
        @input="handleInput"
        @save="handleSave"
        @reset="handleReset"
      />
    </div>

    <!-- 字符串类型配置 -->
    <div v-else class="string-config-section">
      <CodeMirrorEditor
        :value="value"
        mode="text"
        placeholder="请输入配置值..."
        height="200px"
        class="string-editor"
        :saving="saving"
        @input="handleInput"
        @save="handleSave"
        @reset="handleReset"
      />
    </div>
  </div>
</template>

<script>
import CodeMirrorEditor from './CodeMirrorEditor'

export default {
  name: 'ConfigEditor',
  components: {
    CodeMirrorEditor
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    type: {
      type: Number,
      default: 1,
      validator: (value) => [1, 2].includes(value)
    },
    saving: {
      type: Boolean,
      default: false
    }
  },
  
  methods: {
    handleInput(newValue) {
      this.$emit('input', newValue)
    },
    
    handleSave() {
      this.$emit('save')
    },
    
    handleReset() {
      this.$emit('reset')
    }
  }
}
</script>

<style lang="scss" scoped>
.config-editor {
  // JSON配置区域
  .json-config-section {
    .json-editor {
      margin-bottom: 16px;
    }
  }

  // 字符串配置区域
  .string-config-section {
    .string-editor {
      margin-bottom: 16px;
    }
  }
}
</style>