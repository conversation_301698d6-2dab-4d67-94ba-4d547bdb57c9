<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      @submit.native.prevent
      :inline="true"
    >
      <el-form-item label="合作方名称" prop="partnerName">
        <el-input
          clearable
          placeholder="请输入合作方名称"
          size="small"
          @change="handleQuery"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.partnerName"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道来源" prop="source">
        <el-input
          clearable
          @change="handleQuery"
          placeholder="请输入渠道来源"
          size="small"
          @keyup.enter.native="handleQuery"
          v-model="queryParams.source"
        ></el-input>
      </el-form-item>
      <el-form-item label="我方推广渠道号" prop="channelId">
        <el-input
          clearable
          placeholder="请输入我方推广渠道号"
          size="small"
          oninput="value=value.replace(/[^0-9]/g,'')"
          @change="handleQuery"
          @keyup.enter.native="handleQuery"
          v-model.number="queryParams.channelId"
        ></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" @change="handleQuery" clearable>
          <el-option label="启用" value="0"></el-option>
          <el-option label="禁用" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="addDomain"
          v-hasPermi="['loan:api_channel:add']"
          >新增渠道接口</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="mini"
          @click="roundStr"
          >生成随机字符</el-button
        >
        <el-button
          icon="el-icon-setting"
          type="primary"
          size="mini"
          @click="handleH5EnvConfig"
          >H5跳转链接配置</el-button
        >
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" border :data="domianList">
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="合作方名称" prop="partnerName" align="center" />
      <el-table-column
        label="我方落地页渠道号"
        prop="myChannelId"
        align="center"
      />
      <el-table-column label="渠道来源" prop="channelId" align="center" />
      <el-table-column label="类型" prop="typeIds" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.typeIds.length">
            {{ row.typeIds.map((item) => types[item]).join(",") }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="150" align="center">
        <template slot-scope="{ row }">
          <div>
            <el-switch
              v-hasPermi="['loan:api_channel:update_status']"
              v-model="row.status"
              active-text="启用"
              inactive-text="禁用"
              active-value="0"
              inactive-value="1"
              @change="changeStatus($event, row.id)"
            >
            </el-switch>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="第三方AES_IV" prop="apiAesIv" align="center" />
      <el-table-column label="第三方AES_KEY" prop="apiAesKey" align="center" /> -->

      <el-table-column label="创建人" prop="createBy" align="center" />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="修改人" prop="updateBy" align="center" />
      <el-table-column label="修改时间" prop="updateTime" align="center" />
      <!-- <el-table-column label="H5跳转链接" prop="h5Url" align="center" />
      <el-table-column label="我方AES_IV" prop="myAesIv" align="center" />
      <el-table-column label="我方AES_KEY" prop="myAesKey" align="center" /> -->

      <!-- <el-table-column label="其它参数" prop="otherParam" align="center">
        <template slot-scope="{ row }">
          <div>
            {{ row.otherParam }}
          </div>
        </template>
      </el-table-column> -->

      <el-table-column label="备注" prop="remark" align="center" />

      <!-- <el-table-column label="白名单IP" prop="whitelistIp" align="center" /> -->

      <el-table-column align="center" width="160" label="操作">
        <template slot-scope="{ row }">
          <div>
            <el-button
              type="text"
              @click="handleDetail(row)"
              icon="el-icon-document"
              v-hasPermi="['loan:api_channel:query']"
              >查看详情</el-button
            >
            <el-button
              type="text"
              v-hasPermi="['loan:api_channel:update']"
              @click="handleEdit(row)"
              icon="el-icon-edit-outline"
              >修改</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加 -->
    <el-dialog
      :title="title"
      :visible.sync="domainAvisible"
      @close="cancel"
      width="800px"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="160px"
      >
        <el-form-item label="渠道来源" prop="channelId">
          <el-input
            v-model="formData.channelId"
            :disabled="
              title == '接口渠道配置详情' || title == '修改接口渠道配置'
            "
            placeholder="请输入渠道来源"
          />
        </el-form-item>
        <el-form-item label="落地页渠道号">
          <el-select
            @change="handleSelectChannel"
            v-model="formData.myChannelId"
            filterable
            placeholder="请选择落地页渠道号"
          >
            <el-option
              v-for="channel in channelList"
              :key="channel.id"
              :label="`${channel.id} ${channel.channelName}`"
              :value="channel.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="合作方名称" prop="partnerName">
          <el-input
            :disabled="title == '接口渠道配置详情'"
            v-model="formData.partnerName"
            placeholder="请输入合作方名称"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="isAdd">
          <el-radio
            v-model="formData.status"
            :disabled="title == '接口渠道配置详情'"
            label="0"
            >启用</el-radio
          >
          <el-radio
            v-model="formData.status"
            :disabled="title == '接口渠道配置详情'"
            label="1"
            >禁用</el-radio
          >
        </el-form-item>
        <el-form-item label="类型" prop="typeIds">
          <el-select
            v-model="formData.typeIds"
            :disabled="title == '接口渠道配置详情'"
            clearable
            multiple
          >
            <el-option label="撞库" value="0"></el-option>
            <el-option label="半流程" value="1"></el-option>
            <el-option label="注册" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="第三方AES_IV" prop="apiAesIv">
          <el-input
            v-model="formData.apiAesIv"
            :disabled="title == '接口渠道配置详情'"
            type="textarea"
            :rows="4"
            placeholder="请输入第三方AES_IV"
          />
        </el-form-item>
        <el-form-item label="第三方AES_KEY" prop="apiAesKey">
          <el-input
            v-model="formData.apiAesKey"
            :disabled="title == '接口渠道配置详情'"
            type="textarea"
            :rows="4"
            placeholder="请输入第三方AES_KEY"
          />
        </el-form-item>

        <el-form-item label="H5跳转链接" prop="h5Url">
          <el-input
            v-model="formData.h5Url"
            :disabled="title == '接口渠道配置详情'"
            placeholder="请输入H5跳转链接"
          />
        </el-form-item>
        <el-form-item label="我方AES_IV" prop="myAesIv">
          <el-input
            v-model="formData.myAesIv"
            :disabled="title == '接口渠道配置详情'"
            type="textarea"
            :rows="4"
            placeholder="请输入我方AES_IV"
          />
        </el-form-item>
        <el-form-item label="我方AES_KEY" prop="myAesKey">
          <el-input
            v-model="formData.myAesKey"
            :disabled="title == '接口渠道配置详情'"
            type="textarea"
            :rows="4"
            placeholder="请输入我方AES_KEY"
          />
        </el-form-item>
        <el-form-item label="我方落地页渠道号" prop="myChannelId">
          <el-input
            oninput="value=value.replace(/[^0-9]/g,'')"
            :disabled="title == '接口渠道配置详情'"
            v-model.number="formData.myChannelId"
            placeholder="请输入我方落地页渠道号"
          />
        </el-form-item>
        <el-form-item label="其它参数" prop="otherParam">
          <div style="display: flex">
            <el-input
              type="textarea"
              disabled
              :rows="5"
              v-model="formData.otherParam"
              placeholder="请输入其它参数"
            />
            <el-button size="mini" style="height: 30px" @click="handleConfigKey"
              >配置</el-button
            >
          </div>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            :disabled="title == '接口渠道配置详情'"
            placeholder="请输入备注"
          />
        </el-form-item>

        <el-form-item label="白名单IP" prop="whitelistIp">
          <el-input
            type="textarea"
            :rows="3"
            :disabled="title == '接口渠道配置详情'"
            v-model="formData.whitelistIp"
            placeholder="请输入白名单IP"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
        v-if="title != '接口渠道配置详情'"
      >
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="生成随机字符"
      :visible.sync="strAvisible"
      width="400px"
      append-to-body
      center
      @close="cancelCopy"
      :close-on-click-modal="false"
    >
      <el-input
        v-model.number="roundStrs"
        clearable=""
        placeholder="请输入字符"
      />
      <div class="handle-str">
        <el-button type="primary" @click="handleStr">生成</el-button>
        <el-button type="primary" @click="handleCope">复制</el-button>
      </div>
      <div class="handle-str">{{ str }}</div>
    </el-dialog>
    <el-dialog
      title="其他参数"
      :visible.sync="otherAvisible"
      width="1000px"
      append-to-body
      center
      @close="otherAvisible = false"
      :close-on-click-modal="false"
    >
      <el-form label-width="250px">
        <el-form-item
          :class="[otherParamText[item[0]] ? 'item' : '']"
          :label="
            item[0] +
            '\n' +
            (otherParamText[item[0]] ? otherParamText[item[0]] : '')
          "
          v-for="(item, index) in paramData"
          :key="index"
        >
          <div style="display: flex">
            <el-input v-model="paramJson[item[0]]" placeholder="请输入" />
            <el-button
              type="info"
              size="small"
              @click="hanldeDatail(index, item[0])"
              >删除</el-button
            >
            <el-button
              @click="hanldeAddKey"
              type="primary"
              v-if="paramData.length - 1 == index"
              size="small"
              >添加key
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="hanldeAddKey"
          type="primary"
          v-if="!paramData.length"
          size="small"
          >添加key
        </el-button>
        <el-button type="primary" @click="submitJson">确 定</el-button>
        <el-button @click="cancelKey">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="添加key"
      width="900px"
      :visible.sync="keyvisible"
      append-to-body
      center
      :close-on-click-modal="false"
      @close="keyName = ''"
    >
      <el-form label-width="200px">
        <el-form-item label="key名称">
          <el-input v-model="keyName" placeholder="请输入key名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitJsonKey">确 定</el-button>
      </div>
    </el-dialog>
    <!-- H5跳转链接配置弹窗 -->
    <el-dialog
      title="H5跳转链接配置"
      :visible.sync="h5EnvDialogVisible"
      width="800px"
      append-to-body
      center
      :close-on-click-modal="false"
      @close="cancelH5EnvForm"
    >
      <el-button type="primary" @click="addH5EnvItem" size="small" style="margin-bottom: 10px;">添加</el-button>
      <el-table :data="h5EnvFormItems" style="width: 100%" border>
        <el-table-column label="Key" prop="key">
          <template slot-scope="{ row }">
            <el-input v-model.trim="row.key" placeholder="请输入Key" />
          </template>
        </el-table-column>
        <el-table-column label="Value" prop="value">
          <template slot-scope="{ row }">
            <el-input v-model="row.value" placeholder="请输入Value" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="{ row, $index }">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              circle
              @click="removeH5EnvItem($index)"
              title="删除"
            />
            <el-button
              v-if="$index === h5EnvFormItems.length - 1"
              type="success"
              icon="el-icon-plus"
              size="mini"
              circle
              @click="addH5EnvItem"
              title="添加一项"
              style="margin-left: 10px;"
            />
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitH5EnvForm">确 定</el-button>
        <el-button @click="cancelH5EnvForm">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getApiChannelList,
  addApiChannelOne,
  getApiChannelOne,
  editApiChannelOne,
  changeApiChannelOne,
  getChannelList,
  getH5UrlEnv, // 导入获取H5环境配置接口
  updateH5UrlEnv, // 导入更新H5环境配置接口
} from "@/api/xmxrChannelManage/configuration";
import { generateRandomString } from "@/utils/dataGenerator";

export default {
  data() {
    return {
      channelList: [],
      total: 0,
      loading: false,
      isAdd: true,
      domainAvisible: false,
      strAvisible: false,
      otherAvisible: false,
      keyvisible: false,
      h5EnvDialogVisible: false, // H5环境配置弹窗的显示状态
      domianList: [],
      title: "",
      roundStrs: "",
      str: "",
      keyName: "",
      paramData: [],
      paramJson: {},
      types: {
        0: "撞库",
        1: "半流程",
        2: "注册",
      },
      queryParams: {
        source: "",
        channelId: "",
        pageNum: 1,
        pageSize: 10,
        partnerName: "",
        status: "",
      },
      formData: {
        apiAesIv: "",
        apiAesKey: "",
        channelId: "",
        h5Url: "",
        myAesIv: "",
        myAesKey: "",
        myChannelId: "",
        otherParam: "",
        partnerName: "",
        remark: "",
        status: "0",
        typeIds: [],
        whitelistIp: "",
      },
      otherParamText: {},
      h5EnvFormItems: [], // 用于渲染H5环境配置表单项的数组
      rules: {
        channelId: [
          {
            required: true,
            message: "请输入渠道来源",
            trigger: "blur",
          },
        ],
        partnerName: [
          {
            required: true,
            message: "请输入合作方名称",
            trigger: "blur",
          },
        ],
        status: [
          {
            required: true,
            message: "请选择状态",
            trigger: "blur",
          },
        ],
        typeIds: [
          {
            required: true,
            message: "请输入类型",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    handleSelectChannel(channelId) {
      const channel = this.channelList.find((item) => item.id == channelId);
      if (channel) {
        this.formData.otherParam = channel.defaultOtherParam;
        this.formData.myChannelId = channel.id;
        this.otherParamText=channel.otherParamText||{};
      }
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    async fetchChannelList(row) {
      const res = await getChannelList();
      this.channelList = res.data;
      this.otherParamText = {};
      if (row) {
        res.data.forEach((item) => {
          if (row.myChannelId == item.id) {
            this.otherParamText = item.otherParamText || {};
            console.log(this.otherParamText);
          }
        });
      }
    },

    addDomain() {
      this.fetchChannelList();
      this.domainAvisible = true;
      this.isAdd = true;
      this.title = "新增接口渠道配置";
      if (this.formData.id) {
        delete this.formData.id;
      }

      // 新增配置时，自动生成随机字符串
      const str = generateRandomString(16);
      this.formData.myAesIv = str;
      this.formData.myAesKey = str;
    },
    handleConfigKey() {
      this.otherAvisible = true;
      this.paramData = Object.entries(
        JSON.parse(this.formData.otherParam || "{}")
      );
      this.paramJson = JSON.parse(this.formData.otherParam || "{}");
    },

    cancelKey() {
      this.paramData = [];
      this.paramJson = {};
      this.otherAvisible = false;
    },
    hanldeAddKey() {
      this.keyvisible = true;
    },
    submitJson() {
      this.formData.otherParam = Object.keys(this.paramJson).length
        ? JSON.stringify(this.paramJson)
        : "";
      this.otherAvisible = false;
    },
    hanldeDatail(index, key) {
      this.paramData.splice(index, 1);
      delete this.paramJson[key];
    },
    submitJsonKey() {
      if (!this.keyName) return this.$message.error("key不能为空");
      this.paramData.push([this.keyName, ""]);
      this.keyvisible = false;
    },
    //获取列表
    getList() {
      this.loading = true;
      getApiChannelList(this.queryParams).then((res) => {
        this.domianList = res.rows;
        this.total = res.total;
        this.loading = false;
        if (this.domianList.length == 0 && this.queryParams.pageNum > 1) {
          this.queryParams.pageNum = this.queryParams.pageNum - 1;
          this.loading = false;
          this.getList();
          return;
        }
      });
    },
    handleEdit(row) {
      this.fetchChannelList(row);
      this.title = "修改接口渠道配置";
      this.formData.id = row.id;
      this.isAdd = false;
      getApiChannelOne(row.id).then((res) => {
        this.domainAvisible = true;
        for (let key in res.data) {
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = res.data[key];
          }
        }
      });
    },
    handleDetail(row) {
      this.title = "接口渠道配置详情";
      this.domainAvisible = false;
      getApiChannelOne(row.id).then((res) => {
        this.domainAvisible = true;
        for (let key in res.data) {
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = res.data[key];
          }
        }
      });
    },
    cancel() {
      this.formData = {
        apiAesIv: "",
        apiAesKey: "",
        channelId: "",
        h5Url: "",
        myAesIv: "",
        myAesKey: "",
        myChannelId: "",
        otherParam: "",
        partnerName: "",
        remark: "",
        status: "0",
        typeIds: [],
        whitelistIp: "",
      };
      this.title = "";
      this.isAdd = true;
      this.domainAvisible = false;
      this.$refs.formData.resetFields();
    },
    //生成随机字符
    roundStr() {
      this.strAvisible = true;
    },
    handleCope() {
      let oInput = document.createElement("input");
      oInput.value = this.str;
      document.body.appendChild(oInput);
      oInput.select(); // 选择对象;

      document.execCommand("Copy"); // 执行浏览器复制命令
      this.$message({
        message: "复制成功",
        type: "success",
      });
      oInput.remove();
    },
    handleStr() {
      let strarr = [
        "0",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n",
        "o",
        "p",
        "q",
        "r",
        "s",
        "t",
        "u",
        "v",
        "w",
        "x",
        "y",
        "z",
      ];
      this.str = "";

      for (var i = 0; i < this.roundStrs; i++) {
        this.str += strarr[parseInt(Math.random() * strarr.length)];
      }
    },
    cancelCopy() {
      this.str = "";
      this.roundStrs = "";
    },
    changeStatus(e, id) {
      this.$confirm("是否修改投放状态?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          changeApiChannelOne({ status: e, id: id }).then((res) => {
            if (res.code == 200) {
              this.$message.success("状态跟新成功");
              this.getList();
            }
          });
        })
        .catch(() => {
          this.getList();
        });
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            addApiChannelOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("添加成功");
              }
            });
          } else {
            editApiChannelOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.cancel();
                this.getList();
                this.$message.success("修改成功");
              }
            });
          }
        }
      });
    },
    // 打开H5跳转链接环境配置弹窗
    async handleH5EnvConfig() {
      this.h5EnvDialogVisible = true;
      try {
        const res = await getH5UrlEnv();
        if (res.code == 200 && res.data) {
          this.h5EnvFormItems = Object.entries(res.data).map(
            ([key, value]) => ({ key, value })
          );
        } else {
          this.h5EnvFormItems = [];
        }
      } catch (error) {
        this.h5EnvFormItems = [];
        // console.error("获取H5环境配置失败:", error);
      }
    },

    // 添加H5环境配置项
    addH5EnvItem() {
      this.h5EnvFormItems.push({ key: "", value: "" });
    },

    // 删除H5环境配置项
    removeH5EnvItem(index) {
      this.h5EnvFormItems.splice(index, 1);
    },

    // 提交H5环境配置表单
    async submitH5EnvForm() {
      const processedItems = this.h5EnvFormItems.map(item => ({
        key: item.key ? item.key.trim() : "",
        value: item.value
      }));

      // 检查是否有空Key或空Value
      if (processedItems.some(item => !item.key || !item.value)) {
        this.$message.error("Key和Value均不能为空");
        return;
      }

      // 检查是否有重复的Key
      const keys = processedItems.map(item => item.key);
      const uniqueKeys = new Set(keys);
      if (keys.length !== uniqueKeys.size) {
        this.$message.error("Key值不能重复");
        return;
      }

      const params = processedItems.reduce((acc, item) => {
        acc[item.key] = item.value;
        return acc;
      }, {});

      try {
        const res = await updateH5UrlEnv(params);
        if (res.code == 200) {
          this.$message.success("配置成功");
          this.h5EnvDialogVisible = false;
        }
        // 其它code情况由全局拦截器处理
      } catch (error) {
        // console.error("更新H5环境配置失败:", error); // 错误也由全局拦截器处理
      }
    },

    // 取消H5环境配置表单
    cancelH5EnvForm() {
      this.h5EnvDialogVisible = false;
      this.h5EnvFormItems = [];
    },
  },

  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.handle-str {
  display: flex;
  justify-content: center;

  margin-top: 10px;
}
.item {
  ::v-deep .el-form-item__label {
    white-space: pre-wrap;
    font-size: 14px;
    line-height: 20px;
  }
}
</style>
