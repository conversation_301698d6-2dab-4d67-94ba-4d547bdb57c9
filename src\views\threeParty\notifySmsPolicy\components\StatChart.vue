<template>
  <div class="chart-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <div v-for="(channelData, channelId) in statisticsData" :key="channelId">
          <div class="channel-header">
            <h3>{{ getChannelName(channelId) }}</h3>
            <div class="total-info">总发送数: <span class="total-count">{{ getChannelTotal(channelData) }}</span></div>
          </div>
          
          <el-table
            :data="channelData"
            style="width: 100%; margin-bottom: 20px"
            border
            size="small"
          >
            <el-table-column prop="smsKey" label="短信模板Key" min-width="180" />
            <el-table-column prop="smsName" label="短信名称" min-width="180" />
            <el-table-column prop="total" label="发送数量" width="120" />
            <el-table-column prop="ratio" label="占比" width="160">
              <template slot-scope="scope">
                {{ parseFloat(scope.row.ratio).toFixed(2) + '%' }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getAllChannelList } from '@/api/channeManage/channelList'

export default {
  name: 'StatChart',
  props: {
    statisticsData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      channelOptions: []
    }
  },
  created() {
    this.getChannelOptions()
  },
  methods: {
    getChannelOptions() {
      getAllChannelList().then(response => {
        this.channelOptions = response.data || []
      })
    },
    getChannelName(channelId) {
      const channel = this.channelOptions.find(item => item.id == channelId)
      return channel ? channel.channelName : `渠道ID: ${channelId}`
    },
    getChannelTotal(channelData) {
      return channelData.reduce((sum, item) => sum + Number(item.total || 0), 0)
    }
  }
}
</script>

<style scoped>
.chart-container {
  margin-top: 20px;
}
.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.channel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}
.total-info {
  font-size: 14px;
}
.total-count {
  font-weight: bold;
  color: #409EFF;
}
</style> 