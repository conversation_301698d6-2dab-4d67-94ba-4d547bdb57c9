<template>
  <div class="config-info-header">
    <div class="config-info-main">
      <div class="config-title-row">
        <h2 class="config-title">{{ config.title }}</h2>
        <div class="config-meta">
          <el-tag size="mini" type="info">{{ configKey }}</el-tag>
          <span class="platform-count">{{ config.list.length }} 个平台</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConfigHeader',
  props: {
    config: {
      type: Object,
      required: true,
      default: () => ({
        title: '',
        list: []
      })
    },
    configKey: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.config-info-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8eaed;
  background: #f8f9fa;
  flex-shrink: 0;

  .config-info-main {
    .config-title-row {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .config-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #202124;
        line-height: 1.3;
        flex: 1;
      }

      .config-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;

        .el-tag {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 11px;
          background: #f1f3f4;
          border: 1px solid #e8eaed;
          color: #5f6368;
        }

        .platform-count {
          font-size: 12px;
          color: #9aa0a6;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>