<template>
  <div class="nav-bar">
    <div class="nav-bar-left">
      <img class="icon-back" src="https://jst.oss-utos.hmctec.cn/common/path/99b71437ca39491281422195af3335f7.png"
        alt="left">
    </div>
    <div class="nav-bar-title">
      芝麻信用
    </div>

    <div class="nav-bar-right">
      <img class="icon-month" src="@/assets/images/month.png" alt="right">
      <div class="more">
        <div class="more-dot"></div>
        <div class="more-dot"></div>
        <div class="more-dot"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IOSNavBar'
}
</script>

<style scoped lang="scss">
.nav-bar {
  position: relative;
  z-index: 1;
  margin: 0 auto;
  padding: 0 30px;
  width: 100%;
  height: 49px;
  display: flex;
  align-items: center;
}

.nav-bar-left,
.nav-bar-right {
  flex: 1;
}

.nav-bar-left {
  display: flex;
  align-items: center;
}

.nav-bar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 42px;
}

.nav-bar-title {
  font-weight: 500;
  font-size: 34px;
  color: #FFFFFF;
  line-height: 49px;
  font-family: 'SourceHanSansSC-Regular';
}

.icon-back {
  width: 21.15px;
  height: 34px;
}

.icon-month {
  width: 35.5px;
  height: 40px;
}


.more {
  display: flex;
  align-items: center;
  gap: 8px;

  .more-dot {
    width: 8px;
    height: 8px;
    background-color: #FFFFFF;
    border-radius: 50%;
  }
}
</style>