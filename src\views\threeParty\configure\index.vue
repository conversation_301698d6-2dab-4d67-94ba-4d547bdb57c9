<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item label="短信模板名称">
        <el-input size="mini" v-model="queryParams.name" clearable></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >添加</el-button
        >
      </el-form-item>
    </el-form>

    <el-table border :data="list">
      <el-table-column label="短信模板名称" prop="name" align="center" />
      <el-table-column label="接口请求账号" prop="userName" align="center" />
      <el-table-column label="接口请求密码" prop="passWord" align="center" />
      <el-table-column label="接口地址" prop="url" align="center" />
      <el-table-column label="使用类型" prop="useType" align="center" />
      <el-table-column label="状态" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.status == 1 ? "有效" : "无效" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" align="center" />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="handEdit(row)"
              >修改信息</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="isadd ? '新增短信配置' : '修改短信配置'"
      :visible.sync="keyAvisible"
      width="700px"
      append-to-body
      center
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="150px"
      >
        <el-form-item label="短信模板名称" prop="name">
          <el-input
            v-model.trim="formData.name"
            placeholder="请输入短信模板名称"
          />
        </el-form-item>
        <el-form-item label="接口请求账号" prop="userName">
          <el-input
            v-model.trim="formData.userName"
            placeholder="请输入接口请求账号"
          />
        </el-form-item>
        <el-form-item label="接口请求密码" prop="passWord">
          <el-input
            v-model.trim="formData.passWord"
            placeholder="请输入接口请求密码"
          />
        </el-form-item>
        <el-form-item label="接口地址" prop="url">
          <el-input v-model.trim="formData.url" placeholder="请输入接口地址" />
        </el-form-item>
        <el-form-item label="使用类型" prop="useType">
          <el-input v-model.trim="formData.useType" placeholder="请输入使用类型" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio v-model="formData.status" :label="1">有效</el-radio>
          <el-radio v-model="formData.status" :label="2">无效</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSmsConfigurationTemplate,
  addSmsConfigurationOne,
  editSmsConfigurationOne,
} from "@/api/threeParty/note";
export default {
  data() {
    return {
      list: [],
      isadd: true,
      formData: {
        name: "",
        passWord: "",
        url: "",
        userName: "",
        useType: "",
        status: 1,
      },
      keyAvisible: false,
      queryParams: {
        name: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入短信模板名称", trigger: "blur" },
        ],
        userName: [
          { required: true, message: "请输入接口请求账号", trigger: "blur" },
        ],
        passWord: [
          { required: true, message: "请输入接口请求密码", trigger: "blur" },
        ],
        url: [{ required: true, message: "请输入接口地址", trigger: "blur" }],
        status: [{ required: true, message: "请选择状态", trigger: "blur" }],
      },
    };
  },
  methods: {
    handleQuery() {
      this.getList();
    },
    getList() {
      getSmsConfigurationTemplate(this.queryParams).then((res) => {
        this.list = res.data;
      });
    },
    handEdit(row) {
      this.keyAvisible = true;
      this.isadd = false;
      this.formData.status = row.status;
      this.formData.name = row.name;
      this.formData.passWord = row.passWord;
      this.formData.url = row.url;
      this.formData.userName = row.userName;
      this.formData.useType = row.useType;
      this.formData.id = row.id;
    },
    handleAdd() {
      this.keyAvisible = true;
      this.isadd = true;
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isadd) {
            addSmsConfigurationOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.getList();
                this.cancel();
              }
            });
          } else {
            editSmsConfigurationOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.getList();
                this.cancel();
              }
            });
          }
        }
      });
    },
    cancel() {
      this.keyAvisible = false;
      this.formData = {
        name: "",
        passWord: "",
        url: "",
        userName: "",
        useType: "",
        status: 1,
      };
      this.$refs.formData.resetFields();
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
