import request from '@/utils/request'
//获取文章列表
export function getEncyclopediaList(data) {
  return request({
    url: '/loan/encyclopedia/getlist',
    method: "get",
    params: data
  })
}
//新增文章
export function addEncyclopediaOne(data) {
  let formData = new FormData()
  for (var k in data) {
    formData.append(k, data[k])
  }
  if (data.temFiles) {
    data.temFiles.forEach((item) => {
      formData.append("files", item);
    });
  }
  return request({
    url: "/loan/encyclopedia/addone",
    method: "post",
    headers: { 'Content-Type': 'multipart/form-data' },
    data: formData
  })
}
//查询详情
export function getEncyclopediaOne(data) {
  return request({
    url: "/loan/encyclopedia/getoneinfo",
    method: "get",
    params: data
  })
}

//编辑
export function editEncyclopediaOne(data) {
  let formData = new FormData()
  for (var k in data) {
    formData.append(k, data[k])
  }
  if (data.temFiles) {
    data.temFiles.forEach((item) => {
      formData.append("files", item);
    });
  }
  return request({
    url: "/loan/encyclopedia/updateone",
    method: "post",
    headers: { 'Content-Type': 'multipart/form-data' },
    data: formData
  })
}

//删除
export function delEncyclopediaOne(data) {
  return request({
    url: "/loan/encyclopedia/deleteone",
    method: "post",
    data
  })
}
//上传文件
export function uploadFile(data) {
  return request({
    url: "/loan/encyclopedia/uploadFile",
    method: "post",
    data
  })
}

//删除文件
export function delLoadFile(data) {
  return request({
    url: "/loan/encyclopedia/deleteosslist",
    method: "post",
    data
  })
}


//上架下架
export function toggleEncyclopediaOne(data) {
  return request({
    url: '/loan/encyclopedia/goonline',
    method: "post",
    data
  })
}
