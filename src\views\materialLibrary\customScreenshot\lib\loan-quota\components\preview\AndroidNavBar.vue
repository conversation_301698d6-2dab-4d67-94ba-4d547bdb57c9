<template>
  <div class="nav-bar">
    <div class="nav-bar-left">
      <img class="icon-back" src="https://jst.oss-utos.hmctec.cn/common/path/bb8c5f9bfed1421d91196f4f744cd530.png" alt="left">
      <span class="nav-bar-title">总计额度</span>
    </div>
    <div class="nav-bar-right">
      <img class="icon-customer-service" src="https://jst.oss-utos.hmctec.cn/common/path/bb563f87308943739d61aa966a7cf7ad.png" alt="customer-service">
      <div class="more">
        <div class="more-dot"></div>
        <div class="more-dot"></div>
        <div class="more-dot"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AndroidNavBar'
}
</script>

<style scoped lang="scss">
.nav-bar {
  position: relative;
  z-index: 1;
  margin: 0 auto;
  padding: 0 30px;
  width: 100%;
  height: 49px;
  display: flex;
  align-items: center;
}

.nav-bar-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.nav-bar-title {
  font-weight: 500;
  font-size: 34px;
  color: #3D3D3D;
  line-height: 49px;
  font-family: 'SourceHanSansSC-Regular';
  margin-left: 28px;
}

.nav-bar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 42px;
  flex: 1;
}

.icon-back {
  width: 21.15px;
  height: 34px;
}

.icon-customer-service {
  width: 42px;
  height: 39px;
}

.more {
  display: flex;
  align-items: center;
  gap: 8px;

  .more-dot {
    width: 8px;
    height: 8px;
    background-color: #3D3D3D;
    border-radius: 50%;
  }
}
</style> 