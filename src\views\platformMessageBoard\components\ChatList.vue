<template>
  <div class="chat-container" ref="chatContainer" @scroll="handleChatScroll">
    <div v-if="chatList.length === 0" class="empty-chat">
      <i class="el-icon-chat-line-round" style="font-size: 48px; color: #ddd;"></i>
      <p>暂无留言记录</p>
    </div>
    <div v-else class="chat-list">
      <!-- 加载更多指示器 -->
      <div v-if="isLoadingMore" class="loading-more-indicator">
        <i class="el-icon-loading"></i>
        <span>正在加载历史消息...</span>
      </div>
      <!-- 没有更多消息提示 -->
      <div v-else-if="!hasMore && chatTotal > pageSize" class="no-more-indicator">
        <span>没有更多历史消息了</span>
      </div>

      <ChatBubble
        v-for="chat in chatList"
        :key="chat.id"
        :chat-item="chat"
        :avatar-size="avatarSize"
      />
    </div>
  </div>
</template>

<script>
import ChatBubble from './ChatBubble.vue'

export default {
  name: 'ChatList',
  components: {
    ChatBubble
  },
  props: {
    chatList: {
      type: Array,
      default: () => []
    },
    chatTotal: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 100
    },
    isLoadingMore: {
      type: Boolean,
      default: false
    },
    hasMore: {
      type: Boolean,
      default: false
    },
    avatarSize: {
      type: Number,
      default: 24
    },
    scrollThreshold: {
      type: Number,
      default: 50
    }
  },
  methods: {
    /** 处理聊天容器滚动事件 */
    handleChatScroll(event) {
      const container = event.target
      const scrollTop = container.scrollTop

      // 当滚动到顶部附近且还有更多历史消息时，触发加载更多
      if (scrollTop <= this.scrollThreshold &&
          this.hasMore &&
          !this.isLoadingMore) {
        this.$emit('scroll-to-top')
      }
    },
    /** 滚动到聊天容器底部 */
    scrollToBottom() {
      const chatContainer = this.$refs.chatContainer
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight
      }
    },
    /** 保持滚动位置 */
    maintainScrollPosition(callback) {
      const container = this.$refs.chatContainer
      if (!container) return

      const currentScrollTop = container.scrollTop
      const currentScrollHeight = container.scrollHeight

      callback()

      this.$nextTick(() => {
        if (container) {
          // 计算新增内容的高度差，调整滚动位置
          const newScrollHeight = container.scrollHeight
          const heightDiff = newScrollHeight - currentScrollHeight
          container.scrollTop = currentScrollTop + heightDiff
        }
      })
    }
  }
}
</script>

<style scoped>
.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px 20px;
  background-color: #F5F5F5;
}

.empty-chat {
  text-align: center;
  color: #999;
  padding: 60px 20px;
}

.empty-chat p {
  margin: 10px 0 0 0;
  font-size: 14px;
}

.chat-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 滚动条样式 */
.chat-container::-webkit-scrollbar {
  width: 6px;
}

.chat-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载更多指示器样式 */
.loading-more-indicator {
  text-align: center;
  padding: 16px 20px;
  color: #909399;
  font-size: 13px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.loading-more-indicator i {
  margin-right: 8px;
  animation: rotating 1s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.no-more-indicator {
  text-align: center;
  padding: 12px 20px;
  color: #c0c4cc;
  font-size: 12px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fafafa;
}
</style>