import request from '@/utils/request';

/**
 * 统计用户资质变更情况
 * @param {object} queryParams - 查询参数
 * @param {string} queryParams.startTime - 开始时间 yyyy-MM-dd HH:mm:ss
 * @param {string} queryParams.endTime - 结束时间 yyyy-MM-dd HH:mm:ss
 * @param {number} queryParams.type - 统计类型 0: 无逾期 1: 有逾期 2: 芝麻分650+ 3: 芝麻分650- 4: 年龄 <20 5: 年龄 22-45 6: 年龄 50+
 * @returns {Promise<object>} - 包含统计数据的Promise对象
 */
export function getCertificationRedressStats(queryParams) {
  return request({
    url: '/stats/certificationRedress',
    method: 'post',
    data: queryParams
  });
} 