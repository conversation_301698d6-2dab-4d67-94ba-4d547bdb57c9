<template>
  <el-drawer title="商户资料" :visible.sync="drawer_" size="800px" :wrapper-closable="false"  :direction="direction" @close="hanldeClose">
    <div class="drawer-container">
      <div class="drawer-tabs">
        <div class="drawer-tabs-item flex align-items-c">
          <i class="iconfont icon-a-paichu3 tab-active" v-if="tabIndex > 1"></i>
          <span class="drawer-tabs-cicle" v-else>1</span>

          <span :class="[tabIndex == 1 ? 'tab-active' : 'tab-finish',]">账户认证</span>
        </div>
        <div class="drawer-tabs-line"></div>
        <div class="drawer-tabs-item flex align-items-c">
          <i :class="['iconfont icon-a-paichu3 tab-active']" v-if="tabIndex > 2"></i>
          <span :class="['drawer-tabs-cicle', tabIndex == 2 ? '' : 'tab-todo']" v-else>2</span>
          <span :class="[tabIndex == 2 ? 'tab-active' : '', tabIndex > 2 ? 'tab-finish' : '']">联系人信息</span>

        </div>
        <div :class="[tabIndex == 3 ? 'drawer-tabs-line' : 'tab-line']"></div>
        <div class="drawer-tabs-item flex align-items-c"> <span
            :class="['drawer-tabs-cicle', tabIndex == 3 ? '' : 'tab-todo']">3</span>
          <span :class="[tabIndex == 3 ? 'tab-active' : '',]">资质信息</span>
        </div>

      </div>
      <el-form label-position="top" :model="formData" ref="formData" :rules="rules" :key="tabIndex">
        <template v-if="tabIndex == 1">
          <el-row :gutter="24">
            <el-col :span="12"> <el-form-item label="合同编号" prop="">
                <el-input maxlength="50" :disabled="status == 1" size="mini" v-model="formData.contractNo"
                  placeholder="请输入合同编号" />
              </el-form-item></el-col>
            <el-col :span="12"> <el-form-item label="认证类型" prop="authType">
                <el-select :disabled="status == 1" v-model="formData.authType" clearable size="mini" style="width: 100%"
                  placeholder="请选择认证类型">
                  <el-option label="公司" :value="1"></el-option>
                  <el-option label="个人" :value="2"></el-option>
                </el-select>
              </el-form-item></el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12"> <el-form-item label="公司全称：">
                <el-input maxlength="40" :disabled="status == 1" size="mini" v-model="formData.companyName"
                  placeholder="请输入公司全称" />
              </el-form-item></el-col>
            <el-col :span="12"> <el-form-item label="公司统一社会信用码:">
                <el-input maxlength="30" :disabled="status == 1" size="mini" v-model="formData.companyCreditCode"
                  placeholder="请输入公司统一社会信用码" />
              </el-form-item></el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24"> <el-form-item label="备注" prop="remark">
                <el-input :disabled="status == 1" type="textarea" v-model="formData.remark" placeholder="请输入备注" />
              </el-form-item></el-col>
            <!-- <el-col :span="12"></el-col> -->
          </el-row>
          <div class="drawer-title" v-if="processList && processList.length">审批流程</div>
          <div class="drawer-process" v-if="processList && processList.length">
            <div v-for="(item, index) in processList" :key="index">
              <div class="flex align-items-c">
                <span
                  :class="['iconfont f-suceess drawer-process-icon', iconList[item.status], colorList[item.status]]"></span>
                <span class="drawer-process-user">{{ item.name }} </span>
                <span :class="['drawer-process-status', tagList[item.status]]"
                  v-if="item.status != 0 && item.status != -2">
                  <span
                    v-if="processList[1] && processList[1].status == 2 && processList[0] && processList[0].status == -1&&index==0">
                    待修改
                  </span>
                  <span v-else>{{
                    statusJson[item.status] || "" }}</span>
                </span>
              </div>
              <div :class="['drawer-process-line', processList.length - 1 == index ? 'boder-none' : '']">
                <div class="drawer-process-time">{{ item.checkTime || "-" }}</div>
                <div :class="['drawer-process-remark', item.status == 2 ? 'fail' : '']"
                  v-if="item.checkRemark && item.status != -2">{{
                    item.checkRemark || "-" }}</div>
              </div>
            </div>
          </div>
        </template>
        <template v-if="tabIndex == 2">
          <el-row :gutter="24">
            <el-col :span="12"> <el-form-item label="联系人：" prop="contactsName">
                <el-input :disabled="status == 1" size="mini" maxlength="6" placeholder="请输入联系人"
                  v-model="formData.contactsName" />
              </el-form-item></el-col>
            <el-col :span="12"> <el-form-item label="联系人电话:" prop="contactsPhone">
                <el-input :disabled="status == 1" size="mini" maxlength="11" v-model.trim="formData.contactsPhone"
                  placeholder="请输入联系人电话 ：" />
              </el-form-item></el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12"> <el-form-item label="联系人邮箱:" prop="contactsEmail">
                <el-input :disabled="status == 1" size="mini" maxlength="50" v-model.trim="formData.contactsEmail"
                  placeholder="请输入联系人邮箱" />
              </el-form-item></el-col>
            <el-col :span="12"> <el-form-item label="联系人地址:" prop="contactsAddress">
                <el-input maxlength="50" :disabled="status == 1" size="mini" v-model="formData.contactsAddress"
                  placeholder="请输入联系人地址" />
              </el-form-item></el-col>
          </el-row>
        </template>
        <template v-if="tabIndex == 3">
          <el-row :gutter="24">
            <el-col :span="24"> <el-form-item label="营业执照" prop="businessLicenseFilename">

                <el-upload class="avatar-uploader" ref="businessLicenseFilename" :show-file-list="false"
                  :on-change="(e) => changeUpfile(e, { type: 1, name: 'businessLicenseFilename', path: 'businessLicenseFilepath' })"
                  action="#" :auto-upload="false">
                  <img v-if="imageUrl" style="vertical-align: middle; max-height: 100px" :src="imageUrl" />
                  <div class="avatar-uploader-tips" v-else>
                    <i class="el-icon-plus"></i>
                    <span>点击上传</span>
                  </div>
                  <!-- <img v-if="imageUrl" :src="imageUrl" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i> -->
                </el-upload>
                <!-- <el-upload class="avatar-uploader" ref="businessLicenseFilename" :show-file-list="false"
                  :on-change="(e) => changeUpfile(e, { type: 1, name: 'businessLicenseFilename', path: 'businessLicenseFilepath' })"
                  action="#" :auto-upload="false">

                  <img v-if="imageUrl" :src="imageUrl" class="avatar">
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload> -->
              </el-form-item></el-col>
          </el-row>
          <!-- <el-row :gutter="24">
            <el-col :span="24"> <el-form-item label="合同文件" prop="contractFilename">
                <el-upload class="upload-demo" action="#" :auto-upload="false" :show-file-list="false"
                  :on-change="(e) => changeUpfile(e, { type: 3, name: 'contractFilename', path: 'contractFilepath' })">
                  <el-button size="mini" icon="el-icon-upload2">点击上传</el-button>
                </el-upload>
              </el-form-item>
              <el-tag type="info" size="small" v-if="formData.contractFilename"
                @close="handleRemoveFile({ name: 'contractFilename', path: 'contractFilepath' })" closable>
                <span class="tag">{{ formData.contractFilename }}</span>
              </el-tag></el-col>

          </el-row>
          <el-row :gutter="24">
            <el-col :span="24"> <el-form-item label="其他文件">
                <el-upload class="upload-demo" action="#" :auto-upload="false" :limit="10" :fileList="fileList"
                  :on-change="(e, fileList) => handleOtherFile(e, fileList)"
                  :on-remove="(e, fileList) => handleOtherRemove(e, fileList)">
                  <el-button size="mini" icon="el-icon-upload2">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">单个文件不能超过2MB；软著、放款资质、ICP备案或其他资料请上传至补充资料</div>
                </el-upload>
              </el-form-item></el-col>
          </el-row> -->
        </template>





        <div>

          <!-- 流程 -->
          <!-- <div v-if="processList.length">
            <div class="check-title">审核信息</div>
            <div class="check-info">
              <el-timeline>
                <el-timeline-item v-for="(activity, index) in processList" :key="index" :color="processColor(activity)">
                  <div :class="[activity.status == 2 ? 'c_red' : '']">
                    <div> {{ activity.name }}</div>
                    <div>时间:{{ activity.checkTime }}</div>
                    <div v-if="activity.status != -2">状态：{{ statusJson[activity.status] }}</div>
                    <div v-if="activity.status != -2"> 备注：{{ activity.checkRemark || "-" }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div> -->
        </div>
      </el-form>

    </div>
    <div class="drawer__footer flex align-items-c">
      <!-- <el-button type="primary" size="small" @click="handleSubmit"> 确定</el-button> -->
      <el-button size="small" @click="handlePreStep" v-if="tabIndex > 1">上一步</el-button>
      <el-button size="small" type="primary" @click="handleNextStep" v-if="tabIndex < 3">下一步</el-button>
      <el-button size="small" v-if="status == 0 && tabIndex == 3" type="primary" @click="submitform">提交</el-button>
      <el-button type="primary" v-if="(status == 2 || status == 3||status==4||status==5) && tabIndex == 3" class="submit"
        v-hasPermi="['partyaAdmin:contract:update']" @click="submitupDataform">修改</el-button>
    </div>

  </el-drawer>
</template>

<script>
import {
  contractUploadFile,
  addOnLineContract,
  updateOnLineContract,
} from "@/api/partyManage";
import { getContractInfo } from "@/api/partyA";
export default {
  data() {
    var validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("手机号不能为空"));
      } else if (!/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)) {
        callback(new Error("手机号格式错误"));
      } else {
        callback();
      }
    };
    var validateEamil = (rule, value, callback) => {
      if (!value) {
        callback(new Error("邮箱不能为空"));
      } else if (
        !/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(
          value
        )
      ) {
        callback(new Error("邮箱格式错误"));
      } else {
        callback();
      }
    };
    var validateName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("名字不能为空"));
      } else if (!/^[\u4e00-\u9fa5]{2,6}$/.test(value)) {
        callback(new Error("名字格式错误"));
      } else {
        callback();
      }
    };


    var validateID = (rule, value, callback) => {
      if (!value) {
        callback(new Error("授权代表身份证号不能为空"));
      } else if (
        !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/.test(
          value
        ) ||
        !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/.test(
          value
        )
      ) {
        callback(new Error("格式错误"));
      } else {
        callback();
      }
    };
    return {
      imageUrl: "",
      tabIndex: 1,
      cityList: [],
      provinceList: [],
      areaList: [],
      countrysList: [],
      fileList: [],
      processList: [],
      contractCheckRemark: "",
      show: false,
      statusJson: {
        0: "发起",
        1: "已通过",
        2: "已驳回",
        3: "结束",
        "-1": '待审核'
      },
      iconList: {
        0: 'icon-a-chaji6',
        1: 'icon-a-paichu3',
        2: 'icon-a-paichu2',
        3: 'icon-a-paichu3',
        "-1": 'icon-a-paichu1',
        "-2": 'icon-a-paichu3',
      },

      colorList: {
        0: 'f-suceess',
        1: 'f-suceess',
        2: 'f-danger',
        3: 'f-suceess',
        "-1": 'f-info',
      },
      tagList: {
        0: 'success',
        1: 'success',
        2: 'danger',
        3: 'success',
        "-1": 'info',
      },
      statusType: {
        // @ApiModelProperty(value = "合同审核状态(0未上传/1待审核/2审核成功/3审核失败)")
        0: "未上传",
        1: "待审核",
        2: "审核成功",
        3: "审核失败",
      },

      status: 0,
      formData: {
        authType: null,
        partyFirstId: null,
        companyName: null,
        companyCreditCode: null,
        contactsName: null,
        contactsPhone: null,
        contactsEmail: null,
        contactsAddress: null,
        businessLicenseFilename: null,
        businessLicenseFilepath: null,
        address: null,
        contractNo: null,
        remark: null,
        // contractFilename: null,
        // contractFilepath: null,
        // otherInfoFiles: []
      },
      rules: {
        authType: [
          { required: true, message: "请选择认证类型", trigger: "change" },
        ],
        contractFilename: [
          { required: true, message: "请选择上传", trigger: "change" },
        ],
        businessLicenseFilename: [
          { required: true, message: "请选择上传", trigger: "change" },
        ],
        companyName: [
          { required: true, message: "请输入公司全称", trigger: "blur" },
        ],
        companyCreditCode: [
          {
            required: true,
            message: "请输入公司统一社会信用码",
            trigger: "blur",
          },
        ],
        contactsName: [
          { required: true, validator: validateName, trigger: "blur" },
        ],
        contactsPhone: [
          { required: true, validator: validatePhone, trigger: "blur" },
        ],
        contactsEmail: [
          { required: true, validator: validateEamil, trigger: "blur" },
        ],
        contactsAddress: [
          { required: true, message: "请输入联系人地址", trigger: "blur" },
        ],

        address: [{ required: true, message: "请输入地址", trigger: "blur" }],
      },
    };
  },

  methods: {
    handlePreStep() {
      this.tabIndex -= 1
    },
    handleNextStep() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.tabIndex += 1
        }
      })

    },
    hanldeClose() {
      this.tabIndex = 1
      this.$emit('update:drawer', false);
    },
    changeUpfile(e, dataInfo) {
      if (dataInfo.type == 1) {
        const isJPG =
          e.raw.type === "image/jpg" ||
          e.raw.type === "image/jpeg" ||
          e.raw.type === "image/png";
        const isLt2M = e.size / 1024 / 1024 < 10;
        if (!isJPG) {

          this.$message.error("上传图片只能是 JPG/PNG/jpeg 格式!");
          return;
        }
        if (!isLt2M) {

          this.$message.error("上传图片大小不能超过 10MB!");
          return;
        }
      }
      if (dataInfo.type == 3) {
        const isLt2M = e.size / 1024 / 1024 < 10;
        if (!isLt2M) {
          this.$message.error("上传文件大小不能超过 10MB!");
          return
        }
      }
      if (dataInfo.type == 2) {
        if (!e.raw.type.includes("video")) {

          this.$message.error("请上传视频");
          return;
        }
        const isLt2M = e.size / 1024 / 1024 < 20;
        if (!isLt2M) {

          this.$message.error("上传文件大小不能超过 20MB!");
          return;
        }
      }

      this.$refs.formData.clearValidate(dataInfo.name)
      let data = new FormData();
      data.append("fileNum", dataInfo.type);
      data.append("file", e.raw);
      // if (dataInfo.name == "businessLicenseFilename") {
      //   this.imageUrl = URL.createObjectURL(e.raw)
      // }
      contractUploadFile(data, this.partyFirstId).then((res) => {
        this.formData[dataInfo.name] = res.filename
        this.formData[dataInfo.path] = res.filepath
        if (dataInfo.name == "businessLicenseFilename") {
          this.imageUrl = res.url || URL.createObjectURL(e.raw)

        }
        if (dataInfo.name == "frontIdCardFilename") {
          this.imageUrl1 = URL.createObjectURL(e.raw)
        }
        if (dataInfo.name == "reverseIdCardFilename") {
          this.imageUrl2 = URL.createObjectURL(e.raw)
        }
      });
    },
    //删除文件
    handleRemoveFile(e) {
      this.formData[e.name] = ""
      this.formData[e.path] = ""
    },
    //上传其他文件
    handleOtherFile(e, fileList) {
      const isLt2M = e.size / 1024 / 1024 < 10;
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 10MB!");
        return
      }
      let data = new FormData();
      data.append("fileNum", 3);
      data.append("file", e.raw);
      contractUploadFile(data, this.partyFirstId).then(res => {
        this.fileList.push({ name: res.filename, url: res.filepath })
        this.formData.otherInfoFiles = this.fileList.map(item => {
          return {
            filename: item.name,
            filepath: item.url
          }
        })
      })
    },
    //删除其他文件
    handleOtherRemove(e, fileList) {
      this.formData.otherInfoFiles = fileList.map(item => {
        return {
          filename: item.name,
          filepath: item.url
        }
      })
      this.fileList = this.formData.otherInfoFiles || []

    },

    submitform() {
      this.$refs.formData.validate((valid) => {

        if (valid) {
          this.formData.partyFirstId = this.partyFirstId;
          addOnLineContract(this.formData).then((res) => {
            if (res.code == 200) {
              this.getInfo();
              // this.$parent.$parent.contactLineAvisible = false;
              // this.$parent.$parent.getList();
              this.$emit('getList')
              this.drawer_ = false
              this.$message.success("上传成功");
            }
          });
        }
      });
    },


    submitupDataform() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.partyFirstId = this.partyFirstId;
          updateOnLineContract(this.formData).then((res) => {
            this.$message.success("修改成功");
            this.drawer_ = false
            this.$emit('getList')
            this.getInfo();
          });
        }
      });
    },
    getInfo() {
      getContractInfo(this.partyFirstId).then((res) => {
        const data = res.data;
        this.show = true;
        this.status = res.data.contractCheckStatus;
        this.contractCheckRemark = res.data.contractCheckRemark;
        this.imageUrl = res.data.businessLicenseFilenameUrl
        this.processList = res.data.processList
        // let list = res.data.fileOrderList || []
        // this.fileList = list.map(item => {
        //   return {
        //     name: item.filename,
        //     url: item.filepath
        //   }
        // })
        // this.formData.otherInfoFiles = list.map(item => {
        //   return {
        //     filename: item.filename,
        //     filepath: item.filepath
        //   }
        // })
        // 还原数据
        for (let key in data) {
          // 接口请求出来的
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = data[key];
          }
        }
      });
    },
  },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    direction: {
      type: String,
      default: 'rtl'
    },
    partybId: {
      type: [String, Number],
      default: ''
    },
    row: {
      type: Object,
      default: () => { }
    },
    partyFirstId: {
      type: [Number, String],
      default: ''
    },
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer
      },
      set(v) {
        this.$emit("update:drawer", false)
      }
    },
  },
  watch: {
    drawer(val) {
      if (val) {
        this.getInfo();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #EEEEEE;
  background: #DCDCDC;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-tips {
  color: #8c939d;
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  font-size: 12px;
  justify-content: center;
  align-items: center;

  i {
    font-size: 24px;
  }

}

.avatar {
  height: 100px;
  width: 100px;
}

::v-deep .el-upload__tip {
  color: rgba(0, 0, 0, 0.4);
  margin-top: -1px;
}


.drawer-container {
  height: calc(100vh - 100px);
  overflow: auto;
  padding: 20px;
}

.drawer-tabs {
  width: 702px;
  height: 76px;
  background: #F5F7FA;
  border-radius: 4px 4px 4px 4px;
  margin: 0 auto;
  display: flex;
  padding: 0px 20px;
  justify-content: space-between;
  align-items: center;
  color: rgba(0, 0, 0, 0.4);
  font-size: 16px;
  margin-bottom: 10px;

  &-cicle {
    width: 20px;
    height: 20px;
    background: #E37318;
    display: block;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    margin-right: 5px;
    font-size: 12px;
  }

  &-line {
    width: 160px;
    height: 4px;
    background: #E37318;
  }

  .tab-line {
    width: 160px;
    height: 4px;
    background: #DCDCDC;
  }

  .tab-active {
    color: #E37318;
  }

  .tab-finish {
    color: rgba(0, 0, 0, 0.9);
  }

  .tab-todo {
    border: 1px solid rgba(0, 0, 0, 0.4);
    color: rgba(0, 0, 0, 0.4);
    background: transparent;
  }
}

.drawer-title {
  font-size: 18px;
  font-weight: 400;
  color: #3D3D3D;
  position: relative;
  padding-left: 20px;
  margin-bottom: 20px;

  &::after {
    content: "";
    display: block;
    position: absolute;
    width: 4px;
    height: 20px;
    background: #e37318;
    top: 4px;
    left: 0;
  }
}

.drawer-process {
  padding: 20px;

  &-icon {
    margin-right: 10px;
  }

  &-user {
    font-size: 16px;
    color: #181716;
    width: 250px;
  }

  &-status {
    margin-left: 100px;
    font-size: 12px;
    padding: 2px 4px;
    background: #E5F9E9;
    border-radius: 2px;

    &.success {
      background: #E5F9E9;
      color: #3FA372;
      border: 1px solid #3FA372;
    }

    &.danger {
      color: #FF0000;
      background: #FFECEC;
      border: 1px solid #f00;
    }

    &.info {
      color: #FF8F1F;
      background: #FFE8D1;
      border: 1px solid #FF8F1F;
    }
  }

  &-line {
    margin: 3px 0px 5px 8px;
    border-left: 1px dashed #D8D8D8;
    padding-left: 20px;

    padding-bottom: 10px;

  }

  &-time {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 10px;
  }

  &-remark {
    width: 100%;
    background: #FAFAFA;
    border-radius: 4px 4px 4px 4px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    padding: 20px;

    &.fail {
      background: #FFECEC;
      color: #f00;
      border: 1px solid #f00;
    }
  }
}

.drawer-wrap {
  .drawer-label {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    width: 150px;
    text-align: right;
    margin-right: 30px;
  }

  .drawer-value {
    font-size: 14px;
    color: #000000;

    img {
      width: 20px;
      height: 20px;
    }
  }

  .drawer-icon {
    color: #0256FF;
    margin-left: 10px;
    font-size: 20px;
    cursor: pointer;
  }
}


.drawer__footer {
  position: absolute;
  bottom: 0;
  border-top: 1px solid #DCDFE6;
  width: 100%;
  height: 50px;
  padding-left: 10px;
}

::v-deep .el-form-item--medium .el-form-item__label {
  line-height: 0;
}

::v-deep .el-form-item {
  margin-bottom: 18px;
}

::v-deep .el-tag.el-tag--info {
  transform: translateY(-12px);
}

::v-deep .el-upload-list__item-name {
  width: 120px;
  overflow: hidden;
}

.boder-none {
  border: none;
}

.iconfont {
  margin-right: 5px;
}
</style>
