<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" inline>
      <el-form-item label="日期" prop="day">
        <el-date-picker
          v-model="queryParams.day"
          type="date"
          placeholder="选择日期"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="平台类型" prop="platformType">
        <el-select v-model="queryParams.platformType" placeholder="请选择平台类型" clearable>
          <el-option
            v-for="item in platformList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="产品ID" prop="productId">
        <el-input v-model="queryParams.productId" placeholder="请输入产品ID" clearable />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="productList" @cell-dblclick="renderChart" border @sort-change="handleSortChange">
      <el-table-column label="平台" prop="platformType" align="center" min-width="120" sortable="custom">
        <template slot-scope="scope">
          {{ getPlatformName(scope.row.platformType) }}
        </template>
      </el-table-column>
      <el-table-column label="产品ID" prop="productId" align="center" min-width="100" sortable="custom" />
      <el-table-column label="产品名称" prop="productName" align="center" min-width="150" />
      <el-table-column label="趋势" prop="trend" align="center" min-width="100" sortable="custom">
        <template slot-scope="scope">
          <span :class="scope.row.trend < 0 ? 'text-danger' : 'text-success'">
            {{ scope.row.trend }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="趋势图" align="center" min-width="300">
        <template slot-scope="scope">
          <div :id="'trendChart-' + scope.$index" class="trend-chart"></div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getPlatformList } from '@/api/xmxrChannelManage/channelList'
import { getQwProductTrendStatistics } from '@/api/xmxrChannelManage/qwProductAnalysis'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

export default {
  name: 'QwProductAnalysis',
  data() {
    // 使用 dayjs 获取当天日期并格式化
    const formattedToday = dayjs().format('YYYY-MM-DD')

    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 产品列表
      productList: [],
      // 平台列表
      platformList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        day: formattedToday,
        platformType: undefined,
        productId: undefined,
        productName: undefined,
        sortAsc: {}
      },
      // 平台名称映射
      platformMap: {},
      // ECharts 实例
      chartInstances: []
    }
  },
  created() {
    this.getList()
    this.getPlatformList()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    this.disposeCharts() // 组件销毁前销毁图表实例
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    /** 处理窗口大小变化 */
    handleResize() {
      this.chartInstances.forEach(chart => {
        if (chart) {
          chart.resize()
        }
      })
    },
    /** 查询产品趋势统计列表 */
    getList() {
      // 获取列表数据前先销毁旧图表
      this.disposeCharts()
      this.chartInstances = []
      
      getQwProductTrendStatistics(this.queryParams).then(response => {
        this.productList = response.data.records || [] // Ensure productList is always an array
        this.total = response.data.total
        
        // 使用较长的延迟，等待表格完全渲染
        setTimeout(() => {
          this.renderAllCharts()
        }, 500)
      }).catch(() => {
        this.productList = [] // Ensure productList is an array on error
        this.total = 0
      })
    },
    /** 获取平台列表 */
    getPlatformList() {
      getPlatformList().then(response => {
        this.platformList = response.data || [] // Ensure platformList is always an array
        // 构建平台映射
        this.platformMap = {}
        this.platformList.forEach(item => {
          this.platformMap[item.id] = item.name
        })
      })
    },
    /** 获取平台名称 */
    getPlatformName(platformType) {
      return this.platformMap[platformType] || platformType
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryForm.resetFields()
      // 重置日期为当天，使用 dayjs
      this.queryParams.day = dayjs().format('YYYY-MM-DD')
      // 重置排序
      this.queryParams.sortAsc = {}
      this.handleQuery()
    },
    /** 表格排序事件处理 */
    handleSortChange(column) {
      if (column.prop) {
        // 清空之前的排序
        this.queryParams.sortAsc = {}
        
        // 设置新的排序，仅当排序不为 null 时（不是默认状态）
        if (column.order) {
          // 将驼峰命名转换为下划线命名
          const key = column.prop.replace(/([A-Z])/g, '_$1').toLowerCase()
          this.queryParams.sortAsc[key] = column.order === 'ascending'
        }
        
        this.getList()
      }
    },
    /** 渲染所有图表 */
    renderAllCharts() {
      if (this.productList && this.productList.length > 0) {
        this.productList.forEach((product, index) => {
          this.renderChartByIndex(product, index)
        })
      }
    },
    /** 表格单元格双击时渲染图表 */
    renderChart(row, column, cell, event) {
      const index = this.productList.findIndex(item => item.productId === row.productId)
      if (index !== -1) {
        this.renderChartByIndex(row, index)
      }
    },
    /** 渲染单个图表 */
    renderChartByIndex(product, index) {
      if (!product.data || product.data.length === 0) {
        return
      }
      
      const chartDom = document.getElementById('trendChart-' + index)
      if (!chartDom) {
        console.warn('Chart DOM element for index', index, 'not found')
        return
      }

      // 准备 ECharts 数据
      const xAxisData = product.data.map(item => Object.keys(item)[0])
      const seriesData = product.data.map(item => Object.values(item)[0])

      // 初始化 ECharts 实例
      let chart = echarts.getInstanceByDom(chartDom)
      if (chart) {
        // 如果实例已存在，先销毁
        chart.dispose()
      }
      chart = echarts.init(chartDom)
      this.chartInstances[index] = chart

      // 配置 ECharts 选项
      const option = {
        color: ['#004DAB'], // 设置全局主题色
        tooltip: {
          show: false // 不显示悬浮提示框
        },
        grid: {
          left: '2%',
          right: '2%',
          bottom: '3%',
          top: '3%',
          containLabel: false
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          show: false, // 不显示 X 轴，节省空间
          min: 0,
          max: 'dataMax'
        },
        yAxis: {
          type: 'value',
          show: false, // 不显示 Y 轴，节省空间
          scale: true, // 使用数据的自然比例
          min: function(value) {
            return value.min >= 0 ? 0 : value.min; // 如果最小值大于等于0，则从0开始，否则使用实际最小值
          }
        },
        series: [
          {
            name: '数值',
            type: 'line',
            smooth: true,
            symbol: 'circle', // 显示数据点为圆圈
            symbolSize: 4, // 设置数据点大小
            areaStyle: { // 可以保留或移除区域填充，看是否干扰视线
              opacity: 0.3
            },
            data: seriesData,
            lineStyle: {
              width: 2
            }
          }
        ],
        // animation: false // 关闭动画以提高性能
      }

      chart.setOption(option)
    },
    /** 销毁所有图表实例 */
    disposeCharts() {
      this.chartInstances.forEach(chart => {
        if (chart) {
          chart.dispose()
        }
      })
      this.chartInstances = []
    }
  }
}
</script>

<style scoped>
.trend-chart {
  height: 60px;
  width: 100%;
  min-width: 200px;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}
</style> 