<template>
  <div class="app-container" style="overflow: auto">
    <div class="title">推广模拟匹配</div>
    <!-- <div class="tab">
      <div :class="tabIndex == 0 ? 'active' : ''" @click="toggleTab(0)">
        基础筛选
      </div>
      <div :class="tabIndex == 1 ? 'active' : ''" @click="toggleTab(1)">
        资质筛选
      </div>
    </div> -->
    <!-- <div v-if="tabIndex == 0">
      <el-form label-position="left" >
        <el-form-item label="渠道筛选方式">
          <div style="display: flex" class="input">
            <el-select
              clearable
              size="small"
              v-model="baseForm.id1"
              placeholder="请选择归属项目"
            >
              <el-option value="1" label="1"> </el-option>
            </el-select>
            <el-input
              type="number"
              size="small"
              placeholder="请输入渠道号"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="城市筛选方式">
          <div style="display: flex" class="input">
            <el-select
              clearable
              size="small"
              v-model="baseForm.id1"
              placeholder="请选择归属项目"
            >
              <el-option value="1" label="1"> </el-option>
            </el-select>
            <el-input
              type="number"
              size="small"
              placeholder="请输入渠道号"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="端型筛选">
          <el-select
            clearable
            size="small"
            v-model="baseForm.id1"
            placeholder="请选择端型筛选"
          >
            <el-option value="1" label="1"> </el-option>
          </el-select>
        </el-form-item>
          <el-form-item label="运营商类型">
          <el-select
            clearable
            size="small"
            v-model="baseForm.id1"
            placeholder="请选择运营商类型"
          >
            <el-option value="1" label="1"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div> -->
    <div>
      <el-form :model="formData" :rules="rules" ref="formRef">
        <div class="tags" style="margin-bottom: 20px">基本筛选</div>
        <el-form-item label="请选择城市城市" prop="cityCode">
          <el-select
            v-model="formData.cityCode"
            clearable
            filterable
            size="small"
            placeholder="请选择"
          >
            <el-option
              v-for="item in cityList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="渠道" prop="channelId">
          <el-select
            clearable
            size="small"
            v-model="formData.channelId"
            placeholder="请选择渠道筛选方式"
            filterable

          >
            <el-option
              :value="item.id"
              :label="item.id + '--' + item.channelName"
              v-for="item in channelList"
              :key="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年龄范围" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小年龄"
              v-model="formData.startAge"
              type="number"
              size="small"
            ></el-input>
            <div>至</div>
            <el-input
              placeholder="请输入最大年龄"
              v-model="formData.endAge"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="需求额度范围" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小范围"
              v-model="formData.startQuota"
              type="number"
              size="small"
            ></el-input>
            <div>至</div>
            <el-input
              placeholder="请输入最大范围"
              v-model="formData.endQuota"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item
          style="height: 20px"
          v-for="item in basic_typeList"
          :key="item.dictId"
          :label="item.dictName"
        >
          <el-checkbox-group v-model="checkList" style="display: flex">
            <el-checkbox
              v-for="citem in item.dataRules"
              :label="
                item.paramName.split('_')[
                  item.paramName.split('_').length - 1
                ] +
                'Ids-' +
                citem.dictCode
              "
              :key="citem.dictCode"
              >{{ citem.dictLabel }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <hr />
        <div class="tags" style="margin-top: 10px">基本筛选</div>
        <el-form-item
          style="height: 20px"
          v-for="item in assets_typeList"
          :key="item.dictId"
          :label="item.dictName"
        >
          <el-checkbox-group v-model="checkList" style="display: flex">
            <el-checkbox
              v-for="citem in item.dataRules"
              :label="
                item.paramName.split('_')[
                  item.paramName.split('_').length - 1
                ] +
                'Ids-' +
                citem.dictCode
              "
              :key="citem.dictCode"
              >{{ citem.dictLabel }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item>
          <el-radio v-model="formData.ruleStatus" label="0"
            >选中的满足任意一条即可</el-radio
          >
          <el-radio v-model="formData.ruleStatus" label="1"
            >选中的都需要满足</el-radio
          >
        </el-form-item>
        <hr />
<div v-if="false">
          <div class="tags" style="margin-top: 10px">新颜报告筛选</div>
        <el-form-item
          v-for="item in xy_typeList"
          :key="item.dictId"
          :label="item.dictName"
        >
          <el-checkbox-group v-model="checkList">
            <el-checkbox
              v-for="citem in item.dataRules"
              :label="
                item.paramName.split('_')[
                  item.paramName.split('_').length - 1
                ] +
                'Ids-' +
                citem.dictCode
              "
              :key="citem.dictCode"
              >{{ citem.dictLabel }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="近6个月M0+逾期笔数" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小笔数"
              v-model="formData.overdue6M0Min"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>

        <el-form-item label="近3个月贷款笔数" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小笔数"
              v-model="formData.overdue3Min"
              type="number"
              size="small"
            ></el-input>
            <div>至</div>
            <el-input
              placeholder="请输入最大笔数"
              v-model="formData.overdue3Max"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="近6个月贷款笔数" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小笔数"
              v-model="formData.overdue6Min"
              type="number"
              size="small"
            ></el-input>
            <div>至</div>
            <el-input
              placeholder="请输入最大笔数"
              v-model="formData.overdue6Max"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="贷款行为分" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入最小贷款行为分"
              v-model="formData.loanScoreMin"
              type="number"
              size="small"
            ></el-input>
            <div>至</div>
            <el-input
              placeholder="请输入最大贷款行为分"
              v-model="formData.loanScoreMax"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item
          label="正常还款订单数占贷款总订单数比例(%)"
          style="height: 20px"
        >
          <div class="input">
            <el-input
              placeholder="请输入正常还款订单数占贷款总订单数比例(%)"
              v-model="formData.repaymentRateMin"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="贷款已结清最小单数" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入贷款已结清最小单数"
              v-model="formData.b22170052Min"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="近1个月履约贷款最小次数" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入近1个月履约贷款最小次数"
              v-model="formData.b22170045Min"
              type="number"
              size="small"
            ></el-input>
          </div>
        </el-form-item>
</div>

        <!-- <div class="tags" style="margin-top: 10px; margin-bottom: 10px">
          匹配价格
        </div> -->
        <!-- <el-form-item label="价格" style="height: 20px">
          <div class="input">
            <el-input
              placeholder="请输入价格"
              v-model="formData.matchingPriceSort"
              type="number"
              size="small"
            ></el-input>
            元
          </div>
        </el-form-item> -->
      </el-form>
      <el-button type="primary" @click="submit">提交</el-button>
    </div>
    <el-dialog
      :title="`今日符合筛选资质表单数  ${total}`"
      :visible.sync="dialogVisible"
      center
      width="80%"
    >
      <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
        <el-tab-pane label="用户查看产品" name="1">
          <el-table border :data="userList">
            <el-table-column label="类型" align="center" prop="loanModeName" />
            <el-table-column label="推广ID" align="center" prop="id" />
            <el-table-column label="推广名称" align="center" prop="name" />
            <el-table-column
              label="当前合作价格"
              align="center"
              prop="cooperationCost"
            />
            <el-table-column
              label="当前匹配价格"
              align="center"
              prop="matchingPriceSort"
            />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="用户资质符合产品" name="2">
          <el-table border :data="allList">
            <el-table-column label="类型" align="center" prop="loanModeName" />
            <el-table-column label="推广ID" align="center" prop="id" />
            <el-table-column label="推广名称" align="center" prop="name" />
            <el-table-column
              label="当前合作价格"
              align="center"
              prop="cooperationCost"
            />
            <el-table-column
              label="当前匹配价格"
              align="center"
              prop="matchingPriceSort"
            />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMatchingInfo,
  getMatchingList,
  getCityAll,
  getChannelList,
} from "@/api/productManage/product";
export default {
  name: "Setting",
  data() {
    return {
      cityList: [],
      tabIndex: 0,
      total: 0,
      basic_typeList: [],
      assets_typeList: [],
      checkList: [],
      allList: [],
      userList: [],
      channelList: [],
      dialogVisible: false,
      activeName: "1",
      formData: {
        deviceType: "0",
        channelId: null,
        ruleStatus: "0",
        cityCode: "",
        startAge: "",
        endAge: "",
        startQuota: "",
        endQuota: "",
        overdue6Min: "",
        overdue6Max: "",
        overdue3Min: "",
        overdue3Max: "",
        productId: "",
        loanScoreMin: "",
        loanScoreMax: "",
        overdue6M0Min: "",

        repaymentRateMin: "",
        b22170052Min: "",
        b22170045Min: "",
      },

      xy_typeList: [],
      rules: {
        cityCode: [
          { required: true, message: "城市不能为空", trigger: "blur" },
        ],
        channelId: [
          { required: true, message: "渠道不能为空", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    submit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let arr = {};
          this.checkList.forEach((item) => {
            item = item.split("-");
            if (arr[item[0]]) {
              arr[item[0]] = [...arr[item[0]], item[1]];
            } else {
              arr[item[0]] = [];
              arr[item[0]] = [item[1]];
            }
          });

          getMatchingList({ ...this.formData, ...arr }).then((res) => {
            if (res.code == 200) {
              this.dialogVisible = true;
              this.allList = res.data.list;
              this.userList = res.data.userList;
              this.total = res.data.total;
            }
          });
        }
      });
    },
    handleClick() {},
  },
  mounted() {
    getMatchingInfo().then((res) => {
      this.basic_typeList = res.basic_typeList;
      this.assets_typeList = res.assets_typeList;
      this.xy_typeList = res.xy_typeList;
    });
    getChannelList().then((res) => {

      this.channelList = res.data;
    });
    getCityAll().then((res) => {
      let cityList = [];
      res.data.forEach((item) => {
        if (item.citys) {
          cityList.push(item.citys);
        }
      });
      this.cityList = cityList.flat(Infinity);
    });
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  padding-left: 40px;
}
.title {
  font-size: 20px;
  font-weight: 800;
  margin-left: 20px;
  margin-bottom: 20px;
  text-align: center;
}
.tags {
  width: 200px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: #999;
  opacity: 0.8;
  margin-top: -20px;
}
::v-deep .input {
  display: flex;
  justify-content: flex-start;
  .el-input__inner {
    width: 200px;
  }
  div {
    margin: 0 10px;
  }
}
::v-deep .el-input--small {
  width: 200px;
}
.tab {
  display: flex;
  justify-content: center;
  div {
    width: 150px;
    height: 40px;
    text-align: center;
    margin-right: 40px;
    line-height: 40px;
    border: 1px solid black;
    &:hover {
      cursor: pointer;
    }
  }
  .active {
    background: #004DAB;
    color: #fff;
    border: 1px solid #004DAB;
  }
}
</style>
