<template>
  <div class="app-container">
    <div style="margin-bottom: 20px">
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="addIdentificate"
        >新增</el-button
      >
    </div>
    <el-table border :data="List">
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="请求url" prop="url" align="center" />
      <el-table-column label="表达式" prop="format" align="center" />
      <el-table-column label="Key" prop="key" align="center" />
      <el-table-column label="描述" prop="description" align="center" />
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              type="text"
              icon="el-icon-edit-outline"
              @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="text" icon="el-icon-delete" @click="handleDel(row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="isadd ? '新增识别配置' : '修改识别配置'"
      :visible.sync="avisible"
      @close="cancel"
      append-to-body
      center
      :close-on-click-modal="false"
    >
      <el-form ref="formData" :model="formData" :rules="rules">
        <el-form-item label="请求地址" prop="url">
          <el-input v-model="formData.url" placeholder="请输入请求地址" />
        </el-form-item>
        <el-form-item label="表单式" prop="format">
          <el-input v-model="formData.format" placeholder="请输入表单式" />
        </el-form-item>
        <el-form-item label="key" prop="key">
          <el-input v-model="formData.key" placeholder="请输入key" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getIdentificateList,
  addIdentificateOne,
  editIdentificateOne,
  delIdentificateOne,
} from "@/api/threeParty/identification";
export default {
  data() {
    return {
      List: [],
      total: 0,
      avisible: false,
      isadd: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      formData: {
        description: "",
        format: "",
        key: "",
        url: "",
      },
      rules: {
        url: [{ required: true, message: "请输入请求地址", trigger: "blur" }],
        format: [{ required: true, message: "请输入表单式", trigger: "blur" }],
        key: [{ required: true, message: "请输入key", trigger: "blur" }],
      },
    };
  },
  methods: {
    addIdentificate() {
      this.avisible = true;
      this.isadd = true;
    },
    getList() {
      getIdentificateList(this.queryParams).then((res) => {
        this.List = res.rows;
        this.total = res.total;
      });
    },
    handleEdit(row) {
      this.avisible = true;
      this.formData = row;
    },
    handleDel(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {

          delIdentificateOne({ id: row.id })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  message: "删除成功",
                  type: "success",
                });
                this.getList();
              }
            })
            .catch((err) => {
              console.log(err);
            });
        })
        .catch((err) => {});
    },
    //取消
    cancel() {
      this.avisible = false;
      this.formData = {
        description: "",
        format: "",
        key: "",
        url: "",
      };

      this.$refs.formData.resetFields();
    },
    //提交
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (!this.formData.id) {
            addIdentificateOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.getList();
                this.cancel();
              }
            });
          } else {
            editIdentificateOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.getList();
                this.cancel();
              }
            });
          }
        }
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
