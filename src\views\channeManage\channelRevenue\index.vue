<template>
  <div class="app-container">
    <!-- 顶部操作区 -->
    <el-form :inline="true" :model="listQuery">
      <el-form-item label="选择日期">
        <el-date-picker
          v-model="listQuery.enteringDate"
          type="date"
          placeholder="选择日期"
          :picker-options="pickerOptions"
          value-format="yyyy-MM-dd"
          style="width: 200px;"
          @change="handleSearch"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" icon="el-icon-download" @click="handleExport">导出</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-upload2" @click="handleImport">导入</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格区域 -->
    <div class="table-wrapper">
      <!-- 表格顶部操作区 -->
      <div class="table-header" v-if="hasEditPermission">
        <el-button
          v-if="!isEditing"
          type="primary"
          size="small"
          icon="el-icon-edit"
          @click="handleEditStart"
        >
          编辑数据
        </el-button>
        <div v-else class="edit-actions">
          <el-button
            type="success"
            size="small"
            icon="el-icon-check"
            :disabled="!isDataChanged"
            @click="handleSave"
          >
            保存修改
          </el-button>
          <el-button
            type="danger"
            size="small"
            icon="el-icon-close"
            @click="handleEditCancel"
          >
            取消编辑
          </el-button>
        </div>
      </div>

      <el-table
        :data="list"
        border
      >
        <el-table-column label="渠道ID" prop="channelId" align="center" />
        <el-table-column label="渠道名称" prop="channelName" align="center" />
        <el-table-column label="渠道收益" prop="channelProfit" align="center" />
        <el-table-column label="渠道成本" prop="channelCost" align="center">
          <template slot-scope="{row}">
            <template v-if="isEditing">
              <el-input-number
                v-model="row.channelCost"
                size="mini"
                :controls="false"
                :max="999999"
                @change="handleDataChange"
              />
            </template>
            <span v-else>{{ row.channelCost }}</span>
          </template>
        </el-table-column>
        <el-table-column label="外部FA" prop="externalFa" align="center">
          <template slot-scope="{row}">
            <template v-if="isEditing">
              <el-input-number
                v-model="row.externalFa"
                size="mini"
                :controls="false"
                :max="999999"
                @change="handleDataChange"
              />
            </template>
            <span v-else>{{ row.externalFa }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分润" prop="shareBenefit" align="center">
          <template slot-scope="{row}">
            <template v-if="isEditing">
              <el-input-number
                v-model="row.shareBenefit"
                size="mini"
                :controls="false"
                :max="999999"
                @change="handleDataChange"
              />
            </template>
            <span v-else>{{ row.shareBenefit }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" align="center">
          <template slot-scope="{row}">
            <el-tag :type="row.status == 1 ? 'success' : 'info'">
              {{ row.status == 1 ? '已录入' : '未录入' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 导入弹窗 -->
    <el-dialog 
      title="导入数据" 
      :visible.sync="importDialogVisible" 
      width="400px"
      @closed="handleDialogClosed"
    >
      <el-upload
        ref="upload"
        class="upload-demo"
        action="#"
        :http-request="handleFileSelect"
        :show-file-list="true"
        :limit="1"
        accept=".xlsx,.xls"
      >
        <el-button type="primary">选择文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传excel文件</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="!selectedFile" @click="handleUpload(false)">导 入</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOperatingStatisticsList, exportOperatingStatistics, importOperatingStatistics } from '@/api/channeManage/channelRevenue'
import { saveAs } from 'file-saver'
import { updateOperatingStatistics } from '@/api/channeManage/channelRevenue'

export default {
  name: 'ChannelRevenue',
  data() {
    return {
      list: [], // 表格数据
      listQuery: {
        enteringDate: new Date(Date.now() - 24*60*60*1000).toISOString().split('T')[0] // 默认昨天
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 24*60*60*1000 // 禁用今天及以后的日期
        }
      },
      importDialogVisible: false,
      selectedFile: null,
      isDataChanged: false, // 数据是否被修改
      originalData: [], // 保存原始数据用于比对
      isEditing: false, // 是否处于编辑状态
      // 允许编辑的用户名列表
      allowedUsers: ['admin']
    }
  },
  computed: {
    hasEditPermission() {
      return true
      // return this.allowedUsers.includes(this.$store.getters.userName)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 开始编辑
    handleEditStart() {
      this.isEditing = true
      this.$message.info('您已进入编辑模式，可以修改渠道成本、外部FA和分润数据')
    },
    // 取消编辑
    handleEditCancel() {
      if (this.isDataChanged) {
        this.$confirm('有未保存的更改，确定要取消编辑吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.exitEditMode()
        }).catch(() => {
          // 用户点击取消，不做任何操作
        })
      } else {
        this.exitEditMode()
      }
    },
    // 退出编辑模式
    exitEditMode() {
      this.isEditing = false
      this.list = JSON.parse(JSON.stringify(this.originalData))
      this.isDataChanged = false
    },
    async getList() {
      const res = await getOperatingStatisticsList({
        ...this.listQuery,
        amendSure: false
      })
      this.list = res.data || []
      this.originalData = JSON.parse(JSON.stringify(this.list)) // 保存原始数据的副本
      this.isDataChanged = false
      // 获取新数据时，退出编辑模式
      this.isEditing = false
    },
    // 数据变化处理
    handleDataChange() {
      const isChanged = this.list.some((item, index) => {
        const original = this.originalData[index]
        return String(item.channelCost) !== String(original.channelCost) ||
          String(item.externalFa) !== String(original.externalFa) ||
          String(item.shareBenefit) !== String(original.shareBenefit)
      })
      this.isDataChanged = isChanged
    },
    // 保存修改的数据
    async handleSave() {
      const updateData = this.list.map(item => ({
        id: item.id,
        channelCost: item.channelCost,
        externalFa: item.externalFa,
        shareBenefit: item.shareBenefit
      }))

      const res = await updateOperatingStatistics(updateData)
      
      if (res.code == 200) {
        this.$message.success('保存成功')
        this.getList() // 重新获取数据
      }
    },
    handleSearch() {
      this.getList()
    },
    async handleExport() {
      const res = await exportOperatingStatistics({
        enteringDate: this.listQuery.enteringDate,
        amendSure: false
      })
      const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      const fileName = `${this.listQuery.enteringDate}.xlsx`
      saveAs(blob, fileName)
    },
    handleImport() {
      this.selectedFile = null
      this.importDialogVisible = true
    },
    handleFileSelect(params) {
      this.selectedFile = params.file
    },
    async handleUpload(amendSure = false) {
      if (!this.selectedFile) {
        this.$message.warning('请先选择文件')
        return
      }

      const formData = new FormData()
      formData.append('file', this.selectedFile)
      formData.append('enteringDate', this.listQuery.enteringDate)
      formData.append('amendSure', amendSure)
      
      const res = await importOperatingStatistics(formData)
      if (res.code == 309) {
        this.$confirm('该日期数据已存在，是否覆盖？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.handleUpload(true)
        }).catch(() => {
          // 用户点击取消，不做任何操作
        })
        return
      }
      
      this.$message.success('导入成功')
      this.importDialogVisible = false
      this.selectedFile = null
      this.getList()
    },
    handleDialogClosed() {
      this.selectedFile = null
      this.$refs.upload.clearFiles()
    }
  }
}
</script>

<style lang="scss" scoped>
.table-wrapper {
  position: relative;
  margin-top: 20px;
}

.table-header {
  position: absolute;
  right: 0;
  top: -40px;
  z-index: 1;

  .edit-actions {
    display: flex;
    gap: 10px;
  }
}

.upload-demo {
  text-align: center;
}
</style>