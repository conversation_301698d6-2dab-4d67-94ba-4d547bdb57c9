<script>
import { getProdProductList } from "@/api/statisticalManage";
import { addMatchingPrice } from "@/api/productManage/product";

export default {
  name: "AddProductMatchingPrice",

  data() {

    // 自定义表单校验函数,校验价格是否在4-20之间
    const validatePrice = (rule, value, callback) => {
      const n = Number(value);

      if (isNaN(n)) {
        callback(new Error("请输入数字"));
        return;
      }

      if (n < 4 || n > 20) {
        callback(new Error("价格必须在4-20之间"));
        return;
      }

      callback();
    };

    return {
      dialogVisible: false,
      form: {
        productId: "",
        matchPrice: "",
      },
      productList: [],
      isBusy: false,
      rules: {
        productId: [
          { required: true, message: "请选择产品", trigger: "change" },
        ],
        matchPrice: [
          { required: true, message: "请输入匹配价格", trigger: "blur" },
          { validator: validatePrice, trigger: "blur" },
        ],
      },
    };
  },

  methods: {
    open() {
      this.dialogVisible = true;
      this.getProductList();
    },

    async getProductList() {
      const res = await getProdProductList({
        type: 1,
      });
      this.productList = res.data;
    },

    onClosed() {
      this.$refs.form.resetFields();
      this.dialogVisible = false;
      this.isBusy = false;
      this.$emit("closed");
    },

    async onSubmit() {
      try {
        await this.$refs.form.validate();
        this.isBusy = true;
        const params = this.getParams();
        const res = await addMatchingPrice(params);
        if (res.code == 200) {
          this.$message.success(res.msg);
          this.dialogVisible = false;
        }
      } catch (err) {

      } finally {
        this.isBusy = false;
      }
    },

    getParams() {
      return {
        productId: this.form.productId,
        matchPrice: Number(this.form.matchPrice),
      };
    },
  },
};
</script>

<template>
  <el-dialog
    title="新增产品匹配价格"
    :visible.sync="dialogVisible"
    width="600px"
    @close="onClosed"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-form
      :model="form"
      ref="form"
      size="small"
      label-width="100px"
      :rules="rules"
    >
      <el-form-item label="产品" prop="productId">
        <el-select
          v-model="form.productId"
          placeholder="产品"
          filterable
          clearable
          style="width: 300px"
        >
          <el-option
            :value="item.id"
            :label="item.id + '-----' + item.name"
            v-for="item in productList"
            :key="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="匹配价格" prop="matchPrice">
        <!--        <el-input-number v-model="form.matchPrice" :min="4" :max="20" label="匹配价格"></el-input-number>-->
        <el-input
          v-model="form.matchPrice"
          type="number"
          placeholder="请输入匹配价格"
          style="width: 300px"
          min="4"
          max="20"
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false" :disabled="isBusy"
        >取消</el-button
      >
      <el-button type="primary" @click="onSubmit" :loading="isBusy"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<style scoped lang="scss"></style>
