import request from '@/utils/request'
//获取渠道模板列表
export const getChannelTypeList = (data) => {
    return request({
        url: "/loan/channelTemplate/list",
        method: "get",
        params: data
    })
}
//新增渠道标识
export const addChannelTypeList = (data) => {
    return request({
        url: "/loan/channelTemplate/add",
        method: "post",
        data
    })
}
//删除渠道标识
export const delChannelTypeOne = (id) => {
    return request({
        url: `/loan/channelTemplate/del/${id}`,
        method: "post"
    })
}
//修改状态
export const updateChannelTypeOne = (data) => {
    return request({
        url: "/loan/channelTemplate/updateStatus",
        method: "post",
        data

    })
}
//修改详情
export const updatedetailChannelTypeOne = (data) => {
    return request({
        url: "/loan/channelTemplate/edit",
        method: "post",
        data
    })
}
