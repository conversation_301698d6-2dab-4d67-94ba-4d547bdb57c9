<template>
  <div class="dashboard-container">
    <!-- 用户名字 -->
    <div class="user-name">
      <span class="masked-name">{{ maskedUserName }}</span><span>{{ lastChar }},</span>
      <span style="margin-left: 18px;">{{ creditLevel }}</span>
    </div>
    <!-- 更多服务 -->
    <div class="more-service">
      <span class="more-service-text">更多服务</span>
    </div>
    <!-- 分数-->
    <div class="dashboard">
      <div class="dashboard-inner">
        <div class="value-display-score" :style="scoreStyle">{{ score }}</div>
        <i class="el-icon-arrow-right dashboard-inner-icon"></i>
      </div>
    </div>
    <!-- 进度 -->
    <div class="schedule">
      <div>
        <div class="plan">
          <div class="plan-text">涨分进度</div>
          <div class="schedule-bg">
            <div class="progress-bar" :style="{ width: parseInt(arcSettings.percentage * 100) + '%' }">
              <div class="progress-end">
                <img class="progress-end-icon" src="@/assets/images/materialLibrary/sesame-score/dashboard/schedule-icon.png" alt="">
                <span class="progress-percent">{{ parseInt(arcSettings.percentage * 100) }}%</span>
              </div>
            </div>
          </div>
        </div>
        <div class="everyday">
          <div class="everyday-text">每日攒进度，进度达到100%下周一即可涨分</div>
          <img src="@/assets/images/materialLibrary/sesame-score/dashboard/daily-icon.png" class="everyday-icon" />
        </div>
      </div>
      <div class="button">
        <img class="button-image" src="@/assets/images/materialLibrary/sesame-score/dashboard/button-bg.png" alt="">
        <div class="button-text">立即攒进度</div>
      </div>
    </div>
    <!-- 加进度 -->
    <div class="add-schedule">
      <div class="add-schedule-cont" v-for="item in addSchedule" :key="item.id">
        <div class=" number">
          <img class="schedule-number-image" src="@/assets/images/materialLibrary/sesame-score/dashboard/progress-number.png" alt="">
          <div class="schedule-number-text">进度+{{ item.number }}%</div>
        </div>
        <div class="cont">
          <img class="schedule-cont-image" src="@/assets/images/materialLibrary/sesame-score/dashboard/progress-content.png" alt="">
          <div class="schedule-cont-text">{{ item.contText }}<i class="el-icon-arrow-right"></i></div>
        </div>
        <div class="text">{{ item.text }}</div>
        <!-- <img v-if="item.id == 1" class="finger" src="@/assets/images/addSchedule3.png" alt=""> -->
      </div>

    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  props: {
    userName: {
      type: String,
      default: ''
    },
    score: {
      type: Number,
      default: 650
    },
    arcSettings: {
      type: Object,
      default: () => ({
        linkedToScore: true,
        percentage: 0.5
      })
    },
    scoreFont: {
      type: Object,
      default: () => ({
        fontSize: 128,
        fontWeight: 600
      })
    }

  },
  data() {
    return {
      canvasId: `progress-canvas-${this._uid}`,
      addSchedule: [{
        id: 1,
        number: 6,
        contText: "答题学知识",
        text: "行为积累",
      }, {
        id: 2,
        number: 60,
        contText: "去免押租车",
        text: "守约记录",
      }, {
        id: 3,
        number: 80,
        contText: "去上传学历",
        text: "身份证明",
      }, {
        id: 4,
        number: 80,
        contText: "去上传医保",
        text: "资产证明",
      }]
    }
  },
  computed: {
    maskedUserName() {
      const name = this.userName || '';
      if (name.length <= 1) return '';
      return '*'.repeat(name.length - 1);
    },
    lastChar() {
      const name = this.userName || '';
      return name.slice(-1);
    },
    creditLevel() {
      const score = this.score;
      const scoreRanges = [
        { min: 700, max: 950, level: '极好' },
        { min: 650, max: 700, level: '优秀' },
        { min: 600, max: 650, level: '良好' },
        { min: 550, max: 600, level: '中等' },
        { min: 350, max: 550, level: '较差' }
      ];

      for (const range of scoreRanges) {
        if (score >= range.min && score <= range.max) {
          return `信用${range.level}`;
        }
      }
      return '信用较差';
    },
    scoreStyle() {
      return {
        fontSize: `${this.scoreFont.fontSize}px`,
        fontWeight: this.scoreFont.fontWeight
      };
    }
  },
  mounted() {
    this.drawCanvasArc();
  },
  watch: {
    score() {
      this.drawCanvasArc();
    },
    arcSettings: {
      handler() {
        this.drawCanvasArc();
      },
      deep: true
    }
  },
  methods: {
    drawCanvasArc() {
      const canvas = document.getElementById(this.canvasId);
      if (!canvas || !canvas.getContext) {
        console.error("Canvas element not found or context not supported.");
        return;
      }
      const ctx = canvas.getContext('2d');

      // 获取设备像素比
      // const dpr = window.devicePixelRatio || 1;
      const dpr = 1.5;
      const rect = canvas.getBoundingClientRect();

      // 设置canvas的实际大小
      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;

      // 设置canvas的CSS显示大小
      canvas.style.width = `${rect.width}px`;
      canvas.style.height = `${rect.height}px`;

      // 根据dpr缩放canvas上下文
      ctx.scale(dpr, dpr);

      const width = rect.width;
      const height = rect.height;

      // 启用抗锯齿
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';

      // Clear canvas before drawing
      ctx.clearRect(0, 0, width, height);

      // Define arc parameters
      const centerX = width / 2;
      const centerY = height / 2;
      const radius = 119;
      const lineWidth = 26;
      const startAngleRad = 149 * (Math.PI / 180); // 149 degrees in radians
      const totalAngleRad = 240 * (Math.PI / 180); // Total arc span is 240 degrees

      // 计算百分比 - 根据设置选择不同的计算方式
      let percentage;
      if (this.arcSettings.linkedToScore) {
        // 使用芝麻分计算百分比
        const minScore = 350;
        const maxScore = 950;
        const totalRange = maxScore - minScore;
        // 先基于分数计算百分比，然后确保在25%~90%范围内
        percentage = Math.max(0, Math.min(1, (this.score - minScore) / totalRange)); // Clamp between 0 and 1
        percentage = Math.max(0.25, Math.min(0.9, percentage)); // 确保在25%~90%范围内
      } else {
        // 使用自定义百分比
        percentage = Math.max(0.25, Math.min(0.9, this.arcSettings.percentage)); // 确保在25%~90%范围内
      }

      const currentAngleSpanRad = percentage * totalAngleRad;
      const endAngleRad = startAngleRad + currentAngleSpanRad;

      // 创建与当前圆弧匹配的渐变
      // 计算圆弧的起点和终点坐标
      const startX = centerX + radius * Math.cos(startAngleRad);
      const startY = centerY + radius * Math.sin(startAngleRad);
      const endX = centerX + radius * Math.cos(endAngleRad);
      const endY = centerY + radius * Math.sin(endAngleRad);

      // 创建沿着圆弧的渐变，而不是固定的水平渐变
      const gradient = ctx.createLinearGradient(startX, startY, endX, endY);
      const startColor = { r: 166, g: 210, b: 255 }; // #A6D2FF (蓝色)
      const endColor = { r: 255, g: 255, b: 255 }; // #FFFFFF (白色)

      // 添加渐变颜色停止点
      gradient.addColorStop(0, `rgb(${startColor.r}, ${startColor.g}, ${startColor.b})`);
      gradient.addColorStop(1, `rgb(${endColor.r}, ${endColor.g}, ${endColor.b})`);

      // Set drawing styles for the main arc
      ctx.lineWidth = lineWidth;
      ctx.strokeStyle = gradient;
      ctx.lineCap = 'butt'; // Set to butt for flat start

      // Draw the main arc
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, startAngleRad, endAngleRad, false);
      ctx.stroke();

      // --- Add round cap manually at the end --- 
      if (percentage > 0) { // Only add cap if there is some arc
        // Calculate end point coordinates
        const endX = centerX + radius * Math.cos(endAngleRad);
        const endY = centerY + radius * Math.sin(endAngleRad);

        // 使用渐变的终点颜色（白色）作为圆形端点的颜色
        const capColor = `rgb(${endColor.r}, ${endColor.g}, ${endColor.b})`;

        // Draw the round cap (filled circle)
        ctx.beginPath();
        ctx.arc(endX, endY, lineWidth / 2, 0, 2 * Math.PI); // Full circle
        ctx.fillStyle = capColor;
        ctx.fill();
      }
    }
  }
}
</script>

<style scoped lang="scss">
.dashboard-container {
  margin-top: 30px;
  position: relative;
  z-index: 1;
  // background-color: yellowgreen;

  .user-name {
    font-family: 'SourceHanSansSC-Medium';
    position: absolute;
    top: 27px;
    left: 38px;
    color: #fff;
    font-size: 28px;
    line-height: 49px;

    .masked-name {
      font-family: 'SanFranciscoText-Medium';
      vertical-align: top;
      letter-spacing: 4px;
    }
  }

  .more-service {
    font-family: 'SourceHanSansSC-Medium';
    position: absolute;
    top: 15px;
    right: 0px;
    padding-top: 11px;
    padding-left: 27px;
    color: #fff;
    font-size: 26px;
    line-height: 32px;
    width: 154px;
    height: 60px;
    background: rgba(255, 255, 255, 0.15);
    border-top-left-radius: 50px;
    border-bottom-left-radius: 50px;
  }
}

.dashboard {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .dashboard-inner {
    position: relative;
    height: 96px;
    display: flex;
    align-items: center;

    .value-display-score {
      font-family: D-DINExp;
      color: #FFFFFF;
      line-height: 83px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      /* font-size 和 font-weight 通过动态样式设置 */
    }

    .el-icon-arrow-right {
      font-size: 30px;
      color: #fff;
    }

    .dashboard-inner-icon {
      position: absolute;
      top: 31px;
      right: -30px;
    }
  }
}

.schedule {
  display: flex;
  justify-content: space-between;
  margin: 53px 29px 75px 29px;
  background-image: url("../../../../../../../assets/images/schedule-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center 9px;

  .plan {
    display: flex;

    .plan-text {
      margin-right: 8px;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 28px;
      color: #FFFFFF;
      line-height: 41px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .schedule-bg {
      position: relative;
      margin-top: 15px;
      width: 358px;
      height: 12px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 335px 335px 335px 335px;
    }

    // 进度条样式
    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #BACFF4 0%, #FFFFFF 22%, #CFDAED 100%);
      border-radius: 12px;
      transition: width 0.1s ease;
      position: relative;
    }

    .progress-end {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translate(100%, -50%);
      display: flex;
      align-items: center;
      white-space: nowrap;
    }

    .progress-end-icon {
      width: 30px;
      height: 30px;
      margin-right: 5px;
      margin-left: -14px;
      font-size: 16px;
    }

    .progress-percent {
      margin-top: -2px;
      margin-left: 4px;
      font-family: DIN, DIN;
      font-weight: 500;
      font-size: 22px;
      color: #FFFFFF;
      line-height: 21px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      text-shadow: 0 0 28px white, 0 0 18px white;
      /* 添加白色阴影效果 */
    }


  }

  .everyday {
    display: flex;

    .everyday-text {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 22px;
      color: rgba(255, 255, 255, 0.6);
      line-height: 32px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .everyday-icon {
      width: 22px;
      height: 22px;
      transform: translateY(3px);
      margin-left: 5px;
      margin-top: 4px;
    }
  }


  .button {
    width: 172px;
    height: 60px;
    position: relative;

    .button-image {
      width: 172px;
      height: 60px;
    }

    .button-text {
      position: absolute;
      top: 13px;
      left: 16px;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 28px;
      line-height: 31px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      color: #155CC6;
    }
  }
}

.add-schedule {
  display: flex;
  justify-content: space-between;
  margin: 0 26px 24px 48px;

  .add-schedule-cont {
    position: relative;
    overflow: hidden;
    width: 150px;
    // margin-right: 16px;
  }

  .number {
    position: absolute;
    left: 15px;
    width: 93px;
    height: 32px;
    z-index: 2;

    .schedule-number-image {
      width: 93px;
      height: 32px;
    }

    .schedule-number-text {
      position: absolute;
      left: 50%; // 定位到父容器的50%位置
      transform: translateX(-50%); // 向左移动自身宽度的一半
      top: 0;
      font-size: 18px;
      color: #FFFFFF;
      line-height: 30px;
      text-align: center; // 确保文字自身居中
      font-style: normal;
      text-transform: none;
      width: max-content; // 防止宽度被压缩
    }
  }

  .cont {
    position: relative;
    margin-top: 15px;
    width: 122px;
    height: 122px;
    z-index: 1;

    .schedule-cont-image {
      width: 122px;
      height: 122px;
    }

    .schedule-cont-text {
      position: absolute;
      top: 50%; // 定位到父容器的50%
      left: 50%; // 定位到父容器的50%
      transform: translate(-50%, -50%); // 回退自身宽高的一半
      text-align: center; // 确保文字水平居中
      width: 72px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 24px;
      color: #FFFFFF;
      line-height: 36px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .text {
    margin: 10px 0 0 17px;
    width: 89px;
    height: 33px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 22px;
    color: rgba(255, 255, 255, 0.72);
    line-height: 33px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .finger {
    position: absolute;
    // top: 68px; // 定位到父容器的50%
    // left: 44px;
    left: 74px;
    top: 102px;
    z-index: 3;
    width: 96px;
    height: 96px;
  }
}
</style>