<template>
  <div class="reply-section">
    <el-form ref="replyForm" :model="replyForm" :rules="replyRules">
      <el-form-item prop="content">
        <el-input
          v-model.trim="replyForm.content"
          type="textarea"
          :rows="3"
          placeholder="请输入..."
          maxlength="500"
          show-word-limit
          class="reply-textarea"
          @keydown.enter.native.prevent="handleSubmitReply"
        />
      </el-form-item>
      <el-form-item style="margin-bottom: 0; text-align: right;">
        <el-button
          class="send-button"
          @click="handleSubmitReply"
          :loading="loading"
          :type="replyForm.content && replyForm.content.trim() ? 'primary' : 'default'"
        >
          发 送
          <span style="font-size: 11px; margin-left: 2px;">(Enter)</span>
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import sensitiveWordChecker from '@/utils/sensitiveWords'

export default {
  name: 'ReplyInput',
  props: {
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 回复表单
      replyForm: {
        content: ''
      },
      // 回复表单校验规则
      replyRules: {
        content: [
          { required: true, message: "回复内容不能为空", trigger: "blur" },
          { min: 1, max: 500, message: "回复内容长度在1到500个字符", trigger: "blur" }
        ]
      }
    }
  },
  methods: {
    /** 提交回复 */
    handleSubmitReply() {
      this.$refs["replyForm"].validate((valid) => {
        if (valid) {
          // 敏感词校验
          if (sensitiveWordChecker.contains(this.replyForm.content)) {
            this.$message.error("回复内容包含敏感词，请修改后重新发送。")
            return
          }

          this.$emit('send', this.replyForm.content)
        }
      })
    },
    /** 清空回复内容 */
    clearReply() {
      this.replyForm.content = ''
      this.$refs["replyForm"] && this.$refs["replyForm"].resetFields()
    },
    /** 清除验证 */
    clearValidate() {
      this.$refs["replyForm"] && this.$refs["replyForm"].clearValidate()
    },
    /** 聚焦到回复输入框 */
    focus() {
      this.$nextTick(() => {
        const textarea = this.$el.querySelector('.reply-textarea textarea')
        if (textarea) {
          textarea.focus()
        }
      })
    }
  }
}
</script>

<style scoped>
.reply-section {
  border-top: 1px solid #dcdcdc;
  padding: 10px 20px 12px;
  background-color: #f5f5f5;
}

.reply-section >>> .el-form-item {
  margin-bottom: 10px;
}

.reply-section >>> .el-form-item__content {
  line-height: normal;
}

.reply-section >>> .el-form-item:last-child {
  margin-bottom: 0;
}

.reply-section >>> .el-form-item__error {
  padding-top: 2px;
}

.reply-textarea >>> .el-textarea__inner {
  border: none;
  background-color: #f5f5f5;
  box-shadow: none;
  resize: none;
  padding: 8px;
  font-size: 14px;
}

.reply-textarea >>> .el-textarea__inner:focus {
  box-shadow: none;
  background-color: #f5f5f5;
}

.send-button {
  background-color: #f5f5f5;
  color: #222;
  border: 1px solid #e5e5e5;
  font-weight: normal;
  padding: 6px 20px;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s;
}

.send-button:hover,
.send-button:focus {
  background-color: #eaeaea;
  border-color: #eaeaea;
  color: #333;
}

.send-button.el-button--primary {
  background-color: #07c160;
  border-color: #07c160;
  color: #fff;
}

.send-button.el-button--primary:hover,
.send-button.el-button--primary:focus {
  background-color: #06ad56;
  border-color: #06ad56;
}

.reply-section .el-form-item {
  margin-bottom: 10px;
}

.reply-section .el-form-item:last-child {
  margin-bottom: 0;
}
</style>