<script>
import dayjs from "dayjs";
import { getOutputChannelStatistics } from "@/api/statisticalManage";
import ConsumptionStatisticsDialog from "./components/consumptionStatisticsDialog.vue";
import { sum } from '@/utils/calculate'

export default {
  name: "outputChannelStatistics",

  data() {
    return {
      queryParams: {
        dateRange: [],
        channelName: "",
      },
      tableData: [],
      // 当前数据的请求参数，出量消耗统计弹窗内部使用
      requestParams: {},
    };
  },

  mounted() {
    this.setDefaultDate();
    this.handleQuery();
  },

  components: {
    ConsumptionStatisticsDialog,
  },

  computed: {
    showOperationColumn() {
      const backUser = ['dingcan']
      return !backUser.includes(this.$store.getters.userInfo.userName)
    },
  },

  methods: {
    setDefaultDate() {
      const today = dayjs();
      const startTime = today.format("YYYY-MM-DD 00:00:00");
      const endTime = today.format("YYYY-MM-DD 23:59:59");

      this.queryParams.dateRange = [startTime, endTime];
    },

    getParams() {
      const [startTime = "", endTime = ""] = this.queryParams?.dateRange || [];
      const channelName = this.queryParams.channelName;

      return {
        startTime,
        endTime,
        channelName,
      };
    },

    // 开启出量消耗统计弹窗
    openOutputConsumptionStatisticsDialog(row) {
      this.$refs.consumptionStatisticsDialog.open({
        params: {
          ...this.requestParams,
          outputSource: row.outputSource,
        },
      });
    },

    async handleQuery() {
      this.requestParams = this.getParams();
      const res = await getOutputChannelStatistics(this.requestParams);
      this.tableData = res.rows || [];
      if (this.tableData.length) {
        this.tableData.unshift(this.createSummaryRow(this.tableData));
      }
    },

    createSummaryRow(data) {
      const summaryRow = {
        channelName: "合计",
        successNum: 0,
        successPrice: 0,
        isSummary: true,
      };

      summaryRow.successNum = sum(data.map((item) => item.successNum));
      summaryRow.successPrice = sum(data.map((item) => item.successPrice));

      return summaryRow;
    },

    sortChange({ prop, order }) {
      if (!this.tableData.length) {
        return;
      }

      const summaryRow = this.tableData[0];
      const sortRows = this.tableData.slice(1);

      switch (order) {
        //正序
        case "ascending":
          sortRows.sort((a, b) => a[prop] - b[prop]);
          break;
        //倒序
        case "descending":
          sortRows.sort((a, b) => b[prop] - a[prop]);
          break;
      }

      sortRows.unshift(summaryRow);
      this.tableData = sortRows;
    },
  },
};
</script>

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" inline size="small">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="queryParams.dateRange"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道名称">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入渠道名称"
          clearable
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery"
          >搜索
        </el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData" border @sort-change="sortChange">
      <el-table-column
        label="渠道名称"
        align="center"
        prop="channelName"
        fixed
      />
      <el-table-column label="数量" align="center" prop="successNum" />
      <el-table-column label="总额" align="center" prop="successPrice" />
      <el-table-column label="均值" align="center" prop="successMean" />
      <el-table-column
        label="匹配率"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="mateRate"
      >
        <template #default="{ row }">
          <span v-if="!row.isSummary">{{ row.mateRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" v-if="showOperationColumn">
        <template #default="{ row }">
          <el-button
            type="text"
            @click="openOutputConsumptionStatisticsDialog(row)"
            v-if="!row.isSummary"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <ConsumptionStatisticsDialog ref="consumptionStatisticsDialog" />
  </div>
</template>

<style scoped lang="scss"></style>
