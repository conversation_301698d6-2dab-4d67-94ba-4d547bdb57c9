import request from '@/utils/request'

// 获取数据列表
export function getOperatingStatisticsList(params) {
  return request({
    url: '/loan/operatingStatistics/show',
    method: 'post',
    data: params
  })
}

// 导出数据
export function exportOperatingStatistics(params) {
  return request({
    url: '/loan/operatingStatistics/export/out',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

// 导入数据
export function importOperatingStatistics(data) {
  return request({
    url: '/loan/operatingStatistics/export/in',
    method: 'post',
    data
  })
}

// 更新渠道收益数据
export function updateOperatingStatistics(data) {
  return request({
    url: '/loan/operatingStatistics/export/update',
    method: 'post',
    data
  })
} 