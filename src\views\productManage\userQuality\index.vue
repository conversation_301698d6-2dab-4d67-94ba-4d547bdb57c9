<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleImport"
          >导入</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="downLoad"
          >导出</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="list">
      <el-table-column label="手机号" prop="phone" align="center" />
      <el-table-column label="渠道名称" prop="channelName" align="center" />
      <el-table-column label="状态" prop="status" align="center" />
      <el-table-column label="质量" prop="quality" align="center" />
    </el-table>

    <el-dialog
      width="800px"
      title="批量导入用户质量"
      append-to-body
      center
      @close="upClose"
      :visible.sync="upDataAvisiable"
      :close-on-click-modal="false"
    >
      <el-form ref="uploadForm" label-width="200px" label-position="left">
        <el-form-item label="请上传数据" prop="file">
          <el-upload
            :on-remove="handleRemove"
            :on-exceed="handleExceed"
            ref="my-upload"
            :auto-upload="false"
            action="#"
            class="upload-demo"
            drag
            :on-change="handleChange"
            :limit="1"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将EXCEL拖到此处，或<em>点击上传</em>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="type" @input="handleType">
            <el-radio :label="1">产品</el-radio>
            <el-radio :label="2">商户</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="产品" prop="productId" v-if="type == 1">
          <el-select clearable v-model="id" filterable placeholder="请选择产品">
            <el-option
              v-for="item in productList"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商户" prop="partyId" v-if="type == 2">
          <el-select
            clearable
            v-model="partyId"
            filterable
            placeholder="请选择商户"
          >
            <el-option
              v-for="item in partyList"
              :key="item.partyFirstId"
              :value="item.partyFirstId"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUpData">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { upLoadFileUserQuality, getProductList } from "@/api/partyA";
import { getAcquirePartyAall } from "@/api/productManage/product";
import * as xlsx from "xlsx";
export default {
  data() {
    return {
      files: "",
      id: "",
      partyId: "",
      type: 1,
      list: [],
      productList: [],
      upDataAvisiable: false,
      dateRange: [],
      queryParams: {},
      partyList: [],
    };
  },
  methods: {
    handleQuery() {},

    //批量上传
    submitUpData() {
      if (!this.files)
        return this.$notify.warning({
          title: "错误",
          message: "请上传文件",
        });
      let data = new FormData();
      data.append("file", this.files);
      if (this.id) {
        data.append("id", this.id);
      }
      if (this.partyId) {
        data.append("partyId", this.partyId);
      }

      upLoadFileUserQuality(data).then((res) => {
        this.upDataAvisiable = false;
        this.$message.success("操作成功");

        this.list = res.data;
      });
    },

    //关闭上传框
    upClose() {
      this.upDataAvisiable = false;
      this.$refs["my-upload"].clearFiles();
      this.files = "";
      this.id = "";
    },
    //上传exlec
    async handleChange(ev) {
      if (ev.size > 1 * 1024 * 1024) {
        this.$notify.error({
          title: "错误",
          message: "文件大小不能超过2M",
        });
        return;
      }
      // if (this.files) {
      //   this.$refs["my-upload"].clearFiles();
      //   this.files = "";
      // }
      if (!/\.(xls|xlsx)$/.test(ev.name.toLowerCase())) {
        this.$notify.error({
          title: "错误",
          message: "上传格式有误",
        });
        this.$refs["my-upload"].clearFiles();
        return;
      }
      this.files = ev.raw;
      console.log(this.files);
      this.$message.success("上传成功");
    },
    //移除上传文件
    handleRemove() {
      this.$refs["my-upload"].clearFiles();
      this.files = "";
    },
    handleImport() {
      this.upDataAvisiable = true;
    },

    handleExceed() {
      this.$message.error("请删除后在上传");
      return false;
    },
    handleType() {
      this.id = "";
      this.partyId = "";
    },
    downLoad() {
      if(this.list.length == 0) return this.$message.error("请先上传文件");
      let arrlist = [];
      this.list.forEach((item) => {
        arrlist.push({
          手机号: item.phone,
          渠道号: item.channelName,
        });
      });

      let sheet = xlsx.utils.json_to_sheet(arrlist);
      let book = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(book, sheet, "sheet1");
      xlsx.writeFile(book, `模板详情.xls`);
    },
  },
  mounted() {
    getProductList().then((res) => {
      this.productList = res.data;
    });
    getAcquirePartyAall().then((res) => {
      this.partyList = res.data;
    });
  },
};
</script>

<style lang="scss" scoped>
.tips {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
  font-weight: bold;
}
</style>
