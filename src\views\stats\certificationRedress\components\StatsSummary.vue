<template>
  <div class="stats-summary" v-if="stats && stats.lossRatio">
    <div class="stats-item">
      <span class="stats-label">总数:</span>
      <span class="stats-value">{{stats.total}}</span>
    </div>
    <div class="stats-item">
      <span class="stats-label">损失:</span>
      <span class="stats-value">{{stats.loss}}</span>
    </div>
    <div class="stats-item">
      <span class="stats-label">损失率:</span>
      <span class="stats-value">{{stats.lossRatio}}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StatsSummary',
  props: {
    stats: {
      type: Object,
      default: () => null
    }
  }
};
</script>

<style scoped>
.stats-summary {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 6px 0;
  margin-bottom: 12px;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  background-color: #f8f8f8;
}
.stats-item {
  margin: 0 16px;
  display: flex;
  align-items: center;
}
.stats-label {
  font-weight: bold;
  margin-right: 6px;
  color: #606266;
}
.stats-value {
  font-size: 16px;
  color: #409EFF;
  font-weight: 600;
}
</style> 