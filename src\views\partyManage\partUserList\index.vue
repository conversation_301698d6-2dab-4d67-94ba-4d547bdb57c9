<template>
  <div class="app-container">
    <!-- <div class="center">
      <div class="tags">
        本后台线索信息，自生成之日，留存时间为3个自然日，3日后会自动清理，请及时联系
      </div>
    </div> -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="commerce">
        <el-date-picker
          v-model="queryParams.createTime"
          type="date"
          size="small"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          placeholder="选择日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="筛选" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="状态"
          clearable
          size="small"
        >
          <el-option value="0" label="未联系"></el-option>
          <el-option value="1" label="感兴趣"></el-option>
          <el-option value="2" label="不感兴趣"></el-option>
          <el-option value="3" label="未接通"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >筛选</el-button
        >
      </el-form-item>
      <el-form-item label="是否接单" v-if="!isMain">
        <el-switch


          v-model="status"
          active-value="0"
          inactive-value="1"
          active-text="启用"
          inactive-text="禁用"
          @change="changeStatus()"
        >
        </el-switch>
      </el-form-item>
    </el-form>
    <div class="contact">
      今日新增 <span>{{ dayCount }}</span> 个广告线索，还有<span>{{
        statusCount
      }}</span
      >条线索未联系
    </div>

    <el-table border :data="peopleData">
      <el-table-column label="" align="center" prop="title" width="100">
        <template  slot-scope="{row}">
          <div class="link" @click="getInfo(row)">
            <!-- @click="$router.push('/partyManage/userInfo?id=' + row.id)" -->
            {{ row.title }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="姓名" prop="name" align="center">
        <template  slot-scope="{row}">
          <div class="link" @click="getInfo(row)">
            {{ row.name }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="需求金额" prop="demandAmount" align="center" />
      <el-table-column label="年龄" prop="age" align="center" />
      <el-table-column label="房" prop="houseName" align="center" />
      <el-table-column label="车" prop="vehicleName" align="center" />
      <el-table-column label="公积金" prop="providentName" align="center" />
      <el-table-column label="社保" prop="socialName" align="center" />
      <el-table-column label="地区" prop="address" align="center" />
      <el-table-column label="提交时间" prop="createTime" align="center" />
      <el-table-column label="状态" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ statusType[row.status * 1] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分配经理" prop="userName" align="center" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <v-info
      v-if="infoShow.isShow"
      :infoShow="infoShow"
      :pushUser="pushUser"
      :info="info"
      :id="id"
      :isXy="isXy"
    />
  </div>
</template>

<script>
import {
  getUserList,
  getUserTotal,
  getUserAccountType,
  editUserPushStatus,
  getReportDetail,
} from "@/api/partyManage";
import info from "@/views/partyManage/partUserList/info.vue";
export default {
  name: "Partuserlist",

  data() {
    return {
      isMain: false,
      id: null,
      isXy: false,
      pushUser: {},
      info: {},
      status: "0",
      infoShow: {
        isShow: false,
      },
      statusType: {
        0: "未联系",
        1: "感兴趣",
        2: "不感兴趣",
        3: "未接通",
      },
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now() ||
            time.getTime() < Date.now() - 3600 * 3 * 24 * 1000
          );
        },
      },
      total: 0,

      queryParams: {
        createTime: "",
        status: "",
        pageNum: 1,
        pageSize: 10,
      },
      dayCount: 0,
      statusCount: 0,
      peopleData: [],
    };
  },
  components: {
    "v-info": info,
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getInfo(row) {
      if (row) {
        this.id = row.id;
      }
      getReportDetail(this.id).then((res) => {
        this.infoShow.isShow = true;
        this.pushUser = res.pushUser;
        this.info = res.behavior || {};
        this.isXy = res.isXy;
      });
    },

    getList() {
      getUserList(this.queryParams).then((response) => {
        this.peopleData = response.rows;
        this.peopleData.forEach((item) => {
          item.title = "线索信息";
        });
        this.total = response.total;
      });
    },
    changeStatus() {
      this.$confirm(`确定${this.status == "1" ? "禁用" : "启用"}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          editUserPushStatus({ status: this.status, userId: 0 }).then((res) => {

            if (res.code == 200) {
              this.$message.success("修改成功");
            }
          });
        })
        .catch((err) => {
          getUserAccountType().then((res) => {
            this.status = res.pushStatus;
          });
        });
    },
  },
  mounted() {
    this.getList();
    getUserTotal().then((res) => {
      this.dayCount = res.dayCount;
      this.statusCount = res.statusCount;
    });
    getUserAccountType().then((res) => {
      this.isMain = res.isMain;
      this.status = res.pushStatus;
    });
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.tags {
  background-color: red;
  padding: 10px 20px;
  opacity: 0.4;
  color: #ffffff;
  display: inline-block;
  margin-bottom: 20px;
}
.contact {
  font-size: 20px;
  margin-bottom: 20px;
  span {
    color: red;
    font-weight: 800;
  }
}
.link {
  cursor: pointer;
  text-decoration: underline;
  color: #1e90ff;
}
.center {
  display: flex;
  justify-content: center;
}
</style>
