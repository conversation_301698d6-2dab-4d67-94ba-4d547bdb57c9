<template>
  <div class="sesame-score-container">
    <!-- 左侧预览区域 -->
    <div class="preview-area">
      <Preview ref="previewRef" :config-data="configData" />
    </div>

    <!-- 右侧控制面板 -->
    <div class="config-panel">
      <Controller 
        @capture-screenshot="handleCapture" 
        :preview-url="screenshotPreviewUrl"
        :config-data="configData"
        @update:config="updateConfig"
      />
    </div>
  </div>
</template>

<script>
import Preview from './components/preview.vue'
import Controller from './components/controller.vue'

export default {
  name: 'SesameScore',
  components: {
    Preview,
    Controller
  },
  data() {
    return {
      screenshotPreviewUrl: null,
      configData: {
        device: {
          os: 'ios' // 设备系统类型，可选值 'ios' 或 'android'
        },
        // 状态栏配置
        statusBar: {
          time: '9:41',
          showSignal: true,
          showWifi: true,
          showBattery: true,
          batteryLevel: 100, // 电池电量百分比(40-100)
          mode: 'light' // 状态栏模式，可选值 'light' 或 'dark'
        },
        // 用户信息
        userName: '*贤',
        // 芝麻分数据
        score: 730,
        // 圆弧设置
        arcSettings: {
          linkedToScore: true, // 是否关联芝麻分
          percentage: 0.6 // 圆弧进度百分比，0.25-0.9之间
        },
        // 消息数据
        messageCount: 30,
        messages: [
          {
            title: '查收你的3月份月报',
            date: '04月07日'
          },
          {
            title: '查收你的2月份月报',
            date: '03月07日'
          }
        ],
        // 服务数据
        services: {
          sesameGrain: {
            title: '芝麻粒',
            subtitle: '9粒待攒',
            showDot: true
          },
          quota: {
            title: '我的额度',
            subtitle: '淘宝待评估',
            showDot: true
          },
          sesameCard: {
            title: '芝麻名片',
            subtitle: '找对象搞副业',
            showDot: false
          }
        },
        // 广告图配置
        adImage: {
          url: 'https://jst.oss-utos.hmctec.cn/common/path/c226a93f56d545afa90124387838ed85.png'
        }
      }
    };
  },
  methods: {
    async handleCapture() {
      if (this.$refs.previewRef) {
        const dataUrl = await this.$refs.previewRef.captureScreenshot();
        if (dataUrl) {
          this.screenshotPreviewUrl = dataUrl;
        } else {
          this.screenshotPreviewUrl = null;
        }
      } else {
        console.error('Preview component reference not found.');
      }
    },
    updateConfig(newConfig) {
      this.configData = { ...this.configData, ...newConfig };
    }
  }
}
</script>

<style lang="scss" scoped>
.sesame-score-container {
  display: flex;
  background-color: #fff;
  
  .preview-area {
    padding: 20px;
    background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    background-color: #ffffff;
    height: max-content;
  }
  
  .config-panel {
    flex: 1;
    margin-left: 20px;
    padding: 20px;
    overflow-y: auto;
  }
}
</style>
