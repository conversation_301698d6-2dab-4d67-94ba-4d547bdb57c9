<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      border
    >
      <el-table-column
        prop="id"
        label="平台id"
        align="center"
      />
      <el-table-column
        prop="name"
        label="平台名称"
        align="center"
      />
      <el-table-column
        prop="source"
        label="平台标识"
        align="center"
      />
      <el-table-column
        prop="vipLevel"
        label="vip级别"
        align="center"
      />
      <el-table-column
        prop="status"
        label="状态"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag :type="scope.row.status == 1 ? 'success' : 'danger'">
            {{ scope.row.status == 1 ? '启用' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="priceFactor"
        label="比价系数"
        align="center"
      >
        <template slot-scope="scope">
          <el-input-number 
            v-model="scope.row.priceFactor" 
            :min="0"
            :max="999999"
            :precision="2"
            :controls="false"
            style="width: 120px"
            @blur="handlePriceFactorBlur(scope.row)"
            @focus="handlePriceFactorFocus(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getPlatformPriceFactorList, updatePlatformPriceFactor } from '@/api/xmxrChannelManage/platformPriceFactor'
import Pagination from '@/components/Pagination'

export default {
  name: 'PlatformPriceFactor',
  components: { Pagination },
  data() {
    return {
      loading: false,
      total: 0,
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      currentPriceFactor: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    async getList() {
      const res = await getPlatformPriceFactorList(this.queryParams)
      this.tableData = res.rows
      this.total = res.total
    },
    handlePriceFactorFocus(row) {
      this.currentPriceFactor = row.priceFactor
    },
    handlePriceFactorBlur(row) {
      if (row.priceFactor == this.currentPriceFactor) return
      
      if (row.priceFactor < 0 || row.priceFactor > 999999) {
        this.$message.error('请输入0-999999之间的数字')
        row.priceFactor = this.currentPriceFactor
        return
      }

      this.$confirm('确定修改比价系数吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await updatePlatformPriceFactor({
          id: row.id,
          priceFactor: row.priceFactor
        })
        if (res.code == 200) {
          this.$message.success('更新成功')
          this.getList()
        }
      }).catch(() => {
        row.priceFactor = this.currentPriceFactor
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
</style> 