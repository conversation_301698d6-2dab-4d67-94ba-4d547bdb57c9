<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item label="手机号">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入手机号"
          size="small"
          clearable
          v-model="queryParams.phone"
        ></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          v-model="queryParams.status"
          clearable
          size="small"
          placeholder="请选择状态"
        >
          <el-option
            v-for="item in options"
            :key="item.businessId"
            :label="item.businessName"
            :value="item.businessId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="渠道">
        <el-select
          clearable
          v-model="statusArr"
          multiple
          size="small"
          collapse-tags
          placeholder="请选择渠道"
        >
          <el-option
            v-for="item in channelIdOptions"
            :key="item.value"
            :label="'id:' + item.value + '--' + item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="查询时间">
        <el-date-picker
          :default-time="['00:00:00', '23:59:59']"
          value-format="yyyy-MM-dd HH:mm:ss"
          size="small"
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker
      ></el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border @sort-change="handleSortChange">
      <el-table-column
        label="渠道ID"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="channelId"
        align="center"
      >
      </el-table-column>
      <el-table-column label="手机号" prop="phone" align="center">
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
      </el-table-column>
      <el-table-column
        label="查询时间"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="selectTime"
      >
      </el-table-column>
      <el-table-column label="注册时间" align="center" prop="registerTime">
      </el-table-column>
      <el-table-column label="申请记录" align="center" prop="applicationRecord">
      </el-table-column>
      <el-table-column label="咨询记录" align="center" prop="searchRecord">
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getUserPhoneStatusList,
  getUserPhoneStatusOption,
  getUserPhoneStatusChannel,
} from "@/api/nameManage";
export default {
  data() {
    return {
      options: [],
      total: 0,
      channelIdOptions: [],
      queryParams: {
        phone: "",
        pageNum: 1,
        pageSize: 10,
        selectEndTime: "",
        selectStartTime: "",
        status: "",
        orderByColumn: "channelId",
        isAsc: "descending",
      },
      statusArr: [],
      dateRange: [],
      dataList: [],
    };
  },
  methods: {
    getList() {
      if (this.dateRange && this.dateRange.length) {
        this.queryParams.selectStartTime = this.dateRange[0];
        this.queryParams.selectEndTime = this.dateRange[1];
      } else {
        this.queryParams.selectStartTime = "";
        this.queryParams.selectEndTime = "";
      }
      this.queryParams.channelId = this.statusArr.join(",");
      getUserPhoneStatusList(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.total = res.total;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
  },
  mounted() {
    this.getList();
    getUserPhoneStatusOption().then((res) => {
      this.options = res.data;
    });
    getUserPhoneStatusChannel().then((res) => {
      this.channelIdOptions = res.data;
    });
  },
};
</script>

<style lang="scss" scoped></style>
