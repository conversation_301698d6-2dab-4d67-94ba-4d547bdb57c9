<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入渠道名称"
          size="small"
          clearable
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table border :data="dataList">
      <el-table-column label="渠道ID" prop="id" align="center" />
      <el-table-column label="渠道名称" prop="channelName" align="center" />
      <el-table-column label="咨询数" prop="successCount" align="center" />
      <el-table-column label="咨询失败数" prop="failCount" align="center" />
      <el-table-column label="失败原因占比" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button type="primary" size="mini" @click="getFailDetails(row)"
              >查看详情</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getForNonAdmittance } from "@/api/statisticalManage";
export default {
  data() {
    return {
      value1: [this.getWeekTime(), this.getTime()],
      dataList: [],
      queryParams: {
        channelName: "",
        // pageNum: 1,
        // pageSize: 1000000,
        startTime: this.getWeekTime(),
        endTime: this.getTime(),
      },
    };
  },
  methods: {
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    getWeekTime() {
      let wekDay = Date.now() - 86400 * 7 * 1000;
      var date = new Date(parseInt(wekDay));
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    //搜索
    handleQuery() {
      if (this.value1 !== null) {
        // this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },
    getFailDetails(row) {
      this.$router.push({
        path: "/statisticalManage/failDetail/",
        query: {
          channelId: row.id,
          name: row.channelName,
          startTime: this.queryParams.startTime,
          endTime: this.queryParams.endTime,
        },
      });
    },
    getList() {
      getForNonAdmittance(this.queryParams).then((res) => {
        this.dataList = res.data;
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
