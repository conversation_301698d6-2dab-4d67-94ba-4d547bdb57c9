<template>
  <el-card shadow="never">
    <div slot="header" class="card-header">
      <span>{{ title }}</span>
      <el-button type="text" icon="el-icon-plus" @click="handleAddGroup">新增分组</el-button>
    </div>
    <div class="card-content">
      <div class="group-items-wrapper">
        <div v-if="!value.length" class="empty-content">
          <el-empty :image-size="60">
            <template slot="description">
              <el-button type="text" @click="handleAddGroup">暂无分组，点击新增分组</el-button>
            </template>
          </el-empty>
        </div>
        <draggable 
          v-else 
          v-model="groups" 
          :group="{ name: `${type}-${groupKey}` }"
          @end="onDragEnd"
        >
          <div 
            v-for="(group, index) in groups" 
            :key="group.renderKey"
            class="group-item"
          >
            <div class="group-title">
              <span>{{ group.groupName }}</span>
              <div class="group-actions">
                <el-button type="text" @click="handleEditGroup(index)">编辑</el-button>
                <el-button type="text" @click="handleDeleteGroup(index)">删除</el-button>
              </div>
            </div>
            <div class="group-content">
              <div class="platform-list">
                <el-tag 
                  v-for="platform in group.productList" 
                  :key="platform.platformType" 
                  closable 
                  type="info" 
                  class="platform-tag"
                  @close="handleRemoveProduct(index, platform)"
                >
                  {{ platform.platform }}
                </el-tag>
              </div>
            </div>
          </div>
        </draggable>
      </div>
    </div>

    <!-- 使用平台分组编辑弹窗组件 -->
    <platform-group-dialog
      :visible.sync="dialogVisible"
      :title="dialogTitle"
      :platform-list="processedPlatformList"
      :edit-data="currentEditData"
      @save="handleSaveGroup"
    />
  </el-card>
</template>

<script>
import draggable from 'vuedraggable'
import PlatformGroupDialog from './PlatformGroupDialog.vue'

export default {
  name: 'PlatformGroup',

  components: {
    draggable,
    PlatformGroupDialog
  },

  props: {
    // 分组数据
    value: {
      type: Array,
      default: () => []
    },
    // 分组的唯一标识，用于拖拽分组
    groupKey: {
      type: String,
      required: true
    },
    // 平台类型
    type: {
      type: String,
      required: true
    },
    // 平台标题
    title: {
      type: String,
      required: true
    },
    // 平台列表数据
    platformList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      groups: this.value,
      dialogVisible: false,
      dialogTitle: '',
      currentEditIndex: -1,
      currentEditData: {}
    }
  },

  computed: {
    // 处理平台列表，禁用已选择的平台
    processedPlatformList() {
      if (!this.platformList.length) return []

      // 获取当前平台下其他分组已选择的平台
      const selectedPlatforms = new Set()
      if (this.groups && this.groups.length) {
        this.groups.forEach((group, index) => {
          if (index !== this.currentEditIndex && group.productList && group.productList.length) {
            group.productList.forEach(product => {
              selectedPlatforms.add(product.platformType)
            })
          }
        })
      }

      // 更新平台列表的禁用状态
      return this.platformList.map(platform => ({
        ...platform,
        disabled: selectedPlatforms.has(platform.id)
      }))
    }
  },

  watch: {
    value: {
      handler(val) {
        this.groups = val
      },
      deep: true
    },
    groups: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  },

  methods: {
    // 处理添加分组
    handleAddGroup() {
      this.dialogTitle = '新增分组'
      this.currentEditIndex = -1
      this.currentEditData = {}
      this.dialogVisible = true
    },

    // 处理编辑分组
    handleEditGroup(index) {
      this.dialogTitle = '编辑分组'
      this.currentEditIndex = index
      this.currentEditData = { ...this.groups[index] }
      this.dialogVisible = true
    },

    // 处理删除分组
    handleDeleteGroup(index) {
      this.$confirm('确认删除该分组吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.groups.splice(index, 1)
      }).catch(() => {})
    },

    // 处理移除产品
    handleRemoveProduct(groupIndex, product) {
      const group = this.groups[groupIndex]
      if (!group) return

      // 如果只剩一个平台，不允许删除
      if (group.productList.length <= 1) {
        this.$message.warning('分组至少需要保留一个平台')
        return
      }

      this.$confirm('确认移除该平台吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = group.productList.findIndex(item => item.platformType == product.platformType)
        if (index > -1) {
          group.productList.splice(index, 1)
        }
      }).catch(() => {})
    },

    // 处理保存分组
    handleSaveGroup(formData, callback) {
      // 检查分组名是否重复
      const isDuplicate = this.groups.some((item, index) => {
        return item.groupName == formData.groupName && 
          index !== this.currentEditIndex &&
          item.renderKey !== formData.renderKey
      })

      if (isDuplicate) {
        this.$message.error('该平台下已存在相同名称的分组')
        callback(false)
        return
      }

      if (this.currentEditIndex == -1) {
        // 新增
        formData.sort = this.groups.length > 0 
          ? Math.max(...this.groups.map(item => item.sort || 0)) + 1 
          : 1
        this.groups.push(formData)
      } else {
        // 编辑
        Object.assign(this.groups[this.currentEditIndex], formData)
      }

      callback(true)
    },

    // 处理拖拽结束
    onDragEnd() {
      // 更新排序
      this.groups.forEach((item, index) => {
        item.sort = index + 1
      })

      // 根据sort字段重新排序
      this.groups.sort((a, b) => a.sort - b.sort)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-card__header {
  padding: 0;
}

.card-header {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .el-button {
    padding: 0;
  }
}

.card-content {
  .empty-content {
    padding: 20px 0;
    
    ::v-deep .el-button {
      font-size: 14px;
      padding: 0;
    }
  }

  .group-items-wrapper {
    display: flex;
    flex-direction: column;
    margin: -10px;

    .group-item {
      width: calc(100% - 20px);
      margin: 10px;
      border: 1px solid #EBEEF5;
      border-radius: 4px;
      cursor: move;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      &.sortable-ghost {
        opacity: 0.5;
        background: #f5f7fa;
      }

      &.sortable-drag {
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .group-title {
        padding: 8px 12px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #EBEEF5;
        font-size: 13px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .group-actions {
          .el-button {
            padding: 2px 6px;

            & + .el-button {
              margin-left: 4px;
            }
          }
        }
      }

      .group-content {
        padding: 12px;

        .platform-list {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          padding: 10px 0;

          .platform-tag {
            margin-right: 0;
            max-width: 100%;

            &.el-tag {
              display: inline-flex;
              align-items: center;
              height: auto;
              padding: 6px 10px;
              line-height: 1.4;
              white-space: normal;
              word-break: break-all;
            }
          }
        }
      }
    }
  }
}
</style> 