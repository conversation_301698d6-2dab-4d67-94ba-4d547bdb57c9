<template>
  <div class="capture-button-container">
    <el-button type="primary" @click="handleCaptureClick" size="mini">生成截图</el-button>
    
    <!-- 预览弹窗 -->
    <el-dialog
      title="截图预览"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="preview-dialog-content">
        <div class="preview-actions">
          <el-button type="primary" icon="el-icon-download" @click="handleDownload">下载图片</el-button>
        </div>
        <div class="preview-background">
          <img :src="previewUrl" alt="Screenshot Preview" class="preview-image" />
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { saveAs } from 'file-saver'

export default {
  name: 'CaptureButton',
  props: {
    previewUrl: {
      type: String,
      default: null
    },
    deviceOs: {
      type: String,
      default: 'ios'
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  watch: {
    previewUrl(newVal) {
      if (newVal) {
        this.dialogVisible = true
      }
    }
  },
  methods: {
    handleCaptureClick() {
      const loading = this.$loading({
        lock: true,
        text: '正在生成截图...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      
      this.$emit('capture-screenshot')
      
      // 监听previewUrl变化来关闭loading
      const checkPreview = setInterval(() => {
        if (this.previewUrl) {
          loading.close()
          clearInterval(checkPreview)
        }
      }, 100)
    },
    async handleDownload() {
      if (!this.previewUrl) {
        this.$message.warning('没有可下载的预览图');
        return;
      }
      
      try {
        const response = await fetch(this.previewUrl)
        const blob = await response.blob()
        
        let fileName
        if (this.deviceOs === 'ios') {
          // iOS格式：IMG_1234.png
          const randomNum = Math.floor(1000 + Math.random() * 9000)
          fileName = `IMG_${randomNum}.png`
        } else {
          // Android格式：Screenshot_2025-04-18-09-46-14-051_com.eg.android.AlipayGphone.jpg
          const now = new Date()
          const year = now.getFullYear()
          const month = String(now.getMonth() + 1).padStart(2, '0')
          const day = String(now.getDate()).padStart(2, '0')
          const hours = String(now.getHours()).padStart(2, '0')
          const minutes = String(now.getMinutes()).padStart(2, '0')
          const seconds = String(now.getSeconds()).padStart(2, '0')
          const milliseconds = String(now.getMilliseconds()).padStart(3, '0')
          fileName = `Screenshot_${year}-${month}-${day}-${hours}-${minutes}-${seconds}-${milliseconds}.jpg`
        }
        
        saveAs(blob, fileName)
      } catch (error) {
        this.$message.error('下载失败，请重试')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.capture-button-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
  
  .preview-dialog-content {
    text-align: center;
    
    .preview-actions {
      margin-bottom: 15px;
      text-align: right;
    }
    
    .preview-background {
      display: inline-block;
      padding: 20px;
      background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
      background-size: 20px 20px;
      background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
      background-color: #ffffff;
    }
    
    .preview-image {
      max-width: 100%;
      height: auto;
    }
  }
}
</style> 