<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-model="value1"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="渠道" >
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入渠道名称"
          size="small"
          clearable
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item label="筛选渠道" >
        <el-select
          ref="select"
          size="small"
          v-model="checkValue"
          multiple
          clearable
          filterable
          collapse-tags
          placeholder="请选择"
        >
          <el-option
            v-for="item in optionsList"
            :key="item.channelId"
            :label="`id:${item.channelId}--${item.channelName}`"
            :value="item.channelId"
          >
          </el-option>
        </el-select>
      </el-form-item> -->

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >

      </el-form-item>

    </el-form>

    <el-table :data="dataList" border>
      <el-table-column label="渠道ID" align="center" prop="channelId" />
      <el-table-column label="渠道名称" align="center" prop="channelName" />
      <el-table-column label="渠道状态" align="center">
        <template slot-scope="{ row }">
          <div v-if="row.channelStatus">
            {{ row.channelStatus == 1 ? "正常" : "禁用" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="落地页UV" align="center" prop="uvNum" />
      <el-table-column label="落地页UA" align="center" prop="uaNum" />

      <el-table-column label="落地页注册数" align="center" prop="registerNum" />
      <el-table-column label="落地页注册率" align="center">
        <template slot-scope="scope">
          <div
            :class="[
              scope.$index &&
              scope.row.uvNum &&
              scope.row.registerNum &&
              Number(
                ((dataList[0].registerNum / dataList[0].uvNum) * 100).toFixed(2)
              ) >
                Number(
                  scope.row.uvNum
                    ? ((scope.row.registerNum / scope.row.uvNum) * 100).toFixed(
                        2
                      )
                    : 0
                )
                ? 'warning'
                : '',
            ]"
          >
            {{
              scope.row.uvNum
                ? ((scope.row.registerNum / scope.row.uvNum) * 100).toFixed(2)
                : "0.00"
            }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column label="咨询订单提交数量" align="center" prop="fromNum" />
      <el-table-column label="咨询数" align="center" prop="h5PushNum" />
      <el-table-column label="H5转化率" align="center" prop="">
        <template slot-scope="scope">
          <div>
            {{
              scope.row.uvNum
                ? ((scope.row.fromNum / scope.row.uvNum) * 100).toFixed(2)
                : 0
            }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="H5线上产品数量"
        align="center"
        prop="onlineApplyNum"
      />
      <el-table-column
        label="APP点击下载"
        align="center"
        prop="appDownloadNum"
      />
      <el-table-column label="APP登录数" align="center" prop="appLoginNum" />
      <el-table-column label="APP登录率" align="center" prop="registerRatio">
        <template slot-scope="{ row }">
          <div>
            {{
              row.appDownloadNum
                ? ((row.appLoginNum / row.appDownloadNum) * 100).toFixed(2)
                : 0
            }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column label="APP申请量" align="center" prop="appApplyNum" />
      <el-table-column
        label="APP申请人数"
        align="center"
        prop="appApplyPeopleNum"
      />
      <!-- <el-table-column label="付费人数" align="center" prop="payPeopleNum" />
      <el-table-column label="付费金额" align="center" prop="payPrice" /> -->
      <el-table-column label="预估收益" align="center" prop="profit" />
    </el-table>
    <!-- <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    /> -->

  </div>
</template>

<script>
import {
  getNewChannelStatisticsList,

} from "@/api/statisticalManage";
export default {
  name: "ChannelDetail",
  data() {
    return {
      checkValue: [],
      optionsList: [],
      value1: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      value2: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],


      dataList: [],
      matchList: [],
      queryParams: {
        channelName: "vv",
        pageNum: 1,
        pageSize: 10,
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
      },
      queryParams1: {
        channelName: "",
        pageNum: 1,
        pageSize: 10,
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
      },
      pickerOptions: {
        disabledDate(time) {
          console.log(time);
          return time.getTime() > Date.now() + 86100 * 1000;
        },
      },
    };
  },
  methods: {

    handleQuery() {
      if (this.value1 !== null) {
        this.queryParams.pageNum = 1;
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
      this.getList();
    },

    getTotal(arr, type) {
      return arr.reduce((pre, data) => {
        return pre + data[type];
      }, 0);
    },
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    getList() {

      getNewChannelStatisticsList(this.queryParams).then((res) => {
        this.dataList = res.rows;
        this.optionsList = JSON.parse(JSON.stringify(res.rows));

        // this.dataList.unshift({
        //   channelId: "合计",
        //   uvNum: this.getTotal(this.dataList, "uvNum"),
        //   uaNum: this.getTotal(this.dataList, "uaNum"),
        //   registerNum: this.getTotal(this.dataList, "registerNum"),
        //   fromNum: this.getTotal(this.dataList, "fromNum"),
        //   h5PushNum: this.getTotal(this.dataList, "h5PushNum"),
        //   appDownloadNum: this.getTotal(this.dataList, "appDownloadNum"),
        //   appLoginNum: this.getTotal(this.dataList, "appLoginNum"),
        //   appApplyPeopleNum: this.getTotal(this.dataList, "appApplyPeopleNum"),
        //   appApplyNum: this.getTotal(this.dataList, "appApplyNum"),
        //   payPeopleNum: this.getTotal(this.dataList, "payPeopleNum"),
        //   payPrice: this.getTotal(this.dataList, "payPrice").toFixed(2),
        //   profit: this.getTotal(this.dataList, "profit").toFixed(2),
        //   onlineApplyNum: this.getTotal(this.dataList, "onlineApplyNum"),
        // });
      });
    },
    getMatchList() {
      getChannelMatchInfoStats(this.queryParams1).then((res) => {
        this.matchList = res.data;
      });
    },
    handleQuery1() {
      if (this.value2 !== null) {
        this.queryParams1.pageNum = 1;
        this.queryParams1.startTime = this.value2[0];
        this.queryParams1.endTime = this.value2[1];
      } else {
        this.queryParams1.startTime = "";
        this.queryParams1.endTime = "";
      }
      this.getMatchList();
    },
    handleFail() {
      this.value2 = [
        this.getTime() + " 00:00:00",
        this.getTime() + " 23:59:59",
      ];
      this.queryParams1.startTime = `${this.getTime()} 00:00:00`;
      this.queryParams1.endTime = `${this.getTime()} 23:59:59`;
      this.dialogVisible = true;
      this.getMatchList();
    },
  },

  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.warning {
  color: red;
}
::v-deep .el-dialog__body {
  border-bottom: none;
}
</style>
