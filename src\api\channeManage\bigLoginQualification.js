import request from '@/utils/request'

// 获取大联登资质撞库列表
export function getBigLoginQualificationList(query) {
  return request({
    url: '/loan/xm/channel/aptitudeLoginControl',
    method: 'get',
    params: query
  })
}

// 更新大联登资质撞库配置
export function updateBigLoginQualification(data) {
  return request({
    url: '/loan/xm/channel/updateAptitudeLoginControl',
    method: 'post',
    data: data
  })
} 