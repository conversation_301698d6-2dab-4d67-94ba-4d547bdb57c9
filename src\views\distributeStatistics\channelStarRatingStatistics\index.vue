<script>
import { getChannelStarRatingStatistics } from "@/api/distributeStatistics/channelStarRatingStatistics";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

export default {
  name: "ChannelStarRatingStatistics",

  data() {
    return {
      queryParams: {
        channelName: "",
        channelId: "",
        pushDate: "",
        pageSize: 10,
        pageNum: 1,
      },
      openAll: false,
      refreshTable: true,
      tableData: [],
      total: 0,
    };
  },

  mounted() {
    this.setDefaultDate();
    this.fetchTableData();
  },

  methods: {
    setDefaultDate() {
      const today = dayjs();
      this.queryParams.pushDate = today.format("YYYY-MM-DD");
    },

    getParams() {
      return {
        pushDate: this.queryParams.pushDate,
        channelName: this.queryParams.channelName,
        channelId: this.queryParams.channelId,
        pageSize: this.queryParams.pageSize,
        pageNum: this.queryParams.pageNum,
      };
    },

    handleTableData(arr) {
      arr.forEach((row) => {
        row.rowKey = uuidv4();

        if (row.subList && row.subList.length) {
          row.subList.forEach((innerRow) => {
            innerRow.rowKey = uuidv4();
            innerRow.channelId = "";
            innerRow.isInner = true;
          });
        } else {
          row.subList = null;
        }
      });
    },

    async fetchTableData() {
      const params = this.getParams();
      const res = await getChannelStarRatingStatistics(params);

      this.handleTableData(res.rows);
      this.total = res.total;
      this.tableData = res.rows;
    },
    handldeOpen() {
      this.refreshTable = false;
      this.openAll = !this.openAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
  },
};
</script>

<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryParams" size="small">
      <el-form-item label="日期" prop="pushDate">
        <el-date-picker
          v-model="queryParams.pushDate"
          type="date"
          size="small"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          @change="fetchTableData"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="渠道名称">
        <el-input
          v-model="queryParams.channelName"
          placeholder="请输入渠道名称"
          @keyup.enter.native="fetchTableData"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道id">
        <el-input
          v-model="queryParams.channelId"
          placeholder="请输入渠道id"
          @keyup.enter.native="fetchTableData"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="fetchTableData">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handldeOpen">展开/收缩</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      style="width: 100%"
      v-if="refreshTable"
      :default-expand-all="openAll"
      row-key="rowKey"
      :tree-props="{
        children: 'subList',
      }"
    >
      <el-table-column prop="channelId" label="渠道id"></el-table-column>
      <el-table-column prop="channelName" label="渠道">
        <template slot-scope="{ row }">
          <template v-if="row.channelName">
            <el-tag size="small" type="info" v-if="row.isInner">{{
              row.channelName
            }}</el-tag>
            <el-tag size="small" v-else>{{ row.channelName }}</el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="zeroStarRate" label="0星率" sortable>
        <template slot-scope="{ row }">
          {{ `${row.zeroStarNum}（${row.zeroStarRate}%）` }}
        </template>
      </el-table-column>
      <el-table-column prop="overThreeStarRate" label="3星及以上" sortable>
        <template slot-scope="{ row }">
          {{ `${CALCULATE.sum(row.threeStarNum, row.fourStarNum, row.fiveStarNum)}（${row.overThreeStarRate}%）` }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="fetchTableData"
    />
  </div>
</template>

<style scoped lang="scss"></style>
