<template>
  <div class="dashboard-container">
    <!-- 用户名字 -->
    <div class="user-name">
      Hi，<span class="masked-name">{{ maskedUserName }}</span><span>{{ lastChar }}</span>
    </div>
    <!-- 更多服务 -->
    <div class="more-service">
      <span class="more-service-text">更多服务</span>
    </div>
    <!-- 仪表盘 -->
    <div class="dashboard">
      <div class="dashboard-inner">
        <img src="https://jst.oss-utos.hmctec.cn/common/path/045dc1097cd7422cb913919c51ea7892.png"
          class="background-image" alt="Dashboard Background">
        <img src="https://jst.oss-utos.hmctec.cn/common/path/c6275da735324d3eb388a2be0004d9e2.png"
          class="graduation-lines" alt="Graduation Lines">
        <canvas class="progress-canvas" :id="canvasId" width="286" height="286"></canvas>
        <div class="value-display">
          <div class="value-display-text">{{ creditLevel }}</div>
          <div class="value-display-score">{{ score }}</div>
          <div class="value-display-button">去涨分
            <img src="https://jst.oss-utos.hmctec.cn/common/path/b7f047a24b854f709e221c1d7d676469.png" alt="button-bg">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  props: {
    userName: {
      type: String,
      default: ''
    },
    score: {
      type: Number,
      default: 650
    },
    arcSettings: {
      type: Object,
      default: () => ({
        linkedToScore: true,
        percentage: 0.5
      })
    }
  },
  data() {
    return {
      canvasId: `progress-canvas-${this._uid}`
    }
  },
  computed: {
    maskedUserName() {
      const name = this.userName || '';
      if (name.length <= 1) return '';
      return '*'.repeat(name.length - 1);
    },
    lastChar() {
      const name = this.userName || '';
      return name.slice(-1);
    },
    creditLevel() {
      const score = this.score;
      const scoreRanges = [
        { min: 700, max: 950, level: '极好' },
        { min: 650, max: 700, level: '优秀' },
        { min: 600, max: 650, level: '良好' },
        { min: 550, max: 600, level: '中等' },
        { min: 350, max: 550, level: '较差' }
      ];

      for (const range of scoreRanges) {
        if (score >= range.min && score <= range.max) {
          return `信用${range.level}`;
        }
      }
      return '信用较差';
    }
  },
  mounted() {
    this.drawCanvasArc();
  },
  watch: {
    score() {
      this.drawCanvasArc();
    },
    arcSettings: {
      handler() {
        this.drawCanvasArc();
      },
      deep: true
    }
  },
  methods: {
    drawCanvasArc() {
      const canvas = document.getElementById(this.canvasId);
      if (!canvas || !canvas.getContext) {
        console.error("Canvas element not found or context not supported.");
        return;
      }
      const ctx = canvas.getContext('2d');

      // 获取设备像素比
      // const dpr = window.devicePixelRatio || 1;
      const dpr = 1.5;
      const rect = canvas.getBoundingClientRect();

      // 设置canvas的实际大小
      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;

      // 设置canvas的CSS显示大小
      canvas.style.width = `${rect.width}px`;
      canvas.style.height = `${rect.height}px`;

      // 根据dpr缩放canvas上下文
      ctx.scale(dpr, dpr);

      const width = rect.width;
      const height = rect.height;

      // 启用抗锯齿
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';

      // Clear canvas before drawing
      ctx.clearRect(0, 0, width, height);

      // Define arc parameters
      const centerX = width / 2;
      const centerY = height / 2;
      const radius = 119;
      const lineWidth = 26;
      const startAngleRad = 149 * (Math.PI / 180); // 149 degrees in radians
      const totalAngleRad = 240 * (Math.PI / 180); // Total arc span is 240 degrees

      // 计算百分比 - 根据设置选择不同的计算方式
      let percentage;
      if (this.arcSettings.linkedToScore) {
        // 使用芝麻分计算百分比
        const minScore = 350;
        const maxScore = 950;
        const totalRange = maxScore - minScore;
        // 先基于分数计算百分比，然后确保在25%~90%范围内
        percentage = Math.max(0, Math.min(1, (this.score - minScore) / totalRange)); // Clamp between 0 and 1
        percentage = Math.max(0.25, Math.min(0.9, percentage)); // 确保在25%~90%范围内
      } else {
        // 使用自定义百分比
        percentage = Math.max(0.25, Math.min(0.9, this.arcSettings.percentage)); // 确保在25%~90%范围内
      }

      const currentAngleSpanRad = percentage * totalAngleRad;
      const endAngleRad = startAngleRad + currentAngleSpanRad;

      // 创建与当前圆弧匹配的渐变
      // 计算圆弧的起点和终点坐标
      const startX = centerX + radius * Math.cos(startAngleRad);
      const startY = centerY + radius * Math.sin(startAngleRad);
      const endX = centerX + radius * Math.cos(endAngleRad);
      const endY = centerY + radius * Math.sin(endAngleRad);

      // 创建沿着圆弧的渐变，而不是固定的水平渐变
      const gradient = ctx.createLinearGradient(startX, startY, endX, endY);
      const startColor = { r: 166, g: 210, b: 255 }; // #A6D2FF (蓝色)
      const endColor = { r: 255, g: 255, b: 255 }; // #FFFFFF (白色)

      // 添加渐变颜色停止点
      gradient.addColorStop(0, `rgb(${startColor.r}, ${startColor.g}, ${startColor.b})`);
      gradient.addColorStop(1, `rgb(${endColor.r}, ${endColor.g}, ${endColor.b})`);

      // Set drawing styles for the main arc
      ctx.lineWidth = lineWidth;
      ctx.strokeStyle = gradient;
      ctx.lineCap = 'butt'; // Set to butt for flat start

      // Draw the main arc
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, startAngleRad, endAngleRad, false);
      ctx.stroke();

      // --- Add round cap manually at the end --- 
      if (percentage > 0) { // Only add cap if there is some arc
        // Calculate end point coordinates
        const endX = centerX + radius * Math.cos(endAngleRad);
        const endY = centerY + radius * Math.sin(endAngleRad);

        // 使用渐变的终点颜色（白色）作为圆形端点的颜色
        const capColor = `rgb(${endColor.r}, ${endColor.g}, ${endColor.b})`;

        // Draw the round cap (filled circle)
        ctx.beginPath();
        ctx.arc(endX, endY, lineWidth / 2, 0, 2 * Math.PI); // Full circle
        ctx.fillStyle = capColor;
        ctx.fill();
      }
    }
  }
}
</script>

<style scoped lang="scss">
.dashboard-container {
  margin-top: 30px;
  position: relative;
  z-index: 1;

  .user-name {
    font-family: 'SourceHanSansSC-Medium';
    position: absolute;
    top: 27px;
    left: 38px;
    color: #fff;
    font-size: 34px;
    line-height: 49px;

    .masked-name {
      font-family: 'SanFranciscoText-Medium';
      vertical-align: top;
      letter-spacing: 4px;
    }
  }

  .more-service {
    font-family: 'SourceHanSansSC-Medium';
    position: absolute;
    top: 42px;
    right: 0px;
    padding: 16px 17px;
    color: #fff;
    font-size: 24px;
    line-height: 32px;
    width: 130px;
    height: 66px;
    background: rgba(255, 255, 255, 0.15);
    border-top-left-radius: 16px;
    border-bottom-left-radius: 16px;
  }
}

.dashboard {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .dashboard-inner {
    position: relative;
    width: 286px;
    height: 286px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .background-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 286px;
      height: 286px;
      z-index: 1;
    }

    .graduation-lines {
      position: absolute;
      top: 24px;
      left: 50%;
      width: 250px;
      height: auto;
      transform: translateX(-50%);
      z-index: 3;
    }

    .progress-canvas {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
    }

    .value-display {
      position: absolute;
      top: 80px;
      left: 50%;
      transform: translate(-50%, 0);
      text-align: center;
      z-index: 4;

      .value-display-text {
        font-family: 'SourceHanSansSC-Medium';
        font-size: 24px;
        line-height: 24px;
        color: #fff;
      }

      .value-display-score {
        margin-top: 14px;
        margin-bottom: 20px;
        font-family: 'DIN-Bold';
        font-size: 80px;
        font-weight: 700;
        color: #fff;
        line-height: 60px;
      }

      .value-display-button {
        position: relative;
        font-family: 'SourceHanSansSC-Medium';
        width: 196px;
        height: 74px;
        font-size: 28px;
        line-height: 70px;
        text-align: center;
        color: #FFFFFF;
        background: #0127FE;
        border-radius: 600px;
        border-top: 1px solid #fff;
        border-left: 1px solid #ffffffc5;
        border-right: 1px solid #ffffffc5;
        border-bottom: 1px solid #fff;

        img {
          position: absolute;
          bottom: -1px;
          left: 0;
          width: 196px;
          height: 74px;
          pointer-events: none;
        }
      }
    }
  }
}
</style> 