<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item label="短信模板名称">
        <el-input
          size="mini"
          v-model="queryParams.name"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >添加</el-button
        >
      </el-form-item>
    </el-form>

    <el-table border :data="tableList">
      <el-table-column label="短信模板名称" prop="name" align="center" />
      <el-table-column label="接口请求账号" prop="username" align="center" />
      <el-table-column label="接口请求密码" prop="pwd" align="center" />
      <el-table-column label="接口地址" prop="url" align="center" />
      <el-table-column label="状态" align="center">
        <template  slot-scope="{row}">
          <div>
            {{ row.status == 1 ? "有效" : "无效" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" align="center" />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="操作" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="handEdit(row)"
              >修改信息</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="isadd ? '新增短信配置' : '修改短信配置'"
      :visible.sync="avisible"
      width="700px"
      append-to-body
      center
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="rules"
        label-width="150px"
      >
        <el-form-item label="短信模板名称" prop="name">
          <el-input
            v-model.trim="formData.name"
            placeholder="请输入短信模板名称"
          />
        </el-form-item>
        <el-form-item label="接口请求账号" prop="username">
          <el-input
            v-model.trim="formData.username"
            placeholder="请输入接口请求账号"
          />
        </el-form-item>
        <el-form-item label="接口请求密码" prop="pwd">
          <el-input
            v-model.trim="formData.pwd"
            placeholder="请输入接口请求密码"
          />
        </el-form-item>
        <el-form-item label="接口地址" prop="url">
          <el-input v-model.trim="formData.url" placeholder="请输入接口地址" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio v-model="formData.status" :label="1">有效</el-radio>
          <el-radio v-model="formData.status" :label="2">无效</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getConfigList,
  addConfigOne,
  editConfigOne,
} from "@/api/saas/smsConfig";
export default {
  data() {
    return {
      tableList: [],
      isadd: true,
      formData: {
        name: "",
        pwd: "",
        url: "",
        username: "",
        status: 1,
      },
      avisible: false,

      queryParams: {
        name: "",
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      rules: {
        name: [
          { required: true, message: "请输入短信模板名称", trigger: "blur" },
        ],
        username: [
          { required: true, message: "请输入接口请求账号", trigger: "blur" },
        ],
        pwd: [
          { required: true, message: "请输入接口请求密码", trigger: "blur" },
        ],
        url: [{ required: true, message: "请输入接口地址", trigger: "blur" }],
        status: [{ required: true, message: "请选择状态", trigger: "blur" }],
      },
    };
  },
  methods: {
    handleQuery() {
      this.getList();
    },
    getList() {
      getConfigList(this.queryParams).then((res) => {
        this.tableList = res.rows;
        this.total = res.total;
      });
    },
    handEdit(row) {
      this.avisible = true;
      this.isadd = false;
      this.formData.status = row.status;
      this.formData.name = row.name;
      this.formData.pwd = row.pwd;
      this.formData.url = row.url;
      this.formData.username = row.username;
      this.formData.id = row.id;
    },
    handleAdd() {
      this.avisible = true;
      this.isadd = true;
      if (this.formData.id) {
        delete this.formData.id;
      }
    },
    submitForm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          if (this.isadd) {
            addConfigOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("新增成功");
                this.getList();
                this.cancel();
              }
            });
          } else {
            editConfigOne(this.formData).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.getList();
                this.cancel();
              }
            });
          }
        }
      });
    },
    cancel() {
      this.avisible = false;
      this.formData = {
        name: "",
        pwd: "",
        url: "",
        username: "",
        status: 1,
      };
      this.$refs.formData.resetFields();
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
