import request from '@/utils/request'

// 分页查询短信渠道配置
export function searchChannelTemplate(query) {
  return request({
    url: '/loan/xm/channel/template/search',
    method: 'post',
    params: query
  })
}

// 添加短信渠道配置
export function createChannelTemplate(data) {
  return request({
    url: '/loan/xm/channel/template',
    method: 'post',
    data: data
  })
}

// 更新短信渠道配置
export function updateChannelTemplate(data) {
  return request({
    url: '/loan/xm/channel/template',
    method: 'put',
    data: data
  })
}

// 根据id删除短信渠道配置
export function deleteChannelTemplate(id) {
  return request({
    url: `/loan/xm/channel/template/${id}`,
    method: 'delete'
  })
} 