import request from '@/utils/request'

/**
 * 获取所有短信模板
 * @returns {Promise<Object>} 返回短信模板列表数据
 * @property {Array} data 短信模板数据列表
 * @property {Number} data[].id 模板id
 * @property {String} data[].name 模板名称
 * @property {String} data[].content 模板内容(内容请把未知数填写成#{0},#{1})
 * @property {Number} data[].genre 类型名称/1.验证码短信/2.群发短信/3.宣传短信
 * @property {Number} data[].status 状态：1.启动/2.禁用
 * @property {Number} data[].createdate 注册时间 存放的是当前时间轴10位
 * @property {Number} data[].templateId 短信配置模板ID
 * @property {String} data[].templateName 短信配置模板名称
 * @property {String} data[].smsTemplateKey 模板key
 * @property {Number} data[].parametersNumber 参数个数
 * @property {String} data[].createTime 创建时间
 * @property {String} data[].createBy 创建人
 * @property {String} data[].updateTime 修改时间
 * @property {String} data[].updateBy 修改人
 */
export function getSmsTemplates() {
  return request({
    url: '/loan/sms/notify/dreams',
    method: 'get'
  })
}

/**
 * 添加通知短信策略
 * @param {Object} data 短信策略数据
 * @param {Number} [data.id] 更新传，添加不传
 * @param {String} data.name 策略名称
 * @param {Array<Number>} data.channelIds 渠道id数组
 * @param {Array} data.policies 策略列表
 * @param {String} data.policies[].smsKey 短信key
 * @param {Number} data.policies[].weight 权重,所有启用的策略加起来之和必须等于100
 * @param {Boolean} data.policies[].enabled true: 启用 false:禁用
 * @param {String} [data.createTime] 创建时间
 * @param {String} [data.updateTime] 更新时间
 * @param {String} [data.createBy] 创建人
 * @param {String} [data.updateBy] 更新人
 * @param {Number} [data.deleted] 是否删除
 * @returns {Promise<Object>} 返回添加结果
 */
export function addSmsPolicy(data) {
  return request({
    url: '/loan/sms/notify/policy/add',
    method: 'post',
    data
  })
}

/**
 * 更新通知短信策略
 * @param {Object} data 短信策略数据
 * @param {Number} data.id 策略id(更新必传)
 * @param {String} data.name 策略名称
 * @param {Array<Number>} data.channelIds 渠道id数组
 * @param {Array} data.policies 策略列表
 * @param {String} data.policies[].smsKey 短信key
 * @param {Number} data.policies[].weight 权重,所有启用的策略加起来之和必须等于100
 * @param {Boolean} data.policies[].enabled true: 启用 false:禁用
 * @param {String} [data.createTime] 创建时间
 * @param {String} [data.updateTime] 更新时间
 * @param {String} [data.createBy] 创建人
 * @param {String} [data.updateBy] 更新人
 * @param {Number} [data.deleted] 是否删除
 * @returns {Promise<Object>} 返回更新结果
 */
export function updateSmsPolicy(data) {
  return request({
    url: '/loan/sms/notify/policy/update',
    method: 'post',
    data
  })
}

/**
 * 删除通知短信策略
 * @param {Number} id 策略id
 * @returns {Promise<Object>} 返回删除结果
 */
export function deleteSmsPolicy(id) {
  return request({
    url: `/loan/sms/notify/policy/${id}`,
    method: 'delete'
  })
}

/**
 * 搜索通知短信策略
 * @param {Object} query 查询参数
 * @param {Number} query.page 第几页
 * @param {Number} query.size 每页多少条
 * @param {Array} query.channelIds 渠道id数组
 * @param {String} query.name 策略名称
 * @returns {Promise<Object>} 返回策略列表数据
 * @property {Array} records 策略数据列表
 * @property {Number} records[].id 策略id
 * @property {String} records[].name 策略名称
 * @property {Number} records[].channelId 渠道id
 * @property {Array} records[].policies 策略列表
 * @property {Number} total 总数
 */
export function listSmsPolicy(query) {
  return request({
    url: '/loan/sms/notify/policy',
    method: 'post',
    data: query
  })
}

/**
 * 统计短信发送情况
 * @param {Object} params 查询参数
 * @param {String} params.startTime 开始时间 格式:yyyy-MM-dd HH:mm:ss
 * @param {String} params.endTime 结束时间 格式:yyyy-MM-dd HH:mm:ss
 * @returns {Promise<Object>} 返回统计数据
 * @property {Object} data 统计数据，按渠道id分组
 * @property {Array} data[key] 每个渠道的统计数据数组
 * @property {Number} data[key][].total 发送总数
 * @property {String} data[key][].smsKey 短信key
 * @property {String} data[key][].smsName 短信名称
 * @property {Number} data[key][].channelId 渠道id
 * @property {String} data[key][].ratio 占比
 */
export function getStatistics(params) {
  return request({
    url: '/loan/sms/notify/statistics',
    method: 'post',
    params
  })
} 