<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" size="mini">
      <el-form-item label="日期" prop="time">
        <el-date-picker
          v-if="isRangeDate"
          v-model="value1"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleQuery"
        >
        </el-date-picker>

        <template v-else>
          <el-date-picker
            v-model="date"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="选择日期"
            :picker-options="pickerOptions"
            @change="handleQuery"
            :clearable="false"
          >
          </el-date-picker>
          <el-time-picker
            is-range
            value-format="HH:mm:ss"
            v-model="time"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="选择时间范围"
            :clearable="false"
            @change="handleQuery"
          >
          </el-time-picker>
        </template>
      </el-form-item>
      <el-form-item label="渠道" v-if="isShowMore">
        <el-input
          @keyup.enter.native="handleQuery"
          placeholder="请输入渠道名称"
          clearable
          v-model="queryParams.channelName"
        ></el-input>
      </el-form-item>
      <el-form-item label="筛选渠道" v-if="isShowMore">
        <el-select
          ref="select"
          v-model="checkValue"
          multiple
          clearable
          filterable
          collapse-tags
          placeholder="请选择"
        >
          <el-option
            v-for="item in optionsList"
            :key="item.channelId"
            :label="`id:${item.channelId}--${item.channelName}`"
            :value="item.channelId"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          v-if="isShowMore"
          type="primary"
          v-hasPermi="['channel:stats:match_info']"
          icon="el-icon-search"
          size="mini"
          @click="handleFail"
          >查看匹配失败</el-button
        >
      </el-form-item>
      <el-form-item label="惠逸花1" v-if="isShowMore">
        <el-checkbox
          v-model="showZyx"
          :disabled="showMy || showOhter"
        ></el-checkbox>
      </el-form-item>
      <el-form-item label="惠逸花2" v-if="isShowMore">
        <el-checkbox
          v-model="showMy"
          :disabled="showZyx || showOhter"
        ></el-checkbox>
      </el-form-item>
    </el-form>
    <div style="margin-bottom: 10px">
      <el-button
        type="primary"
        size="mini"
        @click="handleTime('00:00:00', '09:00:00')"
        >00:00-09:00</el-button
      >
      <el-button
        type="primary"
        size="mini"
        @click="handleTime('09:00:00', '18:00:00')"
        >09:00-18:00</el-button
      >
      <el-button
        type="primary"
        size="mini"
        @click="handleTime('18:00:00', '23:59:59')"
        >18:00-23:59</el-button
      >
      <el-button
        type="primary"
        size="mini"
        @click="handleTime('12:00:00', '13:30:00')"
        >12:00-13:30</el-button
      >
      <el-button
        type="primary"
        size="mini"
        @click="handleTime('00:00:00', '23:59:59')"
        >全天</el-button
      >
    </div>
    <el-table :data="filterList" border @sort-change="handleSortChange">
      <el-table-column label="渠道ID" align="center" prop="channelId" fixed />
      <el-table-column
        label="渠道名称"
        align="center"
        prop="channelName"
        fixed
      />

      <el-table-column
        label="合作类型"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="apiChannelType"
      >
        <template slot-scope="{ row }">
          <div>
            {{ converType(row.apiChannelType) }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="落地页UV"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="uvNum"
        width="110"
      />
      <!-- <el-table-column label="落地页UA" align="center" prop="uaNum" /> -->

      <el-table-column label="落地页注册数" align="center" prop="registerNum" />
      <el-table-column label="落地页注册率" align="center">
        <template slot-scope="scope">
          <div
            :class="[
              scope.$index &&
              scope.row.uvNum &&
              scope.row.registerNum &&
              Number(
                ((dataList[0].registerNum / dataList[0].uvNum) * 100).toFixed(2)
              ) >
                Number(
                  scope.row.uvNum
                    ? ((scope.row.registerNum / scope.row.uvNum) * 100).toFixed(
                        2
                      )
                    : 0
                )
                ? 'warning'
                : '',
            ]"
          >
            {{
              scope.row.uvNum
                ? ((scope.row.registerNum / scope.row.uvNum) * 100).toFixed(2)
                : "0.00"
            }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="资质提交数量"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="fromNum"
      />
      <el-table-column
        label="匹配通过数量"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="matchNum"
      />
      <el-table-column
        label="咨询数"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        align="center"
        prop="h5PushNum"
      />
      <el-table-column label="H5转化率" align="center" prop="">
        <template slot-scope="scope">
          <div>
            {{
              scope.row.uvNum
                ? ((scope.row.fromNum / scope.row.uvNum) * 100).toFixed(2)
                : 0
            }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="H5线上产品数量"
        align="center"
        prop="onlineApplyNum"
      />
      <el-table-column
        label="APP点击下载"
        align="center"
        prop="appDownloadNum"
      />
      <el-table-column label="APP登录数" align="center" prop="appLoginNum" />
      <el-table-column label="APP登录率" align="center" prop="registerRatio">
        <template slot-scope="{ row }">
          <div>
            {{
              row.appDownloadNum
                ? ((row.appLoginNum / row.appDownloadNum) * 100).toFixed(2)
                : 0
            }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column label="APP申请量" align="center" prop="appApplyNum" />
      <el-table-column
        label="APP申请人数"
        align="center"
        prop="appApplyPeopleNum"
      />

      <!-- <el-table-column label="付费人数" align="center" prop="payPeopleNum" />
      <el-table-column label="付费金额" align="center" prop="payPrice" /> -->

      <el-table-column
        label="线上预估收益"
        align="center"
        width="100"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="onlineProfit"
      >
        <template slot-scope="{ row }">
          <div>
            {{ row.onlineProfit }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="线下预估收益"
        align="center"
        width="100"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="offlineProfit"
      >
        <template slot-scope="{ row }">
          <div>
            {{ row.offlineProfit }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="企微预估收益"
        align="center"
        width="100"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="onlineWeChatProfit"
      >
        <template slot-scope="{ row }">
          <div>
            {{ row.onlineWeChatProfit }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="贷超预估收益"
        align="center"
        width="100"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="onlineDaoChaoProfit"
      >
        <template slot-scope="{ row }">
          <div>
            {{ row.onlineDaoChaoProfit }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="预估收益"
        align="center"
        width="100"
        sortable="custom"
        :sort-orders="['descending', 'ascending']"
        prop="profit"
      >
        <template slot-scope="{ row }">
          <div>
            {{ parseInt(row.profit) }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    /> -->

    <el-dialog
      :visible.sync="dialogVisible"
      title="查看匹配失败"
      append-to-body
      fullscreen
    >
      <el-form :model="queryParams1" ref="queryForm" :inline="true">
        <el-form-item label="日期" prop="time">
          <el-date-picker
            v-model="value2"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            @change="handleQuery1"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="渠道">
          <el-input
            @keyup.enter.native="handleQuery1"
            placeholder="请输入渠道名称"
            size="small"
            clearable
            v-model="queryParams1.channelName"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery1"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
      <el-table :data="matchList" border>
        <el-table-column label="渠道ID" align="center" prop="channelId" />
        <el-table-column label="渠道名称" align="center" prop="channelName" />
        <el-table-column
          label="匹配成功未申请数量"
          align="center"
          prop="matchNum"
        />
        <el-table-column
          label="未匹配成功数量"
          align="center"
          prop="notMatchNum"
        />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  getNewChannelStatisticsList,
  getChannelMatchInfoStats,
} from "@/api/statisticalManage";
import dayjs from 'dayjs'
export default {
  name: "ChannelDetail",
  data() {
    return {
      checkValue: [],
      optionsList: [],
      date: '',
      time: [],
      typeJons: {
        1: "联登",
        2: "半流程",
        3: "api",
        4: "全流程UV",
        5: "信息流",
      },
      value1: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      value2: [this.getTime() + " 00:00:00", this.getTime() + " 23:59:59"],
      total: 0,
      dialogVisible: false,
      showZyx: false,
      showMy: false,
      showOhter: false,
      dataList: [],
      matchList: [],
      queryParams: {
        channelName: "",
        pageNum: 1,
        pageSize: 10,
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
      },
      queryParams1: {
        channelName: "",
        pageNum: 1,
        pageSize: 10,
        startTime: `${this.getTime()} 00:00:00`,
        endTime: `${this.getTime()} 23:59:59`,
      },
      pickerOptions: {
        disabledDate(time) {
          // 仅允许选择最近7天内的日期
          const now = dayjs();
          const sevenDaysAgo = now.subtract(7, 'day');
          return dayjs(time).isAfter(now) || dayjs(time).isBefore(sevenDaysAgo);
        },
      },
    };
  },
  methods: {
    setDefaultDate() {
      if (this.isRangeDate) {

      } else {
        this.date = dayjs().format("YYYY-MM-DD");
        this.time = ["00:00:00", "23:59:59"];
      }
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.isRangeDate) {
        this.queryParams.startTime = this.value1[0];
        this.queryParams.endTime = this.value1[1];
      } else {
        const [startTime, endTime] = this.time;
        this.queryParams.startTime = `${this.date} ${startTime}`;
        this.queryParams.endTime = `${this.date} ${endTime}`;
      }
      this.getList();
    },

    handleTime(start, end) {
      if (this.isRangeDate) {
        let time = this.queryParams.startTime.split(" ");
        this.queryParams.startTime = `${time[0]} ${start}`;
        this.queryParams.endTime = `${time[0]} ${end}`;
        this.value1 = [this.queryParams.startTime, this.queryParams.endTime];
      } else {
        this.time = [start, end];
      }
      this.handleQuery()
    },
    converType(type) {
      if (!type) return;
      let arr = type.split(",") || [];
      return arr.reduce((a, b, i) => {
        return a + this.typeJons[b] + (i == arr.length - 1 ? "" : ",");
      }, "");
    },

    getTotal(arr, type) {
      return arr.reduce((pre, data) => {
        return pre + data[type];
      }, 0);
    },
    getTime() {
      var date = new Date();
      var YY = date.getFullYear();
      //获取月份
      var MM =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //获取日期
      var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return YY + "-" + MM + "-" + DD;
    },
    getList() {
      this.queryParams.channelName = this.isShowMore
        ? this.queryParams.channelName
        : "智优选";
      getNewChannelStatisticsList(this.queryParams).then((res) => {
        this.dataList = res.rows;

        if (this.$store.getters.userInfo.userName === 'dingcan') {
          this.dataList = this.dataList.filter(item => {
            const pass = item.apiChannelType.split(',')
            return pass.includes('1') || pass.includes('4')
          })
        }

        this.optionsList = JSON.parse(JSON.stringify(res.rows));
        this.total = res.total;
        // this.dataList.unshift({
        //   channelId: "合计",
        //   uvNum: this.getTotal(this.dataList, "uvNum"),
        //   uaNum: this.getTotal(this.dataList, "uaNum"),
        //   registerNum: this.getTotal(this.dataList, "registerNum"),
        //   fromNum: this.getTotal(this.dataList, "fromNum"),
        //   h5PushNum: this.getTotal(this.dataList, "h5PushNum"),
        //   appDownloadNum: this.getTotal(this.dataList, "appDownloadNum"),
        //   appLoginNum: this.getTotal(this.dataList, "appLoginNum"),
        //   appApplyPeopleNum: this.getTotal(this.dataList, "appApplyPeopleNum"),
        //   appApplyNum: this.getTotal(this.dataList, "appApplyNum"),
        //   payPeopleNum: this.getTotal(this.dataList, "payPeopleNum"),
        //   payPrice: this.getTotal(this.dataList, "payPrice").toFixed(2),
        //   profit: this.getTotal(this.dataList, "profit").toFixed(2),
        //   onlineApplyNum: this.getTotal(this.dataList, "onlineApplyNum"),
        // });
      });
    },
    getMatchList() {
      getChannelMatchInfoStats(this.queryParams1).then((res) => {
        this.matchList = res.data;
      });
    },
    //前端排序
    handleSortChange(column) {
      this.proptype = column.prop;
      let sortingType = column.order;
      this.sumData = this.dataList[0];
      if (sortingType == "ascending") {
        //正序
        this.dataList = this.dataList
          .splice(1)
          .sort((a, b) => b[this.proptype] - a[this.proptype]);

        this.dataList.unshift(this.sumData);
      }
      if (sortingType == "descending") {
        // 倒序
        this.dataList = this.dataList
          .splice(1)
          .sort((a, b) => a[this.proptype] - b[this.proptype]);
        this.dataList.unshift(this.sumData);
      }
    },

    handleQuery1() {
      if (this.value2 !== null) {
        this.queryParams1.pageNum = 1;
        this.queryParams1.startTime = this.value2[0];
        this.queryParams1.endTime = this.value2[1];
      } else {
        this.queryParams1.startTime = "";
        this.queryParams1.endTime = "";
      }
      this.getMatchList();
    },
    handleFail() {
      this.value2 = [
        this.getTime() + " 00:00:00",
        this.getTime() + " 23:59:59",
      ];
      this.queryParams1.startTime = `${this.getTime()} 00:00:00`;
      this.queryParams1.endTime = `${this.getTime()} 23:59:59`;
      this.dialogVisible = true;
      this.getMatchList();
    },
  },
  computed: {
    isShowMore() {
      return !["zr123456", "zr123123", "shenshenshen", "shengxun"].includes(
        this.$store.getters.userInfo.userName
      );
      // return this.$store.getters.userInfo.userName !== "zr123456";
    },
    filterList() {
      let data = this.checkValue.length
        ? this.dataList.filter((item) =>
            this.checkValue.includes(item.channelId)
          )
        : this.dataList;
      if (this.showZyx && data.length) {
        data = data.filter((item, index) => {
          return item.channelName.includes("智优选");
        });
      }
      if (this.showMy && data.length) {
        data = this.checkValue.length
          ? data.filter((item, index) => {
              return !item.channelName.includes("智优选");
            })
          : data.filter((item, index) => {
              return index && !item.channelName.includes("智优选");
            });
      }
      if (this.showOhter && data.length) {
        let ids = [446, 447, 448, 449, 450, 451, 452, 453, 454, 455];
        data = data.filter((item, index) => {
          return ids.includes(item.channelId);
        });
      }
      if (data.length && data[0]["channelId"] !== "合计") {
        data.unshift({
          channelId: "合计",
          channelName: "",
          uvNum: this.getTotal(data, "uvNum"),
          uaNum: this.getTotal(data, "uaNum"),
          registerNum: this.getTotal(data, "registerNum"),
          fromNum: this.getTotal(data, "fromNum"),
          h5PushNum: this.getTotal(data, "h5PushNum"),
          appDownloadNum: this.getTotal(data, "appDownloadNum"),
          appLoginNum: this.getTotal(data, "appLoginNum"),
          appApplyPeopleNum: this.getTotal(data, "appApplyPeopleNum"),
          appApplyNum: this.getTotal(data, "appApplyNum"),
          payPeopleNum: this.getTotal(data, "payPeopleNum"),
          profit: this.getTotal(data, "profit"),
          onlineApplyNum: this.getTotal(data, "onlineApplyNum"),
          onlineProfit: this.getTotal(data, "onlineProfit"),
          offlineProfit: this.getTotal(data, "offlineProfit"),
          onlineWeChatProfit: this.getTotal(data, "onlineWeChatProfit"),
          onlineDaoChaoProfit: this.getTotal(data, "onlineDaoChaoProfit"),
        });
      }

      return data;
    },

    isRangeDate() {
      const userList = ["beta"];
      const user = this.$store.getters.userName;

      return userList.includes(user);
    }
  },
  mounted() {
    this.setDefaultDate();
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.warning {
  color: red;
}
::v-deep .el-dialog__body {
  border-bottom: none;
}
</style>
