<template>
  <div>
    <el-drawer
      title="商户线下"
      :visible.sync="drawer_"
      size="800px"
      :wrapper-closable="false"
      :direction="direction"
      @close="hanldeClose"
    >
      <div class="drawer-container">
        <el-form
          :model="formData"
          ref="formData"
          label-position="top"
          :rules="rules"
          :key="tabIndex"
        >
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="合同文件" prop="contractFilename">
                <el-upload
                  class="upload-demo"
                  action="#"
                  :auto-upload="false"
                  :show-file-list="false"
                  :on-change="
                    (e) =>
                      changeUpfile(e, {
                        type: 3,
                        name: 'contractFilename',
                        path: 'contractFilepath',
                      })
                  "
                >
                  <el-button size="mini" icon="el-icon-upload2"
                    >点击上传</el-button
                  >
                </el-upload>
              </el-form-item>
              <el-tag
                type="info"
                size="small"
                v-if="formData.contractFilename"
                @close="
                  handleRemoveFile({
                    name: 'contractFilename',
                    path: 'contractFilepath',
                  })
                "
                closable
              >
                <span class="tag">{{ formData.contractFilename }}</span>
              </el-tag>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24" v-if="isLoanType">
              <el-form-item
                label="员工在职证明"
                prop="certificateEmploymentFilename"
              >
                <el-upload
                  class="upload-demo"
                  action="#"
                  :auto-upload="false"
                  ref="certificateEmploymentFilename"
                  :show-file-list="false"
                  :on-change="
                    (e) =>
                      changeUpfile(e, {
                        type: 3,
                        name: 'certificateEmploymentFilename',
                        path: 'certificateEmploymentFilepath',
                      })
                  "
                >
                  <el-button size="mini" icon="el-icon-upload2"
                    >点击上传</el-button
                  >
                </el-upload>
              </el-form-item>
              <el-tag
                type="info"
                size="small"
                v-if="formData.certificateEmploymentFilename"
                @close="
                  handleRemoveFile({
                    name: 'certificateEmploymentFilename',
                    path: 'certificateEmploymentFilepath',
                  })
                "
                closable
              >
                <span class="tag">{{
                  formData.certificateEmploymentFilename
                }}</span>
              </el-tag>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="24">
              <el-form-item label="其他文件">
                <el-upload
                  class="upload-demo"
                  action="#"
                  :auto-upload="false"
                  :limit="10"
                  :fileList="fileList"
                  :on-change="(e, fileList) => handleOtherFile(e, fileList)"
                  :on-remove="(e, fileList) => handleOtherRemove(e, fileList)"
                >
                  <el-button size="mini" icon="el-icon-upload2"
                    >点击上传</el-button
                  >
                  <div slot="tip" class="el-upload__tip">
                    单个文件不能超过2MB；软著、放款资质、ICP备案或其他资料请上传至补充资料
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="drawer-title" v-if="processList && processList.length">
          审批流程
        </div>
        <div class="drawer-process" v-if="processList && processList.length">
          <div v-for="(item, index) in processList" :key="index">
            <div class="flex align-items-c">
              <span
                :class="[
                  'iconfont f-suceess drawer-process-icon',
                  iconList[item.status],
                  colorList[item.status],
                ]"
              ></span>
              <span class="drawer-process-user">{{ item.name }} </span>
              <span
                :class="['drawer-process-status', tagList[item.status]]"
                v-if="item.status != 0 && item.status != -2"
              >
                <span
                  v-if="
                    processList[1] &&
                    processList[1].status == 2 &&
                    processList[0] &&
                    processList[0].status == -1 &&
                    index == 0
                  "
                >
                  待修改
                </span>
                <span v-else>{{ statusJson[item.status] || "" }}</span>
              </span>
            </div>
            <div
              :class="[
                'drawer-process-line',
                processList.length - 1 == index ? 'boder-none' : '',
              ]"
            >
              <div class="drawer-process-time">
                {{ item.checkTime || "-" }}
              </div>
              <div
                :class="[
                  'drawer-process-remark',
                  item.status == 2 ? 'fail' : '',
                ]"
                v-if="item.checkRemark && item.status != -2"
              >
                {{ item.checkRemark || "-" }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="drawer__footer flex align-items-c">
        <!-- <el-button type="primary" size="small" @click="handleSubmit"> 确定</el-button> -->

        <el-button
          size="small"
          v-if="status == 4"
          type="primary"
          @click="submitform"
          >提交</el-button
        >
        <el-button
          type="primary"
          v-if="status == 2 || status == 3 || status == 5"
          class="submit"
          v-hasPermi="['partyaAdmin:contract:update']"
          @click="submitupDataform"
          >修改</el-button
        >
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  contractUploadFile,
  addPartyContractOther,
  updatePartyContractOther,
} from "@/api/partyManage";
import { getContractInfo } from "@/api/partyA";
export default {
  data() {
    return {
      tabIndex: 1,
      imageUrl: "",
      imageUrl1: "",
      imageUrl2: "",
      cityList: [],
      provinceList: [],
      areaList: [],
      countrysList: [],
      fileList: [],
      processList: [],

      statusJson: {
        0: "发起",
        1: "已通过",
        2: "已驳回",
        3: "结束",
        "-1": "待审核",
      },
      iconList: {
        0: "icon-a-chaji6",
        1: "icon-a-paichu3",
        2: "icon-a-paichu2",
        3: "icon-a-paichu3",
        "-1": "icon-a-paichu1",
        "-2": "icon-a-paichu3",
      },

      colorList: {
        0: "f-suceess",
        1: "f-suceess",
        2: "f-danger",
        3: "f-suceess",
        "-1": "f-info",
      },
      tagList: {
        0: "success",
        1: "success",
        2: "danger",
        3: "success",
        "-1": "info",
      },
      statusType: {
        // @ApiModelProperty(value = "合同审核状态(0未上传/1待审核/2审核成功/3审核失败)")
        0: "未上传",
        1: "待审核",
        2: "审核成功",
        3: "审核失败",
      },
      dateStatus: true,
      status: 0,
      formData: {
        contractFilename: null,
        contractFilepath: null,
        certificateEmploymentFilename: null, //员工在职证明
        certificateEmploymentFilepath: null, //员工在职证明路径
        otherInfoFiles: [],
      },
      rules: {
        contractFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
        certificateEmploymentFilename: [
          { required: true, message: "请选择请上传", trigger: "change" },
        ],
      },
    };
  },
  props: {
    drawer: {
      type: Boolean,
      default: false,
    },
    direction: {
      type: String,
      default: "rtl",
    },
    partybId: {
      type: [String, Number],
      default: "",
    },
    row: {
      type: Object,
      default: () => {},
    },
    partyFirstId: {
      type: [Number, String],
      default: "",
    },
    isLoanType: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    drawer_: {
      get() {
        return this.drawer;
      },
      set(v) {
        this.$emit("update:drawer", false);
      },
    },
  },
  watch: {
    drawer(val) {
      if (val) {
        this.getInfo();
      }
    },
  },
  methods: {
    hanldeClose() {
      this.$emit("update:drawer", false);
    },
    // @ApiModelProperty(value = "合同审核状态(0未上传/1待审核/2审核成功/3审核失败)")

    //上传文件
    changeUpfile(e, dataInfo) {
      if (dataInfo.type == 1) {
        const isJPG =
          e.raw.type === "image/jpg" ||
          e.raw.type === "image/jpeg" ||
          e.raw.type === "image/png";
        const isLt2M = e.size / 1024 / 1024 < 10;
        if (!isJPG) {
          this.$message.error("上传图片只能是 JPG/PNG/jpeg 格式!");
          return;
        }
        if (!isLt2M) {
          this.$message.error("上传图片大小不能超过 10MB!");
          return;
        }
      }
      if (dataInfo.type == 3) {
        const isLt2M = e.size / 1024 / 1024 < 10;
        if (!isLt2M) {
          this.$message.error("上传文件大小不能超过 10MB!");
          return;
        }
      }
      if (dataInfo.type == 2) {
        if (!e.raw.type.includes("video")) {
          this.$message.error("请上传视频");
          return;
        }
        const isLt2M = e.size / 1024 / 1024 < 20;
        if (!isLt2M) {
          this.$message.error("上传文件大小不能超过 20MB!");
          return;
        }
      }

      this.$refs.formData.clearValidate(dataInfo.name);
      let data = new FormData();
      data.append("fileNum", dataInfo.type);
      data.append("file", e.raw);
      if (dataInfo.name == "businessLicenseFilename") {
        this.imageUrl = URL.createObjectURL(e.raw);
      }
      contractUploadFile(data, this.partyFirstId).then((res) => {
        this.formData[dataInfo.name] = res.filename;
        this.formData[dataInfo.path] = res.filepath;
        if (dataInfo.name == "businessLicenseFilename") {
          this.imageUrl = URL.createObjectURL(e.raw);
        }
        if (dataInfo.name == "frontIdCardFilename") {
          this.imageUrl1 = URL.createObjectURL(e.raw);
        }
        if (dataInfo.name == "reverseIdCardFilename") {
          this.imageUrl2 = URL.createObjectURL(e.raw);
        }
      });
    },
    //删除文件
    handleRemoveFile(e) {
      this.formData[e.name] = "";
      this.formData[e.path] = "";
    },
    //上传其他文件
    handleOtherFile(e, fileList) {
      const isLt2M = e.size / 1024 / 1024 < 10;
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 10MB!");
        return;
      }
      if (fileList.length > 10) {
        this.$message.error("不能超过10个文件");
        return;
      }
      let data = new FormData();
      data.append("fileNum", 3);
      data.append("file", e.raw);
      contractUploadFile(data, this.partyFirstId).then((res) => {
        this.fileList.push({ name: res.filename, url: res.filepath });
        this.formData.otherInfoFiles = this.fileList.map((item) => {
          return {
            filename: item.name,
            filepath: item.url,
          };
        });
        this.fileList =
          this.formData.otherInfoFiles.map((item) => {
            return {
              name: item.filename,
              url: item.filepath,
            };
          }) || [];
      });
    },
    //删除其他文件
    handleOtherRemove(e, fileList) {
      this.fileList = fileList;
      this.formData.otherInfoFiles = fileList.map((item) => {
        return {
          filename: item.name,
          filepath: item.url,
        };
      });
    },
    submitform() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.partyFirstId = this.partyFirstId;
          addPartyContractOther(this.formData).then((res) => {
            if (res.code == 200) {
              this.getInfo();
              this.$emit("getList");
              this.drawer_ = false;
              this.$message.success("上传成功");
            }
          });
        }
      });
    },

    submitupDataform() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.formData.partyFirstId = this.partyFirstId;

          updatePartyContractOther(this.formData).then((res) => {
            this.$message.success("修改成功");
            this.$emit("getList");
            this.drawer_ = false;
            this.getInfo();
          });
        }
      });
    },
    getInfo() {
      getContractInfo(this.partyFirstId).then((res) => {
        const data = res.data;
        this.status = res.data.contractCheckStatus;
        this.processList = res.data.processList;

        let list = res.data.fileOrderList || [];
        this.fileList = list.map((item) => {
          return {
            name: item.filename,
            url: item.filepath,
          };
        });
        this.formData.otherInfoFiles = list.map((item) => {
          return {
            filename: item.filename,
            filepath: item.filepath,
          };
        });
        // 还原数据
        for (let key in data) {
          // 接口请求出来的
          if (Object.keys(this.formData).includes(key)) {
            this.formData[key] = data[key];
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #eeeeee;
  background: #dcdcdc;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader-tips {
  color: #8c939d;
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  font-size: 12px;
  justify-content: center;
  align-items: center;

  i {
    font-size: 24px;
  }
}

::v-deep .el-upload__tip {
  color: rgba(0, 0, 0, 0.4);
  margin-top: -1px;
}

.drawer-container {
  height: calc(100vh - 100px);
  overflow: auto;
  padding: 20px;
}

.drawer-tabs {
  width: 702px;
  height: 76px;
  background: #f5f7fa;
  border-radius: 4px 4px 4px 4px;
  margin: 0 auto;
  display: flex;
  padding: 0px 20px;
  justify-content: space-between;
  align-items: center;
  color: rgba(0, 0, 0, 0.4);
  font-size: 16px;
  margin-bottom: 10px;

  &-cicle {
    width: 20px;
    height: 20px;
    background: #e37318;
    display: block;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    margin-right: 5px;
    font-size: 12px;
  }

  &-line {
    width: 160px;
    height: 4px;
    background: #e37318;
  }

  .tab-line {
    width: 160px;
    height: 4px;
    background: #dcdcdc;
  }

  .tab-active {
    color: #e37318;
  }

  .tab-finish {
    color: rgba(0, 0, 0, 0.9);
  }

  .tab-todo {
    border: 1px solid rgba(0, 0, 0, 0.4);
    color: rgba(0, 0, 0, 0.4);
    background: transparent;
  }
}

.drawer-title {
  font-size: 18px;
  font-weight: 400;
  color: #3d3d3d;
  position: relative;
  padding-left: 20px;
  margin-bottom: 20px;

  &::after {
    content: "";
    display: block;
    position: absolute;
    width: 4px;
    height: 20px;
    background: #e37318;
    top: 4px;
    left: 0;
  }
}

.drawer-process {
  padding: 20px;

  &-icon {
    margin-right: 10px;
  }

  &-user {
    font-size: 16px;
    color: #181716;
    width: 250px;
  }

  &-status {
    margin-left: 100px;
    font-size: 12px;
    padding: 2px 4px;
    background: #e5f9e9;
    border-radius: 2px;

    &.success {
      background: #e5f9e9;
      color: #3fa372;
      border: 1px solid #3fa372;
    }

    &.danger {
      color: #ff0000;
      background: #ffecec;
      border: 1px solid #f00;
    }

    &.info {
      color: #ff8f1f;
      background: #ffe8d1;
      border: 1px solid #ff8f1f;
    }
  }

  &-line {
    margin: 3px 0px 5px 8px;
    border-left: 1px dashed #d8d8d8;
    padding-left: 20px;

    padding-bottom: 10px;
  }

  &-time {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 10px;
  }

  &-remark {
    width: 100%;
    background: #fafafa;
    border-radius: 4px 4px 4px 4px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    padding: 20px;

    &.fail {
      background: #ffecec;
      color: #f00;
      border: 1px solid #f00;
    }
  }
}

.drawer__footer {
  position: absolute;
  bottom: 0;
  border-top: 1px solid #dcdfe6;
  width: 100%;
  height: 50px;
  padding-left: 10px;
}

.icon-a-paichu3 {
  margin-right: 8px;
}

::v-deep .el-form-item--medium .el-form-item__label {
  line-height: 0;
}

::v-deep .el-form-item {
  margin-bottom: 18px;
}

::v-deep .el-tag.el-tag--info {
  transform: translateY(-12px);
}

::v-deep .el-upload-list__item-name {
  width: 120px;
  overflow: hidden;
}

.boder-none {
  border: none;
}

.iconfont {
  margin-right: 5px;
}
</style>
