<template>
  <div>
    <!-- 资质比例分析设置对话框 -->
    <el-dialog 
      title="资质比例分析设置" 
      :visible.sync="dialogVisible" 
      width="800px"
      @close="handleClose"
    >
      <el-table
        :data="settingList"
        border
        style="width: 100%"
        max-height="500"
      >
        <el-table-column prop="alias" label="别名" />
        <!-- <el-table-column prop="operator" label="条件" min-width="250" /> -->
        <el-table-column prop="visible" label="显示数值" width="120" align="center">
          <template slot-scope="scope">
            <el-switch 
              v-model="scope.row.visible" 
              active-color="#13ce66" 
              inactive-color="#909399"
              @change="(val) => handleVisibleChange(val, scope.$index)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="visibleRatio" label="显示占比" width="120" align="center">
          <template slot-scope="scope">
            <el-switch 
              v-model="scope.row.visibleRatio" 
              active-color="#13ce66" 
              inactive-color="#909399"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序值" width="110" align="center">
          <template slot-scope="scope">
            <el-input-number 
              v-model="scope.row.sort" 
              :min="0" 
              :step="1"
              controls-position="right"
              size="mini"
              style="width: 80px"
              @change="(value) => handleSortChange(value, scope.$index)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleEditSetting(scope.row, scope.$index)">编辑</el-button>
            <el-button type="text" @click="handleDeleteSetting(scope.$index)" style="color: #F56C6C">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 20px; text-align: right;">
        <el-button @click="handleAddSetting">添加条件</el-button>
        <el-button type="primary" @click="handleSaveSetting">保存设置</el-button>
      </div>

      <!-- 编辑或添加设置项 -->
      <el-dialog
        width="600px"
        title="编辑条件"
        :visible.sync="editDialogVisible"
        append-to-body
      >
        <el-form ref="settingForm" :model="currentSetting" :rules="settingRules" label-width="100px">
          <el-form-item label="别名" prop="alias">
            <el-input v-model="currentSetting.alias" placeholder="请输入别名，如：年龄大于25" />
          </el-form-item>
          <el-form-item label="显示数值" prop="visible">
            <el-switch v-model="currentSetting.visible" />
          </el-form-item>
          <el-form-item label="显示占比" prop="visibleRatio">
            <el-switch v-model="currentSetting.visibleRatio" />
          </el-form-item>
          <el-form-item label="排序值" prop="sort">
            <el-input-number v-model="currentSetting.sort" :min="0" :step="1" controls-position="right" style="width: 100%" />
          </el-form-item>
          <el-form-item label="条件字段" prop="field">
            <el-select v-model="conditionField" placeholder="请选择字段" style="width: 100%" @change="handleFieldChange">
              <el-option label="年龄" value="age" />
              <el-option label="芝麻分" value="sesame" />
              <el-option label="逾期" value="overdue" />
            </el-select>
          </el-form-item>
          <el-form-item label="操作符" prop="operator" v-if="conditionField">
            <el-select v-model="conditionOperator" placeholder="请选择操作符" style="width: 100%" @change="handleOperatorChange" :disabled="conditionField === 'sesame' || conditionField === 'overdue'">
              <el-option v-for="op in operatorOptions" :key="op.value" :label="op.label" :value="op.value" />
            </el-select>
          </el-form-item>
          
          <!-- 根据字段和操作符显示不同的输入控件 -->
          <template v-if="conditionField === 'age'">
            <!-- 年龄条件值输入 -->
            <template v-if="conditionOperator === 'between'">
              <el-form-item label="最小值" prop="minValue">
                <el-input-number v-model="conditionMinValue" :min="0" :max="100" style="width: 100%" />
              </el-form-item>
              <el-form-item label="最大值" prop="maxValue">
                <el-input-number v-model="conditionMaxValue" :min="0" :max="100" style="width: 100%" />
              </el-form-item>
            </template>
            <template v-else-if="conditionOperator === 'in'">
              <el-form-item label="包含值" prop="inValues">
                <el-select v-model="conditionInValues" multiple placeholder="请选择包含的值" style="width: 100%">
                  <el-option v-for="i in 80" :key="i" :label="i" :value="i" />
                </el-select>
              </el-form-item>
            </template>
            <template v-else-if="conditionOperator">
              <el-form-item label="值" prop="value">
                <el-input-number v-model="conditionValue" :min="0" :max="100" style="width: 100%" />
              </el-form-item>
            </template>
          </template>

          <template v-if="conditionField === 'sesame'">
            <!-- 芝麻分条件值选择 - 修改为多选 -->
            <el-form-item label="芝麻分" prop="value">
              <el-select v-model="conditionInValues" multiple placeholder="请选择芝麻分" style="width: 100%" @change="(values) => handleInValuesChange('sesame', values)">
                <el-option 
                  v-for="item in sesameOptions" 
                  :key="item.value" 
                  :label="item.label" 
                  :value="item.value" 
                />
              </el-select>
            </el-form-item>
          </template>

          <template v-if="conditionField === 'overdue'">
            <!-- 逾期条件值选择 - 修改为多选 -->
            <el-form-item label="逾期情况" prop="value">
              <el-select v-model="conditionInValues" multiple placeholder="请选择逾期情况" style="width: 100%" @change="(values) => handleInValuesChange('overdue', values)">
                <el-option 
                  v-for="item in overdueOptions" 
                  :key="item.value" 
                  :label="item.label" 
                  :value="item.value" 
                />
              </el-select>
            </el-form-item>
          </template>

          <!-- <el-form-item label="条件表达式" prop="operator">
            <el-input 
              type="textarea"
              :rows="3"
              v-model="currentSetting.operator"
              placeholder="最终生成的条件表达式"
              :disabled="true"
            />
          </el-form-item> -->
        </el-form>
        <span slot="footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitSetting">确定</el-button>
        </span>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import { getQualificationProportionAnalysisSetting, saveQualificationProportionAnalysisSetting } from '@/api/distributionStatistics/channelQualification'

export default {
  name: 'QualificationSetting',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  data() {
    return {
      // 资质比例分析设置相关
      settingList: [],
      editDialogVisible: false,
      currentSetting: {
        alias: '',
        operator: '',
        visible: true,
        visibleRatio: false,
        sort: 0
      },
      editIndex: -1,
      settingRules: {
        alias: [
          { required: true, message: '请输入别名', trigger: 'blur' }
        ],
        operator: [
          { required: true, message: '请输入条件表达式', trigger: 'blur' }
        ]
      },
      // 条件编辑相关
      conditionField: '',
      conditionOperator: '',
      conditionValue: '',
      conditionMinValue: 0,
      conditionMaxValue: 0,
      conditionInValues: [],
      // 操作符映射
      fieldMapping: {
        age: 't.age',
        sesame: 'a.sesame_id',
        overdue: 't.overdue_id'
      },
      // 芝麻分枚举值
      sesameOptions: [
        { label: '600分以下', value: '110' },
        { label: '600-650分', value: '112' },
        { label: '650-700分', value: '113' },
        { label: '700分以上', value: '115' }
      ],
      // 逾期情况枚举值
      overdueOptions: [
        { label: '信用良好，无逾期', value: '122' },
        { label: '无信用卡或贷款', value: '123' },
        { label: '近1年无逾期', value: '124' },
        { label: '1年内逾期少于3次且少于90天', value: '125' },
        { label: '1年内逾期超过3次或者90天', value: '126' }
      ],
      operatorOptions: []
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getSettingList()
      }
    },
    // 监听条件值变化并重新构建表达式
    conditionValue() {
      this.buildExpression()
    },
    conditionMinValue() {
      this.buildExpression()
    },
    conditionMaxValue() {
      this.buildExpression()
    },
    conditionInValues: {
      handler() {
        this.buildExpression()
      },
      deep: true
    },
    // 监听条件字段变化
    conditionField() {
      this.buildExpression()
    },
    // 监听操作符变化
    conditionOperator() {
      this.buildExpression()
    }
  },
  methods: {
    // 获取设置列表
    getSettingList() {
      getQualificationProportionAnalysisSetting().then(response => {
        this.settingList = response.data || []
        // 确保每个条件都有visibleRatio和sort字段
        this.settingList.forEach(item => {
          if (item.visibleRatio === undefined) {
            item.visibleRatio = false
          }
          if (item.sort === undefined) {
            item.sort = 0
          }
        })
      }).catch(() => {
        this.settingList = []
      })
    },
    // 重置条件状态
    resetConditionState() {
      this.conditionField = ''
      this.conditionOperator = ''
      this.conditionValue = ''
      this.conditionMinValue = 0
      this.conditionMaxValue = 0
      this.conditionInValues = []
    },
    // 关闭对话框
    handleClose() {
      this.settingList = []
      this.editDialogVisible = false
      this.dialogVisible = false
    },
    // 添加设置项
    handleAddSetting() {
      this.currentSetting = {
        alias: '',
        operator: '',
        visible: true,
        visibleRatio: false,
        sort: 0
      }
      this.resetConditionState()
      this.editIndex = -1
      this.editDialogVisible = true
    },
    // 编辑设置项
    handleEditSetting(row, index) {
      this.currentSetting = JSON.parse(JSON.stringify(row))
      // 确保visible字段存在，如果不存在则默认为true
      if (this.currentSetting.visible === undefined) {
        this.currentSetting.visible = true
      }
      // 确保visibleRatio字段存在
      if (this.currentSetting.visibleRatio === undefined) {
        this.currentSetting.visibleRatio = false
      }
      // 确保sort字段存在
      if (this.currentSetting.sort === undefined) {
        this.currentSetting.sort = 0
      }
      this.editIndex = index
      // 尝试解析条件表达式
      this.parseConditionExpression(row.operator)
      this.editDialogVisible = true
    },
    // 删除设置项
    handleDeleteSetting(index) {
      this.$confirm('确认删除该条件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.settingList.splice(index, 1)
        // this.$message({
        //   type: 'success',
        //   message: '删除成功！'
        // })
      }).catch(() => {})
    },
    // 解析正则表达式匹配结果
    parseRegexMatch(regex, expression, valueIndex = 1) {
      const matches = expression.match(regex)
      if (matches && matches.length > valueIndex) {
        return matches[valueIndex].replace(/"/g, '')
      }
      return ''
    },
    // 解析IN条件的值
    parseInValues(expression) {
      const matches = expression.match(/IN\(([^)]+)\)/)
      if (matches && matches.length === 2) {
        return matches[1].split(',').map(v => v.trim())
      }
      return []
    },
    // 解析条件表达式
    parseConditionExpression(expression) {
      try {
        this.resetConditionState()

        if (!expression) return

        // 检查使用了哪个字段
        if (expression.includes(this.fieldMapping.age)) {
          this.conditionField = 'age'
        } else if (expression.includes(this.fieldMapping.sesame)) {
          this.conditionField = 'sesame'
          // 芝麻分设置为包含操作符
          this.conditionOperator = 'in'
          
          // 提取芝麻分值数组
          this.conditionInValues = this.parseInValues(expression)
          
          // 芝麻分字段解析完成后直接返回，不需要下面的操作符判断
          return
        } else if (expression.includes(this.fieldMapping.overdue)) {
          this.conditionField = 'overdue'
          // 逾期设置为包含操作符
          this.conditionOperator = 'in'
          
          // 提取逾期值数组
          this.conditionInValues = this.parseInValues(expression)
          
          // 逾期字段解析完成后直接返回，不需要下面的操作符判断
          return
        } else {
          return
        }

        // 自动设置操作符选项
        this.handleFieldChange(this.conditionField)

        // 检查操作符
        if (expression.includes('BETWEEN')) {
          this.conditionOperator = 'between'
          const matches = expression.match(/BETWEEN\s+(\d+)\s+AND\s+(\d+)/)
          if (matches && matches.length === 3) {
            this.conditionMinValue = parseInt(matches[1])
            this.conditionMaxValue = parseInt(matches[2])
          }
        } else if (expression.includes('IN(')) {
          this.conditionOperator = 'in'
          this.conditionInValues = this.parseInValues(expression)
        } else if (expression.includes('>=')) {
          this.conditionOperator = 'gte'
          this.conditionValue = this.parseRegexMatch(/>=\s*(\d+|"[^"]*")/, expression)
        } else if (expression.includes('<=')) {
          this.conditionOperator = 'lte'
          this.conditionValue = this.parseRegexMatch(/<=\s*(\d+|"[^"]*")/, expression)
        } else if (expression.includes('>')) {
          this.conditionOperator = 'gt'
          this.conditionValue = this.parseRegexMatch(/>\s*(\d+|"[^"]*")/, expression)
        } else if (expression.includes('<')) {
          this.conditionOperator = 'lt'
          this.conditionValue = this.parseRegexMatch(/<\s*(\d+|"[^"]*")/, expression)
        } else if (expression.includes('=')) {
          this.conditionOperator = 'eq'
          this.conditionValue = this.parseRegexMatch(/=\s*(\d+|"[^"]*")/, expression)
        }
      } catch (error) {
        console.error('解析条件表达式出错', error)
      }
    },
    // 处理字段选择变化
    handleFieldChange(field) {
      this.conditionField = field
      this.conditionOperator = ''
      this.conditionValue = ''
      this.conditionMinValue = 0
      this.conditionMaxValue = 0
      this.conditionInValues = []
      this.currentSetting.operator = '' // 清空条件表达式

      // 根据不同字段设置操作符选项
      if (field === 'sesame' || field === 'overdue') {
        // 芝麻分和逾期字段显示包含操作符且自动选中
        this.operatorOptions = [
          { label: '包含', value: 'in' }
        ]
        this.conditionOperator = 'in'
      } else {
        this.operatorOptions = [
          { label: '大于', value: 'gt' },
          { label: '大于等于', value: 'gte' },
          { label: '小于', value: 'lt' },
          { label: '小于等于', value: 'lte' },
          { label: '等于', value: 'eq' },
          { label: '包含', value: 'in' }
        ]
        
        // 年龄字段可以使用区间操作符
        if (field === 'age') {
          this.operatorOptions.push({ label: '区间', value: 'between' })
        }
      }
    },
    // 处理操作符选择变化
    handleOperatorChange() {
      // 保留原来的值，仅在操作符为"区间"或"包含"时清空
      if (this.conditionOperator === 'between' || this.conditionOperator === 'in') {
        this.conditionValue = ''
        this.conditionMinValue = 0
        this.conditionMaxValue = 0
        this.conditionInValues = []
      }
      
      this.currentSetting.operator = '' // 清空条件表达式
      this.buildExpression()
    },
    // 统一处理包含(IN)值变化 - 合并之前的handleSesameValuesChange和handleOverdueValuesChange
    handleInValuesChange(field, values) {
      if (values && values.length) {
        this.currentSetting.operator = `${this.fieldMapping[field]} IN(${values.join(',')})`
      } else {
        this.currentSetting.operator = ''
      }
    },
    // 构建普通操作符表达式
    buildSimpleOperatorExpression(fieldName, operator, value) {
      const operatorMap = {
        gt: '>',
        gte: '>=',
        lt: '<',
        lte: '<=',
        eq: '='
      }
      
      // 检查值是否存在
      if (value !== undefined && value !== null && value !== '') {
        return `${fieldName} ${operatorMap[operator]} ${value}`
      } else {
        return `${fieldName} ${operatorMap[operator]} `
      }
    },
    // 构建表达式
    buildExpression() {
      if (!this.conditionField || !this.conditionOperator) {
        this.currentSetting.operator = ''
        return
      }

      // 芝麻分和逾期字段有专门的处理函数，不在这里处理
      if (this.conditionField === 'sesame' || this.conditionField === 'overdue') {
        return
      }

      const fieldName = this.fieldMapping[this.conditionField]

      // 处理简单操作符 (gt, gte, lt, lte, eq)
      if (['gt', 'gte', 'lt', 'lte', 'eq'].includes(this.conditionOperator)) {
        this.currentSetting.operator = this.buildSimpleOperatorExpression(
          fieldName, 
          this.conditionOperator, 
          this.conditionValue
        )
      } 
      // 处理区间操作符
      else if (this.conditionOperator === 'between') {
        const minValueValid = this.conditionMinValue !== undefined && this.conditionMinValue !== null && this.conditionMinValue !== '';
        const maxValueValid = this.conditionMaxValue !== undefined && this.conditionMaxValue !== null && this.conditionMaxValue !== '';
        
        if (minValueValid && maxValueValid) {
          this.currentSetting.operator = `${fieldName} BETWEEN ${this.conditionMinValue} AND ${this.conditionMaxValue}`
        } else {
          this.currentSetting.operator = `${fieldName} BETWEEN ... AND ...`
        }
      } 
      // 处理包含操作符
      else if (this.conditionOperator === 'in') {
        if (this.conditionInValues && this.conditionInValues.length) {
          this.currentSetting.operator = `${fieldName} IN(${this.conditionInValues.join(',')})`
        } else {
          this.currentSetting.operator = `${fieldName} IN(...)`
        }
      } else {
        this.currentSetting.operator = ''
      }
    },
    // 提交设置项
    handleSubmitSetting() {
      this.$refs.settingForm.validate(valid => {
        if (valid) {
          if (!this.currentSetting.operator) {
            this.$message({
              type: 'warning',
              message: '请完成条件表达式设置'
            })
            return
          }

          if (this.editIndex === -1) {
            this.settingList.push(this.currentSetting)
          } else {
            this.settingList.splice(this.editIndex, 1, this.currentSetting)
          }
          this.editDialogVisible = false
        }
      })
    },
    // 保存设置
    handleSaveSetting() {
      if (this.settingList.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少添加一项条件设置'
        })
        return
      }
      
      this.$confirm('确认保存设置?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        saveQualificationProportionAnalysisSetting(this.settingList).then(() => {
          this.$message({
            type: 'success',
            message: '保存成功！'
          })
          this.$emit('success')
          this.handleClose()
        }).catch(() => {})
      }).catch(() => {})
    },
    // 处理显示状态变更
    handleVisibleChange(value, index) {
      // 可以在这里添加其他逻辑，例如提示信息等
      this.settingList[index].visible = value
    },
    // 处理排序值变化
    handleSortChange(value, index) {
      this.settingList[index].sort = value
    }
  }
}
</script>

<style scoped>
.operator-help {
  background-color: #f5f7fa;
  padding: 15px;
  margin-top: 15px;
  border-radius: 4px;
  font-size: 14px;
}

.operator-help p {
  margin: 5px 0;
}

.operator-help ul {
  margin: 5px 0;
  padding-left: 20px;
}
</style> 