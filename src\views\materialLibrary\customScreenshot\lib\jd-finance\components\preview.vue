<template>
  <div class="screen-container">
    <!-- 状态栏 iOS/Android -->
    <component :is="configData.device && configData.device.os == 'android' ? 'AndroidStatusBar' : 'IOSStatusBar'"
      :time="configData.statusBar.time" :show-signal="configData.statusBar.showSignal"
      :show-wifi="configData.statusBar.showWifi" :show-battery="configData.statusBar.showBattery"
      :battery-level="configData.statusBar.batteryLevel" :mode="configData.statusBar.mode" />

    <!-- iOS/Android 导航栏 -->
    <component :is="configData.device && configData.device.os == 'android' ? 'AndroidNavBar' : 'IOSNavBar'" />

    <div class="screen-container-inner">
      <!-- 背景图 -->
      <img class="background-img" src="https://jst.oss-utos.hmctec.cn/common/path/9eab645b4996466aa9cbadab44d45b44.png"
        alt="Background">

      <!-- 引入组件化的内容 -->
      <QuotaStructure :config-data="configData" />
      <QuotaList :config-data="configData" />
      <OtherContent :config-data="configData" />
    </div>
  </div>
</template>

<script>
import IOSStatusBar from '../../components/iOSStatusBar.vue';
import AndroidStatusBar from '../../components/AndroidStatusBar.vue';
import IOSNavBar from './preview/iOSNavBar.vue';
import AndroidNavBar from './preview/AndroidNavBar.vue';
// 导入拆分后的组件
import QuotaStructure from './preview/QuotaStructure.vue';
import QuotaList from './preview/QuotaList.vue';
import OtherContent from './preview/OtherContent.vue';

export default {
  name: 'JdFinancePreview',
  components: {
    IOSStatusBar,
    AndroidStatusBar,
    IOSNavBar,
    AndroidNavBar,
    // 注册拆分后的组件
    QuotaStructure,
    QuotaList,
    OtherContent
  },
  props: {
    configData: {
      type: Object,
      default: () => ({
        device: { os: 'ios' },
        statusBar: {
          time: '09:41',
          showSignal: true,
          showWifi: true,
          showBattery: true,
          batteryLevel: 100
        }
      })
    }
  },
  methods: {
    // 添加截图方法
    async captureScreenshot() {
      // 动态导入html2canvas，这样可以异步加载而不会影响初始渲染性能
      const html2canvas = (await import('html2canvas')).default;

      const targetElement = this.$el;
      if (!targetElement) {
        console.error('Target element .screen-container not found');
        return null;
      }

      try {
        const canvas = await html2canvas(targetElement, {
          useCORS: true, // 允许加载跨域资源
          scale: 1.5, // 更高的缩放比例以提高清晰度
          backgroundColor: null, // 透明背景
          logging: false, // 不输出日志
          width: targetElement.scrollWidth,
          height: targetElement.scrollHeight
        });

        // 根据设备类型决定图片格式
        const imageType = this.configData.device.os === 'android' ? 'image/jpeg' : 'image/png';
        const dataUrl = canvas.toDataURL(imageType);
        return dataUrl;

      } catch (error) {
        console.error('Error capturing screenshot:', error);
        return null;
      }
    }
  }
}
</script>

<style scoped lang="scss">
// 隐藏所有滚动条
::-webkit-scrollbar {
  display: none;
}

.screen-container {
  position: relative;
  width: 750px;
  height: 1334px; // 参考常见设备高度
  font-family: 'SourceHanSansSC-Regular', sans-serif; // 添加备用字体
  display: flex;
  flex-direction: column;
  background-color: #F5F5F5; // 默认背景色

  .screen-container-inner {
    flex: 1;
    overflow-y: hidden; // 根据需要调整
  }
}

.background-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 750px;
  height: 812px;
  object-fit: cover;
  z-index: 0;
}
</style>
