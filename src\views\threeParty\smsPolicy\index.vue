<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :inline="true" :model="listQuery" class="filter-container">
      <el-form-item label="策略名称">
        <el-input
          v-model="listQuery.searchText"
          placeholder="请输入策略名称"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="listQuery.status" placeholder="状态" style="width: 120px" class="filter-item">
          <el-option label="全部" :value="null" />
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="类型">
        <el-select v-model="listQuery.type" placeholder="类型" style="width: 120px" class="filter-item">
          <el-option label="全部" :value="null" />
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
          搜索
        </el-button>
        <el-button class="filter-item" icon="el-icon-refresh" @click="resetFilter">
          重置
        </el-button>
        <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleCreate">
          新增
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格区域 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="策略名称" prop="name" align="center" min-width="120" />
      <el-table-column label="描述" prop="remark" align="center" min-width="150" />
      <el-table-column label="类型" align="center" width="100">
        <template slot-scope="{row}">
          <span>{{ typeMap[row.type] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="200">
        <template slot-scope="{row}">
          <el-switch
            v-model="row.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
            @change="handleModifyStatus(row, $event)"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" align="center" width="160" />
      <el-table-column label="创建人" prop="createBy" align="center" width="120" />
      <el-table-column label="更新时间" prop="updateTime" align="center" width="160" />
      <el-table-column label="更新人" prop="updateBy" align="center" width="120" />
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="text" size="mini" @click="handleDetail(row)">
            查看详情
          </el-button>
          <el-button type="text" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button type="text" size="mini" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      @pagination="getList"
    />

    <!-- 新增/编辑弹窗 -->
    <policy-dialog
      :visible.sync="dialogFormVisible"
      :dialog-status="dialogStatus"
      :row-data="temp"
      :template-options="templateOptions"
      :channel-options="channelOptions"
      @submit="handleDialogSubmit"
    />

    <!-- 详情弹窗 -->
    <policy-detail
      :visible.sync="detailVisible"
      :detail="detailData"
      :template-options="templateOptions"
      :channel-options="channelOptions"
    />
  </div>
</template>

<script>
import { getSmsPolicyList, createSmsPolicy, updateSmsPolicy, deleteSmsPolicy } from '@/api/smsPolicy'
import Pagination from '@/components/Pagination'
import PolicyDialog from './components/PolicyDialog'
import PolicyDetail from './components/PolicyDetail'
import { SMS_POLICY_TYPE_OPTIONS, SMS_POLICY_TYPE_MAP } from '@/const/smsPolicy'
import { getDreams } from '@/api/smsTemplate'
import { getAllChannelList } from '@/api/channeManage/channelList'

export default {
  name: 'SmsPolicy',
  components: { Pagination, PolicyDialog, PolicyDetail },
  data() {
    return {
      typeMap: SMS_POLICY_TYPE_MAP,
      typeOptions: SMS_POLICY_TYPE_OPTIONS,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        size: 10,
        status: null,
        searchText: '',
        type: null
      },
      temp: {},
      dialogFormVisible: false,
      dialogStatus: '',
      detailVisible: false,
      detailData: {},
      templateOptions: [],
      channelOptions: []
    }
  },
  created() {
    this.getList()
    this.getTemplateOptions()
    this.getChannelOptions()
  },
  methods: {
    getList() {
      if (!this.listQuery.searchText) {
        this.listQuery.searchText = ''
      }
      
      this.listLoading = true
      getSmsPolicyList(this.listQuery).then(response => {
        this.list = response.rows
        this.total = response.total
        this.listLoading = false
      })
    },
    getTemplateOptions() {
      getDreams().then(response => {
        this.templateOptions = response.data || []
      })
    },
    getChannelOptions() {
      getAllChannelList().then(response => {
        this.channelOptions = response.data || []
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleCreate() {
      this.temp = {}
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },
    handleUpdate(row) {
      this.temp = JSON.parse(JSON.stringify(row))
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
    },
    handleDialogSubmit(data) {
      const submitData = JSON.parse(JSON.stringify(data))
      if (this.dialogStatus == 'create') {
        createSmsPolicy(submitData).then(() => {
          this.dialogFormVisible = false
          this.$message({
            message: '创建成功',
            type: 'success'
          })
          this.getList()
        })
      } else {
        updateSmsPolicy(submitData.id, submitData).then(() => {
          this.dialogFormVisible = false
          this.$message({
            message: '更新成功',
            type: 'success'
          })
          this.getList()
        })
      }
    },
    handleDelete(row) {
      this.$confirm('确认删除该策略吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSmsPolicy(row.id).then(() => {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.getList()
        })
      })
    },
    handleModifyStatus(row, status) {
      const text = status == 1 ? '启用' : '禁用'
      this.$confirm(`确认${text}该策略吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateSmsPolicy(row.id, { ...row, status }).then(() => {
          this.$message({
            message: `${text}成功`,
            type: 'success'
          })
          this.getList()
        })
      }).catch(() => {
        row.status = row.status == 1? 0 : 1
      })
    },
    resetFilter() {
      this.listQuery = {
        page: 1,
        size: 10,
        status: null,
        searchText: '',
        type: null
      }
      this.getList()
    },
    handleDetail(row) {
      this.detailData = JSON.parse(JSON.stringify(row))
      this.detailVisible = true
    }
  }
}
</script>

<style>
.filter-container {
  padding-bottom: 10px;
}
.filter-item {
  margin-right: 10px;
}
</style>