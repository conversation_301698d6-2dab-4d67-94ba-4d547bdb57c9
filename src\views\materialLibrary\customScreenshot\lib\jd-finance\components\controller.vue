<template>
  <div class="controller-container">
    <!-- 使用截图按钮组件 -->
    <capture-button 
      :preview-url="previewUrl"
      :device-os="form.device.os"
      @capture-screenshot="$emit('capture-screenshot')"
    />
    
    <el-form :model="form" label-width="90px" size="small">
      <!-- 使用平台类型设置组件 -->
      <platform-type-setting 
        :deviceConfig="form.device" 
        @update:platform="updatePlatform" 
      />
      
      <!-- 使用统一的状态栏设置组件 -->
      <status-bar-setting 
        :statusBarConfig="form.statusBar" 
        @update:statusBar="updateStatusBar" 
      />

      <!-- 京东金融特定配置项 -->
      <el-divider>京东金融配置</el-divider>
      
      <!-- 额度信息配置 -->
      <el-form-item label="额度值" prop="quotaValue">
        <el-input v-model="form.quotaValue" placeholder="请输入额度值" @change="handleConfigChange"></el-input>
      </el-form-item>
      
      <el-form-item label="基础额度值" prop="baseValue">
        <el-input v-model="form.baseValue" placeholder="请输入基础额度值" @change="handleConfigChange"></el-input>
      </el-form-item>
      
      <!-- 额度列表配置 -->
      <el-divider>额度列表配置</el-divider>
      
      <div v-for="(item, index) in form.listItems" :key="index" class="list-item">
        <div class="list-item-header">
          <span>项目 {{ index + 1 }}</span>
        </div>
        
        <el-form-item :label="'标题'" :prop="`listItems[${index}].title`">
          <el-input v-model="item.title" placeholder="请输入标题" @change="handleConfigChange"></el-input>
        </el-form-item>
        
        <el-form-item :label="'说明'" :prop="`listItems[${index}].value`">
          <el-input v-model="item.value" placeholder="请输入说明" @change="handleConfigChange"></el-input>
        </el-form-item>
      </div>
      
    </el-form>
  </div>
</template>

<script>
import StatusBarSetting from '../../components/StatusBarSetting.vue'
import PlatformTypeSetting from '../../components/PlatformTypeSetting.vue'
import CaptureButton from '../../components/CaptureButton.vue'

export default {
  name: 'JdFinanceController', 
  components: {
    StatusBarSetting,
    PlatformTypeSetting,
    CaptureButton
  },
  props: {
    previewUrl: {
      type: String,
      default: null
    },
    configData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      form: this.deepClone(this.configData)
    }
  },
  watch: {
    configData: {
      handler(newVal) {
        this.form = this.deepClone(newVal);
      },
      deep: true
    }
  },
  methods: {
    updatePlatform(deviceConfig) {
      this.form.device = deviceConfig;
      this.handleConfigChange();
    },
    updateStatusBar(statusBarConfig) {
      this.form.statusBar = statusBarConfig;
      this.handleConfigChange();
    },
    handleConfigChange() {
      // 使用深拷贝确保传递的是新对象
      this.$emit('update:config', this.deepClone(this.form));
    },
    // 内部实现的深拷贝函数
    deepClone(obj) {
      if (obj === null || typeof obj !== 'object') {
        return obj;
      }
      
      // 处理Date
      if (obj instanceof Date) {
        return new Date(obj.getTime());
      }
      
      // 处理Array
      if (Array.isArray(obj)) {
        return obj.map(item => this.deepClone(item));
      }
      
      // 处理Object
      const clonedObj = {};
      Object.keys(obj).forEach(key => {
        clonedObj[key] = this.deepClone(obj[key]);
      });
      
      return clonedObj;
    }
  }
}
</script>

<style scoped lang="scss">
.controller-container {
  .el-form-item {
    margin-bottom: 22px;

    .el-input + .el-input {
      margin-top: 10px;
    }

    .el-checkbox {
      margin-top: 10px;
      display: block;
    }
  }

  .status-bar-icons {
    display: flex;
    align-items: center;

    .el-select {
      width: 100%;
    }
  }

  .unit-label {
    margin-left: 5px;
    color: #606266;
  }

  .form-row {
    display: flex;
    gap: 20px;
  }

  .form-item-half {
    width: calc(50% - 10px);
  }

  .action-buttons {
    margin: 20px 0;
    display: flex;
    gap: 10px;
  }

  .tip-text {
    margin-top: 5px;
    font-size: 12px;
    color: #E6A23C;
    line-height: 1.4;
  }

  .list-item {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px dashed #DCDFE6;
    border-radius: 4px;
    
    .list-item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      font-weight: bold;
    }
  }
}
</style>
