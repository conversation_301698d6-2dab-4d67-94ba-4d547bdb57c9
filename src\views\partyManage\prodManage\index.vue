<template>
  <div class="app-container">
    <el-table  border :data="productList">
      <el-table-column label="推广ID" prop="id" align="center" />
      <el-table-column label="推广名称" prop="name" align="center" />
      <el-table-column label="状态" align="center">
        <template  slot-scope="{row}">
          <div>
            <el-switch


              v-model="row.status"
              :active-value="1"
              :inactive-value="2"
              active-text="上线"
              inactive-text="下线"
              @change="changeStatus($event, row.id)"
            >
            </el-switch>
          </div> </template
      ></el-table-column>
      <!-- <el-table-column label="合作价格" prop="cooperationCost" align="center" /> -->
      <!-- <el-table-column label="表单量" align="center" prop="putinPush" /> -->
      <el-table-column label="消耗" prop="consumePrice" align="center" />
      <!-- <el-table-column label="返点金额" prop="rebatePrice" align="center" /> -->
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getProductList,productUpdateStatus } from "@/api/partyManage";
export default {
  name:'ProdManage',
  data() {
    return {

      total: 0,
      productList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  methods: {
    getList() {

      getProductList(this.queryParams).then((response) => {
        this.productList = response.rows;
        this.total = response.total;

      });
    },
    changeStatus(e, id) {
      productUpdateStatus({ status: e, id }).then((res) => {
        if (res.code == 200) {
          this.$message.success("修改成功");
          this.getList();
        }
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
</style>
