<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form ref="queryForm" :model="queryParams" inline>
      <el-form-item label="渠道名称" prop="channelIds">
        <el-select
          v-model="queryParams.channelIds"
          placeholder="请选择渠道"
          clearable
          filterable
          multiple
          collapse-tags
          style="width: 240px"
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.id"
            :label="`${item.id}-${item.channelName}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="策略名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入策略名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-s-data"
          @click="showStatDialog"
        >统计分析</el-button>
      </el-col>
    </el-row>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="policyList"
      row-key="id"
    >
      <el-table-column label="渠道信息" align="center" min-width="200">
        <template slot-scope="scope">
          <div v-for="(channelId, index) in parseChannelIds(scope.row.channelIds)" :key="index">
            {{ channelId }}-{{ getChannelNameById(channelId) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="策略名称" prop="name" align="center" />
      <el-table-column label="策略配置" align="center" min-width="300">
        <template slot-scope="scope">
          <div class="policy-summary">
            <span v-if="scope.row.policies && scope.row.policies.length > 0" class="policy-list">
              <template v-for="(item, index) in scope.row.policies">
                <span :key="index" class="policy-text">
                  {{ getSmsTemplateName(item.smsKey) }}: {{ item.weight }}%
                </span>
                <span :key="`dot-${index}`" class="policy-separator" v-if="index < scope.row.policies.length-1">、</span>
              </template>
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 新增/编辑对话框 -->
    <policy-form
      ref="policyForm"
      :visible.sync="formVisible"
      :title="formTitle"
      :form-data="formData"
      :channel-options="channelOptions"
      :sms-template-options="smsTemplateOptions"
      @submit="submitForm"
    />

    <!-- 统计分析弹窗 -->
    <el-dialog
      title="短信发送统计分析"
      :visible.sync="statDialogVisible"
      width="900px"
      append-to-body
    >
      <div class="filter-row">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="getStatisticsData"
        ></el-date-picker>
        <el-button type="primary" @click="getStatisticsData">查询</el-button>
      </div>
      <stat-chart ref="statChart" :statistics-data="statisticsData" v-if="Object.keys(statisticsData).length > 0"/>
      <el-empty description="暂无数据" v-else></el-empty>
    </el-dialog>
  </div>
</template>

<script>
import { getAllChannelList } from '@/api/channeManage/channelList'
import { listSmsPolicy, getSmsTemplates, deleteSmsPolicy, getStatistics } from '@/api/threeParty/notifySmsPolicy'
import Pagination from '@/components/Pagination'
import PolicyForm from './components/PolicyForm'
import StatChart from './components/StatChart'
import dayjs from 'dayjs'

export default {
  name: 'NotifySmsPolicy',
  components: {
    Pagination,
    PolicyForm,
    StatChart
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 策略表格数据
      policyList: [],
      // 弹出层标题
      formTitle: '',
      // 是否显示弹出层
      formVisible: false,
      // 表单数据
      formData: {},
      // 统计对话框可见性
      statDialogVisible: false,
      // 查询参数
      queryParams: {
        page: 1,
        size: 10,
        channelIds: [],
        name: ""
      },
      // 渠道选项
      channelOptions: [],
      // 短信模板选项
      smsTemplateOptions: [],
      // 统计数据
      statisticsData: {},
      // 日期范围
      dateRange: []
    }
  },
  created() {
    // 初始化默认选中当天
    const today = dayjs()
    
    this.dateRange = [
      today.format('YYYY-MM-DD 00:00:00'),
      today.format('YYYY-MM-DD 23:59:59')
    ]
    
    this.getList()
    this.getChannelOptions()
    this.getSmsTemplateOptions()
  },
  methods: {
    // 获取列表数据
    getList() {
      this.loading = true
      listSmsPolicy(this.queryParams).then(response => {
        this.policyList = response.records || []
        this.total = parseInt(response.total) || 0
        this.loading = false
      })
    },
    // 获取渠道选项
    getChannelOptions() {
      getAllChannelList().then(response => {
        this.channelOptions = response.data || []
      })
    },
    // 获取短信模板选项
    getSmsTemplateOptions() {
      getSmsTemplates().then(response => {
        this.smsTemplateOptions = response.data || []
      })
    },
    // 根据短信模板key获取名称
    getSmsTemplateName(smsKey) {
      const template = this.smsTemplateOptions.find(item => item.smsTemplateKey === smsKey)
      return template ? template.name : smsKey
    },
    // 百分比格式化
    percentFormat(percentage) {
      return percentage + '%'
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    // 重置按钮操作
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.channelIds = []
      this.queryParams.name = ""
      this.handleQuery()
    },
    // 新增按钮操作
    handleAdd() {
      this.formData = {
        name: '',
        channelIds: [],
        policies: [{
          smsKey: '',
          weight: 100,
          enabled: true
        }]
      }
      this.formTitle = '添加策略'
      this.formVisible = true
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.formData = JSON.parse(JSON.stringify(row))
      this.formTitle = '修改策略'
      this.formVisible = true
    },
    // 删除按钮操作
    handleDelete(row) {
      const id = row.id
      this.$confirm('是否确认删除策略?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return deleteSmsPolicy(id)
      }).then(() => {
        this.getList()
        this.$message({
          type: 'success',
          message: '删除成功'
        })
      })
    },
    // 表单提交操作
    submitForm() {
      this.getList()
    },
    // 显示统计对话框
    showStatDialog() {
      this.statDialogVisible = true
      this.getStatisticsData()
    },
    // 获取统计数据
    getStatisticsData() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        this.$message({
          message: '请选择完整的日期范围',
          type: 'warning'
        })
        return
      }
      
      const params = {
        startTime: this.dateRange[0],
        endTime: this.dateRange[1]
      }
      
      getStatistics(params).then(response => {
        this.statisticsData = response.data || {}
      })
    },
    // 解析渠道ID数组
    parseChannelIds(channelIds) {
      try {
        return typeof channelIds === 'string' ? JSON.parse(channelIds) : channelIds || []
      } catch (e) {
        return []
      }
    },
    // 根据渠道ID获取渠道名称
    getChannelNameById(channelId) {
      const channel = this.channelOptions.find(item => item.id == channelId)
      return channel ? channel.channelName : '未知渠道'
    }
  }
}
</script>

<style scoped>
.policy-item {
  margin-bottom: 10px;
}
.policy-tags {
  display: flex;
  flex-wrap: wrap;
  max-width: 300px;
}
.policy-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.policy-list {
  line-height: 1.5;
}
.policy-text {
  font-size: 12px;
  color: #606266;
}
.policy-separator {
  margin: 0 2px;
  color: #909399;
}
.filter-row {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}
.filter-row .el-button {
  margin-left: 10px;
}
</style> 