import request from '@/utils/request'

//获取请款列表
export function getPersonalCashOutList(data) {
  return request({
    url: "/system/personalCashOut/personalCashOut/list",
    method: "get",
    params: data

  })
}


//查询请款报销
export function getReimbursementDetail(data){
  return request({
    url:"/system/personalCashOut/reimbursement/detail",
    method:"get",
    params:data
  })
}
//查询外部返点详情
export function getRebatesDeatil(data){
  return request({
    url:"/system/personalCashOut/rebates/detail",
    method:"get",
    params:data
  })
}


//请款审核通过
export function checkDoPass(data){
  return request({
    url:"/system/personalCashOut/check/doPass",
    method:"post",
    data
  })
}
//请款审核拒绝
export function checkDoReject(data){
  return request({
    url:"/system/personalCashOut/check/doReject",
    method:"post",
    data
  })
}
